"""
UnveilVPN Shop - Remnawave API Integration
Интеграция с Remnawave VPN панелью
"""

import time
import aiohttp
import asyncio
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime, timezone

from .. import glv


class RemnaWaveAPIError(Exception):
    """Исключение для ошибок Remnawave API"""
    pass


class RemnaWave:
    """
    Класс для работы с Remnawave API
    """
    
    def __init__(self, panel_url: str, api_key: str, timeout: int = 30):
        if not panel_url:
            raise ValueError("REMNAWAVE_PANEL_URL не может быть пустым")
        if not api_key:
            raise ValueError("REMNAWAVE_API_KEY не может быть пустым")

        self.panel_url = panel_url.rstrip('/')
        self.api_key = api_key
        self.timeout = timeout
        self.logger = logging.getLogger(self.__class__.__name__)

        # Заголовки для API запросов
        self.headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json',
            'User-Agent': 'UnveilVPN-Shop/1.0'
        }
    
    async def _send_request(
        self, 
        method: str, 
        endpoint: str, 
        data: Optional[Dict[str, Any]] = None,
        params: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Выполнение HTTP запроса к Remnawave API
        """
        url = f"{self.panel_url}{endpoint}"
        
        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout)) as session:
            try:
                kwargs = {
                    'headers': self.headers,
                    'ssl': False  # Для разработки, в продакшене использовать SSL
                }
                
                if data:
                    kwargs['json'] = data
                if params:
                    kwargs['params'] = params
                
                async with session.request(method, url, **kwargs) as response:
                    if 200 <= response.status < 300:
                        if response.content_type == 'application/json':
                            return await response.json()
                        else:
                            return {'success': True, 'data': await response.text()}
                    else:
                        error_text = await response.text()
                        self.logger.error(f"API Error {response.status}: {error_text}")
                        raise RemnaWaveAPIError(f"HTTP {response.status}: {error_text}")
                        
            except aiohttp.ClientError as e:
                self.logger.error(f"HTTP ошибка при запросе к {url}: {e}")
                raise RemnaWaveAPIError(f"Ошибка соединения: {e}")
            except asyncio.TimeoutError:
                self.logger.error(f"Таймаут при запросе к {url}")
                raise RemnaWaveAPIError("Таймаут запроса")
    
    async def check_connection(self) -> bool:
        """Проверка соединения с Remnawave API"""
        try:
            await self._send_request('GET', '/api/health')
            return True
        except Exception as e:
            self.logger.error(f"Ошибка соединения с Remnawave: {e}")
            return False
    
    async def get_user(self, user_id: str) -> Optional[Dict[str, Any]]:
        """
        Получение информации о пользователе
        
        Args:
            user_id: UUID пользователя
            
        Returns:
            Информация о пользователе или None если не найден
        """
        try:
            response = await self._send_request('GET', f'/api/users/{user_id}')
            return response
        except RemnaWaveAPIError as e:
            if "404" in str(e):
                return None
            raise
    
    async def get_users(self, limit: int = 100, offset: int = 0) -> Dict[str, Any]:
        """
        Получение списка пользователей
        
        Args:
            limit: Максимальное количество пользователей
            offset: Смещение для пагинации
            
        Returns:
            Список пользователей с метаданными
        """
        params = {'limit': limit, 'offset': offset}
        return await self._send_request('GET', '/api/users', params=params)
    
    async def create_user(self, user_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Создание нового пользователя
        
        Args:
            user_data: Данные пользователя
            
        Returns:
            Информация о созданном пользователе
        """
        return await self._send_request('POST', '/api/users', data=user_data)
    
    async def update_user(self, user_id: str, user_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Обновление пользователя
        
        Args:
            user_id: UUID пользователя
            user_data: Новые данные пользователя
            
        Returns:
            Обновленная информация о пользователе
        """
        return await self._send_request('PUT', f'/api/users/{user_id}', data=user_data)
    
    async def delete_user(self, user_id: str) -> bool:
        """
        Удаление пользователя
        
        Args:
            user_id: UUID пользователя
            
        Returns:
            True если успешно удален
        """
        try:
            await self._send_request('DELETE', f'/api/users/{user_id}')
            return True
        except RemnaWaveAPIError:
            return False
    
    async def suspend_user(self, user_id: str, reason: str = "Payment expired") -> bool:
        """
        Приостановка пользователя
        
        Args:
            user_id: UUID пользователя
            reason: Причина приостановки
            
        Returns:
            True если успешно приостановлен
        """
        data = {
            'reason': reason,
            'suspended_at': datetime.now(timezone.utc).isoformat()
        }
        try:
            await self._send_request('POST', f'/api/users/{user_id}/suspend', data=data)
            return True
        except RemnaWaveAPIError:
            return False
    
    async def activate_user(self, user_id: str) -> bool:
        """
        Активация пользователя
        
        Args:
            user_id: UUID пользователя
            
        Returns:
            True если успешно активирован
        """
        try:
            await self._send_request('POST', f'/api/users/{user_id}/activate')
            return True
        except RemnaWaveAPIError:
            return False
    
    async def create_subscription(
        self, 
        user_id: str, 
        tariff_name: str, 
        duration_days: int,
        traffic_limit_gb: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Создание подписки для пользователя
        
        Args:
            user_id: UUID пользователя
            tariff_name: Название тарифа
            duration_days: Длительность в днях
            traffic_limit_gb: Лимит трафика в ГБ
            
        Returns:
            Информация о созданной подписке
        """
        data = {
            'user_id': user_id,
            'tariff_name': tariff_name,
            'duration_days': duration_days,
            'traffic_limit_gb': traffic_limit_gb,
            'start_date': datetime.now(timezone.utc).isoformat()
        }
        return await self._send_request('POST', '/api/subscriptions', data=data)
    
    async def get_subscription(self, user_id: str) -> Optional[Dict[str, Any]]:
        """
        Получение информации о подписке пользователя
        
        Args:
            user_id: UUID пользователя
            
        Returns:
            Информация о подписке или None
        """
        try:
            response = await self._send_request('GET', f'/api/users/{user_id}/subscription')
            return response
        except RemnaWaveAPIError as e:
            if "404" in str(e):
                return None
            raise
    
    async def extend_subscription(
        self, 
        user_id: str, 
        additional_days: int
    ) -> Dict[str, Any]:
        """
        Продление подписки
        
        Args:
            user_id: UUID пользователя
            additional_days: Дополнительные дни
            
        Returns:
            Обновленная информация о подписке
        """
        data = {'additional_days': additional_days}
        return await self._send_request('POST', f'/api/users/{user_id}/extend', data=data)


    async def get_user_configs(self, user_id: str) -> List[Dict[str, Any]]:
        """
        Получение конфигураций пользователя

        Args:
            user_id: UUID пользователя

        Returns:
            Список конфигураций
        """
        try:
            response = await self._send_request('GET', f'/api/users/{user_id}/configs')
            return response.get('configs', [])
        except RemnaWaveAPIError:
            return []

    async def create_config(
        self,
        user_id: str,
        device_name: str,
        protocol: str = 'vless'
    ) -> Dict[str, Any]:
        """
        Создание новой конфигурации

        Args:
            user_id: UUID пользователя
            device_name: Название устройства
            protocol: Протокол (vless, vmess, trojan, shadowsocks)

        Returns:
            Информация о созданной конфигурации
        """
        data = {
            'device_name': device_name,
            'protocol': protocol
        }
        return await self._send_request('POST', f'/api/users/{user_id}/configs', data=data)

    async def delete_config(self, config_id: str) -> bool:
        """
        Удаление конфигурации

        Args:
            config_id: UUID конфигурации

        Returns:
            True если успешно удалена
        """
        try:
            await self._send_request('DELETE', f'/api/configs/{config_id}')
            return True
        except RemnaWaveAPIError:
            return False

    async def get_user_stats(self, user_id: str) -> Dict[str, Any]:
        """
        Получение статистики пользователя

        Args:
            user_id: UUID пользователя

        Returns:
            Статистика использования
        """
        try:
            return await self._send_request('GET', f'/api/users/{user_id}/stats')
        except RemnaWaveAPIError:
            return {}

    async def get_servers(self) -> List[Dict[str, Any]]:
        """
        Получение списка серверов

        Returns:
            Список доступных серверов
        """
        try:
            response = await self._send_request('GET', '/api/servers')
            return response.get('servers', [])
        except RemnaWaveAPIError:
            return []

    async def test_connection(self) -> bool:
        """
        Тестирование подключения к Remnawave панели

        Returns:
            True если подключение успешно

        Raises:
            RemnaWaveAPIError: При ошибке подключения
        """
        try:
            # Пытаемся получить информацию о панели
            response = await self._send_request("GET", "/api/panel/info")

            if response and response.get("status") == "success":
                logging.info("Подключение к Remnawave панели успешно")
                return True
            else:
                raise RemnaWaveAPIError("Неожиданный ответ от панели")

        except asyncio.TimeoutError:
            raise RemnaWaveAPIError("Таймаут подключения к Remnawave панели")
        except aiohttp.ClientConnectorError:
            raise RemnaWaveAPIError("Ошибка подключения к Remnawave панели")
        except Exception as e:
            if "401" in str(e):
                raise RemnaWaveAPIError("Ошибка аутентификации: неверный API ключ")
            elif "403" in str(e):
                raise RemnaWaveAPIError("Доступ запрещен: недостаточно прав")
            elif "500" in str(e):
                raise RemnaWaveAPIError("Ошибка сервера Remnawave")
            else:
                raise RemnaWaveAPIError(f"Ошибка подключения: {e}")

    async def close(self):
        """Закрытие сессии"""
        if self.session:
            await self.session.close()
            self.session = None


# Протоколы Remnawave
REMNAWAVE_PROTOCOLS = {
    "vless": {
        "name": "VLESS",
        "variants": [
            {"name": "TCP Reality", "flow": "xtls-rprx-vision"},
            {"name": "WebSocket", "path": "/"},
            {"name": "gRPC", "service_name": "vless-grpc"}
        ]
    },
    "vmess": {
        "name": "VMess",
        "variants": [
            {"name": "TCP", "header": "none"},
            {"name": "WebSocket", "path": "/"},
            {"name": "HTTP/2", "host": "example.com"}
        ]
    },
    "trojan": {
        "name": "Trojan",
        "variants": [
            {"name": "TCP", "sni": "example.com"},
            {"name": "WebSocket", "path": "/trojan"},
            {"name": "gRPC", "service_name": "trojan-grpc"}
        ]
    },
    "shadowsocks": {
        "name": "Shadowsocks",
        "variants": [
            {"name": "TCP", "method": "chacha20-ietf-poly1305"},
            {"name": "UDP", "method": "aes-256-gcm"}
        ]
    }
}


def get_protocols() -> Dict[str, Any]:
    """
    Получение конфигурации протоколов для Remnawave

    Returns:
        Словарь с конфигурацией протоколов
    """
    enabled_protocols = glv.config.get('REMNAWAVE_PROTOCOLS', ['vless', 'vmess', 'trojan', 'shadowsocks'])

    protocols = {}
    for proto in enabled_protocols:
        if proto in REMNAWAVE_PROTOCOLS:
            protocols[proto] = REMNAWAVE_PROTOCOLS[proto]

    return protocols


async def generate_test_subscription(telegram_id: int, protocols: List[str] = None) -> str:
    """
    Генерация тестовой подписки для пользователя

    Args:
        telegram_id: Telegram ID пользователя
        protocols: Список протоколов

    Returns:
        URL подписки
    """
    if not panel:
        raise RemnaWaveAPIError("Remnawave panel не инициализирован")

    if protocols is None:
        protocols = glv.config.get('REMNAWAVE_PROTOCOLS', ['vless'])

    # Создаем уникальное имя пользователя
    username = f"test_{telegram_id}_{int(time.time())}"

    # Данные для создания тестового пользователя
    user_data = {
        'username': username,
        'telegram_id': telegram_id,
        'email': f"{username}@unveilvpn.local",
        'protocols': protocols
    }

    try:
        # Создаем пользователя
        user = await panel.create_user(user_data)
        user_id = user.get('id')

        if not user_id:
            raise RemnaWaveAPIError("Не удалось получить ID созданного пользователя")

        # Создаем тестовую подписку
        test_duration = glv.config.get('TEST_PERIOD_DURATION_HOURS', 168) // 24  # Конвертируем часы в дни
        await panel.create_subscription(
            user_id=user_id,
            tariff_name="test",
            duration_days=test_duration
        )

        # Создаем конфигурацию по умолчанию
        await panel.create_config(user_id, "Default", protocols[0])

        # Возвращаем URL подписки
        subscription_url = glv.config.get('REMNAWAVE_SUBSCRIPTION_URL', glv.config.get('REMNAWAVE_PANEL_URL'))
        return f"{subscription_url}/sub/{user_id}"

    except Exception as e:
        raise RemnaWaveAPIError(f"Ошибка создания тестовой подписки: {e}")


async def generate_remnawave_subscription(
    telegram_id: int,
    tariff_name: str,
    duration_days: int,
    protocols: List[str] = None,
    traffic_limit_gb: Optional[int] = None
) -> str:
    """
    Генерация подписки Remnawave для пользователя

    Args:
        telegram_id: Telegram ID пользователя
        tariff_name: Название тарифа
        duration_days: Длительность в днях
        protocols: Список протоколов
        traffic_limit_gb: Лимит трафика в ГБ

    Returns:
        URL подписки
    """
    if not panel:
        raise RemnaWaveAPIError("Remnawave panel не инициализирован")

    if protocols is None:
        protocols = glv.config.get('REMNAWAVE_PROTOCOLS', ['vless'])

    # Создаем уникальное имя пользователя
    username = f"user_{telegram_id}_{int(time.time())}"

    # Данные для создания пользователя
    user_data = {
        'username': username,
        'telegram_id': telegram_id,
        'email': f"{username}@unveilvpn.local",
        'protocols': protocols
    }

    try:
        # Создаем пользователя
        user = await panel.create_user(user_data)
        user_id = user.get('id')

        if not user_id:
            raise RemnaWaveAPIError("Не удалось получить ID созданного пользователя")

        # Создаем подписку
        await panel.create_subscription(
            user_id=user_id,
            tariff_name=tariff_name,
            duration_days=duration_days,
            traffic_limit_gb=traffic_limit_gb
        )

        # Создаем конфигурацию по умолчанию
        await panel.create_config(user_id, "Default", protocols[0])

        # Возвращаем URL подписки
        subscription_url = glv.config.get('REMNAWAVE_SUBSCRIPTION_URL', glv.config.get('REMNAWAVE_PANEL_URL'))
        return f"{subscription_url}/sub/{user_id}"

    except Exception as e:
        raise RemnaWaveAPIError(f"Ошибка создания подписки: {e}")


async def get_remnawave_profile(user_id: str) -> Optional[Dict[str, Any]]:
    """
    Получение профиля пользователя Remnawave

    Args:
        user_id: UUID пользователя

    Returns:
        Профиль пользователя или None
    """
    if not panel:
        return None

    try:
        user = await panel.get_user(user_id)
        if not user:
            return None

        # Получаем подписку и статистику
        subscription = await panel.get_subscription(user_id)
        stats = await panel.get_user_stats(user_id)
        configs = await panel.get_user_configs(user_id)

        # Формируем профиль в формате совместимом с Marzban
        profile = {
            'id': user_id,
            'username': user.get('username'),
            'telegram_id': user.get('telegram_id'),
            'status': user.get('status', 'active'),
            'subscription': subscription,
            'stats': stats,
            'configs': configs,
            'created_at': user.get('created_at'),
            'updated_at': user.get('updated_at')
        }

        return profile

    except Exception as e:
        logging.error(f"Ошибка получения профиля пользователя {user_id}: {e}")
        return None


# Инициализация глобального экземпляра
panel = None
try:
    panel_url = glv.config.get('REMNAWAVE_PANEL_URL')
    api_key = glv.config.get('REMNAWAVE_API_KEY')

    if panel_url and api_key:
        panel = RemnaWave(
            panel_url=panel_url,
            api_key=api_key
        )
        logging.info("Глобальный экземпляр Remnawave API инициализирован")
    else:
        logging.warning("Remnawave API не инициализирован: отсутствуют переменные окружения")
except Exception as e:
    logging.error(f"Ошибка инициализации глобального экземпляра Remnawave API: {e}")
    panel = None
