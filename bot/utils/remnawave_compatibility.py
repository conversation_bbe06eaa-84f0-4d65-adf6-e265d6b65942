"""
UnveilVPN Shop - Remnawave Compatibility Layer
Слой совместимости для работы с Remnawave API
"""

import logging
from typing import Dict, Any, Optional
from .remnawave_api import (
    RemnaWave, 
    RemnaWaveAPIError,
    generate_remnawave_subscription,
    generate_test_subscription as remnawave_generate_test_subscription,
    get_remnawave_profile
)
from .. import glv

logger = logging.getLogger(__name__)

# Протоколы для Remnawave API
PROTOCOLS = {
    "vmess": [
        {},
        ["VMess TCP"]
    ],
    "vless": [
        {
            "flow": "xtls-rprx-vision"
        },
        ["VLESS TCP REALITY"]
    ],
    "trojan": [
        {},
        ["Trojan Websocket TLS"]
    ],
    "shadowsocks": [
        {
            "method": "chacha20-ietf-poly1305"
        },
        ["Shadowsocks TCP"]
    ]
}


class RemnaWaveCompatibility:
    """
    Класс для работы с Remnawave API
    """
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)

        # Инициализация Remnawave API
        try:
            panel_url = glv.config.get('REMNAWAVE_PANEL_URL')
            api_key = glv.config.get('REMNAWAVE_API_KEY')

            if panel_url and api_key:
                self.remnawave = RemnaWave(
                    panel_url=panel_url,
                    api_key=api_key
                )
                self.logger.info("Remnawave API успешно инициализирован")
            else:
                self.logger.warning("Remnawave API не инициализирован: отсутствуют REMNAWAVE_PANEL_URL или REMNAWAVE_API_KEY")
                self.remnawave = None
        except Exception as e:
            self.logger.error(f"Ошибка инициализации Remnawave API: {e}")
            self.remnawave = None
    
    def get_token(self) -> str:
        """
        Эмуляция получения токена (не требуется для Remnawave)
        """
        self.logger.info("Token request - not needed for Remnawave API")
        return "remnawave_api_key_used"
    
    async def get_user(self, username: str) -> Optional[Dict[str, Any]]:
        """
        Получение пользователя по username
        """
        try:
            if not self.remnawave:
                raise RemnaWaveAPIError("Remnawave API не инициализирован")
            
            # В Remnawave используются UUID, но для совместимости
            # попробуем найти пользователя по username
            users = await self.remnawave.get_users()
            for user in users:
                if user.get('username') == username:
                    return await self._format_user_for_remnawave(user)
            
            return None
            
        except Exception as e:
            self.logger.error(f"Ошибка получения пользователя {username}: {e}")
            return None
    
    async def get_users(self) -> Dict[str, Any]:
        """
        Получение списка всех пользователей
        """
        try:
            if not self.remnawave:
                raise RemnaWaveAPIError("Remnawave API не инициализирован")
            
            users = await self.remnawave.get_users()
            formatted_users = []
            
            for user in users:
                formatted_user = await self._format_user_for_remnawave(user)
                if formatted_user:
                    formatted_users.append(formatted_user)
            
            return {'users': formatted_users}
            
        except Exception as e:
            self.logger.error(f"Ошибка получения списка пользователей: {e}")
            return {'users': []}
    
    async def add_user(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Создание нового пользователя
        """
        try:
            if not self.remnawave:
                raise RemnaWaveAPIError("Remnawave API не инициализирован")
            
            # Конвертируем данные в формат Remnawave
            remnawave_data = self._convert_to_remnawave(data)

            user = await self.remnawave.create_user(remnawave_data)
            return await self._format_user_for_remnawave(user)
            
        except Exception as e:
            self.logger.error(f"Ошибка создания пользователя: {e}")
            raise
    
    async def modify_user(self, username: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Изменение пользователя
        """
        try:
            if not self.remnawave:
                raise RemnaWaveAPIError("Remnawave API не инициализирован")
            
            # Находим пользователя по username
            user = await self.get_user(username)
            if not user:
                raise RemnaWaveAPIError(f"Пользователь {username} не найден")
            
            user_id = user.get('id')
            if not user_id:
                raise RemnaWaveAPIError("Не удалось получить ID пользователя")
            
            # Конвертируем данные и обновляем
            remnawave_data = self._convert_to_remnawave(data)
            updated_user = await self.remnawave.update_user(user_id, remnawave_data)

            return await self._format_user_for_remnawave(updated_user)
            
        except Exception as e:
            self.logger.error(f"Ошибка изменения пользователя {username}: {e}")
            raise
    
    async def _format_user_for_remnawave(self, remnawave_user: Dict[str, Any]) -> Dict[str, Any]:
        """
        Форматирование пользователя Remnawave
        """
        try:
            user_id = remnawave_user.get('id')
            subscription = await self.remnawave.get_subscription(user_id)
            stats = await self.remnawave.get_user_stats(user_id)
            
            # Формируем URL подписки
            subscription_url = glv.config.get('REMNAWAVE_SUBSCRIPTION_URL', 
                                            glv.config.get('REMNAWAVE_PANEL_URL'))
            full_subscription_url = f"{subscription_url}/sub/{user_id}"
            
            return {
                'id': user_id,
                'username': remnawave_user.get('username'),
                'status': remnawave_user.get('status', 'active'),
                'expire': subscription.get('end_date_timestamp') if subscription else None,
                'data_limit': subscription.get('traffic_limit_bytes', 0) if subscription else 0,
                'data_limit_reset_strategy': 'no_reset',
                'used_traffic': stats.get('used_traffic_bytes', 0) if stats else 0,
                'subscription_url': full_subscription_url,
                'telegram_id': remnawave_user.get('telegram_id'),
                'created_at': remnawave_user.get('created_at'),
                'updated_at': remnawave_user.get('updated_at')
            }
            
        except Exception as e:
            self.logger.error(f"Ошибка форматирования пользователя: {e}")
            return remnawave_user
    
    def _convert_to_remnawave(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Конвертация данных в формат Remnawave
        """
        remnawave_data = {
            'username': data.get('username'),
            'telegram_id': data.get('telegram_id'),
            'email': f"{data.get('username', 'user')}@unveilvpn.local"
        }
        
        # Конвертируем протоколы
        if 'proxies' in data:
            protocols = list(data['proxies'].keys())
            remnawave_data['protocols'] = protocols

        # Конвертируем ограничения
        if 'expire' in data:
            # Remnawave использует подписки, а не прямые ограничения пользователя
            pass

        if 'data_limit' in data:
            # Также обрабатывается через подписки
            pass
        
        return remnawave_data


def get_protocols() -> Dict[str, Any]:
    """
    Получение протоколов для Remnawave
    """
    proxies = {}
    inbounds = {}
    
    protocols = glv.config.get('PROTOCOLS', ['vless', 'vmess', 'trojan', 'shadowsocks'])
    
    for proto in protocols:
        l = proto.lower()
        if l not in PROTOCOLS:
            continue
        proxies[l] = PROTOCOLS[l][0]
        inbounds[l] = PROTOCOLS[l][1]
    
    return {
        "proxies": proxies,
        "inbounds": inbounds
    }


# Создаем экземпляр для работы с Remnawave
panel = RemnaWaveCompatibility()

# Получаем протоколы
ps = get_protocols()


async def check_if_user_exists(name: str) -> bool:
    """
    Проверка существования пользователя
    """
    try:
        user = await panel.get_user(name)
        return user is not None
    except Exception:
        return False


async def get_remnawave_user_profile(tg_id: int):
    """
    Получение профиля пользователя по Telegram ID
    """
    try:
        return await get_remnawave_profile(tg_id)
    except Exception as e:
        logger.error(f"Ошибка получения профиля для {tg_id}: {e}")
        return None


async def generate_test_subscription(username: str):
    """
    Генерация тестовой подписки
    """
    try:
        # Извлекаем telegram_id из username (предполагаем формат user_12345)
        if username.startswith('user_'):
            telegram_id = int(username.split('_')[1])
        else:
            raise ValueError(f"Неверный формат username: {username}")
        
        subscription_url = await remnawave_generate_test_subscription(
            telegram_id=telegram_id,
            protocols=list(ps["proxies"].keys())
        )
        
        return {
            'subscription_url': subscription_url.replace(glv.config.get('REMNAWAVE_PANEL_URL', ''), ''),
            'username': username,
            'status': 'active'
        }
        
    except Exception as e:
        logger.error(f"Ошибка генерации тестовой подписки для {username}: {e}")
        raise


async def generate_remnawave_user_subscription(username: str, good: Dict[str, Any]):
    """
    Генерация подписки
    """
    try:
        # Извлекаем telegram_id из username
        if username.startswith('user_'):
            telegram_id = int(username.split('_')[1])
        else:
            raise ValueError(f"Неверный формат username: {username}")
        
        subscription_url = await generate_remnawave_subscription(
            telegram_id=telegram_id,
            tariff_name=good.get('name', 'basic'),
            duration_days=good.get('months', 1) * 30,
            protocols=list(ps["proxies"].keys())
        )
        
        return {
            'subscription_url': subscription_url.replace(glv.config.get('REMNAWAVE_PANEL_URL', ''), ''),
            'username': username,
            'status': 'active'
        }
        
    except Exception as e:
        logger.error(f"Ошибка генерации подписки для {username}: {e}")
        raise
