from aiogram import Router, F
from aiogram import Dispatcher
from aiogram.types import Message
from aiogram.utils.i18n import gettext as _
from aiogram.utils.i18n import lazy_gettext as __

from .commands import start
from keyboards import get_buy_menu_keyboard, get_back_keyboard, get_main_menu_keyboard, get_subscription_keyboard
from db.methods import can_get_test_sub, update_test_subscription_state, get_user_by_telegram_id
from utils import remnawave_api
from services.payment_vpn_integration import PaymentVPNIntegration
from services.vpn_panel_service import VPNPanelService
from common.config import BotConfig
import glv

router = Router(name="messages-router") 

@router.message(F.text == __("Join 🏄🏻‍♂️"))
async def buy(message: Message):
    await message.answer(_("Choose the appropriate tariff ⬇️"), reply_markup=get_buy_menu_keyboard())

@router.message(F.text == __("My subscription 👤"))
async def profile(message: Message):
    # Получаем пользователя из локальной БД
    user = await get_user_by_telegram_id(message.from_user.id)
    if not user or not user.remnawave_user_id:
        await message.answer(_("Your profile is not active at the moment.\n️\nYou can choose \"5 days free 🆓\" or \"Join 🏄🏻‍♂️\"."), reply_markup=get_main_menu_keyboard())
        return

    # Получаем профиль из Remnawave
    remnawave_profile = await remnawave_api.get_remnawave_profile(user.remnawave_user_id)
    if remnawave_profile is None:
        await message.answer(_("Your profile is not active at the moment.\n️\nYou can choose \"5 days free 🆓\" or \"Join 🏄🏻‍♂️\"."), reply_markup=get_main_menu_keyboard())
        return

    # Формируем URL подписки
    subscription_url = glv.config.get('REMNAWAVE_SUBSCRIPTION_URL', glv.config.get('REMNAWAVE_PANEL_URL'))
    full_subscription_url = f"{subscription_url}/sub/{user.remnawave_user_id}"

    await message.answer(_("Subscription page ⬇️"), reply_markup=get_subscription_keyboard(full_subscription_url))

@router.message(F.text == __("Frequent questions ℹ️"))
async def information(message: Message):
    await message.answer(
        _("Follow the <a href=\"{link}\">link</a> 🔗").format(
            link=glv.config['ABOUT']),
        reply_markup=get_back_keyboard())

@router.message(F.text == __("Support ❤️"))
async def support(message: Message):
    await message.answer(
        _("Follow the <a href=\"{link}\">link</a> and ask us a question. We are always happy to help 🤗").format(
            link=glv.config['SUPPORT_LINK']),
        reply_markup=get_back_keyboard())

@router.message(F.text == __("5 days free 🆓"))
async def test_subscription(message: Message):
    # Проверяем, может ли пользователь получить тестовую подписку
    can_get_test = await can_get_test_sub(message.from_user.id)
    if can_get_test:
        await message.answer(
            _("Your subscription is available in the \"My subscription 👤\" section."),
            reply_markup=get_main_menu_keyboard())
        return

    try:
        # Генерируем тестовую подписку через Remnawave
        subscription_url = await remnawave_api.generate_test_subscription(
            telegram_id=message.from_user.id,
            protocols=glv.config.get('REMNAWAVE_PROTOCOLS', ['vless', 'vmess'])
        )

        # Обновляем статус тестовой подписки
        await update_test_subscription_state(message.from_user.id)

        await message.answer(
            _("Thank you for choice ❤️\n️\n<a href=\"{link}\">Subscribe</a> so you don't miss any announcements ✅\n️\nYour subscription is purchased and available in \"My subscription 👤\".").format(
                link=subscription_url
            ),
            reply_markup=get_main_menu_keyboard()
        )

    except Exception as e:
        await message.answer(
            _("Sorry, there was an error creating your test subscription. Please try again later or contact support."),
            reply_markup=get_main_menu_keyboard()
        )
    
@router.message(F.text == __("⏪ Back"))
async def start_text(message: Message):
    await start(message)

def register_messages(dp: Dispatcher):
    dp.include_router(router)
