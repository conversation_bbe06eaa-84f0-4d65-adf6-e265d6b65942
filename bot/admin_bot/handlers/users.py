"""
UnveilVPN Shop - Admin Bot Users Handler
Обработчик управления пользователями для админ бота
"""

from typing import Dict, Any, List, Optional
from uuid import UUID

from aiogram.types import Message, CallbackQuery
from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import State, StatesGroup
from aiogram.utils.i18n import gettext as _

from ...common.handlers import BaseHandler
from ...common.utils import MessageFormatter, BotUtils
from ...common.exceptions import NotFoundError, ValidationError, BusinessLogicError
from ...common.ui_components import UIComponents
from ...services.user_service import UserService
from ...services.subscription_service import SubscriptionService
from ...services.vpn_panel_service import VPNPanelService
from ..keyboards import (
    UsersManagementKeyboard
)


class UserManagementStates(StatesGroup):
    """Состояния для управления пользователями"""
    searching_user = State()
    editing_user = State()
    blocking_user = State()
    sending_message = State()


class UsersHandler(BaseHandler):
    """
    Обработчик управления пользователями для админ бота
    """
    
    def __init__(self):
        super().__init__(name='AdminUsersHandler')
    
    def _register_handlers(self):
        """Регистрация обработчиков"""
        
        # Основные обработчики
        self.router.callback_query.register(
            self.show_users_menu,
            lambda c: c.data == 'users'
        )
        
        self.router.callback_query.register(
            self.search_users,
            lambda c: c.data == 'search_users'
        )
        
        self.router.callback_query.register(
            self.show_recent_users,
            lambda c: c.data == 'recent_users'
        )
        
        self.router.callback_query.register(
            self.show_active_users,
            lambda c: c.data == 'active_users'
        )
        
        self.router.callback_query.register(
            self.show_blocked_users,
            lambda c: c.data == 'blocked_users'
        )
        
        self.router.callback_query.register(
            self.view_user,
            lambda c: c.data.startswith('user_')
        )
        
        self.router.callback_query.register(
            self.edit_user,
            lambda c: c.data.startswith('edit_user_')
        )
        
        self.router.callback_query.register(
            self.block_user,
            lambda c: c.data.startswith('block_user_')
        )
        
        self.router.callback_query.register(
            self.unblock_user,
            lambda c: c.data.startswith('unblock_user_')
        )
        
        self.router.callback_query.register(
            self.send_message_to_user,
            lambda c: c.data.startswith('message_user_')
        )
        
        self.router.callback_query.register(
            self.reset_user_trial,
            lambda c: c.data.startswith('reset_trial_')
        )
        
        # Обработчики состояний
        self.router.message.register(
            self.process_user_search,
            UserManagementStates.searching_user
        )
        
        self.router.message.register(
            self.process_user_edit,
            UserManagementStates.editing_user
        )
        
        self.router.message.register(
            self.process_block_reason,
            UserManagementStates.blocking_user
        )
        
        self.router.message.register(
            self.process_message_to_user,
            UserManagementStates.sending_message
        )
    
    async def show_users_menu(self, callback: CallbackQuery, **kwargs):
        """Показ главного меню управления пользователями"""
        try:
            user_data = await self._get_user_data(callback)
            db_session = user_data['db_session']
            
            # Получаем статистику пользователей
            user_service = UserService(db_session)
            stats = await user_service.get_users_statistics()
            
            # Создаем красивую карточку статистики
            stats_card = self._create_users_stats_card(stats)
            
            text = _(
                "👥 <b>Управление пользователями</b>\n\n"
                "{stats_card}\n\n"
                "🔧 <b>Доступные действия:</b>\n"
                "• Поиск пользователей\n"
                "• Просмотр активных/заблокированных\n"
                "• Редактирование профилей\n"
                "• Управление подписками\n"
                "• Отправка сообщений\n\n"
                "Выберите действие:"
            ).format(stats_card=stats_card)
            
            keyboard = await UsersManagementKeyboard().get_keyboard()
            
            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Ошибка показа меню пользователей: {e}")
            await self._send_error(callback, _("Ошибка загрузки меню пользователей"))
    
    async def search_users(self, callback: CallbackQuery, state: FSMContext, **kwargs):
        """Поиск пользователей"""
        try:
            await state.set_state(UserManagementStates.searching_user)
            
            text = _(
                "🔍 <b>Поиск пользователей</b>\n\n"
                "Введите один из параметров для поиска:\n\n"
                "• <b>Telegram ID</b> (например: 123456789)\n"
                "• <b>Username</b> (например: @username)\n"
                "• <b>Email</b> (например: <EMAIL>)\n"
                "• <b>VPN ID</b> (например: vpn_user_123)\n\n"
                "💡 Поиск работает по частичному совпадению"
            )
            
            keyboard = await UsersManagementKeyboard().get_keyboard()
            
            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Ошибка поиска пользователей: {e}")
            await self._send_error(callback, _("Ошибка поиска пользователей"))
    
    async def process_user_search(self, message: Message, state: FSMContext, **kwargs):
        """Обработка поискового запроса"""
        try:
            search_query = message.text.strip()
            
            if len(search_query) < 3:
                await message.answer(_("❌ Поисковый запрос должен содержать минимум 3 символа"))
                return
            
            user_data = await self._get_user_data(message)
            db_session = user_data['db_session']
            
            # Выполняем поиск
            user_service = UserService(db_session)
            found_users = await user_service.search_users(search_query, limit=10)
            
            await state.clear()
            
            if not found_users:
                text = _(
                    "🔍 <b>Результаты поиска</b>\n\n"
                    "❌ Пользователи не найдены по запросу: <code>{query}</code>\n\n"
                    "💡 Попробуйте:\n"
                    "• Изменить поисковый запрос\n"
                    "• Использовать другие параметры\n"
                    "• Проверить правильность ввода"
                ).format(query=search_query)
                
                keyboard = await UsersManagementKeyboard().get_keyboard()
            else:
                text_lines = [
                    f"🔍 <b>Результаты поиска</b>",
                    f"",
                    f"Найдено пользователей: {len(found_users)}",
                    f"Запрос: <code>{search_query}</code>",
                    ""
                ]
                
                for user in found_users:
                    user_info = self._format_user_short_info(user)
                    text_lines.append(user_info)
                    text_lines.append("")
                
                text = "\n".join(text_lines)
                
                from ..keyboards import UserListKeyboard
                keyboard = await UserListKeyboard(found_users, "search").get_keyboard()
            
            await message.answer(text, reply_markup=keyboard)
            
        except Exception as e:
            self.logger.error(f"Ошибка обработки поиска: {e}")
            await message.answer(_("❌ Ошибка выполнения поиска"))
    
    async def show_recent_users(self, callback: CallbackQuery, **kwargs):
        """Показ недавно зарегистрированных пользователей"""
        try:
            user_data = await self._get_user_data(callback)
            db_session = user_data['db_session']
            
            user_service = UserService(db_session)
            recent_users = await user_service.get_recent_users(limit=20)
            
            text = await self._get_users_list_text(recent_users, "Недавние пользователи")
            
            from ..keyboards import UserListKeyboard
            keyboard = await UserListKeyboard(recent_users, "recent").get_keyboard()
            
            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Ошибка показа недавних пользователей: {e}")
            await self._send_error(callback, _("Ошибка загрузки недавних пользователей"))
    
    async def show_active_users(self, callback: CallbackQuery, **kwargs):
        """Показ активных пользователей"""
        try:
            user_data = await self._get_user_data(callback)
            db_session = user_data['db_session']
            
            user_service = UserService(db_session)
            active_users = await user_service.get_active_users(limit=20)
            
            text = await self._get_users_list_text(active_users, "Активные пользователи")
            
            from ..keyboards import UserListKeyboard
            keyboard = await UserListKeyboard(active_users, "active").get_keyboard()
            
            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Ошибка показа активных пользователей: {e}")
            await self._send_error(callback, _("Ошибка загрузки активных пользователей"))

    async def show_blocked_users(self, callback: CallbackQuery, **kwargs):
        """Показ заблокированных пользователей"""
        try:
            user_data = await self._get_user_data(callback)
            db_session = user_data['db_session']

            user_service = UserService(db_session)
            blocked_users = await user_service.get_blocked_users(limit=20)

            text = await self._get_users_list_text(blocked_users, "Заблокированные пользователи")

            from ..keyboards import UserListKeyboard
            keyboard = await UserListKeyboard(blocked_users, "blocked").get_keyboard()

            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()

        except Exception as e:
            self.logger.error(f"Ошибка показа заблокированных пользователей: {e}")
            await self._send_error(callback, _("Ошибка загрузки заблокированных пользователей"))

    async def view_user(self, callback: CallbackQuery, **kwargs):
        """Просмотр детальной информации о пользователе"""
        try:
            # Извлекаем ID пользователя
            user_id = callback.data.split('_', 1)[1]
            
            user_data = await self._get_user_data(callback)
            db_session = user_data['db_session']
            
            user_service = UserService(db_session)
            target_user = await user_service.get_user_by_id(UUID(user_id))
            
            if not target_user:
                await callback.answer(_("Пользователь не найден"), show_alert=True)
                return
            
            # Получаем дополнительную информацию
            vpn_panel_service = VPNPanelService(
                panel_url=self.config.remnawave_panel_url,
                api_key=self.config.remnawave_api_key
            )
            subscription_service = SubscriptionService(db_session, vpn_panel_service)
            
            subscription_info = None
            if target_user.vpn_id:
                try:
                    subscription_info = await subscription_service.get_subscription_info(target_user.id)
                except:
                    pass  # Игнорируем ошибки получения подписки
            
            text = await self._get_user_details_text(target_user, subscription_info)
            keyboard = await UsersManagementKeyboard().get_keyboard()
            
            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Ошибка просмотра пользователя: {e}")
            await self._send_error(callback, _("Ошибка загрузки информации о пользователе"))
    
    async def block_user(self, callback: CallbackQuery, state: FSMContext, **kwargs):
        """Блокировка пользователя"""
        try:
            # Извлекаем ID пользователя
            user_id = callback.data.split('_', 2)[2]
            
            await state.update_data(user_id=user_id)
            await state.set_state(UserManagementStates.blocking_user)
            
            text = _(
                "🚫 <b>Блокировка пользователя</b>\n\n"
                "Введите причину блокировки:\n\n"
                "💡 <b>Примеры причин:</b>\n"
                "• Нарушение правил использования\n"
                "• Мошенничество\n"
                "• Спам или злоупотребления\n"
                "• Технические нарушения\n\n"
                "Или отправьте '-' для блокировки без указания причины."
            )
            
            await callback.message.edit_text(text)
            await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Ошибка блокировки пользователя: {e}")
            await self._send_error(callback, _("Ошибка блокировки пользователя"))
    
    async def process_block_reason(self, message: Message, state: FSMContext, **kwargs):
        """Обработка причины блокировки"""
        try:
            reason = message.text.strip()
            if reason == '-':
                reason = None
            
            # Получаем данные из состояния
            data = await state.get_data()
            user_id = data.get('user_id')
            
            user_data = await self._get_user_data(message)
            db_session = user_data['db_session']
            admin_user = user_data['db_user']
            
            # Блокируем пользователя
            user_service = UserService(db_session)
            success = await user_service.block_user(
                user_id=UUID(user_id),
                blocked_by_id=admin_user.id,
                reason=reason
            )
            
            await db_session.commit()
            await state.clear()
            
            if success:
                text = _(
                    "✅ <b>Пользователь заблокирован</b>\n\n"
                    "👤 ID: {user_id}\n"
                    "🚫 Заблокирован администратором\n"
                )
                
                if reason:
                    text += f"💬 Причина: {reason}\n"
                
                text += "\n🔄 Пользователь уведомлен о блокировке."
                
                keyboard = await UsersManagementKeyboard().get_keyboard()
            else:
                text = _("❌ Не удалось заблокировать пользователя")
                keyboard = None
            
            await message.answer(text, reply_markup=keyboard)
            
        except Exception as e:
            self.logger.error(f"Ошибка обработки блокировки: {e}")
            await message.answer(_("❌ Ошибка блокировки пользователя"))
    
    # Вспомогательные методы
    
    def _create_users_stats_card(self, stats: Dict[str, Any]) -> str:
        """Создание карточки статистики пользователей"""
        lines = [
            "┌─────────────────────────────┐",
            "│ 👥 <b>Статистика пользователей</b>   │",
            "├─────────────────────────────┤"
        ]
        
        total_users = stats.get('total_users', 0)
        active_users = stats.get('active_users', 0)
        blocked_users = stats.get('blocked_users', 0)
        new_today = stats.get('new_today', 0)
        
        lines.extend([
            f"│ 📊 Всего: {total_users:>18} │",
            f"│ 🟢 Активных: {active_users:>15} │",
            f"│ 🔴 Заблокированных: {blocked_users:>9} │",
            f"│ 🆕 Новых сегодня: {new_today:>11} │"
        ])
        
        lines.append("└─────────────────────────────┘")
        
        return "\n".join(lines)
    
    def _format_user_short_info(self, user) -> str:
        """Форматирование краткой информации о пользователе"""
        username = f"@{user.username}" if user.username else "Без username"
        status = "🟢 Активен" if not user.is_blocked else "🔴 Заблокирован"
        vpn_status = "📡 VPN" if user.vpn_id else "❌ Без VPN"
        
        return f"👤 ID {user.telegram_id} | {username} | {status} | {vpn_status}"
    
    async def _get_users_list_text(self, users: List, title: str) -> str:
        """Формирование текста списка пользователей"""
        lines = [
            f"👥 <b>{title}</b>",
            ""
        ]
        
        if not users:
            lines.extend([
                _("Пользователи не найдены."),
            ])
            return "\n".join(lines)
        
        lines.append(f"Найдено пользователей: {len(users)}")
        lines.append("")
        
        for user in users[:10]:  # Показываем первые 10
            user_info = self._format_user_short_info(user)
            lines.append(user_info)
        
        if len(users) > 10:
            lines.append(f"... и еще {len(users) - 10} пользователей")
        
        return "\n".join(lines)
    
    async def _get_user_details_text(self, user, subscription_info: Optional[Dict] = None) -> str:
        """Формирование детальной информации о пользователе"""
        lines = [
            f"👤 <b>Пользователь {user.telegram_id}</b>",
            ""
        ]
        
        # Основная информация
        lines.extend([
            f"🆔 Telegram ID: <code>{user.telegram_id}</code>",
            f"👤 Username: @{user.username}" if user.username else "👤 Username: не указан",
            f"📅 Регистрация: {user.created_at.strftime('%d.%m.%Y %H:%M')}",
            f"🕐 Последняя активность: {user.last_activity.strftime('%d.%m.%Y %H:%M')}" if user.last_activity else "🕐 Активность: неизвестно",
        ])
        
        # Статус
        if user.is_blocked:
            lines.append("🔴 Статус: Заблокирован")
            if user.blocked_reason:
                lines.append(f"💬 Причина: {user.blocked_reason}")
        else:
            lines.append("🟢 Статус: Активен")
        
        # VPN информация
        if user.vpn_id:
            lines.extend([
                "",
                f"📡 VPN ID: <code>{user.vpn_id}</code>"
            ])
            
            if subscription_info:
                is_active = subscription_info.get('is_active', False)
                status_text = "Активна" if is_active else "Неактивна"
                lines.append(f"📊 Подписка: {status_text}")
                
                if is_active:
                    expires_at = subscription_info.get('expires_at')
                    if expires_at:
                        lines.append(f"⏰ Истекает: {expires_at}")
                    
                    traffic_used = subscription_info.get('traffic_used', 0)
                    traffic_limit = subscription_info.get('traffic_limit')
                    if traffic_limit:
                        lines.append(f"📊 Трафик: {traffic_used:.1f}/{traffic_limit} ГБ")
                    else:
                        lines.append(f"📊 Использовано: {traffic_used:.1f} ГБ")
        else:
            lines.extend([
                "",
                "❌ VPN подписка отсутствует"
            ])
        
        # Реферальная информация
        if user.referral_code:
            lines.extend([
                "",
                f"🔗 Реферальный код: <code>{user.referral_code}</code>",
                f"💰 Заработано: {user.referral_earnings:.2f} ₽"
            ])
        
        # Тестовый период
        if user.is_test_used:
            lines.append("🎁 Тестовый период: использован")
        else:
            lines.append("🎁 Тестовый период: доступен")
        
        # Метаданные
        if user.user_metadata:
            metadata = user.user_metadata
            stats = metadata.get('statistics', {})
            if stats:
                lines.extend([
                    "",
                    "📊 <b>Статистика:</b>",
                    f"• Подписок: {stats.get('total_subscriptions', 0)}",
                    f"• Дней куплено: {stats.get('total_days_purchased', 0)}"
                ])
        
        return "\n".join(lines)

    async def edit_user(self, callback: CallbackQuery, **kwargs):
        """Редактирование пользователя"""
        try:
            # Извлекаем ID пользователя
            user_id = callback.data.split('_', 2)[2]

            user_data = await self._get_user_data(callback)
            db_session = user_data['db_session']

            user_service = UserService(db_session)
            user = await user_service.get_user_by_telegram_id(int(user_id))

            if not user:
                await self._send_error(callback, _("Пользователь не найден"))
                return

            # Создаем клавиатуру редактирования
            from ..keyboards import UserEditKeyboard
            keyboard = await UserEditKeyboard(user).get_keyboard()

            text = f"✏️ <b>Редактирование пользователя {user.telegram_id}</b>\n\n"
            text += await self._get_user_details_text(user)

            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()

        except Exception as e:
            self.logger.error(f"Ошибка редактирования пользователя: {e}")
            await self._send_error(callback, _("Ошибка редактирования пользователя"))

    async def unblock_user(self, callback: CallbackQuery, **kwargs):
        """Разблокировка пользователя"""
        try:
            # Извлекаем ID пользователя
            user_id = callback.data.split('_', 2)[2]

            user_data = await self._get_user_data(callback)
            db_session = user_data['db_session']

            user_service = UserService(db_session)
            user = await user_service.get_user_by_telegram_id(int(user_id))

            if not user:
                await self._send_error(callback, _("Пользователь не найден"))
                return

            if not user.is_blocked:
                await callback.answer(_("Пользователь не заблокирован"), show_alert=True)
                return

            # Разблокируем пользователя
            await user_service.unblock_user(user.id)

            # Обновляем информацию
            updated_user = await user_service.get_user_by_telegram_id(int(user_id))

            from ..keyboards import UserDetailsKeyboard
            keyboard = await UserDetailsKeyboard(updated_user).get_keyboard()

            text = f"✅ <b>Пользователь {user.telegram_id} разблокирован</b>\n\n"
            text += await self._get_user_details_text(updated_user)

            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer(_("Пользователь успешно разблокирован"))

        except Exception as e:
            self.logger.error(f"Ошибка разблокировки пользователя: {e}")
            await self._send_error(callback, _("Ошибка разблокировки пользователя"))

    async def send_message_to_user(self, callback: CallbackQuery, state: FSMContext, **kwargs):
        """Отправка сообщения пользователю"""
        try:
            # Извлекаем ID пользователя
            user_id = callback.data.split('_', 2)[2]

            user_data = await self._get_user_data(callback)
            db_session = user_data['db_session']

            user_service = UserService(db_session)
            user = await user_service.get_user_by_telegram_id(int(user_id))

            if not user:
                await self._send_error(callback, _("Пользователь не найден"))
                return

            # Сохраняем ID пользователя в состоянии
            await state.update_data(target_user_id=user_id)
            await state.set_state(UserManagementStates.sending_message)

            text = f"✉️ <b>Отправка сообщения пользователю {user.telegram_id}</b>\n\n"
            text += "Введите сообщение, которое хотите отправить пользователю:"

            from ..keyboards import CancelKeyboard
            keyboard = await CancelKeyboard().get_keyboard()

            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()

        except Exception as e:
            self.logger.error(f"Ошибка отправки сообщения пользователю: {e}")
            await self._send_error(callback, _("Ошибка отправки сообщения"))

    async def reset_user_trial(self, callback: CallbackQuery, **kwargs):
        """Сброс пробного периода пользователя"""
        try:
            # Извлекаем ID пользователя
            user_id = callback.data.split('_', 2)[2]

            user_data = await self._get_user_data(callback)
            db_session = user_data['db_session']

            user_service = UserService(db_session)
            user = await user_service.get_user_by_telegram_id(int(user_id))

            if not user:
                await self._send_error(callback, _("Пользователь не найден"))
                return

            # Сбрасываем пробный период
            await user_service.reset_trial_period(user.id)

            # Обновляем информацию
            updated_user = await user_service.get_user_by_telegram_id(int(user_id))

            from ..keyboards import UserDetailsKeyboard
            keyboard = await UserDetailsKeyboard(updated_user).get_keyboard()

            text = f"🔄 <b>Пробный период сброшен для пользователя {user.telegram_id}</b>\n\n"
            text += await self._get_user_details_text(updated_user)

            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer(_("Пробный период успешно сброшен"))

        except Exception as e:
            self.logger.error(f"Ошибка сброса пробного периода: {e}")
            await self._send_error(callback, _("Ошибка сброса пробного периода"))

    async def process_user_edit(self, message: Message, state: FSMContext, **kwargs):
        """Обработка редактирования пользователя"""
        try:
            # Получаем данные из состояния
            state_data = await state.get_data()
            user_id = state_data.get('editing_user_id')

            if not user_id:
                await message.answer(_("❌ Ошибка: пользователь не найден"))
                await state.clear()
                return

            # Здесь можно добавить логику обработки редактирования
            # Пока что просто отправляем подтверждение
            await message.answer(_("✅ Изменения сохранены"))
            await state.clear()

        except Exception as e:
            self.logger.error(f"Ошибка обработки редактирования пользователя: {e}")
            await message.answer(_("❌ Ошибка сохранения изменений"))
            await state.clear()

    async def process_message_to_user(self, message: Message, state: FSMContext, **kwargs):
        """Обработка отправки сообщения пользователю"""
        try:
            # Получаем данные из состояния
            state_data = await state.get_data()
            target_user_id = state_data.get('target_user_id')

            if not target_user_id:
                await message.answer(_("❌ Ошибка: пользователь не найден"))
                await state.clear()
                return

            user_data = await self._get_user_data(message)
            db_session = user_data['db_session']

            user_service = UserService(db_session)
            target_user = await user_service.get_user_by_telegram_id(int(target_user_id))

            if not target_user:
                await message.answer(_("❌ Пользователь не найден"))
                await state.clear()
                return

            # Отправляем сообщение пользователю
            try:
                admin_message = f"📢 <b>Сообщение от администрации:</b>\n\n{message.text}"
                await message.bot.send_message(
                    chat_id=target_user.telegram_id,
                    text=admin_message,
                    parse_mode='HTML'
                )

                await message.answer(
                    f"✅ Сообщение успешно отправлено пользователю {target_user.telegram_id}"
                )

            except Exception as send_error:
                self.logger.error(f"Ошибка отправки сообщения пользователю {target_user_id}: {send_error}")
                await message.answer(_("❌ Не удалось отправить сообщение пользователю"))

            await state.clear()

        except Exception as e:
            self.logger.error(f"Ошибка обработки сообщения пользователю: {e}")
            await message.answer(_("❌ Ошибка отправки сообщения"))
            await state.clear()
