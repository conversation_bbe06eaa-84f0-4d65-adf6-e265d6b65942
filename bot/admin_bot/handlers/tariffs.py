

"""
UnveilVPN Shop - Admin Tariffs Handler
Обработчик управления тарифами для административного бота
"""

from typing import Dict, Any, List
from uuid import UUID

from aiogram.types import Message, CallbackQuery
from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import State, StatesGroup
from aiogram.utils.i18n import gettext as _

from ...common.handlers import BaseHandler
from ...services.tariff_service import TariffService
from ..keyboards.tariffs_management import (
    TariffsManagementKeyboard,
    TariffListKeyboard,
    TariffActionsKeyboard,
    TariffPricesKeyboard,
)


from ...common.keyboards import CancelKeyboard

class TariffStates(StatesGroup):
    """Состояния для работы с тарифами"""
    waiting_for_input = State()

FIELD_NAMES = {
    "name": "Название",
    "description": "Описание",
    "duration_days": "Продолжительность (в днях)",
}


class TariffsHandler(BaseHandler):
    """
    Обработчик управления тарифами для админ бота
    """

    def __init__(self):
        super().__init__(name='AdminTariffsHandler')

    def _register_handlers(self):
        """Регистрация обработчиков"""
        # Главное меню тарифов
        self.router.callback_query.register(self.show_tariffs_menu, lambda c: c.data == 'admin_tariffs')

        # Список тарифов
        self.router.callback_query.register(self.list_tariffs, lambda c: c.data == 'tariffs_list')

        # Просмотр тарифа
        self.router.callback_query.register(self.view_tariff, lambda c: c.data.startswith('tariff_view_'))

        # Редактирование цен
        self.router.callback_query.register(self.edit_prices, lambda c: c.data.startswith('edit_tariff_prices_'))

        # Статистика тарифа
        self.router.callback_query.register(self.show_tariff_stats, lambda c: c.data.startswith('tariff_stats_'))

        # Переключение популярности
        self.router.callback_query.register(self.toggle_popular, lambda c: c.data.startswith('toggle_popular_'))

        # Обработчики для редактирования полей
        self.router.callback_query.register(self.start_editing_field, lambda c: c.data.startswith('edit_tariff_'))
        self.router.message.register(self.process_edited_field, TariffStates.waiting_for_input)
        self.router.callback_query.register(self.cancel_editing, lambda c: c.data == 'cancel_editing', TariffStates.waiting_for_input)

    async def show_tariffs_menu(self, callback: CallbackQuery, db_session, **kwargs):
        """Показ меню управления тарифами"""
        keyboard = await TariffsManagementKeyboard().get_keyboard()
        await callback.message.edit_text(_("💰 <b>Управление тарифами</b>"), reply_markup=keyboard)
        await callback.answer()

    async def list_tariffs(self, callback: CallbackQuery, db_session, **kwargs):
        """Показ списка тарифов"""
        tariff_service = TariffService(db_session)
        tariffs = await tariff_service.get_all_tariffs(active_only=False)

        keyboard = await TariffListKeyboard(tariffs).get_keyboard()
        await callback.message.edit_text(_("📋 <b>Список тарифов</b>"), reply_markup=keyboard)
        await callback.answer()

    async def view_tariff(self, callback: CallbackQuery, db_session, **kwargs):
        """Просмотр тарифа"""
        tariff_id = UUID(callback.data.split('_')[2])
        tariff_service = TariffService(db_session)
        tariff = await tariff_service.get_tariff_by_id(tariff_id)

        if not tariff:
            await callback.answer(_("Тариф не найден"), show_alert=True)
            return

        text = self._format_tariff_details(tariff)
        keyboard = await TariffActionsKeyboard(str(tariff.id), tariff.is_popular).get_keyboard()
        await callback.message.edit_text(text, reply_markup=keyboard)
        await callback.answer()

    async def start_editing_field(self, callback: CallbackQuery, state: FSMContext, db_session, **kwargs):
        """Начало редактирования поля тарифа"""
        parts = callback.data.split('_')
        field = parts[2]
        tariff_id = UUID(parts[3])

        tariff_service = TariffService(db_session)
        tariff = await tariff_service.get_tariff_by_id(tariff_id)

        if not tariff:
            await callback.answer(_("Тариф не найден"), show_alert=True)
            return

        await state.set_state(TariffStates.waiting_for_input)
        await state.update_data(tariff_id=str(tariff_id), field=field)

        current_value = getattr(tariff, field, _("не задано"))
        field_name = FIELD_NAMES.get(field, field)
        
        text = _("Введите новое значение для поля <b>{field_name}</b>.\nТекущее значение: <code>{current_value}</code>").format(
            field_name=field_name, current_value=current_value
        )
        
        # Используем общую клавиатуру отмены, передавая callback для возврата
        from ..keyboards.tariffs_management import CancelKeyboard
        keyboard = await CancelKeyboard(back_callback=f"tariff_view_{tariff_id}").get_keyboard()
        
        await callback.message.edit_text(text, reply_markup=keyboard)
        await callback.answer()

    async def process_edited_field(self, message: Message, state: FSMContext, db_session, **kwargs):
        """Обработка отредактированного поля"""
        data = await state.get_data()
        tariff_id = UUID(data['tariff_id'])
        field = data['field']
        value = message.text

        tariff_service = TariffService(db_session)
        try:
            await tariff_service.update_tariff_field(tariff_id, field, value)
            await db_session.commit()
            await state.clear()

            tariff = await tariff_service.get_tariff_by_id(tariff_id)
            text = self._format_tariff_details(tariff)
            keyboard = await TariffActionsKeyboard(str(tariff.id), tariff.is_popular).get_keyboard()
            await message.answer(_("✅ Поле <b>{field}</b> успешно обновлено.").format(field=field), reply_markup=keyboard)

        except Exception as e:
            await message.answer(_("❌ Ошибка обновления: {error}").format(error=e))

    async def edit_prices(self, callback: CallbackQuery, db_session, **kwargs):
        """Редактирование цен"""
        tariff_id = UUID(callback.data.split('_')[3])
        keyboard = await TariffPricesKeyboard(str(tariff_id)).get_keyboard()
        await callback.message.edit_text(_("💰 <b>Редактирование цен</b>"), reply_markup=keyboard)
        await callback.answer()

    async def show_tariff_stats(self, callback: CallbackQuery, db_session, **kwargs):
        """Показ статистики по тарифу"""
        tariff_id = UUID(callback.data.split('_')[2])
        tariff_service = TariffService(db_session)
        stats = await tariff_service.get_tariff_stats(tariff_id)

        text = _("📊 <b>Статистика по тарифу</b>\n\nВсего покупок: {total_purchases}\nАктивных подписок: {active_subscriptions}").format(**stats)
        
        from ..keyboards.tariffs_management import CancelKeyboard
        keyboard = await CancelKeyboard(back_callback=f"tariff_view_{tariff_id}").get_keyboard()
        await callback.message.edit_text(text, reply_markup=keyboard, parse_mode="HTML")
        await callback.answer()

    async def toggle_popular(self, callback: CallbackQuery, db_session, **kwargs):
        """Переключение флага is_popular"""
        tariff_id = UUID(callback.data.split('_')[2])
        tariff_service = TariffService(db_session)
        tariff = await tariff_service.get_tariff_by_id(tariff_id)

        if not tariff:
            await callback.answer(_("Тариф не найден"), show_alert=True)
            return

        await tariff_service.set_popular_tariff(tariff_id)
        await db_session.commit()

        tariff = await tariff_service.get_tariff_by_id(tariff_id)
        text = self._format_tariff_details(tariff)
        keyboard = await TariffActionsKeyboard(str(tariff.id), tariff.is_popular).get_keyboard()
        
        from aiogram.exceptions import TelegramBadRequest
        try:
            await callback.message.edit_text(text, reply_markup=keyboard)
        except TelegramBadRequest as e:
            if "message is not modified" not in e.message:
                raise e
        
        await callback.answer(_("Статус популярности обновлен"))

    def _format_tariff_details(self, tariff) -> str:
        """Форматирование деталей тарифа"""
        return _(
            "<b>Тариф: {name}</b>\n\n"
            "📄 <b>Описание:</b> {description}\n"
            "⏱️ <b>Продолжительность:</b> {duration_days} дней\n"
            "💰 <b>Цены:</b> RUB: {price_rub}, USD: {price_usd}, Stars: {price_stars}\n"
            "⭐ <b>Популярный:</b> {is_popular}"
        ).format(
            name=tariff.name,
            description=tariff.description or _("не задано"),
            duration_days=tariff.duration_days,
            price_rub=tariff.prices.get('rub', 0),
            price_usd=tariff.prices.get('usd', 0),
            price_stars=tariff.prices.get('stars', 0),
            is_popular=_("Да") if tariff.is_popular else _("Нет"),
        )

    async def cancel_editing(self, callback: CallbackQuery, state: FSMContext, db_session, **kwargs):
        """Отмена редактирования"""
        await state.clear()
        await self.view_tariff(callback, db_session, **kwargs)

