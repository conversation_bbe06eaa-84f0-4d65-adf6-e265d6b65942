"""
UnveilVPN Shop - Admin Menu Handler
Обработчик главного меню для административного бота
"""

from typing import Dict, Any

from aiogram.types import Message, CallbackQuery
from aiogram.utils.i18n import gettext as _

from ...common.handlers import MenuHandler as BaseMenuHandler
from ...common.utils import MessageFormatter, BotUtils
from ..keyboards import AdminMenuKeyboard


class MenuHandler(BaseMenuHandler):
    """
    Обработчик главного меню административного бота
    """
    
    def __init__(self):
        menu_keyboard = AdminMenuKeyboard()
        super().__init__(menu_keyboard, name='AdminMenuHandler')
        
        # Регистрируем дополнительные обработчики
        self._register_admin_menu_callbacks()
    
    def _register_admin_menu_callbacks(self):
        """Регистрация обработчиков callback для административного меню"""
        
        # Быстрые действия
        self.router.callback_query.register(
            self.quick_stats,
            lambda c: c.data == 'quick_stats'
        )
        
        self.router.callback_query.register(
            self.system_status,
            lambda c: c.data == 'system_status'
        )
        
        self.router.callback_query.register(
            self.recent_activity,
            lambda c: c.data == 'recent_activity'
        )
        
        # Системные функции
        self.router.callback_query.register(
            self.backup_database,
            lambda c: c.data == 'backup_db'
        )
        
        self.router.callback_query.register(
            self.clear_cache,
            lambda c: c.data == 'clear_cache'
        )
        
        self.router.callback_query.register(
            self.restart_services,
            lambda c: c.data == 'restart_services'
        )
    
    async def _get_menu_text(self, user_data: Dict[str, Any]) -> str:
        """Получение текста главного административного меню"""
        user = user_data['user']
        
        name = user.first_name or user.username or _("Администратор")
        
        # Получаем актуальную статистику
        stats = await self._get_dashboard_stats()
        
        lines = [
            _("🔧 <b>Административная панель</b>"),
            "",
            _("Добро пожаловать, {name}!").format(name=name),
            "",
            _("📈 <b>Сводка за сегодня:</b>"),
            _("• Новых пользователей: {count}").format(count=stats.get('new_users_today', 0)),
            _("• Платежей: {count} на {amount} ₽").format(
                count=stats.get('payments_today', 0),
                amount=stats.get('revenue_today', 0)
            ),
            _("• Активных подписок: {count}").format(count=stats.get('active_subscriptions', 0)),
            _("• Обращений в поддержку: {count}").format(count=stats.get('support_tickets_today', 0)),
        ]
        
        # Добавляем предупреждения если есть
        warnings = await self._get_system_warnings()
        if warnings:
            lines.extend([
                "",
                _("⚠️ <b>Требует внимания:</b>")
            ])
            for warning in warnings:
                lines.append(f"• {warning}")
        
        lines.extend([
            "",
            _("Выберите раздел для управления:")
        ])
        
        return "\n".join(lines)
    
    async def quick_stats(self, callback: CallbackQuery, **kwargs):
        """Показ быстрой статистики"""
        try:
            stats = await self._get_detailed_stats()
            
            text = _("📊 <b>Быстрая статистика</b>\n\n")
            
            # Пользователи
            text += _("👥 <b>Пользователи:</b>\n")
            text += _("• Всего: {total}\n").format(total=stats.get('users_total', 0))
            text += _("• Активных: {active}\n").format(active=stats.get('users_active', 0))
            text += _("• Новых за неделю: {new}\n").format(new=stats.get('users_new_week', 0))
            text += "\n"
            
            # Платежи
            text += _("💰 <b>Платежи:</b>\n")
            text += _("• Сегодня: {today} ₽\n").format(today=stats.get('revenue_today', 0))
            text += _("• За неделю: {week} ₽\n").format(week=stats.get('revenue_week', 0))
            text += _("• За месяц: {month} ₽\n").format(month=stats.get('revenue_month', 0))
            text += "\n"
            
            # Подписки
            text += _("📱 <b>Подписки:</b>\n")
            text += _("• Активных: {active}\n").format(active=stats.get('subscriptions_active', 0))
            text += _("• Истекают сегодня: {expiring}\n").format(expiring=stats.get('subscriptions_expiring_today', 0))
            
            from ..keyboards import QuickStatsKeyboard
            keyboard = await QuickStatsKeyboard().get_keyboard()
            
            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Ошибка показа быстрой статистики: {e}")
            await self._send_error(callback, _("Ошибка загрузки статистики"))
    
    async def system_status(self, callback: CallbackQuery, **kwargs):
        """Показ статуса системы"""
        try:
            status = await self._get_system_status()
            
            text = _("🖥️ <b>Статус системы</b>\n\n")
            
            # Основные сервисы
            text += _("🔧 <b>Сервисы:</b>\n")
            for service, is_running in status.get('services', {}).items():
                status_icon = "🟢" if is_running else "🔴"
                text += f"• {service}: {status_icon}\n"
            text += "\n"
            
            # Ресурсы
            text += _("📊 <b>Ресурсы:</b>\n")
            text += _("• CPU: {cpu}%\n").format(cpu=status.get('cpu_usage', 0))
            text += _("• RAM: {ram}%\n").format(ram=status.get('memory_usage', 0))
            text += _("• Диск: {disk}%\n").format(disk=status.get('disk_usage', 0))
            text += "\n"
            
            # База данных
            text += _("🗄️ <b>База данных:</b>\n")
            text += _("• Подключений: {connections}\n").format(connections=status.get('db_connections', 0))
            text += _("• Размер: {size} MB\n").format(size=status.get('db_size', 0))
            
            from ..keyboards import SystemStatusKeyboard
            keyboard = await SystemStatusKeyboard().get_keyboard()
            
            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Ошибка показа статуса системы: {e}")
            await self._send_error(callback, _("Ошибка загрузки статуса системы"))
    
    async def recent_activity(self, callback: CallbackQuery, **kwargs):
        """Показ последней активности"""
        try:
            activities = await self._get_recent_activities()
            
            text = _("📋 <b>Последняя активность</b>\n\n")
            
            if not activities:
                text += _("Нет недавней активности")
            else:
                for activity in activities[:10]:  # Показываем последние 10
                    time_str = BotUtils.format_datetime(activity.get('timestamp'), 'time')
                    text += f"• {time_str} - {activity.get('description', 'N/A')}\n"
            
            from ..keyboards import RecentActivityKeyboard
            keyboard = await RecentActivityKeyboard().get_keyboard()
            
            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Ошибка показа последней активности: {e}")
            await self._send_error(callback, _("Ошибка загрузки активности"))
    
    async def backup_database(self, callback: CallbackQuery, **kwargs):
        """Создание резервной копии базы данных"""
        try:
            await callback.answer(_("Создание резервной копии..."))
            
            # Здесь будет логика создания бэкапа
            backup_result = await self._create_database_backup()
            
            if backup_result['success']:
                text = _("✅ <b>Резервная копия создана</b>\n\n")
                text += _("Файл: {filename}\n").format(filename=backup_result['filename'])
                text += _("Размер: {size}\n").format(size=backup_result['size'])
                text += _("Время: {time}").format(time=BotUtils.format_datetime(backup_result['timestamp']))
            else:
                text = _("❌ <b>Ошибка создания резервной копии</b>\n\n")
                text += _("Причина: {error}").format(error=backup_result['error'])
            
            await callback.message.edit_text(text)
            
        except Exception as e:
            self.logger.error(f"Ошибка создания резервной копии: {e}")
            await self._send_error(callback, _("Ошибка создания резервной копии"))
    
    async def clear_cache(self, callback: CallbackQuery, **kwargs):
        """Очистка кэша"""
        try:
            await callback.answer(_("Очистка кэша..."))
            
            # Здесь будет логика очистки кэша
            cache_result = await self._clear_system_cache()
            
            text = _("🧹 <b>Очистка кэша завершена</b>\n\n")
            text += _("Освобождено: {freed} MB\n").format(freed=cache_result.get('freed_space', 0))
            text += _("Очищено записей: {records}").format(records=cache_result.get('cleared_records', 0))
            
            await callback.message.edit_text(text)
            
        except Exception as e:
            self.logger.error(f"Ошибка очистки кэша: {e}")
            await self._send_error(callback, _("Ошибка очистки кэша"))
    
    async def restart_services(self, callback: CallbackQuery, **kwargs):
        """Перезапуск сервисов"""
        try:
            # Подтверждение действия
            from ..keyboards import ConfirmRestartKeyboard
            keyboard = await ConfirmRestartKeyboard().get_keyboard()
            
            text = _("⚠️ <b>Подтверждение перезапуска</b>\n\n")
            text += _("Вы уверены, что хотите перезапустить сервисы?\n")
            text += _("Это может привести к кратковременной недоступности системы.")
            
            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Ошибка подтверждения перезапуска: {e}")
            await self._send_error(callback, _("Ошибка подтверждения перезапуска"))
    
    # Вспомогательные методы
    
    async def _get_dashboard_stats(self) -> Dict[str, Any]:
        """Получение статистики для дашборда"""
        # Заглушка - здесь будет реальная логика
        return {
            'new_users_today': 0,
            'payments_today': 0,
            'revenue_today': 0,
            'active_subscriptions': 0,
            'support_tickets_today': 0
        }
    
    async def _get_system_warnings(self) -> list:
        """Получение системных предупреждений"""
        # Заглушка - здесь будет реальная логика проверки
        return []
    
    async def _get_detailed_stats(self) -> Dict[str, Any]:
        """Получение детальной статистики"""
        # Заглушка
        return {}
    
    async def _get_system_status(self) -> Dict[str, Any]:
        """Получение статуса системы"""
        # Заглушка
        return {
            'services': {
                'Bot': True,
                'API': True,
                'Database': True,
                'Redis': False
            },
            'cpu_usage': 25,
            'memory_usage': 45,
            'disk_usage': 60,
            'db_connections': 5,
            'db_size': 128
        }
    
    async def _get_recent_activities(self) -> list:
        """Получение последней активности"""
        # Заглушка
        return []
    
    async def _create_database_backup(self) -> Dict[str, Any]:
        """Создание резервной копии БД"""
        # Заглушка
        return {
            'success': True,
            'filename': 'backup_20241224.sql',
            'size': '15.2 MB',
            'timestamp': BotUtils.format_datetime(None)
        }
    
    async def _clear_system_cache(self) -> Dict[str, Any]:
        """Очистка системного кэша"""
        # Заглушка
        return {
            'freed_space': 128,
            'cleared_records': 1500
        }
