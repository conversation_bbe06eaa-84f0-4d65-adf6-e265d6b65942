"""
UnveilVPN Shop - Admin Bot Main Handler
Главный обработчик админ бота
"""

from typing import Dict, Any

from aiogram.types import Message, CallbackQuery
from aiogram.fsm.context import FSMContext
from aiogram.utils.i18n import gettext as _

from ...common.handlers import BaseHandler
from ...common.utils import MessageFormatter, BotUtils
from ...common.exceptions import NotFoundError, ValidationError, BusinessLogicError
from ...common.ui_components import UIComponents
from ...services.user_service import UserService
from ...services.analytics_service import AnalyticsService
from ...services.system_service import SystemService
from ..keyboards import AdminMainMenuKeyboard


class AdminMainHandler(BaseHandler):
    """
    Главный обработчик админ бота
    """
    
    def __init__(self):
        super().__init__(name='AdminMainHandler')
    
    def _register_handlers(self):
        """Регистрация обработчиков"""
        
        # Команда /start
        self.router.message.register(
            self.start_command,
            lambda message: message.text == '/start'
        )
        
        # Главное меню
        self.router.callback_query.register(
            self.show_main_menu,
            lambda c: c.data == 'main_menu'
        )
        
        # Статистика
        self.router.callback_query.register(
            self.show_quick_stats,
            lambda c: c.data == 'quick_stats'
        )
        
        # Системная информация
        self.router.callback_query.register(
            self.show_system_info,
            lambda c: c.data == 'system_info'
        )
        
        # Обновление данных
        self.router.callback_query.register(
            self.refresh_data,
            lambda c: c.data == 'refresh_data'
        )
    
    async def start_command(self, message: Message, **kwargs):
        """Обработка команды /start"""
        self.logger.debug(f"Вызвана start_command для пользователя {message.from_user.id}")
        try:
            user_data = await self._get_user_data(message)
            db_user = user_data['db_user']
            
            # Проверяем права администратора
            if not self._is_admin(db_user):
                await message.answer(_("❌ У вас нет прав администратора"))
                self.logger.debug(f"Пользователь {message.from_user.id} не является администратором.")
                return
            
            # Получаем быструю статистику
            db_session = user_data['db_session']
            analytics_service = AnalyticsService(db_session)
            system_service = SystemService(db_session)
            
            quick_stats = await analytics_service.get_overview_statistics()
            system_info = await system_service.get_system_info()
            
            # Создаем приветственное сообщение
            welcome_text = self._create_welcome_message(db_user, quick_stats, system_info)
            
            keyboard = await AdminMainMenuKeyboard(quick_stats, system_info).get_keyboard()
            
            await message.answer(welcome_text, reply_markup=keyboard)
            self.logger.debug(f"Отправлено приветственное сообщение с главным меню для пользователя {message.from_user.id}")

        except Exception as e:
            self.logger.error(f"Ошибка команды /start: {e}", exc_info=True)
            await message.answer(_("❌ Ошибка загрузки админ панели"))
    
    async def show_main_menu(self, callback: CallbackQuery, **kwargs):
        """Показ главного меню"""
        self.logger.debug(f"Вызвана show_main_menu (callback) для пользователя {callback.from_user.id}")
        self.logger.debug(f"Callback message ID: {callback.message.message_id if callback.message else 'None'}")
        self.logger.debug(f"Callback chat ID: {callback.message.chat.id if callback.message else 'None'}")

        try:
            user_data = await self._get_user_data(callback)
            db_user = user_data['db_user']

            # Проверяем права администратора
            if not self._is_admin(db_user):
                await callback.answer(_("❌ У вас нет прав администратора"), show_alert=True)
                self.logger.debug(f"Пользователь {callback.from_user.id} не является администратором.")
                return

            # Получаем актуальную статистику
            db_session = user_data['db_session']
            analytics_service = AnalyticsService(db_session)
            system_service = SystemService(db_session)

            quick_stats = await analytics_service.get_overview_statistics()
            system_info = await system_service.get_system_info()

            # Создаем текст главного меню
            menu_text = self._create_main_menu_message(quick_stats, system_info)

            keyboard = await AdminMainMenuKeyboard(quick_stats, system_info).get_keyboard()

            if callback.message:
                self.logger.debug(f"Попытка редактирования сообщения {callback.message.message_id} в чате {callback.message.chat.id}")
                await callback.message.edit_text(menu_text, reply_markup=keyboard)
                self.logger.debug(f"Сообщение {callback.message.message_id} успешно отредактировано.")
            else:
                self.logger.warning(f"callback.message отсутствует для пользователя {callback.from_user.id}. Отправка нового сообщения.")
                await callback.message.answer(menu_text, reply_markup=keyboard) # Это вызовет дублирование
            
            await callback.answer()
            self.logger.debug(f"Callback answer отправлен для пользователя {callback.from_user.id}")

        except Exception as e:
            self.logger.error(f"Ошибка показа главного меню: {e}", exc_info=True)
            await self._send_error(callback, _("Ошибка загрузки главного меню"))
    
    async def show_quick_stats(self, callback: CallbackQuery, **kwargs):
        """Показ быстрой статистики"""
        try:
            user_data = await self._get_user_data(callback)
            db_session = user_data['db_session']
            
            analytics_service = AnalyticsService(db_session)
            
            # Получаем расширенную статистику
            overview = await analytics_service.get_overview_statistics()
            revenue_data = await analytics_service.get_revenue_analytics()
            user_analytics = await analytics_service.get_user_analytics()
            subscription_analytics = await analytics_service.get_subscription_analytics()
            
            # Создаем детальную статистику
            stats_text = self._create_detailed_stats_message(
                overview, revenue_data, user_analytics, subscription_analytics
            )
            
            keyboard = await AdminMainMenuKeyboard().get_keyboard()
            
            await callback.message.edit_text(stats_text, reply_markup=keyboard)
            await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Ошибка показа статистики: {e}")
            await self._send_error(callback, _("Ошибка загрузки статистики"))
    
    async def show_system_info(self, callback: CallbackQuery, **kwargs):
        """Показ системной информации"""
        try:
            user_data = await self._get_user_data(callback)
            db_session = user_data['db_session']
            
            system_service = SystemService(db_session)
            
            # Получаем системную информацию
            system_info = await system_service.get_system_info()
            monitoring_data = await system_service.get_monitoring_data()
            health_status = await system_service.check_system_health()
            
            # Создаем сообщение с системной информацией
            system_text = self._create_system_info_message(system_info, monitoring_data, health_status)
            
            keyboard = await AdminMainMenuKeyboard().get_keyboard()
            
            await callback.message.edit_text(system_text, reply_markup=keyboard)
            await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Ошибка показа системной информации: {e}")
            await self._send_error(callback, _("Ошибка загрузки системной информации"))
    
    async def refresh_data(self, callback: CallbackQuery, **kwargs):
        """Обновление данных"""
        try:
            # Показываем индикатор загрузки
            await callback.answer(_("🔄 Обновление данных..."))
            
            # Перенаправляем на главное меню с обновленными данными
            await self.show_main_menu(callback, **kwargs)
            
        except Exception as e:
            self.logger.error(f"Ошибка обновления данных: {e}")
            await self._send_error(callback, _("Ошибка обновления данных"))
    
    # Вспомогательные методы
    
    def _is_admin(self, user) -> bool:
        """Проверка прав администратора"""
        # В реальности здесь будет проверка роли пользователя
        # Пока что проверяем по telegram_id из конфига
        admin_ids = getattr(self.config, 'admin_telegram_ids', [])
        return user.telegram_id in admin_ids
    
    def _create_welcome_message(self, user, stats: Dict[str, Any], system_info: Dict[str, Any]) -> str:
        """Создание приветственного сообщения"""
        username = user.username or f"ID{user.telegram_id}"
        
        # Создаем карточку статистики
        stats_card = self._create_overview_card(stats, system_info)
        
        return _(
            "👋 <b>Добро пожаловать, {username}!</b>\n\n"
            "🔧 <b>Админ панель UnveilVPN Shop</b>\n\n"
            "{stats_card}\n\n"
            "📊 Выберите раздел для управления:"
        ).format(username=username, stats_card=stats_card)
    
    def _create_main_menu_message(self, stats: Dict[str, Any], system_info: Dict[str, Any]) -> str:
        """Создание сообщения главного меню"""
        # Создаем компактную карточку статистики
        stats_card = self._create_overview_card(stats, system_info, compact=True)
        
        return _(
            "🔧 <b>Админ панель UnveilVPN Shop</b>\n\n"
            "{stats_card}\n\n"
            "📊 Выберите раздел:"
        ).format(stats_card=stats_card)
    
    def _create_overview_card(self, stats: Dict[str, Any], system_info: Dict[str, Any], compact: bool = False) -> str:
        """Создание карточки обзора"""
        if compact:
            lines = [
                "┌─────────────────────────────┐",
                "│ 📊 <b>Сегодня</b> | ⚙️ <b>Система</b>      │",
                "├─────────────────────────────┤"
            ]
            
            revenue = stats.get('revenue_today', 0)
            users = stats.get('new_users_today', 0)
            cpu = system_info.get('cpu_usage', 0)
            memory = system_info.get('memory_usage', 0)
            
            lines.extend([
                f"│ 💰 {revenue:.0f}₽ | 👥 +{users} | 🖥{cpu:.0f}% │",
                f"│ 📡 +{stats.get('new_subscriptions_today', 0)} | 🔥 {stats.get('active_users_today', 0)} | 💾{memory:.0f}% │"
            ])
        else:
            lines = [
                "┌─────────────────────────────┐",
                "│ 📊 <b>Статистика за сегодня</b>    │",
                "├─────────────────────────────┤"
            ]
            
            revenue = stats.get('revenue_today', 0)
            new_users = stats.get('new_users_today', 0)
            new_subs = stats.get('new_subscriptions_today', 0)
            active_users = stats.get('active_users_today', 0)
            
            lines.extend([
                f"│ 💰 Доход: {revenue:>17.0f} ₽ │",
                f"│ 👥 Новых пользователей: {new_users:>7} │",
                f"│ 📡 Новых подписок: {new_subs:>11} │",
                f"│ 🔥 Активных: {active_users:>16} │"
            ])
            
            # Добавляем системную информацию
            lines.extend([
                "├─────────────────────────────┤",
                "│ ⚙️ <b>Система</b>                 │",
                "├─────────────────────────────┤"
            ])
            
            cpu = system_info.get('cpu_usage', 0)
            memory = system_info.get('memory_usage', 0)
            
            lines.extend([
                f"│ 🖥 CPU: {cpu:>19.1f}% │",
                f"│ 💾 RAM: {memory:>19.1f}% │"
            ])
        
        lines.append("└─────────────────────────────┘")

        return "\n".join(lines)

    def _create_detailed_stats_message(
        self,
        overview: Dict[str, Any],
        revenue: Dict[str, Any],
        users: Dict[str, Any],
        subscriptions: Dict[str, Any]
    ) -> str:
        """Создание детального сообщения статистики"""
        lines = [
            "📊 <b>Детальная статистика</b>",
            ""
        ]

        # Доходы
        lines.extend([
            "💰 <b>Доходы:</b>",
            f"• Сегодня: {overview.get('revenue_today', 0):.0f} ₽",
            f"• За неделю: {revenue.get('weekly_revenue', 0):.0f} ₽",
            f"• За месяц: {revenue.get('monthly_revenue', 0):.0f} ₽",
            f"• Всего: {revenue.get('total_revenue', 0):.0f} ₽",
            f"• Средний чек: {revenue.get('average_payment', 0):.0f} ₽",
            ""
        ])

        # Пользователи
        lines.extend([
            "👥 <b>Пользователи:</b>",
            f"• Всего: {users.get('total_users', 0)}",
            f"• Активных: {users.get('active_users', 0)}",
            f"• Новых сегодня: {overview.get('new_users_today', 0)}",
            f"• Новых за неделю: {users.get('new_week', 0)}",
            f"• Конверсия: {users.get('conversion_rate', 0):.1f}%",
            ""
        ])

        # Подписки
        lines.extend([
            "📡 <b>Подписки:</b>",
            f"• Всего: {subscriptions.get('total_subscriptions', 0)}",
            f"• Активных: {subscriptions.get('active_subscriptions', 0)}",
            f"• Новых сегодня: {overview.get('new_subscriptions_today', 0)}",
            f"• Истекают скоро: {subscriptions.get('expiring_soon', 0)}",
            ""
        ])

        # Способы оплаты
        payment_methods = revenue.get('by_payment_method', {})
        if payment_methods:
            lines.append("💳 <b>По способам оплаты:</b>")
            for method, amount in payment_methods.items():
                method_name = {
                    'yookassa': 'Банковские карты',
                    'cryptomus': 'Криптовалюта',
                    'telegram_stars': 'Telegram Stars'
                }.get(method, method)
                lines.append(f"• {method_name}: {amount:.0f} ₽")

        return "\n".join(lines)

    def _create_system_info_message(
        self,
        system_info: Dict[str, Any],
        monitoring_data: Dict[str, Any],
        health_status: Dict[str, Any]
    ) -> str:
        """Создание сообщения с системной информацией"""
        lines = [
            "⚙️ <b>Системная информация</b>",
            ""
        ]

        # Общее состояние
        overall_status = health_status.get('overall', 'unknown')
        status_emoji = {
            'healthy': '🟢',
            'warning': '🟡',
            'critical': '🔴',
            'error': '⚫'
        }.get(overall_status, '⚪')

        lines.extend([
            f"📊 <b>Общее состояние:</b> {status_emoji} {overall_status.title()}",
            ""
        ])

        # Ресурсы системы
        lines.extend([
            "🖥 <b>Ресурсы системы:</b>",
            f"• CPU: {system_info.get('cpu_usage', 0):.1f}% ({system_info.get('cpu_count', 0)} ядер)",
            f"• RAM: {system_info.get('memory_usage', 0):.1f}% ({system_info.get('memory_used', 0):.1f}/{system_info.get('memory_total', 0):.1f} ГБ)",
            f"• Диск: {system_info.get('disk_usage', 0):.1f}% ({system_info.get('disk_used', 0):.1f}/{system_info.get('disk_total', 0):.1f} ГБ)",
            f"• Uptime: {system_info.get('uptime', 'Неизвестно')}",
            ""
        ])

        # Сетевая активность
        network = monitoring_data.get('network', {})
        if network:
            lines.extend([
                "🌐 <b>Сетевая активность:</b>",
                f"• Отправлено: {network.get('bytes_sent', 0):.1f} МБ",
                f"• Получено: {network.get('bytes_recv', 0):.1f} МБ",
                ""
            ])

        # Предупреждения и проблемы
        issues = health_status.get('issues', [])
        warnings = health_status.get('warnings', [])

        if issues:
            lines.append("🔴 <b>Проблемы:</b>")
            for issue in issues[:3]:  # Показываем первые 3
                lines.append(f"• {issue}")
            lines.append("")

        if warnings:
            lines.append("🟡 <b>Предупреждения:</b>")
            for warning in warnings[:3]:  # Показываем первые 3
                lines.append(f"• {warning}")
            lines.append("")

        # Статус сервисов
        services = monitoring_data.get('services_status', {})
        if services:
            lines.append("🔧 <b>Статус сервисов:</b>")
            for service, status in services.items():
                status_emoji = "🟢" if status == "running" else "🔴"
                lines.append(f"• {service}: {status_emoji}")

        return "\n".join(lines)
