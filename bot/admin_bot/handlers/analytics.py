"""
UnveilVPN Shop - Admin Bot Analytics Handler
Обработчик аналитики и отчетов для админ бота
"""

from typing import Dict, Any, List, Optional
from datetime import datetime, timezone, timedelta
from decimal import Decimal

from aiogram.types import Message, CallbackQuery
from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import State, StatesGroup
from aiogram.utils.i18n import gettext as _

from ...common.handlers import BaseHandler
from ...common.utils import MessageFormatter, BotUtils
from ...common.exceptions import NotFoundError, ValidationError, BusinessLogicError
from ...common.ui_components import UIComponents
from ...services.analytics_service import AnalyticsService
from ...services.user_service import UserService
from ...services.payment_service import PaymentService
from ...services.referral_service import ReferralService
from ..keyboards import (
    StatisticsKeyboard
)


class AnalyticsStates(StatesGroup):
    """Состояния для работы с аналитикой"""
    selecting_period = State()
    generating_report = State()
    exporting_data = State()


class AnalyticsHandler(BaseHandler):
    """
    Обработчик аналитики и отчетов для админ бота
    """
    
    def __init__(self):
        super().__init__(name='AdminAnalyticsHandler')
    
    def _register_handlers(self):
        """Регистрация обработчиков"""
        
        # Основные обработчики
        self.router.callback_query.register(
            self.show_analytics_menu,
            lambda c: c.data == 'analytics'
        )
        
        self.router.callback_query.register(
            self.show_dashboard,
            lambda c: c.data == 'dashboard'
        )
        
        self.router.callback_query.register(
            self.show_revenue_analytics,
            lambda c: c.data == 'revenue_analytics'
        )
        
        self.router.callback_query.register(
            self.show_user_analytics,
            lambda c: c.data == 'user_analytics'
        )
        
        self.router.callback_query.register(
            self.show_subscription_analytics,
            lambda c: c.data == 'subscription_analytics'
        )
        
        self.router.callback_query.register(
            self.show_referral_analytics,
            lambda c: c.data == 'referral_analytics'
        )
        
        self.router.callback_query.register(
            self.show_payment_analytics,
            lambda c: c.data == 'payment_analytics'
        )
        
        self.router.callback_query.register(
            self.show_promocode_analytics,
            lambda c: c.data == 'promocode_analytics'
        )
        
        self.router.callback_query.register(
            self.generate_report,
            lambda c: c.data.startswith('report_')
        )
        
        self.router.callback_query.register(
            self.export_data,
            lambda c: c.data.startswith('export_')
        )
        
        self.router.callback_query.register(
            self.select_period,
            lambda c: c.data.startswith('period_')
        )
    
    async def show_analytics_menu(self, callback: CallbackQuery, **kwargs):
        """Показ главного меню аналитики"""
        try:
            user_data = await self._get_user_data(callback)
            db_session = user_data['db_session']
            
            # Получаем общую статистику
            analytics_service = AnalyticsService(db_session)
            overview = await analytics_service.get_overview_statistics()
            
            # Создаем красивую карточку обзора
            overview_card = self._create_overview_card(overview)
            
            text = _(
                "📊 <b>Аналитика и отчеты</b>\n\n"
                "{overview_card}\n\n"
                "📈 <b>Доступные разделы:</b>\n"
                "• Дашборд с ключевыми метриками\n"
                "• Анализ доходов и продаж\n"
                "• Статистика пользователей\n"
                "• Аналитика подписок\n"
                "• Реферальная программа\n"
                "• Промокоды и скидки\n\n"
                "Выберите раздел для анализа:"
            ).format(overview_card=overview_card)
            
            keyboard = await StatisticsKeyboard().get_keyboard()
            
            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Ошибка показа меню аналитики: {e}")
            await self._send_error(callback, _("Ошибка загрузки аналитики"))
    
    async def show_dashboard(self, callback: CallbackQuery, **kwargs):
        """Показ дашборда с ключевыми метриками"""
        try:
            user_data = await self._get_user_data(callback)
            db_session = user_data['db_session']
            
            analytics_service = AnalyticsService(db_session)
            
            # Получаем данные за разные периоды
            today_stats = await analytics_service.get_daily_statistics()
            week_stats = await analytics_service.get_weekly_statistics()
            month_stats = await analytics_service.get_monthly_statistics()
            
            text = await self._get_dashboard_text(today_stats, week_stats, month_stats)
            
            keyboard = await StatisticsKeyboard().get_keyboard()
            
            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Ошибка показа дашборда: {e}")
            await self._send_error(callback, _("Ошибка загрузки дашборда"))
    
    async def show_revenue_analytics(self, callback: CallbackQuery, **kwargs):
        """Показ аналитики доходов"""
        try:
            user_data = await self._get_user_data(callback)
            db_session = user_data['db_session']
            
            analytics_service = AnalyticsService(db_session)
            revenue_data = await analytics_service.get_revenue_analytics()
            
            text = await self._get_revenue_analytics_text(revenue_data)
            
            keyboard = await StatisticsKeyboard().get_keyboard()
            
            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Ошибка показа аналитики доходов: {e}")
            await self._send_error(callback, _("Ошибка загрузки аналитики доходов"))
    
    async def show_user_analytics(self, callback: CallbackQuery, **kwargs):
        """Показ аналитики пользователей"""
        try:
            user_data = await self._get_user_data(callback)
            db_session = user_data['db_session']
            
            analytics_service = AnalyticsService(db_session)
            user_data_analytics = await analytics_service.get_user_analytics()
            
            text = await self._get_user_analytics_text(user_data_analytics)
            
            keyboard = await StatisticsKeyboard().get_keyboard()
            
            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Ошибка показа аналитики пользователей: {e}")
            await self._send_error(callback, _("Ошибка загрузки аналитики пользователей"))
    
    async def show_subscription_analytics(self, callback: CallbackQuery, **kwargs):
        """Показ аналитики подписок"""
        try:
            user_data = await self._get_user_data(callback)
            db_session = user_data['db_session']
            
            analytics_service = AnalyticsService(db_session)
            subscription_data = await analytics_service.get_subscription_analytics()
            
            text = await self._get_subscription_analytics_text(subscription_data)
            
            keyboard = await StatisticsKeyboard().get_keyboard()
            
            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Ошибка показа аналитики подписок: {e}")
            await self._send_error(callback, _("Ошибка загрузки аналитики подписок"))
    
    async def show_referral_analytics(self, callback: CallbackQuery, **kwargs):
        """Показ аналитики реферальной программы"""
        try:
            user_data = await self._get_user_data(callback)
            db_session = user_data['db_session']
            
            analytics_service = AnalyticsService(db_session)
            referral_data = await analytics_service.get_referral_analytics()
            
            text = await self._get_referral_analytics_text(referral_data)
            
            keyboard = await StatisticsKeyboard().get_keyboard()
            
            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Ошибка показа аналитики рефералов: {e}")
            await self._send_error(callback, _("Ошибка загрузки аналитики рефералов"))
    
    async def show_payment_analytics(self, callback: CallbackQuery, **kwargs):
        """Показ аналитики платежей"""
        try:
            user_data = await self._get_user_data(callback)
            db_session = user_data['db_session']
            
            analytics_service = AnalyticsService(db_session)
            payment_data = await analytics_service.get_payment_analytics()
            
            text = await self._get_payment_analytics_text(payment_data)
            
            keyboard = await StatisticsKeyboard().get_keyboard()
            
            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Ошибка показа аналитики платежей: {e}")
            await self._send_error(callback, _("Ошибка загрузки аналитики платежей"))
    
    async def generate_report(self, callback: CallbackQuery, **kwargs):
        """Генерация отчета"""
        try:
            # Извлекаем тип отчета
            report_type = callback.data.split('_', 1)[1]
            
            user_data = await self._get_user_data(callback)
            db_session = user_data['db_session']
            
            analytics_service = AnalyticsService(db_session)
            
            # Генерируем отчет в зависимости от типа
            if report_type == 'daily':
                report_data = await analytics_service.generate_daily_report()
                report_title = "Ежедневный отчет"
            elif report_type == 'weekly':
                report_data = await analytics_service.generate_weekly_report()
                report_title = "Еженедельный отчет"
            elif report_type == 'monthly':
                report_data = await analytics_service.generate_monthly_report()
                report_title = "Ежемесячный отчет"
            else:
                await callback.answer(_("Неизвестный тип отчета"), show_alert=True)
                return
            
            text = await self._get_report_text(report_data, report_title)
            
            keyboard = await StatisticsKeyboard().get_keyboard()
            
            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Ошибка генерации отчета: {e}")
            await self._send_error(callback, _("Ошибка генерации отчета"))
    
    # Вспомогательные методы
    
    def _create_overview_card(self, overview: Dict[str, Any]) -> str:
        """Создание карточки обзора"""
        lines = [
            "┌─────────────────────────────┐",
            "│ 📊 <b>Обзор за сегодня</b>        │",
            "├─────────────────────────────┤"
        ]
        
        revenue_today = overview.get('revenue_today', 0)
        new_users = overview.get('new_users_today', 0)
        new_subscriptions = overview.get('new_subscriptions_today', 0)
        active_users = overview.get('active_users_today', 0)
        
        lines.extend([
            f"│ 💰 Доход: {revenue_today:>17.0f} ₽ │",
            f"│ 👥 Новых пользователей: {new_users:>7} │",
            f"│ 📡 Новых подписок: {new_subscriptions:>11} │",
            f"│ 🔥 Активных: {active_users:>16} │"
        ])
        
        lines.append("└─────────────────────────────┘")
        
        return "\n".join(lines)
    
    async def _get_dashboard_text(
        self,
        today_stats: Dict[str, Any],
        week_stats: Dict[str, Any],
        month_stats: Dict[str, Any]
    ) -> str:
        """Формирование текста дашборда"""
        lines = [
            "📊 <b>Дашборд</b>",
            ""
        ]
        
        # Сегодня
        lines.extend([
            "📅 <b>Сегодня:</b>",
            f"💰 Доход: {today_stats.get('revenue', 0):.0f} ₽",
            f"👥 Новых пользователей: {today_stats.get('new_users', 0)}",
            f"📡 Новых подписок: {today_stats.get('new_subscriptions', 0)}",
            f"🎫 Использовано промокодов: {today_stats.get('promocodes_used', 0)}",
            ""
        ])
        
        # За неделю
        lines.extend([
            "📅 <b>За неделю:</b>",
            f"💰 Доход: {week_stats.get('revenue', 0):.0f} ₽",
            f"👥 Новых пользователей: {week_stats.get('new_users', 0)}",
            f"📡 Новых подписок: {week_stats.get('new_subscriptions', 0)}",
            f"👥 Рефералов: {week_stats.get('new_referrals', 0)}",
            ""
        ])
        
        # За месяц
        lines.extend([
            "📅 <b>За месяц:</b>",
            f"💰 Доход: {month_stats.get('revenue', 0):.0f} ₽",
            f"👥 Новых пользователей: {month_stats.get('new_users', 0)}",
            f"📡 Новых подписок: {month_stats.get('new_subscriptions', 0)}",
            f"💸 Возвратов: {month_stats.get('refunds', 0)}",
            ""
        ])
        
        # Тренды
        revenue_trend = self._calculate_trend(
            today_stats.get('revenue', 0),
            week_stats.get('revenue', 0) / 7  # Средний доход за день на неделе
        )
        
        users_trend = self._calculate_trend(
            today_stats.get('new_users', 0),
            week_stats.get('new_users', 0) / 7  # Средние новые пользователи за день на неделе
        )
        
        lines.extend([
            "📈 <b>Тренды:</b>",
            f"💰 Доход: {revenue_trend}",
            f"👥 Пользователи: {users_trend}",
        ])
        
        return "\n".join(lines)
    
    async def _get_revenue_analytics_text(self, revenue_data: Dict[str, Any]) -> str:
        """Формирование текста аналитики доходов"""
        lines = [
            "💰 <b>Аналитика доходов</b>",
            ""
        ]
        
        # Общий доход
        total_revenue = revenue_data.get('total_revenue', 0)
        monthly_revenue = revenue_data.get('monthly_revenue', 0)
        weekly_revenue = revenue_data.get('weekly_revenue', 0)
        
        lines.extend([
            "📊 <b>Общие показатели:</b>",
            f"💰 Общий доход: {total_revenue:.0f} ₽",
            f"📅 За месяц: {monthly_revenue:.0f} ₽",
            f"📅 За неделю: {weekly_revenue:.0f} ₽",
            ""
        ])
        
        # По способам оплаты
        payment_methods = revenue_data.get('by_payment_method', {})
        if payment_methods:
            lines.append("💳 <b>По способам оплаты:</b>")
            for method, amount in payment_methods.items():
                method_name = {
                    'yookassa': 'Банковские карты',
                    'cryptomus': 'Криптовалюта',
                    'telegram_stars': 'Telegram Stars'
                }.get(method, method)
                lines.append(f"• {method_name}: {amount:.0f} ₽")
            lines.append("")
        
        # По тарифам
        tariff_revenue = revenue_data.get('by_tariff', {})
        if tariff_revenue:
            lines.append("📦 <b>По тарифам:</b>")
            for tariff_name, amount in tariff_revenue.items():
                lines.append(f"• {tariff_name}: {amount:.0f} ₽")
            lines.append("")
        
        # Средний чек
        avg_payment = revenue_data.get('average_payment', 0)
        if avg_payment:
            lines.extend([
                f"📊 Средний чек: {avg_payment:.0f} ₽",
                ""
            ])
        
        return "\n".join(lines)
    
    async def _get_user_analytics_text(self, user_data: Dict[str, Any]) -> str:
        """Формирование текста аналитики пользователей"""
        lines = [
            "👥 <b>Аналитика пользователей</b>",
            ""
        ]
        
        # Общие показатели
        total_users = user_data.get('total_users', 0)
        active_users = user_data.get('active_users', 0)
        new_today = user_data.get('new_today', 0)
        new_week = user_data.get('new_week', 0)
        
        lines.extend([
            "📊 <b>Общие показатели:</b>",
            f"👥 Всего пользователей: {total_users}",
            f"🟢 Активных: {active_users}",
            f"🆕 Новых сегодня: {new_today}",
            f"📅 Новых за неделю: {new_week}",
            ""
        ])
        
        # Конверсия
        conversion_rate = user_data.get('conversion_rate', 0)
        if conversion_rate:
            lines.extend([
                f"📈 Конверсия в покупку: {conversion_rate:.1f}%",
                ""
            ])
        
        # Источники регистрации
        sources = user_data.get('registration_sources', {})
        if sources:
            lines.append("📍 <b>Источники регистрации:</b>")
            for source, count in sources.items():
                source_name = {
                    'direct': 'Прямые переходы',
                    'referral': 'Рефералы',
                    'organic': 'Органический поиск'
                }.get(source, source)
                lines.append(f"• {source_name}: {count}")
            lines.append("")
        
        # Активность
        activity_stats = user_data.get('activity_stats', {})
        if activity_stats:
            lines.append("🔥 <b>Активность:</b>")
            lines.append(f"• Активны сегодня: {activity_stats.get('today', 0)}")
            lines.append(f"• Активны на неделе: {activity_stats.get('week', 0)}")
            lines.append(f"• Неактивны >30 дней: {activity_stats.get('inactive', 0)}")
        
        return "\n".join(lines)
    
    async def _get_subscription_analytics_text(self, subscription_data: Dict[str, Any]) -> str:
        """Формирование текста аналитики подписок"""
        lines = [
            "📡 <b>Аналитика подписок</b>",
            ""
        ]
        
        # Общие показатели
        total_subscriptions = subscription_data.get('total_subscriptions', 0)
        active_subscriptions = subscription_data.get('active_subscriptions', 0)
        new_today = subscription_data.get('new_today', 0)
        expiring_soon = subscription_data.get('expiring_soon', 0)
        
        lines.extend([
            "📊 <b>Общие показатели:</b>",
            f"📡 Всего подписок: {total_subscriptions}",
            f"🟢 Активных: {active_subscriptions}",
            f"🆕 Новых сегодня: {new_today}",
            f"⚠️ Истекают скоро: {expiring_soon}",
            ""
        ])
        
        # По тарифам
        by_tariff = subscription_data.get('by_tariff', {})
        if by_tariff:
            lines.append("📦 <b>По тарифам:</b>")
            for tariff_name, count in by_tariff.items():
                lines.append(f"• {tariff_name}: {count}")
            lines.append("")
        
        # Продления
        renewal_rate = subscription_data.get('renewal_rate', 0)
        if renewal_rate:
            lines.extend([
                f"🔄 Процент продлений: {renewal_rate:.1f}%",
                ""
            ])
        
        # Churn rate
        churn_rate = subscription_data.get('churn_rate', 0)
        if churn_rate:
            lines.extend([
                f"📉 Churn rate: {churn_rate:.1f}%",
                ""
            ])
        
        return "\n".join(lines)
    
    async def _get_referral_analytics_text(self, referral_data: Dict[str, Any]) -> str:
        """Формирование текста аналитики рефералов"""
        lines = [
            "👥 <b>Аналитика рефералов</b>",
            ""
        ]
        
        # Общие показатели
        total_referrals = referral_data.get('total_referrals', 0)
        active_referrers = referral_data.get('active_referrers', 0)
        total_earnings = referral_data.get('total_earnings', 0)
        new_today = referral_data.get('new_today', 0)
        
        lines.extend([
            "📊 <b>Общие показатели:</b>",
            f"👥 Всего рефералов: {total_referrals}",
            f"🔥 Активных рефереров: {active_referrers}",
            f"💰 Выплачено: {total_earnings:.0f} ₽",
            f"🆕 Новых сегодня: {new_today}",
            ""
        ])
        
        # По уровням
        by_level = referral_data.get('by_level', {})
        if by_level:
            lines.append("📊 <b>По уровням:</b>")
            for level, data in by_level.items():
                count = data.get('count', 0)
                earnings = data.get('earnings', 0)
                lines.append(f"• Уровень {level}: {count} чел. ({earnings:.0f} ₽)")
            lines.append("")
        
        # Топ рефереры
        top_referrers = referral_data.get('top_referrers', [])
        if top_referrers:
            lines.append("🏆 <b>Топ рефереры:</b>")
            for i, referrer in enumerate(top_referrers[:5], 1):
                username = referrer.get('username', f"ID{referrer.get('user_id')}")
                earnings = referrer.get('earnings', 0)
                lines.append(f"{i}. {username}: {earnings:.0f} ₽")
        
        return "\n".join(lines)
    
    async def _get_payment_analytics_text(self, payment_data: Dict[str, Any]) -> str:
        """Формирование текста аналитики платежей"""
        lines = [
            "💳 <b>Аналитика платежей</b>",
            ""
        ]
        
        # Общие показатели
        total_payments = payment_data.get('total_payments', 0)
        successful_payments = payment_data.get('successful_payments', 0)
        failed_payments = payment_data.get('failed_payments', 0)
        success_rate = payment_data.get('success_rate', 0)
        
        lines.extend([
            "📊 <b>Общие показатели:</b>",
            f"💳 Всего платежей: {total_payments}",
            f"✅ Успешных: {successful_payments}",
            f"❌ Неудачных: {failed_payments}",
            f"📈 Успешность: {success_rate:.1f}%",
            ""
        ])
        
        # По способам оплаты
        by_method = payment_data.get('by_method', {})
        if by_method:
            lines.append("💳 <b>По способам оплаты:</b>")
            for method, data in by_method.items():
                method_name = {
                    'yookassa': 'Банковские карты',
                    'cryptomus': 'Криптовалюта',
                    'telegram_stars': 'Telegram Stars'
                }.get(method, method)
                count = data.get('count', 0)
                success_rate = data.get('success_rate', 0)
                lines.append(f"• {method_name}: {count} ({success_rate:.1f}%)")
            lines.append("")
        
        # Средние суммы
        avg_amount = payment_data.get('average_amount', 0)
        if avg_amount:
            lines.extend([
                f"💰 Средняя сумма: {avg_amount:.0f} ₽",
                ""
            ])
        
        return "\n".join(lines)
    
    async def _get_report_text(self, report_data: Dict[str, Any], title: str) -> str:
        """Формирование текста отчета"""
        lines = [
            f"📋 <b>{title}</b>",
            f"📅 Период: {report_data.get('period', 'Не указан')}",
            ""
        ]
        
        # Основные метрики
        metrics = report_data.get('metrics', {})
        if metrics:
            lines.append("📊 <b>Ключевые метрики:</b>")
            for metric_name, value in metrics.items():
                lines.append(f"• {metric_name}: {value}")
            lines.append("")
        
        # Выводы
        insights = report_data.get('insights', [])
        if insights:
            lines.append("💡 <b>Выводы:</b>")
            for insight in insights:
                lines.append(f"• {insight}")
        
        return "\n".join(lines)
    
    def _calculate_trend(self, current: float, previous: float) -> str:
        """Расчет тренда"""
        if previous == 0:
            return "📊 Нет данных для сравнения"
        
        change = ((current - previous) / previous) * 100
        
        if change > 5:
            return f"📈 +{change:.1f}%"
        elif change < -5:
            return f"📉 {change:.1f}%"
        else:
            return f"➡️ {change:.1f}%"

    async def show_promocode_analytics(self, callback: CallbackQuery, **kwargs):
        """Показ аналитики промокодов"""
        try:
            user_data = await self._get_user_data(callback)
            db_session = user_data['db_session']

            # Получаем аналитику промокодов
            promocode_data = await self._get_promocode_analytics(db_session)

            text = await self._get_promocode_analytics_text(promocode_data)

            from ..keyboards import AnalyticsKeyboard
            keyboard = await AnalyticsKeyboard().get_keyboard()

            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()

        except Exception as e:
            self.logger.error(f"Ошибка показа аналитики промокодов: {e}")
            await self._send_error(callback, _("Ошибка загрузки аналитики промокодов"))

    async def select_period(self, callback: CallbackQuery, **kwargs):
        """Выбор периода для аналитики"""
        try:
            # Извлекаем период
            period = callback.data.split('_', 1)[1]

            user_data = await self._get_user_data(callback)
            db_session = user_data['db_session']

            # Получаем данные за выбранный период
            if period == 'today':
                analytics_data = await self._get_today_analytics(db_session)
                title = "📊 Аналитика за сегодня"
            elif period == 'week':
                analytics_data = await self._get_week_analytics(db_session)
                title = "📊 Аналитика за неделю"
            elif period == 'month':
                analytics_data = await self._get_month_analytics(db_session)
                title = "📊 Аналитика за месяц"
            else:
                await callback.answer(_("Неизвестный период"))
                return

            text = await self._get_period_analytics_text(analytics_data, title)

            from ..keyboards import AnalyticsKeyboard
            keyboard = await AnalyticsKeyboard().get_keyboard()

            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()

        except Exception as e:
            self.logger.error(f"Ошибка выбора периода: {e}")
            await self._send_error(callback, _("Ошибка загрузки аналитики"))

    async def _get_promocode_analytics(self, db_session) -> Dict[str, Any]:
        """Получение аналитики промокодов"""
        try:
            # Здесь должна быть логика получения данных о промокодах
            # Пока возвращаем заглушку
            return {
                'total_promocodes': 0,
                'active_promocodes': 0,
                'used_promocodes': 0,
                'total_discount': 0,
                'popular_promocodes': []
            }
        except Exception as e:
            self.logger.error(f"Ошибка получения аналитики промокодов: {e}")
            return {}

    async def _get_promocode_analytics_text(self, promocode_data: Dict[str, Any]) -> str:
        """Формирование текста аналитики промокодов"""
        lines = [
            "🎫 <b>Аналитика промокодов</b>",
            ""
        ]

        # Общая статистика
        total = promocode_data.get('total_promocodes', 0)
        active = promocode_data.get('active_promocodes', 0)
        used = promocode_data.get('used_promocodes', 0)
        discount = promocode_data.get('total_discount', 0)

        lines.extend([
            "📊 <b>Общая статистика:</b>",
            f"• Всего промокодов: {total}",
            f"• Активных: {active}",
            f"• Использовано: {used}",
            f"• Общая скидка: {discount:.0f} ₽",
            ""
        ])

        # Популярные промокоды
        popular = promocode_data.get('popular_promocodes', [])
        if popular:
            lines.extend([
                "🔥 <b>Популярные промокоды:</b>"
            ])
            for i, promo in enumerate(popular[:5], 1):
                code = promo.get('code', 'N/A')
                uses = promo.get('uses', 0)
                lines.append(f"{i}. {code}: {uses} использований")
        else:
            lines.append("🔥 <b>Популярные промокоды:</b> Нет данных")

        return "\n".join(lines)

    async def _get_period_analytics_text(self, analytics_data: Dict[str, Any], title: str) -> str:
        """Формирование текста аналитики за период"""
        lines = [
            title,
            ""
        ]

        # Основные метрики
        revenue = analytics_data.get('revenue', 0)
        users = analytics_data.get('new_users', 0)
        subscriptions = analytics_data.get('new_subscriptions', 0)
        payments = analytics_data.get('payments', 0)

        lines.extend([
            "💰 <b>Основные метрики:</b>",
            f"• Доход: {revenue:.0f} ₽",
            f"• Новые пользователи: {users}",
            f"• Новые подписки: {subscriptions}",
            f"• Платежи: {payments}",
            ""
        ])

        # Дополнительная информация
        conversion = analytics_data.get('conversion_rate', 0)
        avg_check = analytics_data.get('average_check', 0)

        lines.extend([
            "📈 <b>Дополнительно:</b>",
            f"• Конверсия: {conversion:.1f}%",
            f"• Средний чек: {avg_check:.0f} ₽"
        ])

        return "\n".join(lines)

    async def _get_today_analytics(self, db_session) -> Dict[str, Any]:
        """Получение аналитики за сегодня"""
        # Заглушка
        return {
            'revenue': 0,
            'new_users': 0,
            'new_subscriptions': 0,
            'payments': 0,
            'conversion_rate': 0,
            'average_check': 0
        }

    async def _get_week_analytics(self, db_session) -> Dict[str, Any]:
        """Получение аналитики за неделю"""
        # Заглушка
        return {
            'revenue': 0,
            'new_users': 0,
            'new_subscriptions': 0,
            'payments': 0,
            'conversion_rate': 0,
            'average_check': 0
        }

    async def _get_month_analytics(self, db_session) -> Dict[str, Any]:
        """Получение аналитики за месяц"""
        # Заглушка
        return {
            'revenue': 0,
            'new_users': 0,
            'new_subscriptions': 0,
            'payments': 0,
            'conversion_rate': 0,
            'average_check': 0
        }

    async def export_data(self, callback: CallbackQuery, **kwargs):
        """Экспорт данных"""
        try:
            # Извлекаем тип экспорта
            export_type = callback.data.split('_', 1)[1]

            user_data = await self._get_user_data(callback)
            db_session = user_data['db_session']

            # Показываем сообщение о начале экспорта
            await callback.message.edit_text(
                f"📤 <b>Экспорт данных: {export_type}</b>\n\n"
                "⏳ Подготавливаем данные для экспорта...",
                reply_markup=None
            )

            # Генерируем данные для экспорта
            if export_type == 'users':
                data = await self._export_users_data(db_session)
                filename = "users_export.csv"
            elif export_type == 'payments':
                data = await self._export_payments_data(db_session)
                filename = "payments_export.csv"
            elif export_type == 'subscriptions':
                data = await self._export_subscriptions_data(db_session)
                filename = "subscriptions_export.csv"
            else:
                await callback.message.edit_text(
                    "❌ <b>Неизвестный тип экспорта</b>",
                    reply_markup=None
                )
                return

            # Отправляем файл (пока заглушка)
            text = f"✅ <b>Экспорт завершен</b>\n\n"
            text += f"📁 Файл: {filename}\n"
            text += f"📊 Записей: {len(data) if data else 0}\n\n"
            text += "⚠️ <i>Функция экспорта в разработке</i>"

            from ..keyboards import AnalyticsKeyboard
            keyboard = await AnalyticsKeyboard().get_keyboard()

            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer(_("Экспорт завершен"))

        except Exception as e:
            self.logger.error(f"Ошибка экспорта данных: {e}")
            await self._send_error(callback, _("Ошибка экспорта данных"))

    async def _export_users_data(self, db_session) -> list:
        """Экспорт данных пользователей"""
        # Заглушка
        return []

    async def _export_payments_data(self, db_session) -> list:
        """Экспорт данных платежей"""
        # Заглушка
        return []

    async def _export_subscriptions_data(self, db_session) -> list:
        """Экспорт данных подписок"""
        # Заглушка
        return []
