"""
UnveilVPN Shop - Admin Start Handler
Обработчик команды /start для административного бота
"""

from typing import Dict, Any

from aiogram.types import Message
from aiogram.filters import Command
from aiogram.utils.i18n import gettext as _

from bot.common.handlers import <PERSON><PERSON><PERSON><PERSON>, AdminHandler
from ...common.utils import MessageFormatter, BotUtils
from ..keyboards import AdminMenuKeyboard


class StartHandler(CommandHandler, AdminHandler):
    """
    Обработчик команды /start для административного бота
    """
    
    def __init__(self):
        super().__init__(commands=['start'], name='AdminStartHandler')
        self.admin_menu_keyboard = AdminMenuKeyboard()
        from ...common.middleware import AuthMiddleware
        self.router.message.middleware(AuthMiddleware('admin'))
        self.router.callback_query.middleware(AuthMiddleware('admin'))
    
    
    
    async def handle_command(self, message: Message, db_session=None, db_user=None, user_roles=None, locale='ru', **kwargs):
        """Обработка команды /start"""
        try:
            
            user_data = await self._get_user_data(message, db_session=db_session, db_user=db_user, user_roles=user_roles, locale=locale)

            # Проверяем права администратора
            if not await self._check_admin_rights(message.from_user):
                await self._send_access_denied(message)
                return

            # Формируем приветственное сообщение
            welcome_text = await self._get_admin_welcome_text(user_data, db_session)

            # Получаем клавиатуру административного меню
            keyboard = await self.admin_menu_keyboard.get_keyboard(user_data)

            await message.answer(welcome_text, reply_markup=keyboard)

            # Логируем вход администратора
            self.logger.info(f"Администратор вошел в систему: {message.from_user.id} (@{message.from_user.username})")

        except Exception as e:
            self.logger.error(f"Ошибка обработки команды /start в админ боте: {e}")
            await self._send_error(message, _("Произошла внутренняя ошибка. Пожалуйста, попробуйте позже."))
    
    async def _send_access_denied(self, message: Message):
        """Отправка сообщения об отказе в доступе"""
        text = _("❌ <b>Доступ запрещен</b>\n\nУ вас нет прав для использования административной панели.")
        await message.answer(text)
        
        # Логируем попытку несанкционированного доступа
        self.logger.warning(f"Попытка несанкционированного доступа: {message.from_user.id} (@{message.from_user.username})")
    
    async def _get_admin_welcome_text(self, user_data: Dict[str, Any], db_session) -> str:
        """Формирование приветственного текста для администратора"""
        user = user_data['user']
        
        # Получаем базовую статистику системы
        stats = await self._get_system_stats(db_session)
        
        name = user.first_name or user.username or _("Администратор")
        
        lines = [
            _("🔧 <b>Административная панель {shop_name}</b>").format(
                shop_name=self.config.shop_name
            ),
            "",
            _("Добро пожаловать, {name}!").format(name=name),
            "",
            _("📊 <b>Текущая статистика:</b>"),
            _("👥 Пользователей: {total} (активных: {active})").format(
                total=stats.get('users_total', 0),
                active=stats.get('users_active', 0)
            ),
            _("💰 Платежей сегодня: {count} на сумму {amount} ₽").format(
                count=stats.get('payments_today', 0),
                amount=stats.get('revenue_today', 0)
            ),
            _("🎫 Активных тарифов: {count}").format(
                count=stats.get('tariffs_active', 0)
            ),
            _("🎟️ Активных промокодов: {count}").format(
                count=stats.get('promocodes_active', 0)
            ),
        ]
        
        # Добавляем информацию о системе
        lines.extend([
            "",
            _("🖥️ <b>Система:</b>"),
            _("Время работы: {uptime}").format(uptime=stats.get('uptime', 'N/A')),
            _("Версия: {version}").format(version=self.config.version or '1.0.0'),
            _("Последнее обновление: {date}").format(
                date=BotUtils.format_datetime(stats.get('last_update'), 'short') if stats.get('last_update') else _('неизвестно')
            ),
        ])
        
        lines.extend([
            "",
            _("Выберите раздел для управления:")
        ])
        
        return "\n".join(lines)
    
    async def _get_system_stats(self, db_session) -> Dict[str, Any]:
        """Получение базовой статистики системы"""
        if not db_session:
            self.logger.error("Ошибка: db_session не передан в _get_system_stats.")
            return {
                'users_total': 0,
                'users_active': 0,
                'payments_today': 0,
                'revenue_today': 0,
                'tariffs_active': 0,
                'promocodes_active': 0,
                'uptime': 'N/A',
                'last_update': None
            }
        try:
            from sqlalchemy import select, func
            from ...db.models import VPNUsers, Payments, Tariffs, Promocodes
            from datetime import datetime, timedelta

            # Пользователи
            users_total = await db_session.scalar(select(func.count(VPNUsers.id)))
            users_active = await db_session.scalar(select(func.count(VPNUsers.id)).where(VPNUsers.is_active == True))

            # Платежи сегодня
            today = datetime.utcnow().date()
            payments_today = await db_session.scalar(
                select(func.count(Payments.id)).where(func.date(Payments.created_at) == today)
            )
            revenue_today = await db_session.scalar(
                select(func.sum(Payments.final_amount)).where(func.date(Payments.created_at) == today)
            ) or 0

            # Тарифы и промокоды
            tariffs_active = await db_session.scalar(select(func.count(Tariffs.id)).where(Tariffs.is_active == True))
            promocodes_active = await db_session.scalar(select(func.count(Promocodes.id)).where(Promocodes.is_active == True))

            # Uptime (заглушка, т.к. сложно отследить без доп. сервиса)
            uptime = "N/A"

            # Последнее обновление (заглушка, можно заменить на время последнего коммита)
            last_update = None

            return {
                'users_total': users_total,
                'users_active': users_active,
                'payments_today': payments_today,
                'revenue_today': round(revenue_today, 2),
                'tariffs_active': tariffs_active,
                'promocodes_active': promocodes_active,
                'uptime': uptime,
                'last_update': last_update
            }

        except Exception as e:
            self.logger.error(f"Ошибка получения статистики: {e}")
            return {
                'users_total': 0,
                'users_active': 0,
                'payments_today': 0,
                'revenue_today': 0,
                'tariffs_active': 0,
                'promocodes_active': 0,
                'uptime': 'N/A',
                'last_update': None
            }
