"""
UnveilVPN Shop - Admin Referrals Handler
Обработчик управления реферальной системой для административного бота
"""

from typing import Dict, Any, List
from uuid import UUID

from aiogram.types import Message, CallbackQuery
from aiogram.fsm.context import FSMContext
from aiogram.utils.i18n import gettext as _

from ...common.handlers import BaseHandler
from ...common.utils import MessageFormatter, BotUtils
from ...common.exceptions import ValidationError, BusinessLogicError
from ...services.referral_service import ReferralService
from ...services.analytics_service import AnalyticsService
from ..keyboards import ReferralManagementKeyboard


class ReferralsHandler(BaseHandler):
    """
    Обработчик управления реферальной системой для админ бота
    """
    
    def __init__(self):
        super().__init__(name='AdminReferralsHandler')
    
    def _register_handlers(self):
        """Регистрация обработчиков"""
        
        # Основные обработчики
        self.router.callback_query.register(
            self.show_referrals_menu,
            lambda c: c.data == 'admin_referrals'
        )
        
        self.router.callback_query.register(
            self.show_referral_analytics,
            lambda c: c.data == 'referral_analytics'
        )
        
        self.router.callback_query.register(
            self.show_top_referrers,
            lambda c: c.data == 'top_referrers'
        )
        
        self.router.callback_query.register(
            self.show_referral_settings,
            lambda c: c.data == 'referral_settings'
        )
        
        # Просмотр конкретного пользователя
        self.router.callback_query.register(
            self.view_user_referrals,
            lambda c: c.data.startswith('view_user_referrals_')
        )
        
        # Управление бонусами
        self.router.callback_query.register(
            self.process_pending_bonuses,
            lambda c: c.data == 'process_pending_bonuses'
        )
        
        self.router.callback_query.register(
            self.adjust_referral_rates,
            lambda c: c.data == 'adjust_referral_rates'
        )
    
    async def show_referrals_menu(self, callback: CallbackQuery, **kwargs):
        """Показ меню управления рефералами"""
        try:
            user_data = await self._get_user_data(callback)
            db_session = user_data['db_session']
            
            # Получаем общую статистику
            stats = await self._get_referral_overview(db_session)
            
            text = await self._get_referrals_menu_text(stats)
            keyboard = await ReferralManagementKeyboard().get_keyboard(user_data)
            
            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Ошибка показа меню рефералов: {e}")
            await self._send_error(callback, _("Ошибка загрузки меню рефералов"))
    
    async def show_referral_analytics(self, callback: CallbackQuery, **kwargs):
        """Показ аналитики реферальной системы"""
        try:
            user_data = await self._get_user_data(callback)
            db_session = user_data['db_session']
            
            # Получаем детальную аналитику
            analytics = await self._get_detailed_analytics(db_session)
            
            text = await self._get_analytics_text(analytics)
            
            from ..keyboards import ReferralAnalyticsKeyboard
            keyboard = await ReferralAnalyticsKeyboard().get_keyboard()
            
            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Ошибка показа аналитики рефералов: {e}")
            await self._send_error(callback, _("Ошибка загрузки аналитики"))
    
    async def show_top_referrers(self, callback: CallbackQuery, **kwargs):
        """Показ топ рефереров"""
        try:
            user_data = await self._get_user_data(callback)
            db_session = user_data['db_session']
            
            # Получаем топ рефереров
            top_referrers = await self._get_top_referrers(db_session)
            
            text = await self._get_top_referrers_text(top_referrers)
            
            from ..keyboards import TopReferrersKeyboard
            keyboard = await TopReferrersKeyboard(top_referrers).get_keyboard()
            
            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Ошибка показа топ рефереров: {e}")
            await self._send_error(callback, _("Ошибка загрузки топ рефереров"))
    
    async def view_user_referrals(self, callback: CallbackQuery, **kwargs):
        """Просмотр рефералов конкретного пользователя"""
        try:
            # Извлекаем ID пользователя
            user_id = UUID(callback.data.split('_')[-1])
            
            user_data = await self._get_user_data(callback)
            db_session = user_data['db_session']
            
            referral_service = ReferralService(db_session)
            
            # Получаем статистику пользователя
            stats = await referral_service.get_user_referral_stats(user_id)
            
            # Получаем дерево рефералов
            tree = await referral_service.get_referral_tree(user_id, max_depth=3)
            
            text = await self._get_user_referrals_text(stats, tree)
            
            from ..keyboards import UserReferralsKeyboard
            keyboard = await UserReferralsKeyboard(str(user_id)).get_keyboard()
            
            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Ошибка просмотра рефералов пользователя: {e}")
            await self._send_error(callback, _("Ошибка загрузки данных пользователя"))
    
    async def process_pending_bonuses(self, callback: CallbackQuery, **kwargs):
        """Обработка ожидающих бонусов"""
        try:
            user_data = await self._get_user_data(callback)
            db_session = user_data['db_session']
            
            # Здесь будет логика обработки ожидающих бонусов
            # Пока заглушка
            
            text = _(
                "⏳ <b>Обработка ожидающих бонусов</b>\n\n"
                "Функция в разработке.\n"
                "Будет реализована автоматическая обработка "
                "и выплата накопленных бонусов."
            )
            
            from ..keyboards import BackToReferralsKeyboard
            keyboard = await BackToReferralsKeyboard().get_keyboard()
            
            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Ошибка обработки ожидающих бонусов: {e}")
            await self._send_error(callback, _("Ошибка обработки бонусов"))
    
    async def show_referral_settings(self, callback: CallbackQuery, **kwargs):
        """Показ настроек реферальной системы"""
        try:
            # Получаем текущие настройки
            rates = ReferralService.REFERRAL_RATES
            
            text = _(
                "⚙️ <b>Настройки реферальной системы</b>\n\n"
                "💰 <b>Процентные ставки:</b>\n"
                "• 1-й уровень: {level1}%\n"
                "• 2-й уровень: {level2}%\n"
                "• 3-й уровень: {level3}%\n\n"
                "🔧 <b>Дополнительные настройки:</b>\n"
                "• Минимум для вывода: 100 ₽\n"
                "• Максимальная глубина: 3 уровня\n"
                "• Автоматическое начисление: Включено"
            ).format(
                level1=int(rates[1] * 100),
                level2=int(rates[2] * 100),
                level3=int(rates[3] * 100)
            )
            
            from ..keyboards import ReferralSettingsKeyboard
            keyboard = await ReferralSettingsKeyboard().get_keyboard()
            
            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Ошибка показа настроек рефералов: {e}")
            await self._send_error(callback, _("Ошибка загрузки настроек"))
    
    # Вспомогательные методы
    
    async def _get_referral_overview(self, db_session) -> Dict[str, Any]:
        """Получение общей статистики рефералов"""
        try:
            from sqlalchemy import select, func, text
            from ...db.models import VPNUsers, Referrals
            
            # Общее количество пользователей с реферальными кодами
            users_with_codes_query = select(func.count(VPNUsers.id)).where(
                VPNUsers.referral_code.isnot(None)
            )
            users_with_codes_result = await db_session.execute(users_with_codes_query)
            users_with_codes = users_with_codes_result.scalar()
            
            # Общее количество реферальных связей
            total_referrals_query = select(func.count(Referrals.id))
            total_referrals_result = await db_session.execute(total_referrals_query)
            total_referrals = total_referrals_result.scalar()
            
            # Общая сумма заработанных бонусов
            total_bonuses_query = select(func.sum(Referrals.bonus_earned))
            total_bonuses_result = await db_session.execute(total_bonuses_query)
            total_bonuses = total_bonuses_result.scalar() or 0
            
            # Активные рефереры (с заработком > 0)
            active_referrers_query = select(func.count(func.distinct(Referrals.referrer_id))).where(
                Referrals.bonus_earned > 0
            )
            active_referrers_result = await db_session.execute(active_referrers_query)
            active_referrers = active_referrers_result.scalar()
            
            return {
                'users_with_codes': users_with_codes,
                'total_referrals': total_referrals,
                'total_bonuses': float(total_bonuses),
                'active_referrers': active_referrers
            }
            
        except Exception as e:
            self.logger.error(f"Ошибка получения обзора рефералов: {e}")
            return {
                'users_with_codes': 0,
                'total_referrals': 0,
                'total_bonuses': 0,
                'active_referrers': 0
            }
    
    async def _get_detailed_analytics(self, db_session) -> Dict[str, Any]:
        """Получение детальной аналитики"""
        try:
            analytics_service = AnalyticsService(db_session)
            
            # Здесь будет вызов методов аналитики
            # Пока возвращаем заглушку
            
            return {
                'conversion_rate': 15.5,  # Процент конверсии рефералов в покупки
                'avg_referrals_per_user': 2.3,
                'top_performing_codes': [],
                'monthly_growth': 12.8
            }
            
        except Exception as e:
            self.logger.error(f"Ошибка получения детальной аналитики: {e}")
            return {}
    
    async def _get_top_referrers(self, db_session, limit: int = 10) -> List[Dict[str, Any]]:
        """Получение топ рефереров"""
        try:
            from sqlalchemy import select, func, desc
            from ...db.models import VPNUsers, Referrals
            
            query = (
                select(
                    VPNUsers.id,
                    VPNUsers.username,
                    VPNUsers.telegram_id,
                    VPNUsers.referral_code,
                    func.count(Referrals.id).label('referrals_count'),
                    func.sum(Referrals.bonus_earned).label('total_earned')
                )
                .join(Referrals, VPNUsers.id == Referrals.referrer_id)
                .group_by(VPNUsers.id)
                .order_by(desc('total_earned'))
                .limit(limit)
            )
            
            result = await db_session.execute(query)
            rows = result.fetchall()
            
            top_referrers = []
            for row in rows:
                top_referrers.append({
                    'user_id': str(row.id),
                    'username': row.username,
                    'telegram_id': row.telegram_id,
                    'referral_code': row.referral_code,
                    'referrals_count': row.referrals_count,
                    'total_earned': float(row.total_earned or 0)
                })
            
            return top_referrers
            
        except Exception as e:
            self.logger.error(f"Ошибка получения топ рефереров: {e}")
            return []
    
    async def _get_referrals_menu_text(self, stats: Dict[str, Any]) -> str:
        """Формирование текста меню рефералов"""
        lines = [
            _("👥 <b>Управление реферальной системой</b>"),
            "",
            _("📊 <b>Общая статистика:</b>"),
            _("• Пользователей с кодами: {count}").format(count=stats['users_with_codes']),
            _("• Всего рефералов: {count}").format(count=stats['total_referrals']),
            _("• Активных рефереров: {count}").format(count=stats['active_referrers']),
            _("• Выплачено бонусов: {amount:.2f} ₽").format(amount=stats['total_bonuses']),
            "",
            _("Выберите действие:")
        ]
        
        return "\n".join(lines)
    
    async def _get_analytics_text(self, analytics: Dict[str, Any]) -> str:
        """Формирование текста аналитики"""
        lines = [
            _("📈 <b>Аналитика реферальной системы</b>"),
            "",
            _("📊 <b>Ключевые метрики:</b>"),
            _("• Конверсия рефералов: {rate}%").format(rate=analytics.get('conversion_rate', 0)),
            _("• Среднее рефералов на пользователя: {avg}").format(
                avg=analytics.get('avg_referrals_per_user', 0)
            ),
            _("• Месячный рост: {growth}%").format(growth=analytics.get('monthly_growth', 0)),
            "",
            _("📋 Детальная аналитика доступна в разделах ниже.")
        ]
        
        return "\n".join(lines)
    
    async def _get_top_referrers_text(self, top_referrers: List[Dict[str, Any]]) -> str:
        """Формирование текста топ рефереров"""
        lines = [
            _("🏆 <b>Топ рефереров</b>"),
            ""
        ]
        
        if not top_referrers:
            lines.append(_("Пока нет активных рефереров."))
            return "\n".join(lines)
        
        for i, referrer in enumerate(top_referrers, 1):
            username = referrer.get('username', 'Unknown')
            referrals_count = referrer.get('referrals_count', 0)
            total_earned = referrer.get('total_earned', 0)
            
            medal = "🥇" if i == 1 else "🥈" if i == 2 else "🥉" if i == 3 else f"{i}."
            
            lines.append(
                f"{medal} @{username} - {referrals_count} рефералов, {total_earned:.2f} ₽"
            )
        
        return "\n".join(lines)
    
    async def _get_user_referrals_text(self, stats: Dict[str, Any], tree: Dict[str, Any]) -> str:
        """Формирование текста рефералов пользователя"""
        lines = [
            _("👤 <b>Рефералы пользователя</b>"),
            "",
            _("🔗 Код: <code>{code}</code>").format(code=stats.get('referral_code', 'N/A')),
            "",
            _("📊 <b>Статистика:</b>"),
            _("• Всего рефералов: {count}").format(count=stats.get('total_referrals', 0)),
            _("• Общий заработок: {earnings:.2f} ₽").format(earnings=stats.get('total_earnings', 0)),
            ""
        ]
        
        # Добавляем информацию по уровням
        if stats.get('stats_by_level'):
            lines.append(_("📋 <b>По уровням:</b>"))
            for level, level_stats in stats['stats_by_level'].items():
                if level_stats['count'] > 0:
                    lines.append(
                        f"• {level}-й уровень: {level_stats['count']} чел., {level_stats['earnings']:.2f} ₽"
                    )
        
        return "\n".join(lines)

    async def adjust_referral_rates(self, callback: CallbackQuery, **kwargs):
        """Настройка ставок реферальной системы"""
        try:
            user_data = await self._get_user_data(callback)
            db_session = user_data['db_session']

            # Получаем текущие ставки
            current_rates = ReferralService.REFERRAL_RATES

            text = f"⚙️ <b>Настройка реферальных ставок</b>\n\n"
            text += f"📊 <b>Текущие ставки:</b>\n"

            for level, rate in current_rates.items():
                text += f"• {level}-й уровень: {rate * 100:.1f}%\n"

            text += f"\n💡 <b>Информация:</b>\n"
            text += f"• Ставки указываются в процентах от суммы платежа\n"
            text += f"• Максимальная ставка: 50%\n"
            text += f"• Изменения применяются к новым рефералам\n\n"
            text += f"⚠️ <i>Функция настройки в разработке</i>"

            from ..keyboards import ReferralRatesKeyboard
            keyboard = await ReferralRatesKeyboard().get_keyboard()

            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer(_("Настройка ставок"))

        except Exception as e:
            self.logger.error(f"Ошибка настройки реферальных ставок: {e}")
            await self._send_error(callback, _("Ошибка настройки ставок"))
