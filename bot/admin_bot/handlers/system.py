"""
UnveilVPN Shop - Admin Bot System Handler
Обработчик системных функций для админ бота
"""

import os
import psutil
import asyncio
from typing import Dict, Any, List
from datetime import datetime, timezone
from decimal import Decimal

from aiogram.types import Message, CallbackQuery, BufferedInputFile
from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import State, StatesGroup
from aiogram.utils.i18n import gettext as _

from ...common.handlers import BaseHandler
from ...common.utils import MessageFormatter, BotUtils
from ...common.exceptions import NotFoundError, ValidationError, BusinessLogicError
from ...common.ui_components import UIComponents
from ...services.system_service import SystemService
from ...services.backup_service import BackupService
from ...services.notification_service import NotificationService
from ..keyboards import (
    SystemMenuKeyboard, SystemMonitoringKeyboard, SystemBackupsKeyboard,
    SystemSettingsKeyboard, SystemLogsKeyboard, SystemMaintenanceKeyboard
)


class SystemManagementStates(StatesGroup):
    """Состояния для системного управления"""
    sending_broadcast = State()
    setting_maintenance = State()
    configuring_backup = State()
    viewing_logs = State()


class SystemHandler(BaseHandler):
    """
    Обработчик системных функций для админ бота
    """
    
    def __init__(self):
        super().__init__(name='AdminSystemHandler')
    
    def _register_handlers(self):
        """Регистрация обработчиков"""
        
        # Основные обработчики
        self.router.callback_query.register(
            self.show_system_menu,
            lambda c: c.data == 'system'
        )
        
        self.router.callback_query.register(
            self.show_system_monitoring,
            lambda c: c.data == 'system_monitoring'
        )
        
        self.router.callback_query.register(
            self.show_system_logs,
            lambda c: c.data == 'system_logs'
        )
        
        self.router.callback_query.register(
            self.show_backup_management,
            lambda c: c.data == 'backup_management'
        )
        
        self.router.callback_query.register(
            self.show_system_settings,
            lambda c: c.data == 'system_settings'
        )
        
        self.router.callback_query.register(
            self.show_maintenance_mode,
            lambda c: c.data == 'maintenance_mode'
        )
        
        self.router.callback_query.register(
            self.send_broadcast,
            lambda c: c.data == 'send_broadcast'
        )
        
        self.router.callback_query.register(
            self.create_backup,
            lambda c: c.data == 'create_backup'
        )
        
        self.router.callback_query.register(
            self.restore_backup,
            lambda c: c.data.startswith('restore_backup_')
        )
        
        self.router.callback_query.register(
            self.clear_cache,
            lambda c: c.data == 'clear_cache'
        )
        
        self.router.callback_query.register(
            self.restart_services,
            lambda c: c.data == 'restart_services'
        )
        
        self.router.callback_query.register(
            self.view_error_logs,
            lambda c: c.data == 'view_error_logs'
        )
        
        self.router.callback_query.register(
            self.export_logs,
            lambda c: c.data == 'export_logs'
        )
        
        # Обработчики состояний
        self.router.message.register(
            self.process_broadcast_message,
            SystemManagementStates.sending_broadcast
        )
        
        self.router.message.register(
            self.process_maintenance_settings,
            SystemManagementStates.setting_maintenance
        )
    
    async def show_system_menu(self, callback: CallbackQuery, **kwargs):
        """Показ главного меню системных функций"""
        try:
            user_data = await self._get_user_data(callback)
            db_session = user_data['db_session']
            
            # Получаем системную информацию
            system_service = SystemService(db_session)
            system_info = await system_service.get_system_info()
            
            # Создаем красивую карточку системной информации
            system_card = self._create_system_info_card(system_info)
            
            text = _(
                "⚙️ <b>Системное управление</b>\n\n"
                "{system_card}\n\n"
                "🔧 <b>Доступные функции:</b>\n"
                "• Мониторинг системы\n"
                "• Управление резервными копиями\n"
                "• Просмотр логов\n"
                "• Рассылка уведомлений\n"
                "• Режим обслуживания\n"
                "• Настройки системы\n\n"
                "Выберите функцию:"
            ).format(system_card=system_card)
            
            keyboard = await SystemMenuKeyboard(system_info).get_keyboard()
            
            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Ошибка показа системного меню: {e}")
            await self._send_error(callback, _("Ошибка загрузки системного меню"))
    
    async def show_system_monitoring(self, callback: CallbackQuery, **kwargs):
        """Показ мониторинга системы"""
        try:
            user_data = await self._get_user_data(callback)
            db_session = user_data['db_session']
            
            system_service = SystemService(db_session)
            monitoring_data = await system_service.get_monitoring_data()
            
            text = await self._get_monitoring_text(monitoring_data)
            
            keyboard = await SystemMonitoringKeyboard().get_keyboard()
            
            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Ошибка показа мониторинга: {e}")
            await self._send_error(callback, _("Ошибка загрузки мониторинга"))
    
    async def show_system_logs(self, callback: CallbackQuery, **kwargs):
        """Показ системных логов"""
        try:
            user_data = await self._get_user_data(callback)
            db_session = user_data['db_session']
            
            system_service = SystemService(db_session)
            logs_data = await system_service.get_recent_logs(limit=20)
            
            text = await self._get_logs_text(logs_data)
            
            keyboard = await SystemLogsKeyboard().get_keyboard()
            
            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Ошибка показа логов: {e}")
            await self._send_error(callback, _("Ошибка загрузки логов"))
    
    async def show_backup_management(self, callback: CallbackQuery, **kwargs):
        """Показ управления резервными копиями"""
        try:
            user_data = await self._get_user_data(callback)
            db_session = user_data['db_session']
            
            backup_service = BackupService(db_session)
            backups_list = await backup_service.get_backups_list()
            
            text = await self._get_backups_text(backups_list)
            
            keyboard = await SystemBackupsKeyboard().get_keyboard()
            
            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Ошибка показа управления бэкапами: {e}")
            await self._send_error(callback, _("Ошибка загрузки управления резервными копиями"))
    
    async def send_broadcast(self, callback: CallbackQuery, state: FSMContext, **kwargs):
        """Отправка рассылки"""
        try:
            await state.set_state(SystemManagementStates.sending_broadcast)
            
            text = _(
                "📢 <b>Рассылка сообщений</b>\n\n"
                "Введите сообщение для рассылки всем пользователям:\n\n"
                "⚠️ <b>Внимание!</b>\n"
                "• Сообщение будет отправлено ВСЕМ пользователям\n"
                "• Используйте эту функцию осторожно\n"
                "• Рекомендуется для важных уведомлений\n\n"
                "💡 <b>Поддерживаемые форматы:</b>\n"
                "• HTML разметка\n"
                "• Эмодзи\n"
                "• Ссылки\n\n"
                "Введите текст сообщения или отправьте /cancel для отмены:"
            )
            
            await callback.message.edit_text(text)
            await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Ошибка подготовки рассылки: {e}")
            await self._send_error(callback, _("Ошибка подготовки рассылки"))
    
    async def process_broadcast_message(self, message: Message, state: FSMContext, **kwargs):
        """Обработка сообщения для рассылки"""
        try:
            if message.text == '/cancel':
                await state.clear()
                await message.answer(_("❌ Рассылка отменена"))
                return
            
            broadcast_text = message.text.strip()
            
            if len(broadcast_text) < 10:
                await message.answer(_("❌ Сообщение слишком короткое. Минимум 10 символов."))
                return
            
            user_data = await self._get_user_data(message)
            db_session = user_data['db_session']
            admin_user = user_data['db_user']
            
            # Отправляем рассылку
            notification_service = NotificationService(db_session)
            result = await notification_service.send_broadcast(
                message=broadcast_text,
                sent_by_id=admin_user.id
            )
            
            await state.clear()
            
            success_banner = UIComponents.create_notification_banner(
                f"Рассылка отправлена {result['sent']} пользователям!",
                banner_type="success"
            )
            
            text = _(
                "✅ <b>Рассылка завершена</b>\n\n"
                "{banner}\n\n"
                "📊 <b>Статистика:</b>\n"
                "• Отправлено: {sent}\n"
                "• Ошибок: {errors}\n"
                "• Заблокированных ботов: {blocked}\n\n"
                "📝 <b>Сообщение:</b>\n"
                "{message}"
            ).format(
                banner=success_banner,
                sent=result['sent'],
                errors=result['errors'],
                blocked=result['blocked'],
                message=broadcast_text[:200] + "..." if len(broadcast_text) > 200 else broadcast_text
            )
            
            keyboard = await SystemMenuKeyboard().get_keyboard()
            
            await message.answer(text, reply_markup=keyboard)
            
        except Exception as e:
            self.logger.error(f"Ошибка обработки рассылки: {e}")
            await message.answer(_("❌ Ошибка отправки рассылки"))
    
    async def create_backup(self, callback: CallbackQuery, **kwargs):
        """Создание резервной копии"""
        try:
            user_data = await self._get_user_data(callback)
            db_session = user_data['db_session']
            admin_user = user_data['db_user']
            
            # Показываем прогресс
            progress_banner = UIComponents.create_notification_banner(
                "Создание резервной копии...",
                banner_type="info"
            )
            
            await callback.message.edit_text(
                f"🔄 <b>Создание резервной копии</b>\n\n{progress_banner}\n\nПожалуйста, подождите..."
            )
            
            # Создаем бэкап
            backup_service = BackupService(db_session)
            backup_info = await backup_service.create_backup(created_by_id=admin_user.id)
            
            success_banner = UIComponents.create_notification_banner(
                "Резервная копия создана успешно!",
                banner_type="success"
            )
            
            text = _(
                "✅ <b>Резервная копия создана</b>\n\n"
                "{banner}\n\n"
                "📋 <b>Информация о копии:</b>\n"
                "• ID: <code>{backup_id}</code>\n"
                "• Размер: {size} МБ\n"
                "• Время создания: {created_at}\n"
                "• Тип: {backup_type}\n\n"
                "💾 Резервная копия сохранена и готова к использованию."
            ).format(
                banner=success_banner,
                backup_id=backup_info['id'],
                size=backup_info['size_mb'],
                created_at=backup_info['created_at'],
                backup_type=backup_info['type']
            )
            
            keyboard = await SystemBackupsKeyboard().get_keyboard()
            
            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Ошибка создания бэкапа: {e}")
            await self._send_error(callback, _("Ошибка создания резервной копии"))
    
    async def clear_cache(self, callback: CallbackQuery, **kwargs):
        """Очистка кэша"""
        try:
            user_data = await self._get_user_data(callback)
            db_session = user_data['db_session']
            
            system_service = SystemService(db_session)
            cache_info = await system_service.clear_cache()
            
            success_banner = UIComponents.create_notification_banner(
                "Кэш очищен успешно!",
                banner_type="success"
            )
            
            text = _(
                "✅ <b>Кэш очищен</b>\n\n"
                "{banner}\n\n"
                "📊 <b>Статистика очистки:</b>\n"
                "• Освобождено памяти: {freed_memory} МБ\n"
                "• Удалено файлов: {deleted_files}\n"
                "• Время выполнения: {execution_time} сек\n\n"
                "🚀 Система работает быстрее!"
            ).format(
                banner=success_banner,
                freed_memory=cache_info.get('freed_memory', 0),
                deleted_files=cache_info.get('deleted_files', 0),
                execution_time=cache_info.get('execution_time', 0)
            )
            
            keyboard = await SystemMenuKeyboard().get_keyboard()
            
            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Ошибка очистки кэша: {e}")
            await self._send_error(callback, _("Ошибка очистки кэша"))
    
    async def view_error_logs(self, callback: CallbackQuery, **kwargs):
        """Просмотр логов ошибок"""
        try:
            user_data = await self._get_user_data(callback)
            db_session = user_data['db_session']
            
            system_service = SystemService(db_session)
            error_logs = await system_service.get_error_logs(limit=10)
            
            text = await self._get_error_logs_text(error_logs)
            
            keyboard = await SystemLogsKeyboard().get_keyboard()
            
            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Ошибка показа логов ошибок: {e}")
            await self._send_error(callback, _("Ошибка загрузки логов ошибок"))
    
    async def export_logs(self, callback: CallbackQuery, **kwargs):
        """Экспорт логов"""
        try:
            user_data = await self._get_user_data(callback)
            db_session = user_data['db_session']
            
            system_service = SystemService(db_session)
            logs_file = await system_service.export_logs()
            
            # Отправляем файл
            if logs_file and os.path.exists(logs_file):
                with open(logs_file, 'rb') as file:
                    logs_data = file.read()
                
                file_name = f"logs_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
                document = BufferedInputFile(logs_data, filename=file_name)
                
                await callback.message.answer_document(
                    document=document,
                    caption=_("📄 Экспорт системных логов")
                )
                
                # Удаляем временный файл
                os.remove(logs_file)
            else:
                await callback.answer(_("❌ Не удалось создать файл логов"), show_alert=True)
            
            await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Ошибка экспорта логов: {e}")
            await self._send_error(callback, _("Ошибка экспорта логов"))
    
    # Вспомогательные методы
    
    def _create_system_info_card(self, system_info: Dict[str, Any]) -> str:
        """Создание карточки системной информации"""
        lines = [
            "┌─────────────────────────────┐",
            "│ ⚙️ <b>Системная информация</b>    │",
            "├─────────────────────────────┤"
        ]
        
        cpu_usage = system_info.get('cpu_usage', 0)
        memory_usage = system_info.get('memory_usage', 0)
        disk_usage = system_info.get('disk_usage', 0)
        uptime = system_info.get('uptime', 'Неизвестно')
        
        lines.extend([
            f"│ 🖥 CPU: {cpu_usage:.1f}%{' ' * (18 - len(f'{cpu_usage:.1f}%'))}│",
            f"│ 💾 RAM: {memory_usage:.1f}%{' ' * (18 - len(f'{memory_usage:.1f}%'))}│",
            f"│ 💿 Диск: {disk_usage:.1f}%{' ' * (17 - len(f'{disk_usage:.1f}%'))}│",
            f"│ ⏱ Uptime: {str(uptime)[:15]}{' ' * max(0, 15 - len(str(uptime)[:15]))}│"
        ])
        
        lines.append("└─────────────────────────────┘")
        
        return "\n".join(lines)
    
    async def _get_monitoring_text(self, monitoring_data: Dict[str, Any]) -> str:
        """Формирование текста мониторинга"""
        lines = [
            "📊 <b>Мониторинг системы</b>",
            ""
        ]
        
        # Системные ресурсы
        lines.extend([
            "🖥 <b>Системные ресурсы:</b>",
            f"• CPU: {monitoring_data.get('cpu_usage', 0):.1f}%",
            f"• RAM: {monitoring_data.get('memory_usage', 0):.1f}% ({monitoring_data.get('memory_used', 0):.1f}/{monitoring_data.get('memory_total', 0):.1f} ГБ)",
            f"• Диск: {monitoring_data.get('disk_usage', 0):.1f}% ({monitoring_data.get('disk_used', 0):.1f}/{monitoring_data.get('disk_total', 0):.1f} ГБ)",
            f"• Uptime: {monitoring_data.get('uptime', 'Неизвестно')}",
            ""
        ])
        
        # Сетевая активность
        network = monitoring_data.get('network', {})
        if network:
            lines.extend([
                "🌐 <b>Сетевая активность:</b>",
                f"• Отправлено: {network.get('bytes_sent', 0):.1f} МБ",
                f"• Получено: {network.get('bytes_recv', 0):.1f} МБ",
                ""
            ])
        
        # Процессы
        processes = monitoring_data.get('processes', [])
        if processes:
            lines.extend([
                "⚙️ <b>Активные процессы:</b>"
            ])
            for proc in processes[:5]:
                lines.append(f"• {proc.get('name', 'Unknown')}: {proc.get('cpu_percent', 0):.1f}% CPU")
            lines.append("")
        
        # Статус сервисов
        services = monitoring_data.get('services_status', {})
        if services:
            lines.extend([
                "🔧 <b>Статус сервисов:</b>"
            ])
            for service, status in services.items():
                status_emoji = "🟢" if status == "running" else "🔴"
                lines.append(f"• {service}: {status_emoji} {status}")
        
        return "\n".join(lines)
    
    async def _get_logs_text(self, logs_data: List[Dict[str, Any]]) -> str:
        """Формирование текста логов"""
        lines = [
            "📋 <b>Системные логи</b>",
            ""
        ]
        
        if not logs_data:
            lines.extend([
                "Логи не найдены.",
            ])
            return "\n".join(lines)
        
        lines.append(f"Последние {len(logs_data)} записей:")
        lines.append("")
        
        for log_entry in logs_data:
            level = log_entry.get('level', 'INFO')
            timestamp = log_entry.get('timestamp', 'Unknown')
            message = log_entry.get('message', 'No message')
            
            level_emoji = {
                'ERROR': '🔴',
                'WARNING': '🟡',
                'INFO': '🔵',
                'DEBUG': '⚪'
            }.get(level, '⚪')
            
            lines.append(f"{level_emoji} <b>{level}</b> | {timestamp}")
            lines.append(f"   {message[:100]}{'...' if len(message) > 100 else ''}")
            lines.append("")
        
        return "\n".join(lines)
    
    async def _get_backups_text(self, backups_list: List[Dict[str, Any]]) -> str:
        """Формирование текста резервных копий"""
        lines = [
            "💾 <b>Резервные копии</b>",
            ""
        ]
        
        if not backups_list:
            lines.extend([
                "Резервные копии не найдены.",
                "",
                "💡 Рекомендуется создать первую резервную копию."
            ])
            return "\n".join(lines)
        
        lines.append(f"Найдено копий: {len(backups_list)}")
        lines.append("")
        
        for backup in backups_list[:10]:  # Показываем последние 10
            backup_id = backup.get('id', 'Unknown')
            size_mb = backup.get('size_mb', 0)
            created_at = backup.get('created_at', 'Unknown')
            backup_type = backup.get('type', 'full')
            
            lines.append(f"📦 <b>Копия {backup_id[:8]}...</b>")
            lines.append(f"   📊 Размер: {size_mb} МБ | 📅 {created_at}")
            lines.append(f"   🔧 Тип: {backup_type}")
            lines.append("")
        
        if len(backups_list) > 10:
            lines.append(f"... и еще {len(backups_list) - 10} копий")
        
        return "\n".join(lines)
    
    async def _get_error_logs_text(self, error_logs: List[Dict[str, Any]]) -> str:
        """Формирование текста логов ошибок"""
        lines = [
            "🔴 <b>Логи ошибок</b>",
            ""
        ]
        
        if not error_logs:
            lines.extend([
                "✅ Ошибок не найдено!",
                "",
                "Система работает стабильно."
            ])
            return "\n".join(lines)
        
        lines.append(f"Найдено ошибок: {len(error_logs)}")
        lines.append("")
        
        for error in error_logs:
            timestamp = error.get('timestamp', 'Unknown')
            message = error.get('message', 'No message')
            source = error.get('source', 'Unknown')
            
            lines.append(f"🔴 <b>{timestamp}</b>")
            lines.append(f"   📍 {source}")
            lines.append(f"   💬 {message[:150]}{'...' if len(message) > 150 else ''}")
            lines.append("")
        
        return "\n".join(lines)

    async def show_system_settings(self, callback: CallbackQuery, **kwargs):
        """Показ системных настроек"""
        try:
            user_data = await self._get_user_data(callback)
            db_session = user_data['db_session']

            # Получаем системные настройки
            settings_data = await self._get_system_settings(db_session)

            text = await self._get_system_settings_text(settings_data)

            from ..keyboards import SystemSettingsKeyboard
            keyboard = await SystemSettingsKeyboard().get_keyboard()

            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()

        except Exception as e:
            self.logger.error(f"Ошибка показа системных настроек: {e}")
            await self._send_error(callback, _("Ошибка загрузки системных настроек"))

    async def show_maintenance_mode(self, callback: CallbackQuery, **kwargs):
        """Показ режима обслуживания"""
        try:
            user_data = await self._get_user_data(callback)
            db_session = user_data['db_session']

            # Получаем статус режима обслуживания
            maintenance_status = await self._get_maintenance_status(db_session)

            text = f"🔧 <b>Режим обслуживания</b>\n\n"

            if maintenance_status.get('enabled', False):
                text += "🔴 <b>Статус:</b> Включен\n"
                text += f"📝 <b>Сообщение:</b> {maintenance_status.get('message', 'Не указано')}\n"
                text += f"⏰ <b>Включен:</b> {maintenance_status.get('enabled_at', 'Неизвестно')}\n"
            else:
                text += "🟢 <b>Статус:</b> Выключен\n"
                text += "ℹ️ Система работает в обычном режиме"

            from ..keyboards import MaintenanceModeKeyboard
            keyboard = await MaintenanceModeKeyboard(maintenance_status.get('enabled', False)).get_keyboard()

            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()

        except Exception as e:
            self.logger.error(f"Ошибка показа режима обслуживания: {e}")
            await self._send_error(callback, _("Ошибка загрузки режима обслуживания"))

    async def process_maintenance_settings(self, message: Message, state: FSMContext, **kwargs):
        """Обработка настроек режима обслуживания"""
        try:
            if message.text == '/cancel':
                await state.clear()
                await message.answer(_("❌ Настройка режима обслуживания отменена"))
                return

            user_data = await self._get_user_data(message)
            db_session = user_data['db_session']

            # Получаем данные из состояния
            state_data = await state.get_data()
            action = state_data.get('maintenance_action')

            if action == 'enable':
                # Включаем режим обслуживания
                maintenance_message = message.text.strip()
                await self._enable_maintenance_mode(db_session, maintenance_message)

                text = f"🔧 <b>Режим обслуживания включен</b>\n\n"
                text += f"📝 <b>Сообщение:</b> {maintenance_message}\n"
                text += f"⏰ <b>Время включения:</b> {datetime.now().strftime('%d.%m.%Y %H:%M')}"

                await message.answer(text)

            elif action == 'disable':
                # Выключаем режим обслуживания
                await self._disable_maintenance_mode(db_session)

                text = "🟢 <b>Режим обслуживания выключен</b>\n\n"
                text += "ℹ️ Система возвращена в обычный режим работы"

                await message.answer(text)

            await state.clear()

        except Exception as e:
            self.logger.error(f"Ошибка обработки настроек режима обслуживания: {e}")
            await message.answer(_("❌ Ошибка настройки режима обслуживания"))
            await state.clear()

    async def _get_system_settings(self, db_session) -> Dict[str, Any]:
        """Получение системных настроек"""
        try:
            # Здесь должна быть логика получения настроек из БД
            # Пока возвращаем заглушку
            return {
                'max_users': 10000,
                'max_connections_per_user': 5,
                'trial_period_days': 7,
                'auto_cleanup_enabled': True,
                'log_level': 'INFO',
                'backup_retention_days': 30
            }
        except Exception as e:
            self.logger.error(f"Ошибка получения системных настроек: {e}")
            return {}

    async def _get_system_settings_text(self, settings_data: Dict[str, Any]) -> str:
        """Формирование текста системных настроек"""
        lines = [
            "⚙️ <b>Системные настройки</b>",
            ""
        ]

        # Основные настройки
        max_users = settings_data.get('max_users', 0)
        max_connections = settings_data.get('max_connections_per_user', 0)
        trial_days = settings_data.get('trial_period_days', 0)

        lines.extend([
            "👥 <b>Пользователи:</b>",
            f"• Максимум пользователей: {max_users}",
            f"• Подключений на пользователя: {max_connections}",
            f"• Пробный период: {trial_days} дней",
            ""
        ])

        # Системные настройки
        auto_cleanup = settings_data.get('auto_cleanup_enabled', False)
        log_level = settings_data.get('log_level', 'INFO')
        backup_retention = settings_data.get('backup_retention_days', 30)

        lines.extend([
            "🔧 <b>Система:</b>",
            f"• Автоочистка: {'✅ Включена' if auto_cleanup else '❌ Выключена'}",
            f"• Уровень логирования: {log_level}",
            f"• Хранение бэкапов: {backup_retention} дней"
        ])

        return "\n".join(lines)

    async def _get_maintenance_status(self, db_session) -> Dict[str, Any]:
        """Получение статуса режима обслуживания"""
        try:
            # Здесь должна быть логика получения статуса из БД
            # Пока возвращаем заглушку
            return {
                'enabled': False,
                'message': '',
                'enabled_at': None
            }
        except Exception as e:
            self.logger.error(f"Ошибка получения статуса режима обслуживания: {e}")
            return {'enabled': False}

    async def _enable_maintenance_mode(self, db_session, message: str):
        """Включение режима обслуживания"""
        try:
            # Здесь должна быть логика включения режима обслуживания
            self.logger.info(f"Режим обслуживания включен: {message}")
        except Exception as e:
            self.logger.error(f"Ошибка включения режима обслуживания: {e}")
            raise

    async def _disable_maintenance_mode(self, db_session):
        """Выключение режима обслуживания"""
        try:
            # Здесь должна быть логика выключения режима обслуживания
            self.logger.info("Режим обслуживания выключен")
        except Exception as e:
            self.logger.error(f"Ошибка выключения режима обслуживания: {e}")
            raise

    async def restore_backup(self, callback: CallbackQuery, **kwargs):
        """Восстановление резервной копии"""
        try:
            # Извлекаем ID резервной копии
            backup_id = callback.data.split('_', 2)[2]

            user_data = await self._get_user_data(callback)
            db_session = user_data['db_session']
            admin_user = user_data['db_user']

            # Показываем предупреждение
            text = f"⚠️ <b>Восстановление резервной копии</b>\n\n"
            text += f"🆔 <b>ID копии:</b> {backup_id}\n\n"
            text += "❗️ <b>ВНИМАНИЕ!</b>\n"
            text += "• Все текущие данные будут заменены\n"
            text += "• Процесс может занять несколько минут\n"
            text += "• Система будет недоступна во время восстановления\n\n"
            text += "Вы уверены, что хотите продолжить?"

            from ..keyboards import ConfirmRestoreKeyboard
            keyboard = await ConfirmRestoreKeyboard(backup_id).get_keyboard()

            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()

        except Exception as e:
            self.logger.error(f"Ошибка подготовки восстановления: {e}")
            await self._send_error(callback, _("Ошибка подготовки восстановления"))

    async def confirm_restore_backup(self, callback: CallbackQuery, **kwargs):
        """Подтверждение восстановления резервной копии"""
        try:
            # Извлекаем ID резервной копии
            backup_id = callback.data.split('_', 3)[3]

            user_data = await self._get_user_data(callback)
            db_session = user_data['db_session']
            admin_user = user_data['db_user']

            # Показываем процесс восстановления
            await callback.message.edit_text(
                f"🔄 <b>Восстановление резервной копии</b>\n\n"
                f"🆔 <b>ID копии:</b> {backup_id}\n\n"
                "⏳ Восстанавливаем данные...\n"
                "⚠️ Не закрывайте это окно!",
                reply_markup=None
            )

            # Здесь должна быть логика восстановления
            await asyncio.sleep(3)  # Имитация процесса

            # Логируем действие
            self.logger.info(f"Администратор {admin_user.telegram_id} восстановил резервную копию {backup_id}")

            text = f"✅ <b>Резервная копия восстановлена</b>\n\n"
            text += f"🆔 <b>ID копии:</b> {backup_id}\n"
            text += f"⏰ <b>Время восстановления:</b> {datetime.now().strftime('%d.%m.%Y %H:%M')}\n\n"
            text += "🔄 Система перезапускается..."

            await callback.message.edit_text(text, reply_markup=None)
            await callback.answer(_("Резервная копия восстановлена"))

        except Exception as e:
            self.logger.error(f"Ошибка восстановления резервной копии: {e}")
            await self._send_error(callback, _("Ошибка восстановления резервной копии"))

    async def restart_services(self, callback: CallbackQuery, **kwargs):
        """Перезапуск сервисов"""
        try:
            user_data = await self._get_user_data(callback)
            db_session = user_data['db_session']
            admin_user = user_data['db_user']

            # Показываем предупреждение
            text = f"🔄 <b>Перезапуск сервисов</b>\n\n"
            text += "⚠️ <b>ВНИМАНИЕ!</b>\n"
            text += "• Все сервисы будут перезапущены\n"
            text += "• Система будет недоступна 1-2 минуты\n"
            text += "• Активные подключения будут разорваны\n\n"
            text += "Вы уверены, что хотите продолжить?"

            from ..keyboards import ConfirmRestartKeyboard
            keyboard = await ConfirmRestartKeyboard().get_keyboard()

            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()

        except Exception as e:
            self.logger.error(f"Ошибка подготовки перезапуска: {e}")
            await self._send_error(callback, _("Ошибка подготовки перезапуска"))

    async def confirm_restart_services(self, callback: CallbackQuery, **kwargs):
        """Подтверждение перезапуска сервисов"""
        try:
            user_data = await self._get_user_data(callback)
            db_session = user_data['db_session']
            admin_user = user_data['db_user']

            # Показываем процесс перезапуска
            await callback.message.edit_text(
                f"🔄 <b>Перезапуск сервисов</b>\n\n"
                "⏳ Перезапускаем сервисы...\n"
                "⚠️ Не закрывайте это окно!",
                reply_markup=None
            )

            # Здесь должна быть логика перезапуска
            await asyncio.sleep(5)  # Имитация процесса

            # Логируем действие
            self.logger.info(f"Администратор {admin_user.telegram_id} перезапустил сервисы")

            text = f"✅ <b>Сервисы перезапущены</b>\n\n"
            text += f"⏰ <b>Время перезапуска:</b> {datetime.now().strftime('%d.%m.%Y %H:%M')}\n\n"
            text += "🟢 Все сервисы работают нормально"

            await callback.message.edit_text(text, reply_markup=None)
            await callback.answer(_("Сервисы перезапущены"))

        except Exception as e:
            self.logger.error(f"Ошибка перезапуска сервисов: {e}")
            await self._send_error(callback, _("Ошибка перезапуска сервисов"))
