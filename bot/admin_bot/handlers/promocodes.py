"""
UnveilVPN Shop - Admin Promocodes Handler
Обработчик управления промокодами для административного бота
"""

from typing import Dict, Any, List
from uuid import UUID
from decimal import Decimal
from datetime import datetime, timezone, timedelta

from aiogram.types import Message, CallbackQuery
from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import State, StatesGroup
from aiogram.utils.i18n import gettext as _

from ...common.handlers import BaseHandler
from ...common.utils import MessageFormatter, BotUtils
from ...common.exceptions import ValidationError, BusinessLogicError
from ...services.promocode_service import PromocodeService
from ..keyboards import PromocodeManagementKeyboard


class PromocodeStates(StatesGroup):
    """Состояния для работы с промокодами"""
    creating_promocode = State()
    editing_promocode = State()


class PromocodesHandler(BaseHandler):
    """
    Обработчик управления промокодами для админ бота
    """
    
    def __init__(self):
        super().__init__(name='AdminPromocodesHandler')
    
    def _register_handlers(self):
        """Регистрация обработчиков"""
        
        # Основные обработчики
        self.router.callback_query.register(
            self.show_promocodes_menu,
            lambda c: c.data == 'admin_promocodes'
        )
        
        self.router.callback_query.register(
            self.list_promocodes,
            lambda c: c.data == 'list_promocodes'
        )
        
        self.router.callback_query.register(
            self.create_promocode,
            lambda c: c.data == 'create_promocode'
        )
        
        self.router.callback_query.register(
            self.generate_promocode,
            lambda c: c.data == 'generate_promocode'
        )
        
        self.router.callback_query.register(
            self.show_promocode_stats,
            lambda c: c.data == 'promocode_stats'
        )
        
        # Просмотр конкретного промокода
        self.router.callback_query.register(
            self.view_promocode,
            lambda c: c.data.startswith('view_promocode_')
        )
        
        # Управление промокодом
        self.router.callback_query.register(
            self.edit_promocode,
            lambda c: c.data.startswith('edit_promocode_')
        )
        
        self.router.callback_query.register(
            self.delete_promocode,
            lambda c: c.data.startswith('delete_promocode_')
        )
        
        # Быстрые действия
        self.router.callback_query.register(
            self.quick_generate,
            lambda c: c.data.startswith('quick_gen_')
        )
    
    async def show_promocodes_menu(self, callback: CallbackQuery, **kwargs):
        """Показ меню управления промокодами"""
        try:
            user_data = await self._get_user_data(callback)
            db_session = user_data['db_session']
            
            # Получаем общую статистику
            stats = await self._get_promocodes_overview(db_session)
            
            text = await self._get_promocodes_menu_text(stats)
            keyboard = await PromocodeManagementKeyboard().get_keyboard(user_data)
            
            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Ошибка показа меню промокодов: {e}")
            await self._send_error(callback, _("Ошибка загрузки меню промокодов"))
    
    async def list_promocodes(self, callback: CallbackQuery, **kwargs):
        """Показ списка промокодов"""
        try:
            user_data = await self._get_user_data(callback)
            db_session = user_data['db_session']
            
            promocode_service = PromocodeService(db_session)
            promocodes = await promocode_service.get_all_promocodes(limit=20)
            
            text = await self._get_promocodes_list_text(promocodes)
            
            from ..keyboards import PromocodesListKeyboard
            keyboard = await PromocodesListKeyboard(promocodes).get_keyboard()
            
            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Ошибка показа списка промокодов: {e}")
            await self._send_error(callback, _("Ошибка загрузки списка промокодов"))
    
    async def create_promocode(self, callback: CallbackQuery, state: FSMContext, **kwargs):
        """Создание нового промокода"""
        try:
            await state.set_state(PromocodeStates.creating_promocode)
            
            text = _(
                "➕ <b>Создание промокода</b>\n\n"
                "Отправьте данные промокода в формате:\n"
                "<code>КОД|тип|значение|лимит|дни_действия</code>\n\n"
                "<b>Примеры:</b>\n"
                "• <code>SALE20|percent|20|100|30</code>\n"
                "• <code>FIXED50|fixed|50|50|7</code>\n"
                "• <code>BONUS7|days|7|10|14</code>\n\n"
                "<b>Типы скидок:</b>\n"
                "• percent - процентная скидка\n"
                "• fixed - фиксированная сумма\n"
                "• days - дополнительные дни"
            )
            
            from ..keyboards import CancelKeyboard
            keyboard = await CancelKeyboard().get_keyboard()
            
            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Ошибка создания промокода: {e}")
            await self._send_error(callback, _("Ошибка создания промокода"))
    
    async def generate_promocode(self, callback: CallbackQuery, **kwargs):
        """Генерация случайного промокода"""
        try:
            user_data = await self._get_user_data(callback)
            db_session = user_data['db_session']
            db_user = user_data['db_user']
            
            text = _(
                "🎲 <b>Генерация промокода</b>\n\n"
                "Выберите тип промокода для быстрой генерации:"
            )
            
            from ..keyboards import PromocodeGenerationKeyboard
            keyboard = await QuickGenerateKeyboard().get_keyboard()
            
            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Ошибка генерации промокода: {e}")
            await self._send_error(callback, _("Ошибка генерации промокода"))
    
    async def quick_generate(self, callback: CallbackQuery, **kwargs):
        """Быстрая генерация промокода"""
        try:
            # Извлекаем тип генерации
            gen_type = callback.data.split('_')[-1]
            
            user_data = await self._get_user_data(callback)
            db_session = user_data['db_session']
            db_user = user_data['db_user']
            
            promocode_service = PromocodeService(db_session)
            
            # Параметры по типу
            params = self._get_generation_params(gen_type)
            
            promocode = await promocode_service.generate_promocode(
                prefix=params['prefix'],
                discount_type=params['discount_type'],
                discount_value=Decimal(str(params['discount_value'])),
                expires_in_days=params['expires_in_days'],
                usage_limit=params['usage_limit'],
                created_by_id=db_user.id
            )
            
            await db_session.commit()
            
            text = _(
                "✅ <b>Промокод создан!</b>\n\n"
                "🎫 Код: <code>{code}</code>\n"
                "💰 Скидка: {discount}\n"
                "📊 Лимит: {limit}\n"
                "📅 Действует до: {expires}"
            ).format(
                code=promocode.code,
                discount=self._format_discount(promocode),
                limit=promocode.usage_limit or "∞",
                expires=promocode.expires_at.strftime("%d.%m.%Y") if promocode.expires_at else "Без ограничений"
            )
            
            from ..keyboards import PromocodeActionsKeyboard
            keyboard = await PromocodeActionsKeyboard(str(promocode.id)).get_keyboard()
            
            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Ошибка быстрой генерации: {e}")
            await self._send_error(callback, _("Ошибка генерации промокода"))
    
    async def view_promocode(self, callback: CallbackQuery, **kwargs):
        """Просмотр промокода"""
        try:
            # Извлекаем ID промокода
            promocode_id = UUID(callback.data.split('_')[-1])
            
            user_data = await self._get_user_data(callback)
            db_session = user_data['db_session']
            
            promocode_service = PromocodeService(db_session)
            promocode = await promocode_service.get_promocode_by_id(promocode_id)
            
            if not promocode:
                await callback.answer(_("Промокод не найден"), show_alert=True)
                return
            
            text = await self._get_promocode_details_text(promocode)
            
            from ..keyboards import PromocodeDetailsKeyboard
            keyboard = await PromocodeDetailsKeyboard(str(promocode.id)).get_keyboard()
            
            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Ошибка просмотра промокода: {e}")
            await self._send_error(callback, _("Ошибка загрузки промокода"))
    
    async def show_promocode_stats(self, callback: CallbackQuery, **kwargs):
        """Показ статистики промокодов"""
        try:
            user_data = await self._get_user_data(callback)
            db_session = user_data['db_session']
            
            promocode_service = PromocodeService(db_session)
            stats = await promocode_service.get_promocode_statistics()
            
            text = await self._get_promocode_stats_text(stats)
            
            from ..keyboards import PromocodeStatsKeyboard
            keyboard = await PromocodeStatsKeyboard().get_keyboard()
            
            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Ошибка показа статистики промокодов: {e}")
            await self._send_error(callback, _("Ошибка загрузки статистики"))
    
    # Вспомогательные методы
    
    async def _get_promocodes_overview(self, db_session) -> Dict[str, Any]:
        """Получение общей статистики промокодов"""
        try:
            promocode_service = PromocodeService(db_session)
            
            # Общее количество промокодов
            all_promocodes = await promocode_service.get_all_promocodes(limit=1000)
            total_promocodes = len(all_promocodes)
            
            # Активные промокоды
            active_promocodes = await promocode_service.get_all_promocodes(active_only=True, limit=1000)
            active_count = len(active_promocodes)
            
            # Публичные промокоды
            public_promocodes = await promocode_service.get_public_promocodes()
            public_count = len(public_promocodes)
            
            # Статистика использования
            stats = await promocode_service.get_promocode_statistics()
            
            return {
                'total_promocodes': total_promocodes,
                'active_count': active_count,
                'public_count': public_count,
                'total_usage': stats.get('total_usage', 0),
                'total_discount': stats.get('total_discount', 0)
            }
            
        except Exception as e:
            self.logger.error(f"Ошибка получения обзора промокодов: {e}")
            return {
                'total_promocodes': 0,
                'active_count': 0,
                'public_count': 0,
                'total_usage': 0,
                'total_discount': 0
            }
    
    def _get_generation_params(self, gen_type: str) -> Dict[str, Any]:
        """Получение параметров генерации по типу"""
        params = {
            'sale10': {
                'prefix': 'SALE',
                'discount_type': 'percent',
                'discount_value': 10,
                'expires_in_days': 30,
                'usage_limit': 100
            },
            'sale20': {
                'prefix': 'SALE',
                'discount_type': 'percent',
                'discount_value': 20,
                'expires_in_days': 14,
                'usage_limit': 50
            },
            'fixed50': {
                'prefix': 'SAVE',
                'discount_type': 'fixed',
                'discount_value': 50,
                'expires_in_days': 7,
                'usage_limit': 25
            },
            'bonus7': {
                'prefix': 'BONUS',
                'discount_type': 'days',
                'discount_value': 7,
                'expires_in_days': 30,
                'usage_limit': 100
            }
        }
        
        return params.get(gen_type, params['sale10'])
    
    async def _get_promocodes_menu_text(self, stats: Dict[str, Any]) -> str:
        """Формирование текста меню промокодов"""
        lines = [
            _("🎫 <b>Управление промокодами</b>"),
            "",
            _("📊 <b>Общая статистика:</b>"),
            _("• Всего промокодов: {total}").format(total=stats['total_promocodes']),
            _("• Активных: {active}").format(active=stats['active_count']),
            _("• Публичных: {public}").format(public=stats['public_count']),
            _("• Использований: {usage}").format(usage=stats['total_usage']),
            _("• Общая скидка: {discount:.2f} ₽").format(discount=stats['total_discount']),
            "",
            _("Выберите действие:")
        ]
        
        return "\n".join(lines)
    
    async def _get_promocodes_list_text(self, promocodes: List) -> str:
        """Формирование текста списка промокодов"""
        lines = [
            _("📋 <b>Список промокодов</b>"),
            ""
        ]
        
        if not promocodes:
            lines.append(_("Промокоды не найдены."))
            return "\n".join(lines)
        
        for promo in promocodes[:10]:  # Показываем первые 10
            status = "🟢" if promo.is_active else "🔴"
            public = "🌐" if promo.is_public else "🔒"
            
            lines.append(
                f"{status}{public} <code>{promo.code}</code> - {self._format_discount(promo)} "
                f"({promo.usage_count}/{promo.usage_limit or '∞'})"
            )
        
        if len(promocodes) > 10:
            lines.append(f"\n... и еще {len(promocodes) - 10} промокодов")
        
        return "\n".join(lines)
    
    async def _get_promocode_details_text(self, promocode) -> str:
        """Формирование текста деталей промокода"""
        lines = [
            _("🎫 <b>Промокод {code}</b>").format(code=promocode.code),
            ""
        ]
        
        # Основная информация
        lines.extend([
            _("📝 Название: {name}").format(name=promocode.name or "Без названия"),
            _("💰 Скидка: {discount}").format(discount=self._format_discount(promocode)),
            _("📊 Использований: {used}/{limit}").format(
                used=promocode.usage_count,
                limit=promocode.usage_limit or "∞"
            ),
            _("👤 Лимит на пользователя: {limit}").format(limit=promocode.usage_limit_per_user),
            ""
        ])
        
        # Статус
        status = "🟢 Активен" if promocode.is_active else "🔴 Неактивен"
        public = "🌐 Публичный" if promocode.is_public else "🔒 Приватный"
        lines.extend([
            f"📍 Статус: {status}",
            f"🔓 Доступ: {public}",
            ""
        ])
        
        # Даты
        if promocode.starts_at:
            lines.append(_("📅 Начало: {date}").format(
                date=promocode.starts_at.strftime("%d.%m.%Y %H:%M")
            ))
        
        if promocode.expires_at:
            lines.append(_("📅 Окончание: {date}").format(
                date=promocode.expires_at.strftime("%d.%m.%Y %H:%M")
            ))
        
        # Ограничения
        if promocode.min_purchase_amount:
            lines.append(_("💳 Мин. сумма: {amount} ₽").format(
                amount=promocode.min_purchase_amount
            ))
        
        if promocode.applicable_tariffs:
            lines.append(_("🎯 Применим к тарифам: {count}").format(
                count=len(promocode.applicable_tariffs)
            ))
        
        # Описание
        if promocode.description:
            lines.extend([
                "",
                _("ℹ️ Описание:"),
                promocode.description
            ])
        
        return "\n".join(lines)
    
    async def _get_promocode_stats_text(self, stats: Dict[str, Any]) -> str:
        """Формирование текста статистики промокодов"""
        lines = [
            _("📈 <b>Статистика промокодов</b>"),
            "",
            _("📊 <b>Общие показатели:</b>"),
            _("• Всего использований: {usage}").format(usage=stats.get('total_usage', 0)),
            _("• Общая скидка: {discount:.2f} ₽").format(discount=stats.get('total_discount', 0)),
            _("• Средняя скидка: {avg:.1f}%").format(avg=stats.get('discount_percentage', 0)),
            ""
        ]
        
        # Топ промокодов
        breakdown = stats.get('promocode_breakdown', {})
        if breakdown:
            lines.append(_("🏆 <b>Топ промокодов:</b>"))
            
            # Сортируем по использованию
            sorted_promos = sorted(
                breakdown.items(),
                key=lambda x: x[1]['usage_count'],
                reverse=True
            )
            
            for code, promo_stats in sorted_promos[:5]:
                lines.append(
                    f"• {code}: {promo_stats['usage_count']} исп., {promo_stats['total_discount']:.0f} ₽"
                )
        
        return "\n".join(lines)
    
    def _format_discount(self, promocode) -> str:
        """Форматирование скидки для отображения"""
        if promocode.discount_type == 'percent':
            return f"{int(promocode.discount_value)}%"
        elif promocode.discount_type == 'fixed':
            return f"{int(promocode.discount_value)} ₽"
        elif promocode.discount_type == 'days':
            return f"+{int(promocode.discount_value)} дней"
        else:
            return "Скидка"

    async def edit_promocode(self, callback: CallbackQuery, state: FSMContext, **kwargs):
        """Редактирование промокода"""
        try:
            # Извлекаем ID промокода
            promocode_id = UUID(callback.data.split('_')[-1])

            user_data = await self._get_user_data(callback)
            db_session = user_data['db_session']

            promocode_service = PromocodeService(db_session)
            promocode = await promocode_service.get_promocode_by_id(promocode_id)

            if not promocode:
                await self._send_error(callback, _("Промокод не найден"))
                return

            # Сохраняем ID промокода в состоянии
            await state.update_data(editing_promocode_id=str(promocode_id))
            await state.set_state(PromocodeStates.editing_promocode)

            text = f"✏️ <b>Редактирование промокода: {promocode.code}</b>\n\n"
            text += "Выберите, что хотите изменить:"

            from ..keyboards import PromocodeEditKeyboard
            keyboard = await PromocodeEditKeyboard(promocode).get_keyboard()

            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()

        except Exception as e:
            self.logger.error(f"Ошибка редактирования промокода: {e}")
            await self._send_error(callback, _("Ошибка редактирования промокода"))

    async def delete_promocode(self, callback: CallbackQuery, **kwargs):
        """Удаление промокода"""
        try:
            # Извлекаем ID промокода
            promocode_id = UUID(callback.data.split('_')[-1])

            user_data = await self._get_user_data(callback)
            db_session = user_data['db_session']

            promocode_service = PromocodeService(db_session)
            promocode = await promocode_service.get_promocode_by_id(promocode_id)

            if not promocode:
                await self._send_error(callback, _("Промокод не найден"))
                return

            # Проверяем, есть ли активные использования
            # (здесь можно добавить проверку)

            # Удаляем промокод
            await promocode_service.delete_promocode(promocode_id)

            text = f"🗑 <b>Промокод '{promocode.code}' удален</b>\n\n"
            text += "Возвращаемся к списку промокодов..."

            # Возвращаемся к списку промокодов
            await self.list_promocodes(callback)
            await callback.answer(_("Промокод успешно удален"))

        except Exception as e:
            self.logger.error(f"Ошибка удаления промокода: {e}")
            await self._send_error(callback, _("Ошибка удаления промокода"))
