"""
UnveilVPN Shop - Admin Bot Main
Точка входа для административного бота
"""

import asyncio
import logging
import sys
from pathlib import Path

# Добавляем путь к корню проекта
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

from bot.common.config import init_config
from bot.admin_bot.app import AdminBotApplication


async def main():
    """Основная функция запуска административного бота"""
    try:
        # Инициализируем конфигурацию
        config = init_config('admin')
        
        # Настраиваем логирование в stdout
        logging.basicConfig(
            level=config.log_level,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            stream=sys.stdout
        )
        
        # Проверяем наличие администраторов
        if not config.admin_user_ids:
            logging.error("❌ Не настроены ID администраторов! Установите ADMIN_USER_IDS в переменных окружения.")
            return 1
        
        # Создаем и запускаем приложение
        app = AdminBotApplication(config)
        await app.run()
        
    except KeyboardInterrupt:
        logging.info("Получен сигнал остановки")
    except Exception as e:
        logging.error(f"Критическая ошибка: {e}", exc_info=True)
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
