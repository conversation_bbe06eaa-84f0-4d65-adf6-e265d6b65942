"""
UnveilVPN Shop - Admin Bot Application
Основное приложение административного бота
"""

import logging
from typing import List

from ..common.base import BaseBotApplication
from ..common.config import BotConfig
from ..common.middleware import AuthMiddleware, DatabaseMiddleware
from .handlers import (
    StartHandler,
    Menu<PERSON>andler,
    UsersHandler,
    TariffsHandler,
    PromocodesHandler,
    AnalyticsHandler,
    SystemHandler,
    ReferralsHandler,
    AdminMainHandler
)


class AdminBotApplication(BaseBotApplication):
    """
    Приложение административного бота
    """
    
    def __init__(self, config: BotConfig):
        super().__init__(config)
        self.logger = logging.getLogger("AdminBot")
    
    def get_bot_type(self) -> str:
        """Возвращает тип бота"""
        return "admin"
    
    def get_bot_specific_middlewares(self) -> List:
        """Возвращает специфичные для админ бота middleware"""
        return [
            DatabaseMiddleware(),  # Инициализация сессии базы данных
            AuthMiddleware('admin')  # Проверка прав администратора
        ]
    
    def get_handlers(self) -> List:
        """Возвращает список обработчиков для админ бота"""
        return [
            StartHandler(),
            MenuHandler(),
            UsersHandler(),
            TariffsHandler(),
            PromocodesHandler(),
            AnalyticsHandler(),
            SystemHandler(),
            ReferralsHandler(),
            AdminMainHandler()
        ]
    
    def get_webhook_routes(self) -> List[tuple]:
        """Возвращает webhook маршруты для админ бота"""
        return [
            # API endpoints для административных функций
            ("GET", "/admin/stats", self._handle_stats_api),
            ("GET", "/admin/users", self._handle_users_api),
            ("GET", "/admin/payments", self._handle_payments_api),
            ("POST", "/admin/broadcast", self._handle_broadcast_api),
        ]
    
    async def on_startup(self):
        """Действия при запуске админ бота"""
        self.logger.info("🚀 Административный бот запущен")
        
        # Проверяем права администраторов
        await self._check_admin_permissions()
        
        # Инициализируем системные компоненты
        await self._initialize_admin_components()
    
    async def on_shutdown(self):
        """Действия при остановке админ бота"""
        self.logger.info("🛑 Административный бот остановлен")
    
    async def _check_admin_permissions(self):
        """Проверка прав администраторов"""
        if not self.config.admin_user_ids:
            self.logger.warning("⚠️ Не настроены ID администраторов!")
            return
        
        self.logger.info(f"✅ Настроено {len(self.config.admin_user_ids)} администраторов")
        for admin_id in self.config.admin_user_ids:
            self.logger.info(f"  - Администратор: {admin_id}")
    
    async def _initialize_admin_components(self):
        """Инициализация административных компонентов"""
        try:
            # Здесь можно добавить инициализацию специфичных для админа компонентов
            # Например, подключение к системам мониторинга, настройка уведомлений и т.д.
            
            self.logger.info("✅ Административные компоненты инициализированы")
            
        except Exception as e:
            self.logger.error(f"❌ Ошибка инициализации административных компонентов: {e}")
    
    async def _handle_stats_api(self, request):
        """API endpoint для получения статистики"""
        try:
            from aiohttp import web
            
            # Здесь будет логика получения статистики
            stats = await self._get_system_stats()
            
            return web.json_response({
                "success": True,
                "data": stats
            })
            
        except Exception as e:
            self.logger.error(f"Ошибка API статистики: {e}")
            return web.json_response({
                "success": False,
                "error": str(e)
            }, status=500)
    
    async def _handle_users_api(self, request):
        """API endpoint для получения списка пользователей"""
        try:
            from aiohttp import web
            
            # Здесь будет логика получения пользователей
            users = await self._get_users_list()
            
            return web.json_response({
                "success": True,
                "data": users
            })
            
        except Exception as e:
            self.logger.error(f"Ошибка API пользователей: {e}")
            return web.json_response({
                "success": False,
                "error": str(e)
            }, status=500)
    
    async def _handle_payments_api(self, request):
        """API endpoint для получения платежей"""
        try:
            from aiohttp import web
            
            # Здесь будет логика получения платежей
            payments = await self._get_payments_list()
            
            return web.json_response({
                "success": True,
                "data": payments
            })
            
        except Exception as e:
            self.logger.error(f"Ошибка API платежей: {e}")
            return web.json_response({
                "success": False,
                "error": str(e)
            }, status=500)
    
    async def _handle_broadcast_api(self, request):
        """API endpoint для массовой рассылки"""
        try:
            from aiohttp import web
            
            data = await request.json()
            message = data.get('message')
            target_users = data.get('target_users', 'all')
            
            if not message:
                return web.json_response({
                    "success": False,
                    "error": "Сообщение не может быть пустым"
                }, status=400)
            
            # Здесь будет логика массовой рассылки
            result = await self._send_broadcast(message, target_users)
            
            return web.json_response({
                "success": True,
                "data": result
            })
            
        except Exception as e:
            self.logger.error(f"Ошибка API рассылки: {e}")
            return web.json_response({
                "success": False,
                "error": str(e)
            }, status=500)
    
    async def _get_system_stats(self):
        """Получение системной статистики"""
        async with self.db_manager.get_session() as session:
            user_service = UserService(session)
            payment_service = PaymentService(session)
            
            users_total = await user_service.get_users_count()
            users_active = await user_service.get_active_users_count(days=30)
            
            payments_today = await payment_service.get_payments_count_for_today()
            revenue_today = await payment_service.get_revenue_for_today()
            
            return {
                "users_total": users_total,
                "users_active": users_active,
                "payments_today": payments_today,
                "revenue_today": float(revenue_today)
            }
    
    async def _get_users_list(self):
        """Получение списка пользователей"""
        async with self.db_manager.get_session() as session:
            user_service = UserService(session)
            users = await user_service.get_all_users()
            return [user.to_dict() for user in users]

    async def _get_payments_list(self):
        """Получение списка платежей"""
        async with self.db_manager.get_session() as session:
            payment_service = PaymentService(session)
            payments = await payment_service.get_all_payments()
            return [payment.to_dict() for payment in payments]
    
    async def _send_broadcast(self, message: str, target_users: str):
        """Отправка массовой рассылки"""
        sent_count = 0
        failed_count = 0
        async with self.db_manager.get_session() as session:
            user_service = UserService(session)
            if target_users == 'all':
                users = await user_service.get_all_users()
            elif target_users == 'active':
                users = await user_service.get_users_with_active_subscriptions()
            else:
                users = []

            for user in users:
                try:
                    await self.bot.send_message(user.telegram_id, message)
                    sent_count += 1
                except Exception as e:
                    self.logger.warning(f"Failed to send message to {user.telegram_id}: {e}")
                    failed_count += 1
        return {
            "sent": sent_count,
            "failed": failed_count,
            "message": message
        }
