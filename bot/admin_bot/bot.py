"""
UnveilVPN Shop - Admin Bot Class
Класс админ бота
"""

import asyncio
import logging
from typing import Dict, Any

from aiogram import <PERSON><PERSON>, Dispatcher
from aiogram.fsm.storage.memory import MemoryStorage

from ..common.config import Config
from ..common.database import DatabaseManager
from ..common.middleware import DatabaseMiddleware, UserMiddleware
from .handlers.main import AdminMainHandler
from .handlers.users import UsersHandler
from .handlers.tariffs import TariffsHandler
from .handlers.promocodes import PromocodesHandler
from .handlers.analytics import AnalyticsHandler
from .handlers.system import SystemHandler


class AdminBot:
    """
    Админ бот для управления системой UnveilVPN Shop
    """
    
    def __init__(self, config: Config):
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # Инициализация бота
        self.bot = Bot(token=config.admin_bot_token)
        
        # Инициализация диспетчера
        storage = MemoryStorage()
        self.dp = Dispatcher(storage=storage)
        
        # Инициализация базы данных
        self.db_manager = DatabaseManager(config.database_url)
        
        # Регистрация middleware
        self._register_middleware()
        
        # Регистрация обработчиков
        self._register_handlers()
    
    def _register_middleware(self):
        """Регистрация middleware"""
        # Middleware для работы с базой данных
        self.dp.message.middleware(DatabaseMiddleware(self.db_manager))
        self.dp.callback_query.middleware(DatabaseMiddleware(self.db_manager))
        
        # Middleware для работы с пользователями
        self.dp.message.middleware(UserMiddleware())
        self.dp.callback_query.middleware(UserMiddleware())
    
    def _register_handlers(self):
        """Регистрация обработчиков"""
        try:
            # Главный обработчик
            main_handler = AdminMainHandler()
            main_handler.register(self.dp)
            
            # Обработчик пользователей
            users_handler = UsersHandler()
            users_handler.register(self.dp)
            
            # Обработчик тарифов
            tariffs_handler = TariffsHandler()
            tariffs_handler.register(self.dp)
            
            # Обработчик промокодов
            promocodes_handler = PromocodesHandler()
            promocodes_handler.register(self.dp)
            
            # Обработчик аналитики
            analytics_handler = AnalyticsHandler()
            analytics_handler.register(self.dp)
            
            # Обработчик системы
            system_handler = SystemHandler()
            system_handler.register(self.dp)
            
            self.logger.info("✅ Все обработчики админ бота зарегистрированы")
            
        except Exception as e:
            self.logger.error(f"❌ Ошибка регистрации обработчиков: {e}")
            raise
    
    async def start(self):
        """Запуск админ бота"""
        try:
            self.logger.info("🚀 Запуск админ бота...")
            
            # Инициализация базы данных
            await self.db_manager.init_db()
            
            # Установка команд бота
            await self._set_bot_commands()
            
            # Уведомление администраторов о запуске
            await self._notify_admins_startup()
            
            # Запуск polling
            await self.dp.start_polling(self.bot)
            
        except Exception as e:
            self.logger.error(f"❌ Ошибка запуска админ бота: {e}")
            raise
        finally:
            await self.stop()
    
    async def stop(self):
        """Остановка админ бота"""
        try:
            self.logger.info("🛑 Остановка админ бота...")
            
            # Уведомление администраторов об остановке
            await self._notify_admins_shutdown()
            
            # Закрытие соединения с базой данных
            await self.db_manager.close()
            
            # Закрытие сессии бота
            await self.bot.session.close()
            
            self.logger.info("✅ Админ бот остановлен")
            
        except Exception as e:
            self.logger.error(f"❌ Ошибка остановки админ бота: {e}")
    
    async def _set_bot_commands(self):
        """Установка команд бота"""
        try:
            from aiogram.types import BotCommand
            
            commands = [
                BotCommand(command="start", description="🏠 Главное меню"),
                BotCommand(command="stats", description="📊 Быстрая статистика"),
                BotCommand(command="users", description="👥 Управление пользователями"),
                BotCommand(command="system", description="⚙️ Системная информация"),
                BotCommand(command="backup", description="💾 Создать резервную копию"),
                BotCommand(command="help", description="❓ Помощь")
            ]
            
            await self.bot.set_my_commands(commands)
            self.logger.info("✅ Команды админ бота установлены")
            
        except Exception as e:
            self.logger.error(f"❌ Ошибка установки команд: {e}")
    
    async def _notify_admins_startup(self):
        """Уведомление администраторов о запуске"""
        try:
            startup_message = (
                "🚀 <b>Админ бот запущен</b>\n\n"
                "✅ Все системы готовы к работе\n"
                "📊 Доступна полная функциональность\n\n"
                "Используйте /start для доступа к панели управления"
            )
            
            for admin_id in self.config.admin_telegram_ids:
                try:
                    await self.bot.send_message(
                        chat_id=admin_id,
                        text=startup_message,
                        parse_mode='HTML'
                    )
                except Exception as e:
                    self.logger.warning(f"Не удалось уведомить админа {admin_id}: {e}")
            
            self.logger.info("✅ Администраторы уведомлены о запуске")
            
        except Exception as e:
            self.logger.error(f"❌ Ошибка уведомления о запуске: {e}")
    
    async def _notify_admins_shutdown(self):
        """Уведомление администраторов об остановке"""
        try:
            shutdown_message = (
                "🛑 <b>Админ бот остановлен</b>\n\n"
                "⚠️ Панель управления недоступна\n"
                "🔧 Выполняется техническое обслуживание"
            )
            
            for admin_id in self.config.admin_telegram_ids:
                try:
                    await self.bot.send_message(
                        chat_id=admin_id,
                        text=shutdown_message,
                        parse_mode='HTML'
                    )
                except Exception as e:
                    self.logger.warning(f"Не удалось уведомить админа {admin_id}: {e}")
            
            self.logger.info("✅ Администраторы уведомлены об остановке")
            
        except Exception as e:
            self.logger.error(f"❌ Ошибка уведомления об остановке: {e}")
    
    async def send_admin_notification(self, message: str, admin_ids: list = None):
        """Отправка уведомления администраторам"""
        try:
            target_admins = admin_ids or self.config.admin_telegram_ids
            
            sent_count = 0
            for admin_id in target_admins:
                try:
                    await self.bot.send_message(
                        chat_id=admin_id,
                        text=message,
                        parse_mode='HTML'
                    )
                    sent_count += 1
                except Exception as e:
                    self.logger.warning(f"Не удалось отправить уведомление админу {admin_id}: {e}")
            
            self.logger.info(f"✅ Уведомление отправлено {sent_count} администраторам")
            return sent_count
            
        except Exception as e:
            self.logger.error(f"❌ Ошибка отправки уведомления: {e}")
            return 0
    
    def get_bot_info(self) -> Dict[str, Any]:
        """Получение информации о боте"""
        return {
            'bot_id': self.bot.id if hasattr(self.bot, 'id') else None,
            'username': getattr(self.bot, 'username', None),
            'handlers_count': len(self.dp.message.handlers) + len(self.dp.callback_query.handlers),
            'middleware_count': len(self.dp.message.middleware) + len(self.dp.callback_query.middleware),
            'admin_count': len(self.config.admin_telegram_ids),
            'database_url': self.config.database_url.split('@')[-1] if '@' in self.config.database_url else 'local'
        }


async def create_admin_bot(config: Config) -> AdminBot:
    """Создание экземпляра админ бота"""
    try:
        admin_bot = AdminBot(config)
        return admin_bot
    except Exception as e:
        logging.error(f"❌ Ошибка создания админ бота: {e}")
        raise


async def run_admin_bot(config: Config):
    """Запуск админ бота"""
    admin_bot = None
    try:
        # Создаем админ бота
        admin_bot = await create_admin_bot(config)
        
        # Запускаем бота
        await admin_bot.start()
        
    except KeyboardInterrupt:
        logging.info("🛑 Получен сигнал остановки")
    except Exception as e:
        logging.error(f"❌ Критическая ошибка админ бота: {e}")
        raise
    finally:
        if admin_bot:
            await admin_bot.stop()


if __name__ == "__main__":
    # Настройка логирования
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Загрузка конфигурации
    from ..common.config import load_config
    config = load_config()
    
    # Запуск админ бота
    asyncio.run(run_admin_bot(config))
