"""
UnveilVPN Shop - Tariffs Management Keyboard
Клавиатуры для управления тарифами в админ боте
"""

from typing import Dict, Any, List

from aiogram.types import InlineKeyboardMarkup
from aiogram.utils.i18n import gettext as _

from ...common.keyboards import InlineKeyboard


class TariffsManagementKeyboard(InlineKeyboard):
    """
    Клавиатура управления тарифами
    """
    
    def __init__(self):
        super().__init__(max_width=2)
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры управления тарифами"""
        
        self.add_button(_("📋 Список тарифов"), "tariffs_list")
        self.add_button(_("➕ Создать тариф"), "tariffs_create")
        self.add_main_menu_button()
        
        return self.build()


class TariffListKeyboard(InlineKeyboard):
    """Клавиатура списка тарифов"""
    
    def __init__(self, tariffs: List):
        super().__init__(max_width=1)
        self.tariffs = tariffs
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры списка тарифов"""
        
        for tariff in self.tariffs:
            status_icon = "🟢" if tariff.is_active else "🔴"
            popular_icon = "⭐" if tariff.is_popular else ""
            
            button_text = f"{status_icon} {popular_icon}{tariff.name}"
            self.add_button(button_text, f"tariff_view_{tariff.id}")
        
        self.add_button(_("⬅️ Назад"), "admin_tariffs")
        
        return self.build()


class TariffActionsKeyboard(InlineKeyboard):
    """Клавиатура действий с тарифом"""
    
    def __init__(self, tariff_id: str, is_popular: bool):
        super().__init__(max_width=2)
        self.tariff_id = tariff_id
        self.is_popular = is_popular
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры действий с тарифом"""
        
        self.add_button(_("📝 Название"), f"edit_tariff_name_{self.tariff_id}")
        self.add_button(_("📄 Описание"), f"edit_tariff_description_{self.tariff_id}")
        self.add_button(_("⏱️ Продолжительность"), f"edit_tariff_duration_{self.tariff_id}")
        self.add_button(_("💰 Цены"), f"edit_tariff_prices_{self.tariff_id}")
        
        popular_text = _("☆ Убрать из популярных") if self.is_popular else _("⭐ Сделать популярным")
        self.add_button(popular_text, f"toggle_popular_{self.tariff_id}")
        
        self.add_button(_("📊 Статистика"), f"tariff_stats_{self.tariff_id}")
        
        self.add_button(_("⬅️ Назад"), "tariffs_list")
        
        return self.build()


class TariffPricesKeyboard(InlineKeyboard):
    """Клавиатура редактирования цен тарифа"""
    
    def __init__(self, tariff_id: str):
        super().__init__(max_width=1)
        self.tariff_id = tariff_id
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры редактирования цен"""
        
        self.add_button(_("₽ Цена в рублях"), f"edit_price_rub_{self.tariff_id}")
        self.add_button(_("$ Цена в долларах"), f"edit_price_usd_{self.tariff_id}")
        self.add_button(_("⭐ Цена в Stars"), f"edit_price_stars_{self.tariff_id}")
        
        self.add_button(_("⬅️ Назад"), f"tariff_view_{self.tariff_id}")
        
        return self.build()
