"""
UnveilVPN Shop - <PERSON><PERSON>t Analytics Keyboards
Клавиатуры для аналитики и отчетов в админ боте
"""

from typing import Dict, Any, List, Optional

from aiogram.types import InlineKeyboardMarkup
from aiogram.utils.i18n import gettext as _

from ...common.keyboards import InlineKeyboard


class AnalyticsMenuKeyboard(InlineKeyboard):
    """
    Клавиатура главного меню аналитики
    """
    
    def __init__(self, overview: Dict[str, Any] = None):
        super().__init__(max_width=2)
        self.overview = overview or {}
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры меню аналитики"""
        
        # Основные разделы аналитики
        self.add_button(_("📊 Дашборд"), "dashboard")
        self.add_button(_("💰 Доходы"), "revenue_analytics")
        
        self.add_button(_("👥 Пользователи"), "user_analytics")
        self.add_button(_("📡 Подписки"), "subscription_analytics")
        
        self.add_button(_("👥 Рефералы"), "referral_analytics")
        self.add_button(_("💳 Платежи"), "payment_analytics")
        
        self.add_button(_("🎫 Промокоды"), "promocode_analytics")
        self.add_button(_("📈 Тренды"), "trends_analytics")
        
        # Отчеты
        self.add_button(_("📋 Отчеты"), "reports_menu")
        self.add_button(_("📤 Экспорт"), "export_menu")
        
        # Возврат в главное меню
        self.add_button(_("🏠 Главное меню"), "main_menu")
        
        return self.build()


class AnalyticsReportsKeyboard(InlineKeyboard):
    """Клавиатура отчетов"""
    
    def __init__(self, analytics_type: str = "general"):
        super().__init__(max_width=2)
        self.analytics_type = analytics_type
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры отчетов"""
        
        # Периоды отчетов
        self.add_button(_("📅 Сегодня"), f"report_daily")
        self.add_button(_("📅 Неделя"), f"report_weekly")
        
        self.add_button(_("📅 Месяц"), f"report_monthly")
        self.add_button(_("📅 Квартал"), f"report_quarterly")
        
        # Специальные отчеты
        if self.analytics_type == "revenue":
            self.add_button(_("💰 По тарифам"), f"report_revenue_by_tariff")
            self.add_button(_("💳 По способам оплаты"), f"report_revenue_by_method")
        elif self.analytics_type == "users":
            self.add_button(_("👥 Регистрации"), f"report_user_registrations")
            self.add_button(_("🔄 Активность"), f"report_user_activity")
        elif self.analytics_type == "subscriptions":
            self.add_button(_("📡 Новые подписки"), f"report_new_subscriptions")
            self.add_button(_("⏰ Продления"), f"report_renewals")
        
        # Экспорт
        self.add_button(_("📤 Экспорт данных"), f"export_{self.analytics_type}")
        
        # Возврат
        self.add_button(_("⬅️ К аналитике"), "analytics")
        
        return self.build()


class AnalyticsChartsKeyboard(InlineKeyboard):
    """Клавиатура графиков и диаграмм"""
    
    def __init__(self):
        super().__init__(max_width=2)
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры графиков"""
        
        # Типы графиков
        self.add_button(_("📈 Линейный график"), "chart_line")
        self.add_button(_("📊 Столбчатая диаграмма"), "chart_bar")
        
        self.add_button(_("🥧 Круговая диаграмма"), "chart_pie")
        self.add_button(_("📉 График тренда"), "chart_trend")
        
        # Периоды
        self.add_button(_("📅 За день"), "chart_period_day")
        self.add_button(_("📅 За неделю"), "chart_period_week")
        
        self.add_button(_("📅 За месяц"), "chart_period_month")
        self.add_button(_("📅 За год"), "chart_period_year")
        
        # Возврат
        self.add_button(_("⬅️ К аналитике"), "analytics")
        
        return self.build()


class AnalyticsExportKeyboard(InlineKeyboard):
    """Клавиатура экспорта данных"""
    
    def __init__(self, data_type: str = "general"):
        super().__init__(max_width=2)
        self.data_type = data_type
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры экспорта"""
        
        # Форматы экспорта
        self.add_button(_("📄 CSV"), f"export_csv_{self.data_type}")
        self.add_button(_("📊 Excel"), f"export_excel_{self.data_type}")
        
        self.add_button(_("📋 JSON"), f"export_json_{self.data_type}")
        self.add_button(_("📄 PDF отчет"), f"export_pdf_{self.data_type}")
        
        # Периоды экспорта
        self.add_button(_("📅 За сегодня"), f"export_period_today_{self.data_type}")
        self.add_button(_("📅 За неделю"), f"export_period_week_{self.data_type}")
        
        self.add_button(_("📅 За месяц"), f"export_period_month_{self.data_type}")
        self.add_button(_("📅 Весь период"), f"export_period_all_{self.data_type}")
        
        # Возврат
        self.add_button(_("⬅️ К отчетам"), f"report_{self.data_type}")
        
        return self.build()


class AnalyticsPeriodKeyboard(InlineKeyboard):
    """Клавиатура выбора периода"""
    
    def __init__(self):
        super().__init__(max_width=2)
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры периодов"""
        
        # Быстрые периоды
        self.add_button(_("📅 Сегодня"), "period_today")
        self.add_button(_("📅 Вчера"), "period_yesterday")
        
        self.add_button(_("📅 Эта неделя"), "period_this_week")
        self.add_button(_("📅 Прошлая неделя"), "period_last_week")
        
        self.add_button(_("📅 Этот месяц"), "period_this_month")
        self.add_button(_("📅 Прошлый месяц"), "period_last_month")
        
        self.add_button(_("📅 Этот квартал"), "period_this_quarter")
        self.add_button(_("📅 Этот год"), "period_this_year")
        
        # Пользовательский период
        self.add_button(_("📅 Выбрать период"), "period_custom")
        
        # Возврат
        self.add_button(_("⬅️ К аналитике"), "analytics")
        
        return self.build()


class DashboardKeyboard(InlineKeyboard):
    """Клавиатура дашборда"""
    
    def __init__(self):
        super().__init__(max_width=2)
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры дашборда"""
        
        # Быстрые действия
        self.add_button(_("🔄 Обновить"), "dashboard")
        self.add_button(_("📊 Детальная аналитика"), "detailed_analytics")
        
        # Периоды
        self.add_button(_("📅 Сегодня"), "dashboard_today")
        self.add_button(_("📅 Неделя"), "dashboard_week")
        
        self.add_button(_("📅 Месяц"), "dashboard_month")
        self.add_button(_("📅 Квартал"), "dashboard_quarter")
        
        # Экспорт дашборда
        self.add_button(_("📤 Экспорт дашборда"), "export_dashboard")
        
        # Настройки
        self.add_button(_("⚙️ Настройки дашборда"), "dashboard_settings")
        
        # Возврат
        self.add_button(_("⬅️ К аналитике"), "analytics")
        
        return self.build()


class RevenueAnalyticsKeyboard(InlineKeyboard):
    """Клавиатура аналитики доходов"""
    
    def __init__(self):
        super().__init__(max_width=2)
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры аналитики доходов"""
        
        # Детализация доходов
        self.add_button(_("💰 По тарифам"), "revenue_by_tariffs")
        self.add_button(_("💳 По способам оплаты"), "revenue_by_methods")
        
        self.add_button(_("📅 По дням"), "revenue_by_days")
        self.add_button(_("👥 По пользователям"), "revenue_by_users")
        
        # Прогнозы
        self.add_button(_("📈 Прогноз доходов"), "revenue_forecast")
        self.add_button(_("📊 Тренды"), "revenue_trends")
        
        # Сравнения
        self.add_button(_("⚖️ Сравнить периоды"), "revenue_compare")
        self.add_button(_("📈 Рост доходов"), "revenue_growth")
        
        # Экспорт
        self.add_button(_("📤 Экспорт"), "export_revenue")
        
        # Возврат
        self.add_button(_("⬅️ К аналитике"), "analytics")
        
        return self.build()


class UserAnalyticsKeyboard(InlineKeyboard):
    """Клавиатура аналитики пользователей"""
    
    def __init__(self):
        super().__init__(max_width=2)
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры аналитики пользователей"""
        
        # Детализация пользователей
        self.add_button(_("👥 Регистрации"), "users_registrations")
        self.add_button(_("🔥 Активность"), "users_activity")
        
        self.add_button(_("💰 Конверсия"), "users_conversion")
        self.add_button(_("📊 Сегментация"), "users_segmentation")
        
        # Поведение
        self.add_button(_("🛒 Воронка продаж"), "users_funnel")
        self.add_button(_("🔄 Retention"), "users_retention")
        
        # География
        self.add_button(_("🌍 География"), "users_geography")
        self.add_button(_("📱 Устройства"), "users_devices")
        
        # Экспорт
        self.add_button(_("📤 Экспорт"), "export_users")
        
        # Возврат
        self.add_button(_("⬅️ К аналитике"), "analytics")
        
        return self.build()


class SubscriptionAnalyticsKeyboard(InlineKeyboard):
    """Клавиатура аналитики подписок"""
    
    def __init__(self):
        super().__init__(max_width=2)
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры аналитики подписок"""
        
        # Детализация подписок
        self.add_button(_("📡 Новые подписки"), "subscriptions_new")
        self.add_button(_("🔄 Продления"), "subscriptions_renewals")
        
        self.add_button(_("⏰ Истечения"), "subscriptions_expirations")
        self.add_button(_("❌ Отмены"), "subscriptions_cancellations")
        
        # Метрики
        self.add_button(_("📊 Churn Rate"), "subscriptions_churn")
        self.add_button(_("💰 LTV"), "subscriptions_ltv")
        
        self.add_button(_("📈 MRR"), "subscriptions_mrr")
        self.add_button(_("📉 Churn Analysis"), "subscriptions_churn_analysis")
        
        # Экспорт
        self.add_button(_("📤 Экспорт"), "export_subscriptions")
        
        # Возврат
        self.add_button(_("⬅️ К аналитике"), "analytics")
        
        return self.build()


class TrendsAnalyticsKeyboard(InlineKeyboard):
    """Клавиатура анализа трендов"""
    
    def __init__(self):
        super().__init__(max_width=2)
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры анализа трендов"""
        
        # Типы трендов
        self.add_button(_("📈 Рост доходов"), "trend_revenue_growth")
        self.add_button(_("👥 Рост пользователей"), "trend_user_growth")
        
        self.add_button(_("📡 Рост подписок"), "trend_subscription_growth")
        self.add_button(_("💳 Конверсия платежей"), "trend_payment_conversion")
        
        # Прогнозы
        self.add_button(_("🔮 Прогноз доходов"), "forecast_revenue")
        self.add_button(_("🔮 Прогноз пользователей"), "forecast_users")
        
        # Сезонность
        self.add_button(_("🌡 Сезонные тренды"), "seasonal_trends")
        self.add_button(_("📊 Цикличность"), "cyclical_analysis")
        
        # Экспорт
        self.add_button(_("📤 Экспорт трендов"), "export_trends")
        
        # Возврат
        self.add_button(_("⬅️ К аналитике"), "analytics")
        
        return self.build()


class AnalyticsSettingsKeyboard(InlineKeyboard):
    """Клавиатура настроек аналитики"""
    
    def __init__(self):
        super().__init__(max_width=1)
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры настроек аналитики"""
        
        # Настройки отображения
        self.add_button(_("🎨 Настройки дашборда"), "settings_dashboard")
        self.add_button(_("📊 Настройки графиков"), "settings_charts")
        
        # Уведомления
        self.add_button(_("🔔 Уведомления"), "settings_notifications")
        self.add_button(_("📧 Email отчеты"), "settings_email_reports")
        
        # Экспорт
        self.add_button(_("📤 Настройки экспорта"), "settings_export")
        
        # Автоматизация
        self.add_button(_("🤖 Автоотчеты"), "settings_auto_reports")
        
        # Возврат
        self.add_button(_("⬅️ К аналитике"), "analytics")
        
        return self.build()
