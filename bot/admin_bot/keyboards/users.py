"""
UnveilVPN Shop - Ad<PERSON> Bot Users Keyboards
Клавиатуры для управления пользователями в админ боте
"""

from typing import Dict, Any, List, Optional

from aiogram.types import InlineKeyboardMarkup
from aiogram.utils.i18n import gettext as _

from ...common.keyboards import InlineKeyboard


class UsersMenuKeyboard(InlineKeyboard):
    """
    Клавиатура главного меню управления пользователями
    """
    
    def __init__(self, stats: Dict[str, Any] = None):
        super().__init__(max_width=2)
        self.stats = stats or {}
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры меню пользователей"""
        
        # Основные действия
        self.add_button(_("🔍 Поиск пользователей"), "search_users")
        self.add_button(_("🆕 Недавние"), "recent_users")
        
        # Фильтры по статусу
        active_count = self.stats.get('active_users', 0)
        blocked_count = self.stats.get('blocked_users', 0)
        
        self.add_button(f"🟢 Активные ({active_count})", "active_users")
        self.add_button(f"🔴 Заблокированные ({blocked_count})", "blocked_users")
        
        # Дополнительные фильтры
        self.add_button(_("📡 С VPN"), "users_with_vpn")
        self.add_button(_("❌ Без VPN"), "users_without_vpn")
        
        self.add_button(_("💰 С рефералами"), "users_with_referrals")
        self.add_button(_("📊 Статистика"), "users_statistics")
        
        # Массовые операции
        self.add_button(_("📢 Рассылка"), "mass_message")
        self.add_button(_("📤 Экспорт"), "export_users")
        
        # Возврат в главное меню
        self.add_button(_("🏠 Главное меню"), "main_menu")
        
        return self.build()


class UserListKeyboard(InlineKeyboard):
    """Клавиатура списка пользователей"""
    
    def __init__(self, users: List, list_type: str = "all"):
        super().__init__(max_width=1)
        self.users = users
        self.list_type = list_type
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры списка пользователей"""
        
        # Кнопки для каждого пользователя
        for user in self.users[:10]:  # Максимум 10 пользователей
            status_emoji = "🔴" if user.is_blocked else "🟢"
            vpn_emoji = "📡" if user.vpn_id else "❌"
            
            username = user.username or f"ID{user.telegram_id}"
            button_text = f"{status_emoji}{vpn_emoji} {username}"
            
            self.add_button(button_text, f"user_{user.id}")
        
        # Навигация
        if len(self.users) > 10:
            self.add_button(_("📄 Показать еще"), f"more_{self.list_type}")
        
        # Действия
        if self.list_type == "search":
            self.add_button(_("🔍 Новый поиск"), "search_users")
        elif self.list_type == "recent":
            self.add_button(_("🔄 Обновить"), "recent_users")
        elif self.list_type == "active":
            self.add_button(_("🔄 Обновить"), "active_users")
        elif self.list_type == "blocked":
            self.add_button(_("🔄 Обновить"), "blocked_users")
        
        # Возврат
        self.add_button(_("⬅️ К пользователям"), "users")
        
        return self.build()


class UserDetailsKeyboard(InlineKeyboard):
    """Клавиатура деталей пользователя"""
    
    def __init__(self, user_id: str, user_obj = None):
        super().__init__(max_width=2)
        self.user_id = user_id
        self.user_obj = user_obj
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры деталей пользователя"""
        
        # Основные действия
        self.add_button(_("✏️ Редактировать"), f"edit_user_{self.user_id}")
        self.add_button(_("📊 Подписки"), f"user_subscriptions_{self.user_id}")
        
        # Управление статусом
        if self.user_obj and self.user_obj.is_blocked:
            self.add_button(_("✅ Разблокировать"), f"unblock_user_{self.user_id}")
        else:
            self.add_button(_("🚫 Заблокировать"), f"block_user_{self.user_id}")
        
        # Дополнительные действия
        self.add_button(_("💬 Отправить сообщение"), f"message_user_{self.user_id}")
        self.add_button(_("📜 История"), f"user_history_{self.user_id}")
        
        # Remnawave VPN управление
        if self.user_obj and self.user_obj.remnawave_user_id:
            self.add_button(_("📡 Управление Remnawave"), f"user_vpn_{self.user_id}")
            self.add_button(_("⏸ Приостановить VPN"), f"suspend_vpn_{self.user_id}")
        else:
            self.add_button(_("📡 Создать Remnawave"), f"create_vpn_{self.user_id}")
        
        # Тестовый период
        if self.user_obj and self.user_obj.is_test_used:
            self.add_button(_("🎁 Сбросить тест"), f"reset_trial_{self.user_id}")
        
        # Реферальная система
        if self.user_obj and self.user_obj.referral_code:
            self.add_button(_("👥 Рефералы"), f"user_referrals_{self.user_id}")
        
        # Навигация
        self.add_button(_("🔄 Обновить"), f"user_{self.user_id}")
        self.add_button(_("⬅️ К списку"), "users")
        
        return self.build()


class UserActionsKeyboard(InlineKeyboard):
    """Клавиатура действий с пользователем"""
    
    def __init__(self, user_id: str):
        super().__init__(max_width=2)
        self.user_id = user_id
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры действий"""
        
        # Быстрые действия
        self.add_button(_("💰 Добавить подписку"), f"add_subscription_{self.user_id}")
        self.add_button(_("🎁 Дать тест"), f"give_trial_{self.user_id}")
        
        self.add_button(_("💸 Возврат средств"), f"refund_{self.user_id}")
        self.add_button(_("🔄 Продлить подписку"), f"extend_subscription_{self.user_id}")
        
        # Административные действия
        self.add_button(_("⚠️ Предупреждение"), f"warn_user_{self.user_id}")
        self.add_button(_("📝 Добавить заметку"), f"add_note_{self.user_id}")
        
        # Возврат
        self.add_button(_("⬅️ Назад"), f"user_{self.user_id}")
        
        return self.build()


class UserSearchKeyboard(InlineKeyboard):
    """Клавиатура поиска пользователей"""
    
    def __init__(self):
        super().__init__(max_width=2)
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры поиска"""
        
        # Быстрые фильтры
        self.add_button(_("🆕 Сегодня"), "users_today")
        self.add_button(_("📅 Эта неделя"), "users_week")
        
        self.add_button(_("💰 С покупками"), "users_with_purchases")
        self.add_button(_("🎁 Использовали тест"), "users_used_trial")
        
        # Расширенный поиск
        self.add_button(_("🔍 Расширенный поиск"), "advanced_search")
        
        # Возврат
        self.add_button(_("❌ Отмена"), "users")
        
        return self.build()


class UserFiltersKeyboard(InlineKeyboard):
    """Клавиатура фильтров пользователей"""
    
    def __init__(self):
        super().__init__(max_width=2)
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры фильтров"""
        
        # Фильтры по статусу
        self.add_button(_("🟢 Активные"), "filter_active")
        self.add_button(_("🔴 Заблокированные"), "filter_blocked")
        
        # Фильтры по Remnawave VPN
        self.add_button(_("📡 С Remnawave"), "filter_with_vpn")
        self.add_button(_("❌ Без Remnawave"), "filter_without_vpn")
        
        # Фильтры по активности
        self.add_button(_("🔥 Активные сегодня"), "filter_active_today")
        self.add_button(_("😴 Неактивные"), "filter_inactive")
        
        # Фильтры по покупкам
        self.add_button(_("💰 С покупками"), "filter_with_purchases")
        self.add_button(_("🆓 Только тест"), "filter_trial_only")
        
        # Сброс фильтров
        self.add_button(_("🔄 Сбросить"), "filter_reset")
        
        # Возврат
        self.add_button(_("⬅️ К пользователям"), "users")
        
        return self.build()


class UserSubscriptionKeyboard(InlineKeyboard):
    """Клавиатура управления подписками пользователя"""
    
    def __init__(self, user_id: str, has_subscription: bool = False):
        super().__init__(max_width=2)
        self.user_id = user_id
        self.has_subscription = has_subscription
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры управления подписками"""
        
        if self.has_subscription:
            # Пользователь с подпиской
            self.add_button(_("⏰ Продлить"), f"extend_sub_{self.user_id}")
            self.add_button(_("⏸ Приостановить"), f"suspend_sub_{self.user_id}")
            
            self.add_button(_("📊 Статистика"), f"sub_stats_{self.user_id}")
            self.add_button(_("📱 Конфигурации"), f"sub_configs_{self.user_id}")
            
            self.add_button(_("💸 Возврат"), f"refund_sub_{self.user_id}")
            self.add_button(_("🗑 Удалить"), f"delete_sub_{self.user_id}")
        else:
            # Пользователь без подписки
            self.add_button(_("💰 Создать подписку"), f"create_sub_{self.user_id}")
            self.add_button(_("🎁 Дать тест"), f"give_trial_{self.user_id}")
            
            self.add_button(_("📋 Выбрать тариф"), f"select_tariff_{self.user_id}")
        
        # Возврат
        self.add_button(_("⬅️ К пользователю"), f"user_{self.user_id}")
        
        return self.build()


class MassActionsKeyboard(InlineKeyboard):
    """Клавиатура массовых действий"""
    
    def __init__(self):
        super().__init__(max_width=2)
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры массовых действий"""
        
        # Массовые операции
        self.add_button(_("📢 Рассылка всем"), "mass_message_all")
        self.add_button(_("📢 Рассылка активным"), "mass_message_active")
        
        self.add_button(_("📢 Рассылка с VPN"), "mass_message_vpn")
        self.add_button(_("📢 Рассылка без VPN"), "mass_message_no_vpn")
        
        # Экспорт данных
        self.add_button(_("📤 Экспорт всех"), "export_all_users")
        self.add_button(_("📤 Экспорт активных"), "export_active_users")
        
        # Статистика
        self.add_button(_("📊 Общая статистика"), "general_stats")
        self.add_button(_("📈 Аналитика"), "analytics")
        
        # Возврат
        self.add_button(_("⬅️ К пользователям"), "users")
        
        return self.build()


class UserVPNKeyboard(InlineKeyboard):
    """Клавиатура управления VPN пользователя"""
    
    def __init__(self, user_id: str, vpn_id: Optional[str] = None):
        super().__init__(max_width=2)
        self.user_id = user_id
        self.vpn_id = vpn_id
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры управления VPN"""
        
        if self.vpn_id:
            # Remnawave VPN существует
            self.add_button(_("📊 Статистика Remnawave"), f"vpn_stats_{self.user_id}")
            self.add_button(_("📱 Конфигурации"), f"vpn_configs_{self.user_id}")

            self.add_button(_("⏸ Приостановить"), f"vpn_suspend_{self.user_id}")
            self.add_button(_("▶️ Активировать"), f"vpn_activate_{self.user_id}")

            self.add_button(_("🔄 Сбросить подписку"), f"vpn_reset_{self.user_id}")
            self.add_button(_("🗑 Удалить из Remnawave"), f"vpn_delete_{self.user_id}")
        else:
            # Remnawave VPN не существует
            self.add_button(_("📡 Создать в Remnawave"), f"vpn_create_{self.user_id}")
            self.add_button(_("📋 Выбрать тариф"), f"vpn_select_tariff_{self.user_id}")
        
        # Возврат
        self.add_button(_("⬅️ К пользователю"), f"user_{self.user_id}")
        
        return self.build()


class UserNotesKeyboard(InlineKeyboard):
    """Клавиатура заметок о пользователе"""
    
    def __init__(self, user_id: str):
        super().__init__(max_width=1)
        self.user_id = user_id
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры заметок"""
        
        # Действия с заметками
        self.add_button(_("📝 Добавить заметку"), f"add_note_{self.user_id}")
        self.add_button(_("📋 Все заметки"), f"view_notes_{self.user_id}")
        
        # Быстрые заметки
        self.add_button(_("⚠️ Отметить как проблемного"), f"mark_problematic_{self.user_id}")
        self.add_button(_("⭐ Отметить как VIP"), f"mark_vip_{self.user_id}")
        
        # Возврат
        self.add_button(_("⬅️ К пользователю"), f"user_{self.user_id}")
        
        return self.build()
