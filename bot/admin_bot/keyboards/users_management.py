"""
UnveilVPN Shop - Клавиатуры управления пользователями для админ-бота
"""

from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton
from aiogram.utils.keyboard import InlineKeyboardBuilder
from typing import List, Optional, Dict, Any

from bot.db.models import VPNUsers
from bot.common.keyboards import InlineKeyboard


def get_users_management_keyboard() -> InlineKeyboardMarkup:
    """Главная клавиатура управления пользователями"""
    builder = InlineKeyboardBuilder()
    
    builder.row(
        InlineKeyboardButton(
            text="👥 Список пользователей",
            callback_data="users_list"
        )
    )
    
    builder.row(
        InlineKeyboardButton(
            text="🔍 Найти пользователя",
            callback_data="find_user"
        )
    )
    
    builder.row(
        InlineKeyboardButton(
            text="📊 Статистика пользователей",
            callback_data="users_stats"
        )
    )
    
    builder.row(
        InlineKeyboardButton(
            text="✅ Активные подписки",
            callback_data="active_subscriptions"
        )
    )
    
    builder.row(
        InlineKeyboardButton(
            text="⚠️ Проблемные пользователи",
            callback_data="problem_users"
        )
    )
    
    builder.row(
        InlineKeyboardButton(
            text="🔙 Главное меню",
            callback_data="admin_main_menu"
        )
    )
    
    return builder.as_markup()


def get_user_actions_keyboard(user_id: int) -> InlineKeyboardMarkup:
    """Клавиатура действий с пользователем"""
    builder = InlineKeyboardBuilder()
    
    builder.row(
        InlineKeyboardButton(
            text="📋 Информация",
            callback_data=f"user_info:{user_id}"
        )
    )
    
    builder.row(
        InlineKeyboardButton(
            text="💳 История платежей",
            callback_data=f"user_payments:{user_id}"
        )
    )
    
    builder.row(
        InlineKeyboardButton(
            text="🎫 Подписки",
            callback_data=f"user_subscriptions:{user_id}"
        )
    )
    
    builder.row(
        InlineKeyboardButton(
            text="✉️ Отправить сообщение",
            callback_data=f"send_message:{user_id}"
        )
    )
    
    builder.row(
        InlineKeyboardButton(
            text="🎁 Выдать подписку",
            callback_data=f"grant_subscription:{user_id}"
        )
    )
    
    builder.row(
        InlineKeyboardButton(
            text="🚫 Заблокировать",
            callback_data=f"block_user:{user_id}"
        ),
        InlineKeyboardButton(
            text="✅ Разблокировать",
            callback_data=f"unblock_user:{user_id}"
        )
    )
    
    builder.row(
        InlineKeyboardButton(
            text="🔙 Назад",
            callback_data="users_management"
        )
    )
    
    return builder.as_markup()


def get_users_list_keyboard(users: List[VPNUsers], page: int = 0, has_next: bool = False) -> InlineKeyboardMarkup:
    """Клавиатура списка пользователей"""
    builder = InlineKeyboardBuilder()
    
    # Добавляем пользователей (по 5 на страницу)
    for user in users:
        display_name = user.first_name or user.username or f"ID: {user.telegram_id}"
        builder.row(
            InlineKeyboardButton(
                text=f"👤 {display_name}",
                callback_data=f"select_user:{user.id}"
            )
        )
    
    # Навигация по страницам
    nav_buttons = []
    
    if page > 0:
        nav_buttons.append(
            InlineKeyboardButton(
                text="⬅️ Предыдущая",
                callback_data=f"users_list:{page-1}"
            )
        )
    
    if has_next:
        nav_buttons.append(
            InlineKeyboardButton(
                text="➡️ Следующая",
                callback_data=f"users_list:{page+1}"
            )
        )
    
    if nav_buttons:
        builder.row(*nav_buttons)
    
    builder.row(
        InlineKeyboardButton(
            text="🔙 Управление пользователями",
            callback_data="users_management"
        )
    )
    
    return builder.as_markup()


def get_user_search_keyboard() -> InlineKeyboardMarkup:
    """Клавиатура поиска пользователей"""
    builder = InlineKeyboardBuilder()
    
    builder.row(
        InlineKeyboardButton(
            text="🆔 По Telegram ID",
            callback_data="search_by_telegram_id"
        )
    )
    
    builder.row(
        InlineKeyboardButton(
            text="👤 По имени пользователя",
            callback_data="search_by_username"
        )
    )
    
    builder.row(
        InlineKeyboardButton(
            text="📧 По email",
            callback_data="search_by_email"
        )
    )
    
    builder.row(
        InlineKeyboardButton(
            text="🔙 Назад",
            callback_data="users_management"
        )
    )
    
    return builder.as_markup()


def get_subscription_grant_keyboard(user_id: int) -> InlineKeyboardMarkup:
    """Клавиатура выдачи подписки"""
    builder = InlineKeyboardBuilder()
    
    # Стандартные периоды
    periods = [
        ("1 день", 1),
        ("7 дней", 7),
        ("30 дней", 30),
        ("90 дней", 90),
        ("365 дней", 365)
    ]
    
    for period_name, days in periods:
        builder.row(
            InlineKeyboardButton(
                text=f"📅 {period_name}",
                callback_data=f"grant_days:{user_id}:{days}"
            )
        )
    
    builder.row(
        InlineKeyboardButton(
            text="⚙️ Свой период",
            callback_data=f"grant_custom:{user_id}"
        )
    )
    
    builder.row(
        InlineKeyboardButton(
            text="🔙 Назад",
            callback_data=f"select_user:{user_id}"
        )
    )
    
    return builder.as_markup()


def get_user_block_confirmation_keyboard(user_id: int) -> InlineKeyboardMarkup:
    """Клавиатура подтверждения блокировки пользователя"""
    builder = InlineKeyboardBuilder()
    
    builder.row(
        InlineKeyboardButton(
            text="✅ Да, заблокировать",
            callback_data=f"confirm_block:{user_id}"
        )
    )
    
    builder.row(
        InlineKeyboardButton(
            text="❌ Отменить",
            callback_data=f"select_user:{user_id}"
        )
    )
    
    return builder.as_markup()


def get_broadcast_keyboard() -> InlineKeyboardMarkup:
    """Клавиатура массовой рассылки"""
    builder = InlineKeyboardBuilder()
    
    builder.row(
        InlineKeyboardButton(
            text="📢 Всем пользователям",
            callback_data="broadcast_all"
        )
    )
    
    builder.row(
        InlineKeyboardButton(
            text="✅ Активным подписчикам",
            callback_data="broadcast_active"
        )
    )
    
    builder.row(
        InlineKeyboardButton(
            text="❌ Неактивным пользователям",
            callback_data="broadcast_inactive"
        )
    )
    
    builder.row(
        InlineKeyboardButton(
            text="🆕 Новым пользователям",
            callback_data="broadcast_new"
        )
    )
    
    builder.row(
        InlineKeyboardButton(
            text="🔙 Назад",
            callback_data="users_management"
        )
    )
    
    return builder.as_markup()


def get_user_stats_keyboard() -> InlineKeyboardMarkup:
    """Клавиатура статистики пользователей"""
    builder = InlineKeyboardBuilder()
    
    builder.row(
        InlineKeyboardButton(
            text="📊 Общая статистика",
            callback_data="stats_general"
        )
    )
    
    builder.row(
        InlineKeyboardButton(
            text="📈 По дням",
            callback_data="stats_daily"
        )
    )
    
    builder.row(
        InlineKeyboardButton(
            text="📅 По месяцам",
            callback_data="stats_monthly"
        )
    )
    
    builder.row(
        InlineKeyboardButton(
            text="💰 По доходам",
            callback_data="stats_revenue"
        )
    )
    
    builder.row(
        InlineKeyboardButton(
            text="🔙 Назад",
            callback_data="users_management"
        )
    )
    
    return builder.as_markup()


class UsersManagementKeyboard(InlineKeyboard):
    """Класс клавиатуры управления пользователями"""

    def __init__(self):
        super().__init__(max_width=2)

    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры управления пользователями"""
        return get_users_management_keyboard()
