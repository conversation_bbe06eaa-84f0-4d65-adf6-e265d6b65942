"""
UnveilVPN Shop - Admin Menu Keyboard
Клавиатура главного меню административного бота
"""

from typing import Dict, Any

from aiogram.types import InlineKeyboardMarkup
from aiogram.utils.i18n import gettext as _

from ...common.keyboards import InlineKeyboard


class AdminMenuKeyboard(InlineKeyboard):
    """
    Клавиатура главного меню административного бота
    """
    
    def __init__(self):
        super().__init__(max_width=2)
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры административного меню"""
        
        # Основные разделы управления
        self.add_button(_("👥 Пользователи"), "admin_users")
        self.add_button(_("💰 Тарифы"), "admin_tariffs")
        
        self.add_button(_("🎫 Промокоды"), "admin_promocodes")
        self.add_button(_("💳 Платежи"), "admin_payments")
        
        self.add_button(_("🔗 Рефералы"), "admin_referrals")
        self.add_button(_("🎧 Поддержка"), "admin_support")
        
        # Статистика и аналитика
        self.add_button(_("📊 Статистика"), "admin_statistics")
        self.add_button(_("📈 Аналитика"), "admin_analytics")
        
        # Системные функции
        self.add_button(_("⚙️ Настройки"), "admin_settings")
        self.add_button(_("📝 Логи"), "admin_logs")
        
        # Быстрые действия
        self.add_button(_("⚡ Быстрая статистика"), "quick_stats")
        self.add_button(_("🖥️ Статус системы"), "system_status")
        
        # Массовые операции
        self.add_button(_("📢 Рассылка"), "admin_broadcast")
        self.add_button(_("🔄 Обновить данные"), "refresh_data")
        
        return self.build()


class QuickStatsKeyboard(InlineKeyboard):
    """Клавиатура быстрой статистики"""
    
    def __init__(self):
        super().__init__(max_width=2)
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры быстрой статистики"""
        
        # Детальные отчеты
        self.add_button(_("📊 Подробная статистика"), "detailed_stats")
        self.add_button(_("📈 Графики"), "stats_charts")
        
        # Экспорт данных
        self.add_button(_("📤 Экспорт в Excel"), "export_excel")
        self.add_button(_("📄 PDF отчет"), "export_pdf")
        
        # Возврат в главное меню
        self.add_main_menu_button()
        
        return self.build()


class SystemStatusKeyboard(InlineKeyboard):
    """Клавиатура статуса системы"""
    
    def __init__(self):
        super().__init__(max_width=2)
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры статуса системы"""
        
        # Управление сервисами
        self.add_button(_("🔄 Перезапустить сервисы"), "restart_services")
        self.add_button(_("🧹 Очистить кэш"), "clear_cache")
        
        # Мониторинг
        self.add_button(_("📊 Мониторинг ресурсов"), "resource_monitoring")
        self.add_button(_("📋 Логи системы"), "system_logs")
        
        # Резервное копирование
        self.add_button(_("💾 Создать бэкап"), "backup_db")
        self.add_button(_("📂 Управление бэкапами"), "manage_backups")
        
        # Возврат в главное меню
        self.add_main_menu_button()
        
        return self.build()


class RecentActivityKeyboard(InlineKeyboard):
    """Клавиатура последней активности"""
    
    def __init__(self):
        super().__init__(max_width=2)
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры последней активности"""
        
        # Фильтры активности
        self.add_button(_("👥 Пользователи"), "activity_users")
        self.add_button(_("💳 Платежи"), "activity_payments")
        
        self.add_button(_("🎧 Поддержка"), "activity_support")
        self.add_button(_("⚙️ Система"), "activity_system")
        
        # Управление
        self.add_button(_("🔄 Обновить"), "refresh_activity")
        self.add_button(_("📤 Экспорт"), "export_activity")
        
        # Возврат в главное меню
        self.add_main_menu_button()
        
        return self.build()


class ConfirmRestartKeyboard(InlineKeyboard):
    """Клавиатура подтверждения перезапуска"""
    
    def __init__(self):
        super().__init__(max_width=2)
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры подтверждения перезапуска"""
        
        # Подтверждение
        self.add_button(_("✅ Да, перезапустить"), "confirm_restart")
        self.add_button(_("❌ Отмена"), "cancel_restart")
        
        return self.build()


class BroadcastKeyboard(InlineKeyboard):
    """Клавиатура массовой рассылки"""
    
    def __init__(self):
        super().__init__(max_width=2)
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры массовой рассылки"""
        
        # Типы рассылки
        self.add_button(_("📢 Всем пользователям"), "broadcast_all")
        self.add_button(_("👥 Активным пользователям"), "broadcast_active")
        
        self.add_button(_("💰 Платящим клиентам"), "broadcast_paid")
        self.add_button(_("🆓 Пользователям без подписки"), "broadcast_free")
        
        # Шаблоны сообщений
        self.add_button(_("📝 Шаблоны"), "broadcast_templates")
        self.add_button(_("📊 История рассылок"), "broadcast_history")
        
        # Возврат в главное меню
        self.add_main_menu_button()
        
        return self.build()


class AdminActionsKeyboard(InlineKeyboard):
    """Клавиатура административных действий"""
    
    def __init__(self):
        super().__init__(max_width=3)
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры административных действий"""
        
        # Быстрые действия
        self.add_button(_("🔄"), "refresh_data", width=1)
        self.add_button(_("📊"), "quick_stats", width=1)
        self.add_button(_("⚙️"), "admin_settings", width=1)
        
        return self.build()
