"""
UnveilVPN Shop - Admin Bot System Management Keyboards
Клавиатуры управления системой для административного бота
"""

from aiogram.types import InlineKeyboardButton, InlineKeyboardMarkup
from aiogram.utils.keyboard import InlineKeyboardBuilder
from typing import Dict, Any, List, Optional

from bot.common.keyboards import BaseKeyboard


class SystemMenuKeyboard(BaseKeyboard):
    """Клавиатура системного меню"""
    
    def __init__(self, max_width: int = 2):
        super().__init__(max_width)
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры системного меню"""
        builder = InlineKeyboardBuilder()
        
        # Мониторинг системы
        builder.add(InlineKeyboardButton(
            text="📊 Статус системы",
            callback_data="system_status"
        ))
        builder.add(InlineKeyboardButton(
            text="📈 Мониторинг",
            callback_data="system_monitoring"
        ))
        
        # Логи и диагностика
        builder.add(InlineKeyboardButton(
            text="📋 Логи системы",
            callback_data="system_logs"
        ))
        builder.add(InlineKeyboardButton(
            text="🔍 Диагностика",
            callback_data="system_diagnostics"
        ))
        
        # Резервное копирование
        builder.add(InlineKeyboardButton(
            text="💾 Резервные копии",
            callback_data="system_backups"
        ))
        builder.add(InlineKeyboardButton(
            text="🔄 Создать бэкап",
            callback_data="create_backup"
        ))
        
        # Управление сервисами
        builder.add(InlineKeyboardButton(
            text="⚙️ Сервисы",
            callback_data="system_services"
        ))
        builder.add(InlineKeyboardButton(
            text="🔧 Настройки",
            callback_data="system_settings"
        ))
        
        # Обслуживание
        builder.add(InlineKeyboardButton(
            text="🧹 Очистка",
            callback_data="system_cleanup"
        ))
        builder.add(InlineKeyboardButton(
            text="🔄 Перезагрузка",
            callback_data="system_restart"
        ))
        
        # Возврат в главное меню
        builder.add(InlineKeyboardButton(
            text="⬅️ Админ панель",
            callback_data="admin_menu"
        ))
        
        builder.adjust(2, 2, 2, 2, 2, 1)
        return builder.as_markup()


class SystemStatusKeyboard(BaseKeyboard):
    """Клавиатура статуса системы"""
    
    def __init__(self, max_width: int = 2):
        super().__init__(max_width)
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры статуса системы"""
        builder = InlineKeyboardBuilder()
        
        # Компоненты системы
        builder.add(InlineKeyboardButton(
            text="🐘 PostgreSQL",
            callback_data="status_postgresql"
        ))
        builder.add(InlineKeyboardButton(
            text="🔴 Redis",
            callback_data="status_redis"
        ))
        
        builder.add(InlineKeyboardButton(
            text="🤖 Боты",
            callback_data="status_bots"
        ))
        builder.add(InlineKeyboardButton(
            text="🌐 API",
            callback_data="status_api"
        ))
        
        builder.add(InlineKeyboardButton(
            text="🔗 Remnawave",
            callback_data="status_remnawave"
        ))
        builder.add(InlineKeyboardButton(
            text="💳 Платежи",
            callback_data="status_payments"
        ))
        
        # Системные ресурсы
        builder.add(InlineKeyboardButton(
            text="💻 CPU/RAM",
            callback_data="status_resources"
        ))
        builder.add(InlineKeyboardButton(
            text="💾 Диск",
            callback_data="status_disk"
        ))
        
        # Действия
        builder.add(InlineKeyboardButton(
            text="🔄 Обновить",
            callback_data="refresh_system_status"
        ))
        builder.add(InlineKeyboardButton(
            text="⬅️ Система",
            callback_data="system_menu"
        ))
        
        builder.adjust(2, 2, 2, 2, 2)
        return builder.as_markup()


class SystemLogsKeyboard(BaseKeyboard):
    """Клавиатура логов системы"""
    
    def __init__(self, max_width: int = 2):
        super().__init__(max_width)
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры логов системы"""
        builder = InlineKeyboardBuilder()
        
        # Типы логов
        builder.add(InlineKeyboardButton(
            text="🔴 Ошибки",
            callback_data="logs_errors"
        ))
        builder.add(InlineKeyboardButton(
            text="⚠️ Предупреждения",
            callback_data="logs_warnings"
        ))
        
        builder.add(InlineKeyboardButton(
            text="ℹ️ Информация",
            callback_data="logs_info"
        ))
        builder.add(InlineKeyboardButton(
            text="🐛 Отладка",
            callback_data="logs_debug"
        ))
        
        # Источники логов
        builder.add(InlineKeyboardButton(
            text="🤖 Боты",
            callback_data="logs_bots"
        ))
        builder.add(InlineKeyboardButton(
            text="🌐 API",
            callback_data="logs_api"
        ))
        
        builder.add(InlineKeyboardButton(
            text="💳 Платежи",
            callback_data="logs_payments"
        ))
        builder.add(InlineKeyboardButton(
            text="🔗 VPN",
            callback_data="logs_vpn"
        ))
        
        # Периоды
        builder.add(InlineKeyboardButton(
            text="📅 Сегодня",
            callback_data="logs_today"
        ))
        builder.add(InlineKeyboardButton(
            text="📅 Неделя",
            callback_data="logs_week"
        ))
        
        # Действия
        builder.add(InlineKeyboardButton(
            text="📤 Экспорт",
            callback_data="export_logs"
        ))
        builder.add(InlineKeyboardButton(
            text="🗑 Очистить",
            callback_data="clear_logs"
        ))
        
        builder.add(InlineKeyboardButton(
            text="⬅️ Система",
            callback_data="system_menu"
        ))
        
        builder.adjust(2, 2, 2, 2, 2, 2, 1)
        return builder.as_markup()


class SystemBackupsKeyboard(BaseKeyboard):
    """Клавиатура резервных копий"""
    
    def __init__(self, max_width: int = 2):
        super().__init__(max_width)
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры резервных копий"""
        builder = InlineKeyboardBuilder()
        
        # Создание бэкапов
        builder.add(InlineKeyboardButton(
            text="💾 База данных",
            callback_data="backup_database"
        ))
        builder.add(InlineKeyboardButton(
            text="📁 Файлы",
            callback_data="backup_files"
        ))
        
        builder.add(InlineKeyboardButton(
            text="🔄 Полный бэкап",
            callback_data="backup_full"
        ))
        builder.add(InlineKeyboardButton(
            text="⚡ Быстрый бэкап",
            callback_data="backup_quick"
        ))
        
        # Управление бэкапами
        builder.add(InlineKeyboardButton(
            text="📋 Список бэкапов",
            callback_data="list_backups"
        ))
        builder.add(InlineKeyboardButton(
            text="🔍 Проверить",
            callback_data="verify_backups"
        ))
        
        builder.add(InlineKeyboardButton(
            text="📤 Восстановить",
            callback_data="restore_backup"
        ))
        builder.add(InlineKeyboardButton(
            text="🗑 Удалить старые",
            callback_data="cleanup_backups"
        ))
        
        # Настройки
        builder.add(InlineKeyboardButton(
            text="⚙️ Настройки",
            callback_data="backup_settings"
        ))
        builder.add(InlineKeyboardButton(
            text="📊 Статистика",
            callback_data="backup_stats"
        ))
        
        builder.add(InlineKeyboardButton(
            text="⬅️ Система",
            callback_data="system_menu"
        ))
        
        builder.adjust(2, 2, 2, 2, 2, 1)
        return builder.as_markup()


class SystemServicesKeyboard(BaseKeyboard):
    """Клавиатура управления сервисами"""
    
    def __init__(self, max_width: int = 2):
        super().__init__(max_width)
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры управления сервисами"""
        builder = InlineKeyboardBuilder()
        
        # Основные сервисы
        builder.add(InlineKeyboardButton(
            text="🤖 Client Bot",
            callback_data="service_client_bot"
        ))
        builder.add(InlineKeyboardButton(
            text="🛠 Admin Bot",
            callback_data="service_admin_bot"
        ))
        
        builder.add(InlineKeyboardButton(
            text="💬 Support Bot",
            callback_data="service_support_bot"
        ))
        builder.add(InlineKeyboardButton(
            text="🌐 API",
            callback_data="service_api"
        ))
        
        # Инфраструктура
        builder.add(InlineKeyboardButton(
            text="🐘 PostgreSQL",
            callback_data="service_postgresql"
        ))
        builder.add(InlineKeyboardButton(
            text="🔴 Redis",
            callback_data="service_redis"
        ))
        
        # Массовые действия
        builder.add(InlineKeyboardButton(
            text="▶️ Запустить все",
            callback_data="start_all_services"
        ))
        builder.add(InlineKeyboardButton(
            text="⏸ Остановить все",
            callback_data="stop_all_services"
        ))
        
        builder.add(InlineKeyboardButton(
            text="🔄 Перезапустить все",
            callback_data="restart_all_services"
        ))
        builder.add(InlineKeyboardButton(
            text="📊 Статус всех",
            callback_data="status_all_services"
        ))
        
        builder.add(InlineKeyboardButton(
            text="⬅️ Система",
            callback_data="system_menu"
        ))
        
        builder.adjust(2, 2, 2, 2, 2, 1)
        return builder.as_markup()


class SystemMonitoringKeyboard(BaseKeyboard):
    """Клавиатура мониторинга системы"""

    def __init__(self, max_width: int = 2):
        super().__init__(max_width)

    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры мониторинга системы"""
        builder = InlineKeyboardBuilder()

        # Метрики производительности
        builder.add(InlineKeyboardButton(
            text="📊 CPU",
            callback_data="monitor_cpu"
        ))
        builder.add(InlineKeyboardButton(
            text="💾 RAM",
            callback_data="monitor_ram"
        ))

        builder.add(InlineKeyboardButton(
            text="💿 Диск",
            callback_data="monitor_disk"
        ))
        builder.add(InlineKeyboardButton(
            text="🌐 Сеть",
            callback_data="monitor_network"
        ))

        # Процессы и сервисы
        builder.add(InlineKeyboardButton(
            text="⚙️ Процессы",
            callback_data="monitor_processes"
        ))
        builder.add(InlineKeyboardButton(
            text="🔧 Сервисы",
            callback_data="monitor_services"
        ))

        # Логи и события
        builder.add(InlineKeyboardButton(
            text="📋 Системные логи",
            callback_data="monitor_system_logs"
        ))
        builder.add(InlineKeyboardButton(
            text="⚠️ Ошибки",
            callback_data="monitor_errors"
        ))

        # Пользователи и подключения
        builder.add(InlineKeyboardButton(
            text="👥 Пользователи",
            callback_data="monitor_users"
        ))
        builder.add(InlineKeyboardButton(
            text="🔗 Подключения",
            callback_data="monitor_connections"
        ))

        # Настройки мониторинга
        builder.add(InlineKeyboardButton(
            text="⚙️ Настройки",
            callback_data="monitor_settings"
        ))
        builder.add(InlineKeyboardButton(
            text="🔄 Обновить",
            callback_data="refresh_monitoring"
        ))

        builder.add(InlineKeyboardButton(
            text="⬅️ Система",
            callback_data="system_menu"
        ))

        builder.adjust(2, 2, 2, 2, 2, 2, 1)
        return builder.as_markup()


class SystemBackupKeyboard(BaseKeyboard):
    """Клавиатура управления резервными копиями"""

    def __init__(self, max_width: int = 2):
        super().__init__(max_width)

    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры управления резервными копиями"""
        builder = InlineKeyboardBuilder()

        # Создание резервных копий
        builder.add(InlineKeyboardButton(
            text="💾 Создать бэкап БД",
            callback_data="backup_create_database"
        ))
        builder.add(InlineKeyboardButton(
            text="📁 Создать бэкап файлов",
            callback_data="backup_create_files"
        ))

        builder.add(InlineKeyboardButton(
            text="🔄 Полный бэкап",
            callback_data="backup_create_full"
        ))
        builder.add(InlineKeyboardButton(
            text="⚡ Быстрый бэкап",
            callback_data="backup_create_quick"
        ))

        # Управление существующими бэкапами
        builder.add(InlineKeyboardButton(
            text="📋 Список бэкапов",
            callback_data="backup_list"
        ))
        builder.add(InlineKeyboardButton(
            text="🔍 Проверить целостность",
            callback_data="backup_verify"
        ))

        builder.add(InlineKeyboardButton(
            text="📤 Восстановить",
            callback_data="backup_restore"
        ))
        builder.add(InlineKeyboardButton(
            text="🗑 Очистить старые",
            callback_data="backup_cleanup"
        ))

        # Настройки и статистика
        builder.add(InlineKeyboardButton(
            text="⚙️ Настройки",
            callback_data="backup_settings"
        ))
        builder.add(InlineKeyboardButton(
            text="📊 Статистика",
            callback_data="backup_statistics"
        ))

        # Автоматизация
        builder.add(InlineKeyboardButton(
            text="🤖 Автобэкап",
            callback_data="backup_automation"
        ))
        builder.add(InlineKeyboardButton(
            text="📅 Расписание",
            callback_data="backup_schedule"
        ))

        builder.add(InlineKeyboardButton(
            text="⬅️ Резервные копии",
            callback_data="system_backups"
        ))

        builder.adjust(2, 2, 2, 2, 2, 2, 1)
        return builder.as_markup()


class SystemSettingsKeyboard(BaseKeyboard):
    """Клавиатура настроек системы"""

    def __init__(self, max_width: int = 2):
        super().__init__(max_width)

    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры настроек системы"""
        builder = InlineKeyboardBuilder()

        # Общие настройки
        builder.add(InlineKeyboardButton(
            text="⚙️ Общие",
            callback_data="settings_general"
        ))
        builder.add(InlineKeyboardButton(
            text="🔐 Безопасность",
            callback_data="settings_security"
        ))

        # Настройки ботов
        builder.add(InlineKeyboardButton(
            text="🤖 Боты",
            callback_data="settings_bots"
        ))
        builder.add(InlineKeyboardButton(
            text="📱 Уведомления",
            callback_data="settings_notifications"
        ))

        # Настройки API и интеграций
        builder.add(InlineKeyboardButton(
            text="🌐 API",
            callback_data="settings_api"
        ))
        builder.add(InlineKeyboardButton(
            text="🔗 Интеграции",
            callback_data="settings_integrations"
        ))

        # Настройки базы данных
        builder.add(InlineKeyboardButton(
            text="🗄️ База данных",
            callback_data="settings_database"
        ))
        builder.add(InlineKeyboardButton(
            text="🔴 Redis",
            callback_data="settings_redis"
        ))

        # Настройки логирования
        builder.add(InlineKeyboardButton(
            text="📋 Логирование",
            callback_data="settings_logging"
        ))
        builder.add(InlineKeyboardButton(
            text="📊 Мониторинг",
            callback_data="settings_monitoring"
        ))

        # Действия
        builder.add(InlineKeyboardButton(
            text="💾 Сохранить",
            callback_data="settings_save"
        ))
        builder.add(InlineKeyboardButton(
            text="🔄 Сбросить",
            callback_data="settings_reset"
        ))

        builder.add(InlineKeyboardButton(
            text="⬅️ Система",
            callback_data="system_menu"
        ))

        builder.adjust(2, 2, 2, 2, 2, 2, 1)
        return builder.as_markup()


class SystemMaintenanceKeyboard(BaseKeyboard):
    """Клавиатура обслуживания системы"""

    def __init__(self, max_width: int = 2):
        super().__init__(max_width)

    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры обслуживания системы"""
        builder = InlineKeyboardBuilder()

        # Очистка и оптимизация
        builder.add(InlineKeyboardButton(
            text="🧹 Очистка логов",
            callback_data="maintenance_clean_logs"
        ))
        builder.add(InlineKeyboardButton(
            text="🗑 Очистка кэша",
            callback_data="maintenance_clean_cache"
        ))

        builder.add(InlineKeyboardButton(
            text="📊 Оптимизация БД",
            callback_data="maintenance_optimize_db"
        ))
        builder.add(InlineKeyboardButton(
            text="🔄 Переиндексация",
            callback_data="maintenance_reindex"
        ))

        # Обновления и миграции
        builder.add(InlineKeyboardButton(
            text="⬆️ Обновления",
            callback_data="maintenance_updates"
        ))
        builder.add(InlineKeyboardButton(
            text="🔄 Миграции",
            callback_data="maintenance_migrations"
        ))

        # Диагностика
        builder.add(InlineKeyboardButton(
            text="🔍 Диагностика",
            callback_data="maintenance_diagnostics"
        ))
        builder.add(InlineKeyboardButton(
            text="🩺 Проверка здоровья",
            callback_data="maintenance_health_check"
        ))

        # Перезапуск сервисов
        builder.add(InlineKeyboardButton(
            text="🔄 Перезапуск ботов",
            callback_data="maintenance_restart_bots"
        ))
        builder.add(InlineKeyboardButton(
            text="🔄 Перезапуск API",
            callback_data="maintenance_restart_api"
        ))

        # Режим обслуживания
        builder.add(InlineKeyboardButton(
            text="🚧 Режим обслуживания",
            callback_data="maintenance_mode_toggle"
        ))
        builder.add(InlineKeyboardButton(
            text="📋 Планировщик",
            callback_data="maintenance_scheduler"
        ))

        builder.add(InlineKeyboardButton(
            text="⬅️ Система",
            callback_data="system_menu"
        ))

        builder.adjust(2, 2, 2, 2, 2, 2, 1)
        return builder.as_markup()
