"""
UnveilVPN Shop - <PERSON><PERSON> Main Keyboards
Главные клавиатуры админ бота
"""

from typing import Dict, Any, Optional

from aiogram.types import InlineKeyboardMarkup
from aiogram.utils.i18n import gettext as _

from ...common.keyboards import InlineKeyboard


class AdminMainMenuKeyboard(InlineKeyboard):
    """
    Главная клавиатура админ бота
    """
    
    def __init__(self, stats: Dict[str, Any] = None, system_info: Dict[str, Any] = None):
        super().__init__(max_width=2)
        self.stats = stats or {}
        self.system_info = system_info or {}
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение главной клавиатуры админ бота"""
        
        # Основные разделы управления
        self.add_button(_("👥 Пользователи"), "users")
        self.add_button(_("💰 Тарифы"), "tariffs")
        
        self.add_button(_("🎫 Промокоды"), "promocodes")
        self.add_button(_("👥 Рефералы"), "referrals")
        
        # Аналитика и отчеты
        self.add_button(_("📊 Аналитика"), "analytics")
        self.add_button(_("📋 Отчеты"), "reports")
        
        # Системное управление
        self.add_button(_("⚙️ Система"), "system")
        self.add_button(_("💬 Поддержка"), "support_management")
        
        # Быстрые действия
        self.add_button(_("📢 Рассылка"), "send_broadcast")
        self.add_button(_("💾 Бэкап"), "create_backup")
        
        # Информация и настройки
        self.add_button(_("📊 Статистика"), "quick_stats")
        self.add_button(_("ℹ️ Система"), "system_info")
        
        # Обновление данных
        self.add_button(_("🔄 Обновить"), "refresh_data")
        
        return self.build()


class AdminQuickActionsKeyboard(InlineKeyboard):
    """Клавиатура быстрых действий"""
    
    def __init__(self):
        super().__init__(max_width=2)
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры быстрых действий"""
        
        # Быстрые действия
        self.add_button(_("📢 Рассылка всем"), "send_broadcast")
        self.add_button(_("🔍 Поиск пользователя"), "search_users")
        
        self.add_button(_("💰 Создать тариф"), "create_tariff")
        self.add_button(_("🎫 Создать промокод"), "create_promocode")
        
        self.add_button(_("💾 Создать бэкап"), "create_backup")
        self.add_button(_("🧹 Очистить кэш"), "clear_cache")
        
        # Мониторинг
        self.add_button(_("📊 Мониторинг"), "system_monitoring")
        self.add_button(_("📋 Логи"), "system_logs")
        
        # Возврат
        self.add_button(_("⬅️ Главное меню"), "main_menu")
        
        return self.build()


class AdminStatsKeyboard(InlineKeyboard):
    """Клавиатура статистики"""
    
    def __init__(self, stats_type: str = "overview"):
        super().__init__(max_width=2)
        self.stats_type = stats_type
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры статистики"""
        
        # Типы статистики
        self.add_button(_("📊 Обзор"), "quick_stats")
        self.add_button(_("💰 Доходы"), "revenue_analytics")
        
        self.add_button(_("👥 Пользователи"), "user_analytics")
        self.add_button(_("📡 Подписки"), "subscription_analytics")
        
        self.add_button(_("💳 Платежи"), "payment_analytics")
        self.add_button(_("🎫 Промокоды"), "promocode_analytics")
        
        # Периоды
        self.add_button(_("📅 Сегодня"), "stats_today")
        self.add_button(_("📅 Неделя"), "stats_week")
        
        self.add_button(_("📅 Месяц"), "stats_month")
        self.add_button(_("📈 Тренды"), "stats_trends")
        
        # Экспорт
        self.add_button(_("📤 Экспорт"), "export_stats")
        
        # Возврат
        self.add_button(_("⬅️ Главное меню"), "main_menu")
        
        return self.build()


class AdminSystemKeyboard(InlineKeyboard):
    """Клавиатура системного управления"""
    
    def __init__(self, system_status: str = "unknown"):
        super().__init__(max_width=2)
        self.system_status = system_status
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры системного управления"""
        
        # Мониторинг
        self.add_button(_("📊 Мониторинг"), "system_monitoring")
        self.add_button(_("📋 Логи"), "system_logs")
        
        # Резервное копирование
        self.add_button(_("💾 Бэкапы"), "backup_management")
        self.add_button(_("💾 Создать бэкап"), "create_backup")
        
        # Обслуживание
        self.add_button(_("🧹 Очистить кэш"), "clear_cache")
        self.add_button(_("🔄 Перезапуск"), "restart_services")
        
        # Настройки
        self.add_button(_("⚙️ Настройки"), "system_settings")
        self.add_button(_("🔧 Обслуживание"), "maintenance_mode")
        
        # Уведомления
        self.add_button(_("📢 Рассылка"), "send_broadcast")
        self.add_button(_("📧 Уведомления"), "notification_settings")
        
        # Возврат
        self.add_button(_("⬅️ Главное меню"), "main_menu")
        
        return self.build()


class AdminNavigationKeyboard(InlineKeyboard):
    """Клавиатура навигации"""
    
    def __init__(self, current_section: str = "main"):
        super().__init__(max_width=3)
        self.current_section = current_section
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры навигации"""
        
        # Основные разделы
        sections = [
            ("👥", "users", "Пользователи"),
            ("💰", "tariffs", "Тарифы"),
            ("🎫", "promocodes", "Промокоды"),
            ("📊", "analytics", "Аналитика"),
            ("⚙️", "system", "Система")
        ]
        
        for emoji, section, name in sections:
            if section == self.current_section:
                # Текущий раздел выделяем
                self.add_button(f"• {emoji} {name} •", f"{section}")
            else:
                self.add_button(f"{emoji}", f"{section}")
        
        # Главное меню
        self.add_button(_("🏠"), "main_menu")
        
        return self.build()


class AdminConfirmationKeyboard(InlineKeyboard):
    """Клавиатура подтверждения действий"""
    
    def __init__(self, action: str, item_id: str = None):
        super().__init__(max_width=2)
        self.action = action
        self.item_id = item_id
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры подтверждения"""
        
        # Подтверждение
        confirm_callback = f"confirm_{self.action}"
        if self.item_id:
            confirm_callback += f"_{self.item_id}"
        
        self.add_button(_("✅ Подтвердить"), confirm_callback)
        self.add_button(_("❌ Отмена"), "cancel_action")
        
        return self.build()


class AdminPaginationKeyboard(InlineKeyboard):
    """Клавиатура пагинации"""
    
    def __init__(self, current_page: int, total_pages: int, callback_prefix: str):
        super().__init__(max_width=5)
        self.current_page = current_page
        self.total_pages = total_pages
        self.callback_prefix = callback_prefix
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры пагинации"""
        
        if self.total_pages <= 1:
            return self.build()
        
        # Первая страница
        if self.current_page > 1:
            self.add_button("⏪", f"{self.callback_prefix}_page_1")
        
        # Предыдущая страница
        if self.current_page > 1:
            self.add_button("◀️", f"{self.callback_prefix}_page_{self.current_page - 1}")
        
        # Текущая страница
        self.add_button(f"• {self.current_page} •", "current_page")
        
        # Следующая страница
        if self.current_page < self.total_pages:
            self.add_button("▶️", f"{self.callback_prefix}_page_{self.current_page + 1}")
        
        # Последняя страница
        if self.current_page < self.total_pages:
            self.add_button("⏩", f"{self.callback_prefix}_page_{self.total_pages}")
        
        return self.build()


class AdminFilterKeyboard(InlineKeyboard):
    """Клавиатура фильтров"""
    
    def __init__(self, filter_type: str, active_filters: Dict[str, Any] = None):
        super().__init__(max_width=2)
        self.filter_type = filter_type
        self.active_filters = active_filters or {}
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры фильтров"""
        
        if self.filter_type == "users":
            # Фильтры для пользователей
            filters = [
                ("🟢 Активные", "filter_active", "active"),
                ("🔴 Заблокированные", "filter_blocked", "blocked"),
                ("📡 С VPN", "filter_with_vpn", "with_vpn"),
                ("❌ Без VPN", "filter_without_vpn", "without_vpn"),
                ("💰 С покупками", "filter_with_purchases", "with_purchases"),
                ("🎁 Только тест", "filter_trial_only", "trial_only")
            ]
        elif self.filter_type == "payments":
            # Фильтры для платежей
            filters = [
                ("✅ Успешные", "filter_completed", "completed"),
                ("⏳ В обработке", "filter_pending", "pending"),
                ("❌ Неудачные", "filter_failed", "failed"),
                ("💳 Карты", "filter_yookassa", "yookassa"),
                ("₿ Крипто", "filter_cryptomus", "cryptomus"),
                ("⭐ Stars", "filter_stars", "telegram_stars")
            ]
        else:
            filters = []
        
        for name, callback, filter_key in filters:
            if filter_key in self.active_filters:
                # Активный фильтр
                self.add_button(f"• {name} •", callback)
            else:
                self.add_button(name, callback)
        
        # Сброс фильтров
        if self.active_filters:
            self.add_button(_("🔄 Сбросить"), "reset_filters")
        
        # Применить фильтры
        self.add_button(_("✅ Применить"), "apply_filters")
        
        return self.build()


class AdminBulkActionsKeyboard(InlineKeyboard):
    """Клавиатура массовых действий"""
    
    def __init__(self, action_type: str, selected_count: int = 0):
        super().__init__(max_width=2)
        self.action_type = action_type
        self.selected_count = selected_count
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры массовых действий"""
        
        if self.action_type == "users":
            # Массовые действия для пользователей
            self.add_button(_("📢 Рассылка"), "bulk_message")
            self.add_button(_("🚫 Заблокировать"), "bulk_block")
            
            self.add_button(_("✅ Разблокировать"), "bulk_unblock")
            self.add_button(_("📤 Экспорт"), "bulk_export")
        elif self.action_type == "payments":
            # Массовые действия для платежей
            self.add_button(_("💸 Возврат"), "bulk_refund")
            self.add_button(_("📤 Экспорт"), "bulk_export")
        
        # Информация о выбранных элементах
        if self.selected_count > 0:
            self.add_button(f"📋 Выбрано: {self.selected_count}", "selected_info")
        
        # Отмена выбора
        self.add_button(_("❌ Отменить"), "cancel_bulk")
        
        return self.build()
