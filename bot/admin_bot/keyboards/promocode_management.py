"""
UnveilVPN Shop - Admin Bot Promocode Management Keyboards
Клавиатуры управления промокодами для административного бота
"""

from aiogram.types import InlineKeyboardButton, InlineKeyboardMarkup
from aiogram.utils.keyboard import InlineKeyboardBuilder
from typing import Dict, Any, List, Optional
from decimal import Decimal
from datetime import datetime

from bot.common.keyboards import BaseKeyboard


class PromocodeManagementKeyboard(BaseKeyboard):
    """Клавиатура управления промокодами"""
    
    def __init__(self, max_width: int = 2):
        super().__init__(max_width)
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры управления промокодами"""
        builder = InlineKeyboardBuilder()
        
        # Основные действия
        builder.add(InlineKeyboardButton(
            text="📋 Список промокодов",
            callback_data="list_promocodes"
        ))
        builder.add(InlineKeyboardButton(
            text="➕ Создать промокод",
            callback_data="create_promocode"
        ))
        
        # Генерация промокодов
        builder.add(InlineKeyboardButton(
            text="🎲 Сгенерировать",
            callback_data="generate_promocode"
        ))
        builder.add(InlineKeyboardButton(
            text="📊 Статистика",
            callback_data="promocode_stats"
        ))
        
        # Массовые операции
        builder.add(InlineKeyboardButton(
            text="🔄 Обновить все",
            callback_data="refresh_promocodes"
        ))
        builder.add(InlineKeyboardButton(
            text="🗑 Очистить истекшие",
            callback_data="cleanup_expired"
        ))
        
        # Быстрая генерация
        builder.add(InlineKeyboardButton(
            text="⚡ Скидка 10%",
            callback_data="quick_generate_10percent"
        ))
        builder.add(InlineKeyboardButton(
            text="⚡ Скидка 20%",
            callback_data="quick_generate_20percent"
        ))
        
        # Возврат в главное меню
        builder.add(InlineKeyboardButton(
            text="⬅️ Админ панель",
            callback_data="admin_menu"
        ))
        
        builder.adjust(2, 2, 2, 2, 1)
        return builder.as_markup()


class PromocodeListKeyboard(BaseKeyboard):
    """Клавиатура списка промокодов"""
    
    def __init__(self, max_width: int = 1):
        super().__init__(max_width)
    
    async def get_keyboard(self, promocodes: List[Dict[str, Any]], page: int = 0, per_page: int = 10) -> InlineKeyboardMarkup:
        """Получение клавиатуры списка промокодов"""
        builder = InlineKeyboardBuilder()
        
        # Промокоды на текущей странице
        start_idx = page * per_page
        end_idx = start_idx + per_page
        page_promocodes = promocodes[start_idx:end_idx]
        
        for promocode in page_promocodes:
            status_emoji = "✅" if promocode.get('is_active') else "❌"
            usage_info = f"{promocode.get('used_count', 0)}/{promocode.get('usage_limit', '∞')}"
            
            button_text = f"{status_emoji} {promocode['code']} ({usage_info})"
            callback_data = f"view_promocode_{promocode['id']}"
            
            builder.add(InlineKeyboardButton(
                text=button_text,
                callback_data=callback_data
            ))
        
        # Навигация по страницам
        nav_buttons = []
        if page > 0:
            nav_buttons.append(InlineKeyboardButton(
                text="⬅️ Назад",
                callback_data=f"promocodes_page_{page-1}"
            ))
        
        if end_idx < len(promocodes):
            nav_buttons.append(InlineKeyboardButton(
                text="➡️ Далее",
                callback_data=f"promocodes_page_{page+1}"
            ))
        
        if nav_buttons:
            builder.row(*nav_buttons)
        
        # Дополнительные действия
        builder.add(InlineKeyboardButton(
            text="🔄 Обновить",
            callback_data="refresh_promocodes_list"
        ))
        builder.add(InlineKeyboardButton(
            text="⬅️ Назад к управлению",
            callback_data="admin_promocodes"
        ))
        
        builder.adjust(1, len(nav_buttons) if nav_buttons else 0, 1, 1)
        return builder.as_markup()


class PromocodeDetailsKeyboard(BaseKeyboard):
    """Клавиатура деталей промокода"""
    
    def __init__(self, max_width: int = 2):
        super().__init__(max_width)
    
    async def get_keyboard(self, promocode_id: str, promocode_data: Dict[str, Any]) -> InlineKeyboardMarkup:
        """Получение клавиатуры деталей промокода"""
        builder = InlineKeyboardBuilder()
        
        # Основные действия
        if promocode_data.get('is_active'):
            builder.add(InlineKeyboardButton(
                text="⏸ Деактивировать",
                callback_data=f"deactivate_promocode_{promocode_id}"
            ))
        else:
            builder.add(InlineKeyboardButton(
                text="▶️ Активировать",
                callback_data=f"activate_promocode_{promocode_id}"
            ))
        
        builder.add(InlineKeyboardButton(
            text="✏️ Редактировать",
            callback_data=f"edit_promocode_{promocode_id}"
        ))
        
        # Статистика и использование
        builder.add(InlineKeyboardButton(
            text="📊 Статистика",
            callback_data=f"promocode_stats_{promocode_id}"
        ))
        builder.add(InlineKeyboardButton(
            text="👥 Пользователи",
            callback_data=f"promocode_users_{promocode_id}"
        ))
        
        # Дополнительные действия
        builder.add(InlineKeyboardButton(
            text="📋 Копировать код",
            callback_data=f"copy_promocode_{promocode_id}"
        ))
        builder.add(InlineKeyboardButton(
            text="🗑 Удалить",
            callback_data=f"delete_promocode_{promocode_id}"
        ))
        
        # Возврат
        builder.add(InlineKeyboardButton(
            text="⬅️ К списку",
            callback_data="list_promocodes"
        ))
        
        builder.adjust(2, 2, 2, 1)
        return builder.as_markup()


class PromocodeGenerationKeyboard(BaseKeyboard):
    """Клавиатура генерации промокодов"""
    
    def __init__(self, max_width: int = 2):
        super().__init__(max_width)
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры генерации промокодов"""
        builder = InlineKeyboardBuilder()
        
        # Типы скидок
        builder.add(InlineKeyboardButton(
            text="💯 Процентная скидка",
            callback_data="generate_percent_discount"
        ))
        builder.add(InlineKeyboardButton(
            text="💰 Фиксированная сумма",
            callback_data="generate_fixed_discount"
        ))
        
        # Дополнительные дни
        builder.add(InlineKeyboardButton(
            text="📅 Дополнительные дни",
            callback_data="generate_days_bonus"
        ))
        builder.add(InlineKeyboardButton(
            text="🎁 Бесплатный период",
            callback_data="generate_free_period"
        ))
        
        # Быстрые шаблоны
        builder.add(InlineKeyboardButton(
            text="⚡ Скидка 10% (100 исп.)",
            callback_data="quick_generate_10percent"
        ))
        builder.add(InlineKeyboardButton(
            text="⚡ Скидка 20% (50 исп.)",
            callback_data="quick_generate_20percent"
        ))
        
        builder.add(InlineKeyboardButton(
            text="⚡ Скидка 50% (10 исп.)",
            callback_data="quick_generate_50percent"
        ))
        builder.add(InlineKeyboardButton(
            text="⚡ +7 дней (20 исп.)",
            callback_data="quick_generate_7days"
        ))
        
        # Возврат
        builder.add(InlineKeyboardButton(
            text="⬅️ Назад к управлению",
            callback_data="admin_promocodes"
        ))
        
        builder.adjust(2, 2, 2, 2, 1)
        return builder.as_markup()


class PromocodeStatsKeyboard(BaseKeyboard):
    """Клавиатура статистики промокодов"""
    
    def __init__(self, max_width: int = 2):
        super().__init__(max_width)
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры статистики промокодов"""
        builder = InlineKeyboardBuilder()
        
        # Периоды статистики
        builder.add(InlineKeyboardButton(
            text="📅 Сегодня",
            callback_data="promocode_stats_today"
        ))
        builder.add(InlineKeyboardButton(
            text="📅 Неделя",
            callback_data="promocode_stats_week"
        ))
        
        builder.add(InlineKeyboardButton(
            text="📅 Месяц",
            callback_data="promocode_stats_month"
        ))
        builder.add(InlineKeyboardButton(
            text="📅 Все время",
            callback_data="promocode_stats_all"
        ))
        
        # Детальная статистика
        builder.add(InlineKeyboardButton(
            text="📊 По типам скидок",
            callback_data="promocode_stats_by_type"
        ))
        builder.add(InlineKeyboardButton(
            text="👥 По пользователям",
            callback_data="promocode_stats_by_users"
        ))
        
        # Экспорт
        builder.add(InlineKeyboardButton(
            text="📤 Экспорт CSV",
            callback_data="export_promocode_stats"
        ))
        builder.add(InlineKeyboardButton(
            text="🔄 Обновить",
            callback_data="refresh_promocode_stats"
        ))
        
        # Возврат
        builder.add(InlineKeyboardButton(
            text="⬅️ Назад к управлению",
            callback_data="admin_promocodes"
        ))
        
        builder.adjust(2, 2, 2, 2, 1)
        return builder.as_markup()
