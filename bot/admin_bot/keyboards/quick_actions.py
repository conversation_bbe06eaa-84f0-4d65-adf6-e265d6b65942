"""
UnveilVPN Shop - Admin Bot Quick Actions Keyboards
Клавиатуры быстрых действий для административного бота
"""

from aiogram.types import InlineKeyboardButton, InlineKeyboardMarkup
from aiogram.utils.keyboard import InlineKeyboardBuilder
from typing import Dict, Any

from bot.common.keyboards import BaseKeyboard


class QuickStatsKeyboard(BaseKeyboard):
    """Клавиатура быстрой статистики"""
    
    def __init__(self, max_width: int = 2):
        super().__init__(max_width)
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры быстрой статистики"""
        builder = InlineKeyboardBuilder()
        
        builder.add(InlineKeyboardButton(
            text="📊 Сегодня",
            callback_data="quick_stats:today"
        ))
        builder.add(InlineKeyboardButton(
            text="📈 Неделя",
            callback_data="quick_stats:week"
        ))
        builder.add(InlineKeyboardButton(
            text="📉 Месяц",
            callback_data="quick_stats:month"
        ))
        builder.add(InlineKeyboardButton(
            text="🔄 Обновить",
            callback_data="quick_stats:refresh"
        ))
        builder.add(InlineKeyboardButton(
            text="🔙 Назад",
            callback_data="admin_menu:main"
        ))
        
        builder.adjust(2, 2, 1)
        return builder.as_markup()


class SystemStatusKeyboard(BaseKeyboard):
    """Клавиатура статуса системы"""
    
    def __init__(self, max_width: int = 2):
        super().__init__(max_width)
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры статуса системы"""
        builder = InlineKeyboardBuilder()
        
        builder.add(InlineKeyboardButton(
            text="🖥️ Сервер",
            callback_data="system_status:server"
        ))
        builder.add(InlineKeyboardButton(
            text="🗄️ База данных",
            callback_data="system_status:database"
        ))
        builder.add(InlineKeyboardButton(
            text="🤖 Боты",
            callback_data="system_status:bots"
        ))
        builder.add(InlineKeyboardButton(
            text="🌐 API",
            callback_data="system_status:api"
        ))
        builder.add(InlineKeyboardButton(
            text="🔄 Обновить",
            callback_data="system_status:refresh"
        ))
        builder.add(InlineKeyboardButton(
            text="🔙 Назад",
            callback_data="admin_menu:main"
        ))
        
        builder.adjust(2, 2, 1, 1)
        return builder.as_markup()


class RecentActivityKeyboard(BaseKeyboard):
    """Клавиатура последней активности"""
    
    def __init__(self, max_width: int = 2):
        super().__init__(max_width)
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры последней активности"""
        builder = InlineKeyboardBuilder()
        
        builder.add(InlineKeyboardButton(
            text="👥 Пользователи",
            callback_data="recent_activity:users"
        ))
        builder.add(InlineKeyboardButton(
            text="💰 Платежи",
            callback_data="recent_activity:payments"
        ))
        builder.add(InlineKeyboardButton(
            text="🎫 Подписки",
            callback_data="recent_activity:subscriptions"
        ))
        builder.add(InlineKeyboardButton(
            text="❌ Ошибки",
            callback_data="recent_activity:errors"
        ))
        builder.add(InlineKeyboardButton(
            text="🔄 Обновить",
            callback_data="recent_activity:refresh"
        ))
        builder.add(InlineKeyboardButton(
            text="🔙 Назад",
            callback_data="admin_menu:main"
        ))
        
        builder.adjust(2, 2, 1, 1)
        return builder.as_markup()


class ConfirmRestartKeyboard(BaseKeyboard):
    """Клавиатура подтверждения перезапуска"""
    
    def __init__(self, max_width: int = 2):
        super().__init__(max_width)
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры подтверждения перезапуска"""
        builder = InlineKeyboardBuilder()
        
        builder.add(InlineKeyboardButton(
            text="✅ Подтвердить",
            callback_data="confirm_restart:yes"
        ))
        builder.add(InlineKeyboardButton(
            text="❌ Отмена",
            callback_data="confirm_restart:no"
        ))
        
        builder.adjust(2)
        return builder.as_markup()
    
    @staticmethod
    def get_restart_confirmation(service_name: str) -> InlineKeyboardMarkup:
        """Получение клавиатуры подтверждения перезапуска конкретного сервиса"""
        builder = InlineKeyboardBuilder()
        
        builder.add(InlineKeyboardButton(
            text="✅ Да, перезапустить",
            callback_data=f"restart_service:{service_name}:confirm"
        ))
        builder.add(InlineKeyboardButton(
            text="❌ Отмена",
            callback_data="admin_menu:main"
        ))
        
        builder.adjust(1)
        return builder.as_markup()
