"""
Клавиатуры для управления реферальной системой в админ боте
"""

from typing import Dict, Any, List, Optional
from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton
from aiogram.utils.keyboard import InlineKeyboardBuilder

from ...common.keyboards import InlineKeyboard


class ReferralManagementKeyboard(InlineKeyboard):
    """Клавиатура управления реферальной системой"""
    
    def __init__(self, max_width: int = 2):
        super().__init__(max_width)
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры управления реферальной системой"""
        builder = InlineKeyboardBuilder()
        
        # Основные действия
        builder.add(InlineKeyboardButton(
            text="📊 Статистика",
            callback_data="referral_stats"
        ))
        builder.add(InlineKeyboardButton(
            text="👥 Рефералы",
            callback_data="referral_list"
        ))
        
        # Настройки
        builder.add(InlineKeyboardButton(
            text="⚙️ Настройки",
            callback_data="referral_settings"
        ))
        builder.add(InlineKeyboardButton(
            text="💰 Комиссии",
            callback_data="referral_commissions"
        ))
        
        # Управление
        builder.add(InlineKeyboardButton(
            text="🎁 Бонусы",
            callback_data="referral_bonuses"
        ))
        builder.add(InlineKeyboardButton(
            text="🏆 Уровни",
            callback_data="referral_levels"
        ))
        
        # Отчеты
        builder.add(InlineKeyboardButton(
            text="📈 Отчеты",
            callback_data="referral_reports"
        ))
        builder.add(InlineKeyboardButton(
            text="💸 Выплаты",
            callback_data="referral_payouts"
        ))
        
        builder.add(InlineKeyboardButton(
            text="⬅️ Главное меню",
            callback_data="admin_menu"
        ))
        
        builder.adjust(2, 2, 2, 2, 1)
        return builder.as_markup()


class ReferralStatsKeyboard(InlineKeyboard):
    """Клавиатура статистики реферальной системы"""
    
    def __init__(self, max_width: int = 2):
        super().__init__(max_width)
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры статистики"""
        builder = InlineKeyboardBuilder()
        
        # Периоды
        builder.add(InlineKeyboardButton(
            text="📅 Сегодня",
            callback_data="referral_stats_today"
        ))
        builder.add(InlineKeyboardButton(
            text="📅 Неделя",
            callback_data="referral_stats_week"
        ))
        
        builder.add(InlineKeyboardButton(
            text="📅 Месяц",
            callback_data="referral_stats_month"
        ))
        builder.add(InlineKeyboardButton(
            text="📅 Год",
            callback_data="referral_stats_year"
        ))
        
        # Детализация
        builder.add(InlineKeyboardButton(
            text="👤 По пользователям",
            callback_data="referral_stats_users"
        ))
        builder.add(InlineKeyboardButton(
            text="💰 По доходам",
            callback_data="referral_stats_revenue"
        ))
        
        # Экспорт
        builder.add(InlineKeyboardButton(
            text="📊 Экспорт",
            callback_data="referral_stats_export"
        ))
        builder.add(InlineKeyboardButton(
            text="🔄 Обновить",
            callback_data="referral_stats_refresh"
        ))
        
        builder.add(InlineKeyboardButton(
            text="⬅️ Рефералы",
            callback_data="referral_management"
        ))
        
        builder.adjust(2, 2, 2, 2, 1)
        return builder.as_markup()


class ReferralSettingsKeyboard(InlineKeyboard):
    """Клавиатура настроек реферальной системы"""
    
    def __init__(self, max_width: int = 2):
        super().__init__(max_width)
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры настроек"""
        builder = InlineKeyboardBuilder()
        
        # Основные настройки
        builder.add(InlineKeyboardButton(
            text="🔧 Общие",
            callback_data="referral_settings_general"
        ))
        builder.add(InlineKeyboardButton(
            text="💰 Комиссии",
            callback_data="referral_settings_commissions"
        ))
        
        # Уровни и бонусы
        builder.add(InlineKeyboardButton(
            text="🏆 Уровни",
            callback_data="referral_settings_levels"
        ))
        builder.add(InlineKeyboardButton(
            text="🎁 Бонусы",
            callback_data="referral_settings_bonuses"
        ))
        
        # Ограничения
        builder.add(InlineKeyboardButton(
            text="⏰ Лимиты",
            callback_data="referral_settings_limits"
        ))
        builder.add(InlineKeyboardButton(
            text="🚫 Блокировки",
            callback_data="referral_settings_blocks"
        ))
        
        # Уведомления
        builder.add(InlineKeyboardButton(
            text="📱 Уведомления",
            callback_data="referral_settings_notifications"
        ))
        builder.add(InlineKeyboardButton(
            text="📧 Email",
            callback_data="referral_settings_email"
        ))
        
        # Действия
        builder.add(InlineKeyboardButton(
            text="💾 Сохранить",
            callback_data="referral_settings_save"
        ))
        builder.add(InlineKeyboardButton(
            text="🔄 Сбросить",
            callback_data="referral_settings_reset"
        ))
        
        builder.add(InlineKeyboardButton(
            text="⬅️ Рефералы",
            callback_data="referral_management"
        ))
        
        builder.adjust(2, 2, 2, 2, 2, 1)
        return builder.as_markup()


class ReferralListKeyboard(InlineKeyboard):
    """Клавиатура списка рефералов"""
    
    def __init__(self, max_width: int = 2):
        super().__init__(max_width)
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры списка рефералов"""
        builder = InlineKeyboardBuilder()
        
        # Фильтры
        builder.add(InlineKeyboardButton(
            text="🔍 Поиск",
            callback_data="referral_search"
        ))
        builder.add(InlineKeyboardButton(
            text="📊 Фильтры",
            callback_data="referral_filters"
        ))
        
        # Сортировка
        builder.add(InlineKeyboardButton(
            text="📈 По доходу",
            callback_data="referral_sort_revenue"
        ))
        builder.add(InlineKeyboardButton(
            text="👥 По рефералам",
            callback_data="referral_sort_referrals"
        ))
        
        # Действия
        builder.add(InlineKeyboardButton(
            text="📤 Экспорт",
            callback_data="referral_export"
        ))
        builder.add(InlineKeyboardButton(
            text="📧 Рассылка",
            callback_data="referral_broadcast"
        ))
        
        # Навигация
        builder.add(InlineKeyboardButton(
            text="⬅️ Предыдущая",
            callback_data="referral_page_prev"
        ))
        builder.add(InlineKeyboardButton(
            text="➡️ Следующая",
            callback_data="referral_page_next"
        ))
        
        builder.add(InlineKeyboardButton(
            text="⬅️ Рефералы",
            callback_data="referral_management"
        ))
        
        builder.adjust(2, 2, 2, 2, 1)
        return builder.as_markup()


class ReferralUserKeyboard(InlineKeyboard):
    """Клавиатура управления конкретным рефералом"""
    
    def __init__(self, user_id: int, max_width: int = 2):
        super().__init__(max_width)
        self.user_id = user_id
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры управления рефералом"""
        builder = InlineKeyboardBuilder()
        
        # Информация
        builder.add(InlineKeyboardButton(
            text="👤 Профиль",
            callback_data=f"referral_user_profile_{self.user_id}"
        ))
        builder.add(InlineKeyboardButton(
            text="📊 Статистика",
            callback_data=f"referral_user_stats_{self.user_id}"
        ))
        
        # Управление
        builder.add(InlineKeyboardButton(
            text="💰 Выплаты",
            callback_data=f"referral_user_payouts_{self.user_id}"
        ))
        builder.add(InlineKeyboardButton(
            text="🎁 Бонусы",
            callback_data=f"referral_user_bonuses_{self.user_id}"
        ))
        
        # Действия
        builder.add(InlineKeyboardButton(
            text="✉️ Сообщение",
            callback_data=f"referral_user_message_{self.user_id}"
        ))
        builder.add(InlineKeyboardButton(
            text="🚫 Заблокировать",
            callback_data=f"referral_user_block_{self.user_id}"
        ))
        
        builder.add(InlineKeyboardButton(
            text="⬅️ Список",
            callback_data="referral_list"
        ))
        
        builder.adjust(2, 2, 2, 1)
        return builder.as_markup()
