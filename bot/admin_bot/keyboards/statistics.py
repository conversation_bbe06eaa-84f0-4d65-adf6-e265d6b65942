"""
UnveilVPN Shop - Statistics Keyboards
Клавиатуры для статистики в админ-боте
"""

from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta

from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton
from aiogram.utils.keyboard import InlineKeyboardBuilder
from aiogram.utils.i18n import gettext as _

from bot.common.keyboards import InlineKeyboard


class StatisticsKeyboard(InlineKeyboard):
    """Главная клавиатура статистики"""
    
    def __init__(self):
        super().__init__(max_width=2)
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры статистики"""
        
        # Основные разделы статистики
        self.add_button(_("👥 Пользователи"), "stats_users")
        self.add_button(_("💰 Продажи"), "stats_sales")
        
        self.add_button(_("📊 Тарифы"), "stats_tariffs")
        self.add_button(_("🎫 Промокоды"), "stats_promocodes")
        
        self.add_button(_("👥 Рефералы"), "stats_referrals")
        self.add_button(_("🎧 Поддержка"), "stats_support")
        
        self.add_button(_("📈 Аналитика"), "stats_analytics")
        self.add_button(_("📋 Отчеты"), "stats_reports")
        
        # Возврат в главное меню
        self.add_button(_("🔙 Главное меню"), "admin_main_menu")
        
        return self.build()


def get_users_stats_keyboard() -> InlineKeyboardMarkup:
    """Клавиатура статистики пользователей"""
    builder = InlineKeyboardBuilder()
    
    builder.row(
        InlineKeyboardButton(
            text="📊 Общая статистика",
            callback_data="users_stats_general"
        )
    )
    
    builder.row(
        InlineKeyboardButton(
            text="📈 Рост по дням",
            callback_data="users_stats_growth"
        )
    )
    
    builder.row(
        InlineKeyboardButton(
            text="🌍 По странам",
            callback_data="users_stats_countries"
        )
    )
    
    builder.row(
        InlineKeyboardButton(
            text="📱 По устройствам",
            callback_data="users_stats_devices"
        )
    )
    
    builder.row(
        InlineKeyboardButton(
            text="⏰ Активность",
            callback_data="users_stats_activity"
        )
    )
    
    builder.row(
        InlineKeyboardButton(
            text="🔙 Статистика",
            callback_data="admin_statistics"
        )
    )
    
    return builder.as_markup()


def get_sales_stats_keyboard() -> InlineKeyboardMarkup:
    """Клавиатура статистики продаж"""
    builder = InlineKeyboardBuilder()
    
    builder.row(
        InlineKeyboardButton(
            text="💰 Общий доход",
            callback_data="sales_stats_revenue"
        )
    )
    
    builder.row(
        InlineKeyboardButton(
            text="📊 По тарифам",
            callback_data="sales_stats_tariffs"
        )
    )
    
    builder.row(
        InlineKeyboardButton(
            text="💳 По способам оплаты",
            callback_data="sales_stats_methods"
        )
    )
    
    builder.row(
        InlineKeyboardButton(
            text="📈 Динамика продаж",
            callback_data="sales_stats_dynamics"
        )
    )
    
    builder.row(
        InlineKeyboardButton(
            text="🔄 Возвраты",
            callback_data="sales_stats_refunds"
        )
    )
    
    builder.row(
        InlineKeyboardButton(
            text="🔙 Статистика",
            callback_data="admin_statistics"
        )
    )
    
    return builder.as_markup()


def get_period_selection_keyboard(stat_type: str) -> InlineKeyboardMarkup:
    """Клавиатура выбора периода для статистики"""
    builder = InlineKeyboardBuilder()
    
    periods = [
        ("📅 Сегодня", "today"),
        ("📅 Вчера", "yesterday"),
        ("📅 7 дней", "week"),
        ("📅 30 дней", "month"),
        ("📅 90 дней", "quarter"),
        ("📅 Год", "year"),
        ("📅 Все время", "all_time"),
        ("📅 Свой период", "custom")
    ]
    
    for period_name, period_code in periods:
        builder.row(
            InlineKeyboardButton(
                text=period_name,
                callback_data=f"stats_{stat_type}_period_{period_code}"
            )
        )
    
    builder.row(
        InlineKeyboardButton(
            text="🔙 Назад",
            callback_data="admin_statistics"
        )
    )
    
    return builder.as_markup()


def get_analytics_keyboard() -> InlineKeyboardMarkup:
    """Клавиатура аналитики"""
    builder = InlineKeyboardBuilder()
    
    builder.row(
        InlineKeyboardButton(
            text="📊 Дашборд",
            callback_data="analytics_dashboard"
        )
    )
    
    builder.row(
        InlineKeyboardButton(
            text="🎯 Конверсия",
            callback_data="analytics_conversion"
        )
    )
    
    builder.row(
        InlineKeyboardButton(
            text="💡 Инсайты",
            callback_data="analytics_insights"
        )
    )
    
    builder.row(
        InlineKeyboardButton(
            text="📈 Прогнозы",
            callback_data="analytics_forecasts"
        )
    )
    
    builder.row(
        InlineKeyboardButton(
            text="🔍 Детальный анализ",
            callback_data="analytics_detailed"
        )
    )
    
    builder.row(
        InlineKeyboardButton(
            text="🔙 Статистика",
            callback_data="admin_statistics"
        )
    )
    
    return builder.as_markup()


def get_reports_keyboard() -> InlineKeyboardMarkup:
    """Клавиатура отчетов"""
    builder = InlineKeyboardBuilder()
    
    builder.row(
        InlineKeyboardButton(
            text="📋 Ежедневный отчет",
            callback_data="report_daily"
        )
    )
    
    builder.row(
        InlineKeyboardButton(
            text="📋 Недельный отчет",
            callback_data="report_weekly"
        )
    )
    
    builder.row(
        InlineKeyboardButton(
            text="📋 Месячный отчет",
            callback_data="report_monthly"
        )
    )
    
    builder.row(
        InlineKeyboardButton(
            text="📋 Финансовый отчет",
            callback_data="report_financial"
        )
    )
    
    builder.row(
        InlineKeyboardButton(
            text="📋 Пользовательский отчет",
            callback_data="report_custom"
        )
    )
    
    builder.row(
        InlineKeyboardButton(
            text="📤 Экспорт данных",
            callback_data="export_data"
        )
    )
    
    builder.row(
        InlineKeyboardButton(
            text="🔙 Статистика",
            callback_data="admin_statistics"
        )
    )
    
    return builder.as_markup()


def get_export_format_keyboard() -> InlineKeyboardMarkup:
    """Клавиатура выбора формата экспорта"""
    builder = InlineKeyboardBuilder()
    
    builder.row(
        InlineKeyboardButton(
            text="📊 Excel (XLSX)",
            callback_data="export_xlsx"
        )
    )
    
    builder.row(
        InlineKeyboardButton(
            text="📄 CSV",
            callback_data="export_csv"
        )
    )
    
    builder.row(
        InlineKeyboardButton(
            text="📋 JSON",
            callback_data="export_json"
        )
    )
    
    builder.row(
        InlineKeyboardButton(
            text="📑 PDF отчет",
            callback_data="export_pdf"
        )
    )
    
    builder.row(
        InlineKeyboardButton(
            text="🔙 Отчеты",
            callback_data="stats_reports"
        )
    )
    
    return builder.as_markup()


def get_chart_type_keyboard(stat_type: str) -> InlineKeyboardMarkup:
    """Клавиатура выбора типа графика"""
    builder = InlineKeyboardBuilder()
    
    builder.row(
        InlineKeyboardButton(
            text="📈 Линейный график",
            callback_data=f"chart_{stat_type}_line"
        )
    )
    
    builder.row(
        InlineKeyboardButton(
            text="📊 Столбчатая диаграмма",
            callback_data=f"chart_{stat_type}_bar"
        )
    )
    
    builder.row(
        InlineKeyboardButton(
            text="🥧 Круговая диаграмма",
            callback_data=f"chart_{stat_type}_pie"
        )
    )
    
    builder.row(
        InlineKeyboardButton(
            text="📉 Область",
            callback_data=f"chart_{stat_type}_area"
        )
    )
    
    builder.row(
        InlineKeyboardButton(
            text="🔙 Назад",
            callback_data=f"stats_{stat_type}"
        )
    )
    
    return builder.as_markup()


def get_refresh_stats_keyboard(stat_type: str, period: str) -> InlineKeyboardMarkup:
    """Клавиатура обновления статистики"""
    builder = InlineKeyboardBuilder()
    
    builder.row(
        InlineKeyboardButton(
            text="🔄 Обновить",
            callback_data=f"refresh_stats_{stat_type}_{period}"
        )
    )
    
    builder.row(
        InlineKeyboardButton(
            text="📤 Поделиться",
            callback_data=f"share_stats_{stat_type}_{period}"
        )
    )
    
    builder.row(
        InlineKeyboardButton(
            text="💾 Сохранить",
            callback_data=f"save_stats_{stat_type}_{period}"
        )
    )
    
    builder.row(
        InlineKeyboardButton(
            text="🔙 Назад",
            callback_data=f"stats_{stat_type}"
        )
    )
    
    return builder.as_markup()
