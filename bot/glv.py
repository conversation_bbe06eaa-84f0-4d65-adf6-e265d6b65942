"""
UnveilVPN Shop - Глобальная конфигурация
Централизованная система конфигурации для всех компонентов проекта
"""

import os
import logging
from typing import Optional, Dict, Any
from aiogram import Bot, Dispatcher
from dotenv import load_dotenv

load_dotenv()

# Настройка логирования
logging.basicConfig(
    level=getattr(logging, os.environ.get('LOG_LEVEL', 'INFO')),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


class Config:
    """Класс конфигурации с валидацией и типизацией"""

    def __init__(self):
        self._config = self._load_config()
        self._validate_config()

    def _load_config(self) -> Dict[str, Any]:
        """Загрузка конфигурации из переменных окружения"""
        return {
            # Bot Configuration
            'BOT_TYPE': os.environ.get('BOT_TYPE', 'client'),
            'CLIENT_BOT_TOKEN': os.environ.get('CLIENT_BOT_TOKEN'),
            'ADMIN_BOT_TOKEN': os.environ.get('ADMIN_BOT_TOKEN'),
            'SUPPORT_BOT_TOKEN': os.environ.get('SUPPORT_BOT_TOKEN'),

            # Shop Configuration
            'SHOP_NAME': os.environ.get('SHOP_NAME', 'UnveilVPN'),
            'PROTOCOLS': os.environ.get('PROTOCOLS') or os.environ.get('REMNAWAVE_PROTOCOLS', 'vless vmess trojan shadowsocks'),
            'PROTOCOLS_LIST': (os.environ.get('PROTOCOLS') or os.environ.get('REMNAWAVE_PROTOCOLS', 'vless vmess trojan shadowsocks')).split(),
            'TEST_PERIOD': os.environ.get('TEST_PERIOD', 'false').lower() == 'true' or os.environ.get('TEST_PERIOD_ENABLED', 'true').lower() == 'true',
            'PERIOD_LIMIT': int(os.environ.get('PERIOD_LIMIT') or os.environ.get('TEST_PERIOD_DURATION_HOURS', 168)),
            'ABOUT': os.environ.get('ABOUT', 'Надежный VPN сервис'),
            'RULES_LINK': os.environ.get('RULES_LINK'),
            'SUPPORT_LINK': os.environ.get('SUPPORT_LINK'),

            # PostgreSQL Database Configuration
            'DB_HOST': os.environ.get('DB_HOST', 'localhost'),
            'DB_PORT': int(os.environ.get('DB_PORT', 5432)),
            'DB_NAME': os.environ.get('DB_NAME', 'unveilvpn'),
            'DB_USER': os.environ.get('DB_USER', 'unveilvpn_user'),
            'DB_PASS': os.environ.get('DB_PASS'),
            'DB_URL': self._build_db_url(),

            # Redis Configuration
            'REDIS_HOST': os.environ.get('REDIS_HOST', 'localhost'),
            'REDIS_PORT': int(os.environ.get('REDIS_PORT', 6379)),
            'REDIS_PASSWORD': os.environ.get('REDIS_PASSWORD'),
            'REDIS_URL': self._build_redis_url(),

            # Payment Systems
            'YOOKASSA_TOKEN': os.environ.get('YOOKASSA_TOKEN'),
            'YOOKASSA_SHOPID': os.environ.get('YOOKASSA_SHOPID'),
            'CRYPTO_TOKEN': os.environ.get('CRYPTO_TOKEN'),
            'MERCHANT_UUID': os.environ.get('MERCHANT_UUID'),
            'TELEGRAM_STARS_ENABLED': os.environ.get('TELEGRAM_STARS_ENABLED', 'false').lower() == 'true',

            # VPN Panel Configuration (Remnawave)
            'REMNAWAVE_PANEL_URL': os.environ.get('REMNAWAVE_PANEL_URL'),
            'REMNAWAVE_API_KEY': os.environ.get('REMNAWAVE_API_KEY'),
            'REMNAWAVE_SUBSCRIPTION_URL': os.environ.get('REMNAWAVE_SUBSCRIPTION_URL'),
            'REMNAWAVE_PROTOCOLS': os.environ.get('REMNAWAVE_PROTOCOLS', 'vless vmess trojan shadowsocks').split(),



            # Webhook Configuration
            'WEBHOOK_URL': os.environ.get('WEBHOOK_URL'),
            'WEBHOOK_PORT': int(os.environ.get('WEBHOOK_PORT', 8080)),

            # Notifications
            'RENEW_NOTIFICATION_TIME': os.environ.get('RENEW_NOTIFICATION_TIME', '16:00'),
            'EMAIL': os.environ.get('EMAIL'),

            # Referral System
            'REFERRAL_LEVEL_1_PERCENT': float(os.environ.get('REFERRAL_LEVEL_1_PERCENT', 10)),
            'REFERRAL_LEVEL_2_PERCENT': float(os.environ.get('REFERRAL_LEVEL_2_PERCENT', 5)),
            'REFERRAL_LEVEL_3_PERCENT': float(os.environ.get('REFERRAL_LEVEL_3_PERCENT', 2)),
            'REFERRAL_MIN_PAYOUT': int(os.environ.get('REFERRAL_MIN_PAYOUT', 100)),

            # Security
            'JWT_SECRET': os.environ.get('JWT_SECRET'),
            'ENCRYPTION_KEY': os.environ.get('ENCRYPTION_KEY'),

            # Environment
            'ENVIRONMENT': os.environ.get('ENVIRONMENT', 'development'),
            'DEBUG': os.environ.get('DEBUG', 'false').lower() == 'true',
            'LOG_LEVEL': os.environ.get('LOG_LEVEL', 'INFO'),
        }

    def _build_db_url(self) -> str:
        """Построение URL для подключения к PostgreSQL"""
        host = os.environ.get('DB_HOST', 'localhost')
        port = os.environ.get('DB_PORT', 5432)
        name = os.environ.get('DB_NAME', 'unveilvpn')
        user = os.environ.get('DB_USER', 'unveilvpn_user')
        password = os.environ.get('DB_PASS', '')

        return f"postgresql+asyncpg://{user}:{password}@{host}:{port}/{name}"

    def _build_redis_url(self) -> str:
        """Построение URL для подключения к Redis"""
        host = os.environ.get('REDIS_HOST', 'localhost')
        port = os.environ.get('REDIS_PORT', 6379)
        password = os.environ.get('REDIS_PASSWORD', '')

        if password:
            return f"redis://:{password}@{host}:{port}/0"
        return f"redis://{host}:{port}/0"

    def _validate_config(self) -> None:
        """Валидация критически важных параметров конфигурации"""
        bot_type = self._config['BOT_TYPE']

        # Проверка токена бота в зависимости от типа
        if bot_type == 'client' and not self._config['CLIENT_BOT_TOKEN']:
            raise ValueError("CLIENT_BOT_TOKEN is required for client bot")
        elif bot_type == 'admin' and not self._config['ADMIN_BOT_TOKEN']:
            raise ValueError("ADMIN_BOT_TOKEN is required for admin bot")
        elif bot_type == 'support' and not self._config['SUPPORT_BOT_TOKEN']:
            raise ValueError("SUPPORT_BOT_TOKEN is required for support bot")

        # Проверка базы данных
        if not self._config['DB_PASS']:
            logger.warning("DB_PASS is not set, using empty password")

        # Проверка панели VPN (Remnawave)
        if not self._config['REMNAWAVE_PANEL_URL']:
            logger.warning("REMNAWAVE_PANEL_URL is not set")

    def get(self, key: str, default: Any = None) -> Any:
        """Получение значения конфигурации"""
        return self._config.get(key, default)

    def get_bot_token(self) -> str:
        """Получение токена бота в зависимости от типа"""
        bot_type = self._config['BOT_TYPE']

        if bot_type == 'client':
            return self._config['CLIENT_BOT_TOKEN']
        elif bot_type == 'admin':
            return self._config['ADMIN_BOT_TOKEN']
        elif bot_type == 'support':
            return self._config['SUPPORT_BOT_TOKEN']
        else:
            raise ValueError(f"Unknown bot type: {bot_type}")

    @property
    def is_development(self) -> bool:
        """Проверка режима разработки"""
        return self._config['ENVIRONMENT'] == 'development'

    @property
    def is_production(self) -> bool:
        """Проверка продакшен режима"""
        return self._config['ENVIRONMENT'] == 'production'


# Глобальный экземпляр конфигурации
config = Config()

# Глобальные объекты для ботов (будут инициализированы в main.py)
bot: Optional[Bot] = None
storage = None
dp: Optional[Dispatcher] = None

# Логгер для использования в других модулях
logger = logging.getLogger(__name__)