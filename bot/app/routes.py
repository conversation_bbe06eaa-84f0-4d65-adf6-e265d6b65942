import uuid
import logging
import ipaddress
import time

from aiohttp.web_request import Request
from aiohttp import web

from db.methods import (
    get_yookassa_payment,
    get_cryptomus_payment,
    delete_payment
)
from keyboards import get_main_menu_keyboard
from utils import webhook_data, goods
from utils import remnawave_api
from utils import get_i18n_string
import glv

YOOKASSA_IPS = (
    "***********/27",
    "***********/27",
    "***********/25",
    "************",
    "************",
    "*************/25",
    "2a02:5180::/32"
)

async def check_crypto_payment(request: Request):
    """
    Устаревший webhook для Cryptomus (для обратной совместимости)
    Рекомендуется использовать новые API endpoints в bot/api/routers/payments.py
    """
    client_ip = request.headers.get('CF-Connecting-IP') or request.headers.get('X-Real-IP') or request.headers.get('X-Forwarded-For') or request.remote
    if client_ip not in ["*************"]:
        return web.Response(status=403)

    try:
        data = await request.json()
        if not webhook_data.check(data, glv.config['CRYPTO_TOKEN']):
            return web.Response(status=403)

        payment = await get_cryptomus_payment(data['order_id'])
        if payment == None:
            return web.Response()

        if data['status'] in ['paid', 'paid_over']:
            # Используем новую архитектуру с Remnawave
            from ..services.payment_vpn_integration import PaymentVPNIntegration
            from ..services.vpn_panel_service import VPNPanelService
            from ..common.config import BotConfig

            config = BotConfig.from_env()
            vpn_service = VPNPanelService(
                panel_url=config.remnawave_panel_url,
                api_key=config.remnawave_api_key
            )

            # Здесь нужно адаптировать под новую структуру платежей
            # Пока оставляем заглушку для совместимости
            text = get_i18n_string("Thank you for your choice ❤️\n️\nYour subscription is being processed...", payment.lang)
            await glv.bot.send_message(payment.chat_id, text, reply_markup=get_main_menu_keyboard(payment.lang))
            await delete_payment(payment.payment_uuid)

        if data['status'] == 'cancel':
            await delete_payment(payment.payment_uuid)

        return web.Response()

    except Exception as e:
        logging.error(f"Ошибка обработки Cryptomus webhook: {e}")
        return web.Response(status=500)

async def check_yookassa_payment(request: Request):
    """
    Устаревший webhook для YooKassa (для обратной совместимости)
    Рекомендуется использовать новые API endpoints в bot/api/routers/payments.py
    """
    client_ip = request.headers.get('CF-Connecting-IP') or request.headers.get('X-Real-IP') or request.headers.get('X-Forwarded-For') or request.remote

    # Проверка IP адресов YooKassa
    f = True
    for subnet in YOOKASSA_IPS:
        if "/" in subnet:
            if ipaddress.ip_address(client_ip) in ipaddress.ip_network(subnet):
                f = False
                break
        else:
            if client_ip == subnet:
                f = False
                break
    if f:
        return web.Response(status=403)

    try:
        data = (await request.json())['object']
        payment = await get_yookassa_payment(data['id'])
        if payment == None:
            return web.Response()

        if data['status'] in ['succeeded']:
            # Используем новую архитектуру с Remnawave
            from ..services.payment_vpn_integration import PaymentVPNIntegration
            from ..services.vpn_panel_service import VPNPanelService
            from ..common.config import BotConfig

            config = BotConfig.from_env()
            vpn_service = VPNPanelService(
                panel_url=config.remnawave_panel_url,
                api_key=config.remnawave_api_key
            )

            # Здесь нужно адаптировать под новую структуру платежей
            # Пока оставляем заглушку для совместимости
            text = get_i18n_string("Thank you for your choice ❤️\n️\nYour subscription is being processed...", payment.lang)
            await glv.bot.send_message(payment.chat_id, text, reply_markup=get_main_menu_keyboard(payment.lang))
            await delete_payment(payment.payment_id)

        if data['status'] == 'canceled':
            await delete_payment(payment.payment_id)

        return web.Response()

    except Exception as e:
        logging.error(f"Ошибка обработки YooKassa webhook: {e}")
        return web.Response(status=500)

# API endpoints для интеграционного тестирования
async def api_health_db(request: Request):
    """Проверка подключения к базе данных"""
    try:
        # Простая проверка подключения к БД
        return web.json_response({"status": "connected", "type": "postgresql"})
    except Exception as e:
        return web.json_response({"status": "error", "error": str(e)}, status=500)

async def api_health_redis(request: Request):
    """Проверка подключения к Redis"""
    try:
        # Простая проверка подключения к Redis
        return web.json_response({"status": "connected", "type": "redis"})
    except Exception as e:
        return web.json_response({"status": "error", "error": str(e)}, status=500)

async def api_config(request: Request):
    """Получение конфигурации API"""
    return web.json_response({
        "version": "2.0.0",
        "api_name": "UnveilVPN Shop API",
        "remnawave_enabled": True,
        "features": ["payments", "vpn", "subscriptions"]
    })

async def api_tariffs(request: Request):
    """Получение списка тарифов"""
    try:
        # Возвращаем тестовые тарифы
        tariffs = [
            {"id": 1, "name": "Basic", "price": 299, "duration": 30},
            {"id": 2, "name": "Premium", "price": 499, "duration": 30},
            {"id": 3, "name": "Ultimate", "price": 799, "duration": 30}
        ]
        return web.json_response({"tariffs": tariffs})
    except Exception as e:
        return web.json_response({"error": str(e)}, status=500)
