# =============================================================================
# UnveilVPN Shop - Alembic Configuration for PostgreSQL
# =============================================================================

[alembic]
# path to migration scripts
script_location = migration

# template used to generate migration file names with timestamp
file_template = %%(year)d%%(month).2d%%(day).2d_%%(hour).2d%%(minute).2d_%%(rev)s_%%(slug)s

# sys.path path, will be prepended to sys.path if present.
prepend_sys_path = .

# timezone for migration timestamps
timezone = UTC

# max length of characters to apply to the "slug" field
truncate_slug_length = 40

# set to 'true' to run the environment during the 'revision' command
revision_environment = true

# set to 'true' to allow .pyc and .pyo files without a source .py file
sourceless = false

# version location specification
version_locations = %(here)s/migration/versions

# version path separator
version_path_separator = os

# set to 'true' to search source files recursively
recursive_version_locations = false

# the output encoding used when revision files are written
output_encoding = utf-8

# PostgreSQL connection URL (will be overridden by env.py from environment variables)
sqlalchemy.url = postgresql+asyncpg://unveilvpn_user:password@localhost:5432/unveilvpn

# Compare types for autogenerate
compare_type = true
compare_server_default = true

# Render schema changes
render_as_batch = false

# Include object names in autogenerate
include_object = true


[post_write_hooks]
# post_write_hooks defines scripts or Python functions that are run
# on newly generated revision scripts.  See the documentation for further
# detail and examples

# format using "black" - use the console_scripts runner, against the "black" entrypoint
# hooks = black
# black.type = console_scripts
# black.entrypoint = black
# black.options = -l 79 REVISION_SCRIPT_FILENAME

# Logging configuration
[loggers]
keys = root,sqlalchemy,alembic

[handlers]
keys = console

[formatters]
keys = generic

[logger_root]
level = WARN
handlers = console
qualname =

[logger_sqlalchemy]
level = WARN
handlers =
qualname = sqlalchemy.engine

[logger_alembic]
level = INFO
handlers =
qualname = alembic

[handler_console]
class = StreamHandler
args = (sys.stderr,)
level = NOTSET
formatter = generic

[formatter_generic]
format = %(levelname)-5.5s [%(name)s] %(message)s
datefmt = %H:%M:%S
