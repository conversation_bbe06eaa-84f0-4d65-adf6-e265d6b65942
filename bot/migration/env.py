"""
UnveilVPN Shop - Alembic Environment Configuration
Настройка окружения для миграций PostgreSQL с async SQLAlchemy
"""

import asyncio
import os
import sys
import sys
sys.path.append('/app')

from logging.config import fileConfig
from typing import Any

from sqlalchemy import engine_from_config, pool
from sqlalchemy.engine import Connection
from sqlalchemy.ext.asyncio import create_async_engine

from alembic import context

# Добавляем путь к модулям проекта
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Импорт моделей и базы
from bot.db.models import Base
from bot.db import configure_relationships

# Настройка связей между моделями
configure_relationships()

# this is the Alembic Config object, which provides
# access to the values within the .ini file in use.
config = context.config

# Interpret the config file for Python logging.
# This line sets up loggers basically.
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

# add your model's MetaData object here
# for 'autogenerate' support
target_metadata = Base.metadata

# other values from the config, defined by the needs of env.py,
# can be acquired:
# my_important_option = config.get_main_option("my_important_option")
# ... etc.


def get_database_url() -> str:
    """Получение URL базы данных из переменных окружения"""
    db_host = os.environ.get('DB_HOST', 'localhost')
    db_port = os.environ.get('DB_PORT', '5432')
    db_name = os.environ.get('DB_NAME', 'unveilvpn')
    db_user = os.environ.get('DB_USER', 'unveilvpn_user')
    db_pass = os.environ.get('DB_PASS', '')

    # Для миграций используем синхронный драйвер
    return f"postgresql+psycopg2://{db_user}:{db_pass}@{db_host}:{db_port}/{db_name}"


def get_async_database_url() -> str:
    """Получение async URL базы данных"""
    db_host = os.environ.get('DB_HOST', 'localhost')
    db_port = os.environ.get('DB_PORT', '5432')
    db_name = os.environ.get('DB_NAME', 'unveilvpn')
    db_user = os.environ.get('DB_USER', 'unveilvpn_user')
    db_pass = os.environ.get('DB_PASS', '')

    return f"postgresql+asyncpg://{db_user}:{db_pass}@{db_host}:{db_port}/{db_name}"


def include_object(object: Any, name: str, type_: str, reflected: bool, compare_to: Any) -> bool:
    """
    Функция для фильтрации объектов при автогенерации миграций
    """
    # Исключаем системные таблицы PostgreSQL
    if type_ == "table" and name in [
        "spatial_ref_sys",  # PostGIS
        "geography_columns", "geometry_columns",  # PostGIS
        "raster_columns", "raster_overviews",  # PostGIS
    ]:
        return False

    # Исключаем временные таблицы
    if type_ == "table" and name.startswith("temp_"):
        return False

    return True


def compare_type(context: Any, inspected_column: Any, metadata_column: Any,
                inspected_type: Any, metadata_type: Any) -> bool:
    """
    Функция для сравнения типов при автогенерации
    """
    # Игнорируем различия в размерах VARCHAR
    if hasattr(inspected_type, 'length') and hasattr(metadata_type, 'length'):
        if inspected_type.length != metadata_type.length:
            return False

    return True

# add your model's MetaData object here
# for 'autogenerate' support
# from myapp import mymodel
# target_metadata = mymodel.Base.metadata

# other values from the config, defined by the needs of env.py,
# can be acquired:
# my_important_option = config.get_main_option("my_important_option")
# ... etc.


def run_migrations_offline() -> None:
    """
    Run migrations in 'offline' mode.

    This configures the context with just a URL and not an Engine,
    though an Engine is acceptable here as well. By skipping the Engine creation
    we don't even need a DBAPI to be available.
    """
    url = get_database_url()
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
        compare_type=True,
        compare_server_default=True,
        include_object=include_object,
    )

    with context.begin_transaction():
        context.run_migrations()


def run_migrations_online() -> None:
    """
    Run migrations in 'online' mode.

    In this scenario we need to create an Engine and associate a connection
    with the context.
    """
    # Переопределяем URL из переменных окружения
    configuration = config.get_section(config.config_ini_section, {})
    configuration["sqlalchemy.url"] = get_database_url()

    connectable = engine_from_config(
        configuration,
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,
    )

    with connectable.connect() as connection:
        context.configure(
            connection=connection,
            target_metadata=target_metadata,
            compare_type=True,
            compare_server_default=True,
            include_object=include_object,
            render_as_batch=False,  # PostgreSQL поддерживает DDL транзакции
        )

        with context.begin_transaction():
            context.run_migrations()


def run_async_migrations() -> None:
    """
    Run migrations in async mode.

    Для использования с async SQLAlchemy движком.
    """
    async def do_run_migrations(connection: Connection) -> None:
        context.configure(
            connection=connection,
            target_metadata=target_metadata,
            compare_type=True,
            compare_server_default=True,
            include_object=include_object,
        )

        async with context.begin_transaction():
            await context.run_migrations()

    async def run_migrations() -> None:
        connectable = create_async_engine(
            get_async_database_url(),
            poolclass=pool.NullPool,
        )

        async with connectable.connect() as connection:
            await do_run_migrations(connection)

        await connectable.dispose()

    asyncio.run(run_migrations())


# Определяем режим выполнения миграций
if context.is_offline_mode():
    run_migrations_offline()
else:
    # Используем синхронный режим для совместимости с Alembic
    run_migrations_online()
