"""
UnveilVPN Shop - Tariff Service
Сервис для работы с тарифами
"""

import logging
from typing import List, Optional, Dict, Any
from uuid import UUID
from decimal import Decimal

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete, func, and_, or_
from sqlalchemy.orm import selectinload

from ..db.models import Tariffs
from ..common.exceptions import NotFoundError, ValidationError, BusinessLogicError


class TariffService:
    """
    Сервис для работы с тарифами
    """
    
    def __init__(self, db_session: AsyncSession):
        self.db_session = db_session
        self.logger = logging.getLogger(self.__class__.__name__)
    
    async def get_all_tariffs(
        self, 
        active_only: bool = True,
        include_test: bool = True,
        sort_by: str = "sort_order"
    ) -> List[Tariffs]:
        """Получение всех тарифов"""
        async with self.db_session as session:
            try:
                query = select(Tariffs)
                
                # Фильтры
                filters = []
                if active_only:
                    filters.append(Tariffs.is_active == True)
                
                if not include_test:
                    filters.append(Tariffs.is_test_available == False)
                
                if filters:
                    query = query.where(and_(*filters))
                
                # Сортировка
                if sort_by == "sort_order":
                    query = query.order_by(Tariffs.sort_order.asc(), Tariffs.created_at.desc())
                elif sort_by == "price_asc":
                    query = query.order_by(Tariffs.prices['rub'].astext.cast(Decimal).asc())
                elif sort_by == "price_desc":
                    query = query.order_by(Tariffs.prices['rub'].astext.cast(Decimal).desc())
                elif sort_by == "duration":
                    query = query.order_by(Tariffs.duration_days.asc())
                elif sort_by == "popular":
                    query = query.order_by(Tariffs.is_popular.desc(), Tariffs.sort_order.asc())
                else:
                    query = query.order_by(Tariffs.created_at.desc())
                
                result = await session.execute(query)
                return result.scalars().all()
                
            except Exception as e:
                self.logger.error(f"Ошибка получения тарифов: {e}")
                raise BusinessLogicError(f"Не удалось получить список тарифов: {e}")
    
    async def get_tariff_by_id(self, tariff_id: UUID) -> Optional[Tariffs]:
        """Получение тарифа по ID"""
        async with self.db_session as session:
            try:
                query = select(Tariffs).where(Tariffs.id == tariff_id)
                result = await session.execute(query)
                return result.scalar_one_or_none()
                
            except Exception as e:
                self.logger.error(f"Ошибка получения тарифа {tariff_id}: {e}")
                raise BusinessLogicError(f"Не удалось получить тариф: {e}")
    
    async def get_popular_tariffs(self, limit: int = 3) -> List[Tariffs]:
        """Получение популярных тарифов"""
        try:
            query = (
                select(Tariffs)
                .where(and_(Tariffs.is_active == True, Tariffs.is_popular == True))
                .order_by(Tariffs.sort_order.asc())
                .limit(limit)
            )
            
            result = await self.db_session.execute(query)
            return result.scalars().all()
            
        except Exception as e:
            self.logger.error(f"Ошибка получения популярных тарифов: {e}")
            raise BusinessLogicError(f"Не удалось получить популярные тарифы: {e}")

    async def get_active_tariffs(self, include_test: bool = True) -> List[Tariffs]:
        """Получение активных тарифов"""
        async with self.db_session as session:
            try:
                filters = [Tariffs.is_active == True]

                # if not include_test:
                #     filters.append(Tariffs.is_test_available == False)

                query = (
                    select(Tariffs)
                    .where(and_(*filters))
                    .order_by(Tariffs.sort_order.asc(), Tariffs.created_at.desc())
                )

                result = await session.execute(query)
                return result.scalars().all()

            except Exception as e:
                self.logger.error(f"Ошибка получения активных тарифов: {e}")
                raise BusinessLogicError(f"Не удалось получить активные тарифы: {e}")

    async def create_tariff(self, tariff_data: Dict[str, Any]) -> Tariffs:
        """Создание нового тарифа"""
        try:
            # Валидация данных
            await self._validate_tariff_data(tariff_data)
            
            # Создаем тариф
            tariff = Tariffs(**tariff_data)
            
            self.db_session.add(tariff)
            await self.db_session.flush()
            
            self.logger.info(f"Создан новый тариф: {tariff.name} (ID: {tariff.id})")
            return tariff
            
        except ValidationError:
            raise
        except Exception as e:
            self.logger.error(f"Ошибка создания тарифа: {e}")
            raise BusinessLogicError(f"Не удалось создать тариф: {e}")
    
    async def update_tariff(self, tariff_id: UUID, update_data: Dict[str, Any]) -> Tariffs:
        """Обновление тарифа"""
        try:
            # Получаем тариф
            tariff = await self.get_tariff_by_id(tariff_id)
            if not tariff:
                raise NotFoundError(f"Тариф с ID {tariff_id} не найден")
            
            # Валидация данных
            await self._validate_tariff_data(update_data, is_update=True)
            
            # Увеличиваем версию при изменении цен или характеристик
            if 'prices' in update_data or 'features' in update_data:
                update_data['version'] = tariff.version + 1
            
            # Обновляем поля
            for field, value in update_data.items():
                if hasattr(tariff, field):
                    setattr(tariff, field, value)
            
            await self.db_session.flush()
            
            self.logger.info(f"Обновлен тариф: {tariff.name} (ID: {tariff_id})")
            return tariff
            
        except (NotFoundError, ValidationError):
            raise
        except Exception as e:
            self.logger.error(f"Ошибка обновления тарифа {tariff_id}: {e}")
            raise BusinessLogicError(f"Не удалось обновить тариф: {e}")
    
    async def delete_tariff(self, tariff_id: UUID, soft_delete: bool = True) -> bool:
        """Удаление тарифа"""
        try:
            # Получаем тариф
            tariff = await self.get_tariff_by_id(tariff_id)
            if not tariff:
                raise NotFoundError(f"Тариф с ID {tariff_id} не найден")
            
            # Проверяем, есть ли активные платежи по этому тарифу
            if await self._has_active_payments(tariff_id):
                if not soft_delete:
                    raise BusinessLogicError("Нельзя удалить тариф с активными платежами")
                
                # Мягкое удаление - деактивируем тариф
                tariff.is_active = False
                await self.db_session.flush()
                
                self.logger.info(f"Тариф деактивирован: {tariff.name} (ID: {tariff_id})")
                return True
            
            # Жесткое удаление
            await self.db_session.delete(tariff)
            await self.db_session.flush()
            
            self.logger.info(f"Тариф удален: {tariff.name} (ID: {tariff_id})")
            return True
            
        except (NotFoundError, BusinessLogicError):
            raise
        except Exception as e:
            self.logger.error(f"Ошибка удаления тарифа {tariff_id}: {e}")
            raise BusinessLogicError(f"Не удалось удалить тариф: {e}")
    
    async def calculate_price_with_discount(
        self, 
        tariff_id: UUID, 
        currency: str = "rub",
        promocode_discount: Decimal = Decimal('0')
    ) -> Dict[str, Any]:
        """Расчет цены с учетом скидок"""
        try:
            tariff = await self.get_tariff_by_id(tariff_id)
            if not tariff:
                raise NotFoundError(f"Тариф с ID {tariff_id} не найден")
            
            if currency not in tariff.prices:
                raise ValidationError(f"Валюта {currency} не поддерживается для этого тарифа")
            
            original_price = Decimal(str(tariff.prices[currency]))
            
            # Скидка тарифа
            tariff_discount = Decimal(str(tariff.discount_percent)) / 100
            
            # Общая скидка
            total_discount = min(tariff_discount + promocode_discount, Decimal('0.95'))  # Максимум 95%
            
            # Расчет цены
            discount_amount = original_price * total_discount
            final_price = original_price - discount_amount
            
            return {
                'original_price': original_price,
                'discount_percent': float(total_discount * 100),
                'discount_amount': discount_amount,
                'final_price': final_price,
                'currency': currency,
                'tariff_name': tariff.name,
                'duration_days': tariff.duration_days
            }
            
        except (NotFoundError, ValidationError):
            raise
        except Exception as e:
            self.logger.error(f"Ошибка расчета цены для тарифа {tariff_id}: {e}")
            raise BusinessLogicError(f"Не удалось рассчитать цену: {e}")
    
    async def get_tariffs_by_duration(self, duration_days: int) -> List[Tariffs]:
        """Получение тарифов по продолжительности"""
        try:
            query = (
                select(Tariffs)
                .where(and_(
                    Tariffs.is_active == True,
                    Tariffs.duration_days == duration_days
                ))
                .order_by(Tariffs.sort_order.asc())
            )
            
            result = await self.db_session.execute(query)
            return result.scalars().all()
            
        except Exception as e:
            self.logger.error(f"Ошибка получения тарифов по продолжительности {duration_days}: {e}")
            raise BusinessLogicError(f"Не удалось получить тарифы: {e}")
    
    async def search_tariffs(self, search_term: str) -> List[Tariffs]:
        """Поиск тарифов по названию или описанию"""
        try:
            search_pattern = f"%{search_term.lower()}%"
            
            query = (
                select(Tariffs)
                .where(and_(
                    Tariffs.is_active == True,
                    or_(
                        func.lower(Tariffs.name).like(search_pattern),
                        func.lower(Tariffs.description).like(search_pattern)
                    )
                ))
                .order_by(Tariffs.sort_order.asc())
            )
            
            result = await self.db_session.execute(query)
            return result.scalars().all()
            
        except Exception as e:
            self.logger.error(f"Ошибка поиска тарифов по запросу '{search_term}': {e}")
            raise BusinessLogicError(f"Не удалось выполнить поиск: {e}")
    
    async def update_tariff_field(self, tariff_id: UUID, field: str, value: Any) -> Tariffs:
        """Атомарное обновление одного поля тарифа"""
        try:
            tariff = await self.get_tariff_by_id(tariff_id)
            if not tariff:
                raise NotFoundError(f"Тариф с ID {tariff_id} не найден")

            # Валидация
            await self._validate_tariff_data({field: value}, is_update=True)

            setattr(tariff, field, value)
            
            # Увеличиваем версию при изменении цен или характеристик
            if field in ['prices', 'features']:
                tariff.version += 1

            await self.db_session.flush()
            self.logger.info(f"Поле '{field}' обновлено для тарифа: {tariff.name} (ID: {tariff_id})")
            return tariff

        except (NotFoundError, ValidationError):
            raise
        except Exception as e:
            self.logger.error(f"Ошибка обновления поля '{field}' для тарифа {tariff_id}: {e}")
            raise BusinessLogicError(f"Не удалось обновить поле '{field}': {e}")

    async def get_tariff_stats(self, tariff_id: UUID) -> Dict[str, Any]:
        """Получение статистики по конкретному тарифу"""
        try:
            from ..db.models import Payments

            # Общее количество покупок
            total_purchases_query = select(func.count(Payments.id)).where(
                Payments.tariff_id == tariff_id,
                Payments.status == 'completed'
            )
            total_purchases = await self.db_session.scalar(total_purchases_query)

            # Активные подписки
            # (здесь нужна более сложная логика, если у вас есть таблица подписок)
            active_subscriptions = 0 # Заглушка

            return {
                'total_purchases': total_purchases,
                'active_subscriptions': active_subscriptions,
            }

        except Exception as e:
            self.logger.error(f"Ошибка получения статистики для тарифа {tariff_id}: {e}")
            raise BusinessLogicError(f"Не удалось получить статистику: {e}")

    async def _validate_tariff_data(self, data: Dict[str, Any], is_update: bool = False) -> None:
        """Валидация данных тарифа"""
        required_fields = ['name', 'duration_days', 'prices'] if not is_update else []
        
        # Проверка обязательных полей
        for field in required_fields:
            if field not in data or data[field] is None:
                raise ValidationError(f"Поле '{field}' обязательно для заполнения")
        
        # Валидация названия
        if 'name' in data:
            if not data['name'] or len(data['name'].strip()) < 3:
                raise ValidationError("Название тарифа должно содержать минимум 3 символа")
            
            if len(data['name']) > 255:
                raise ValidationError("Название тарифа не может быть длиннее 255 символов")
        
        # Валидация продолжительности
        if 'duration_days' in data:
            if not isinstance(data['duration_days'], int) or data['duration_days'] <= 0:
                raise ValidationError("Продолжительность должна быть положительным числом")
            
            if data['duration_days'] > 3650:  # Максимум 10 лет
                raise ValidationError("Продолжительность не может превышать 3650 дней")
        
        # Валидация цен
        if 'prices' in data:
            if not isinstance(data['prices'], dict):
                raise ValidationError("Цены должны быть представлены в виде словаря")
            
            required_currencies = ['rub', 'usd', 'stars']
            for currency in required_currencies:
                if currency not in data['prices']:
                    data['prices'][currency] = 0
                
                price = data['prices'][currency]
                if not isinstance(price, (int, float)) or price < 0:
                    raise ValidationError(f"Цена в валюте {currency} должна быть неотрицательным числом")
        
        # Валидация скидки
        if 'discount_percent' in data:
            discount = data['discount_percent']
            if not isinstance(discount, int) or discount < 0 or discount > 100:
                raise ValidationError("Скидка должна быть числом от 0 до 100")
        
        # Валидация характеристик
        if 'features' in data and data['features'] is not None:
            if not isinstance(data['features'], list):
                raise ValidationError("Характеристики должны быть представлены в виде списка")
    
    async def _has_active_payments(self, tariff_id: UUID) -> bool:
        """Проверка наличия активных платежей по тарифу"""
        async with self.db_session as session:
            try:
                from ..db.models import Payments
                
                query = (
                    select(func.count(Payments.id))
                    .where(and_(
                        Payments.tariff_id == tariff_id,
                        Payments.status.in_(['pending', 'completed'])
                    ))
                )
                
                result = await session.execute(query)
                count = result.scalar()
                
                return count > 0
                
            except Exception as e:
                self.logger.error(f"Ошибка проверки активных платежей для тарифа {tariff_id}: {e}")
                return True  # В случае ошибки считаем, что есть активные платежи

    async def set_popular_tariff(self, tariff_id: UUID) -> None:
        """Переключение статуса популярности тарифа"""
        async with self.db_session as session:
            try:
                tariff = await self.get_tariff_by_id(tariff_id)
                if not tariff:
                    raise NotFoundError(f"Тариф с ID {tariff_id} не найден")

                # Переключаем флаг is_popular
                tariff.is_popular = not tariff.is_popular
                await session.flush()
                self.logger.info(f"Статус популярности для тарифа {tariff.id} изменен на {tariff.is_popular}")

            except NotFoundError:
                raise
            except Exception as e:
                self.logger.error(f"Ошибка переключения популярного тарифа {tariff_id}: {e}")
                raise BusinessLogicError(f"Не удалось переключить популярный тариф: {e}")
