"""
UnveilVPN Shop - Payment VPN Integration Service
Сервис интеграции платежей с Remnawave VPN панелью
"""

import logging
from typing import Dict, Any, Optional
from uuid import UUID
from datetime import timedelta

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from ..db.models import Payments, VPNUsers, Tariffs, Subscriptions
from ..common.exceptions import NotFoundError, BusinessLogicError
from .vpn_panel_service import VPNPanelService, VPNPanelIntegration
from .subscription_service import SubscriptionService
from ..utils.remnawave_api import RemnaWaveAPIError


class PaymentVPNIntegration:
    """
    Сервис для интеграции платежей с VPN панелью
    """
    
    def __init__(self, db_session: AsyncSession, vpn_service: VPNPanelService):
        self.db_session = db_session
        self.vpn_service = vpn_service
        self.vpn_integration = VPNPanelIntegration(vpn_service)
        self.logger = logging.getLogger(self.__class__.__name__)
    
    async def activate_subscription_after_payment(self, payment: Payments) -> Dict[str, Any]:
        """
        Активация VPN подписки после успешной оплаты
        
        Args:
            payment: Объект платежа
            
        Returns:
            Результат активации подписки
        """
        try:
            # Получаем пользователя и тариф
            user = await self._get_user(payment.user_id)
            tariff = await self._get_tariff(payment.tariff_id)
            
            if not user:
                raise NotFoundError(f"Пользователь {payment.user_id} не найден")
            if not tariff:
                raise NotFoundError(f"Тариф {payment.tariff_id} не найден")
            
            # Проверяем, есть ли уже активная подписка
            existing_subscription = await self._get_active_subscription(user.id)
            
            if existing_subscription and existing_subscription.remnawave_subscription_id:
                # Продлеваем существующую подписку
                result = await self._extend_existing_subscription(
                    existing_subscription, 
                    tariff, 
                    payment
                )
            else:
                # Создаем новую подписку
                result = await self._create_new_subscription(user, tariff, payment)
            
            self.logger.info(f"Активирована VPN подписка для платежа {payment.id}")
            return result
            
        except RemnaWaveAPIError as e:
            self.logger.error(f"Ошибка Remnawave API при активации подписки: {e}")
            raise BusinessLogicError(f"Не удалось активировать VPN подписку: {e}")
        except Exception as e:
            self.logger.error(f"Ошибка активации подписки для платежа {payment.id}: {e}")
            raise BusinessLogicError(f"Не удалось активировать подписку: {e}")
    
    async def _create_new_subscription(
        self, 
        user: VPNUsers, 
        tariff: Tariffs, 
        payment: Payments
    ) -> Dict[str, Any]:
        """Создание новой VPN подписки"""
        try:
            # Создаем подписку через VPN интеграцию
            vpn_result = await self.vpn_integration.setup_user_subscription(
                telegram_id=user.telegram_id,
                tariff_name=tariff.name,
                duration_days=tariff.duration_days,
                username=user.username
            )
            
            # Обновляем пользователя с Remnawave ID
            if not user.remnawave_user_id:
                user.remnawave_user_id = vpn_result['vpn_user_id']
                await self.db_session.flush()
            
            # Создаем запись подписки в локальной БД
            subscription = Subscriptions(
                user_id=user.id,
                tariff_id=tariff.id,
                start_date=payment.created_at,
                end_date=payment.created_at + timedelta(days=tariff.duration_days),
                remnawave_subscription_id=vpn_result['subscription'].get('id'),
                traffic_limit_gb=tariff.features.get('traffic_limit_gb'),
                is_active=True
            )
            
            self.db_session.add(subscription)
            await self.db_session.commit()
            
            return {
                'type': 'new_subscription',
                'subscription_id': str(subscription.id),
                'vpn_user_id': vpn_result['vpn_user_id'],
                'configs': vpn_result.get('configs', []),
                'end_date': subscription.end_date.isoformat()
            }
            
        except Exception as e:
            await self.db_session.rollback()
            raise
    
    async def _extend_existing_subscription(
        self, 
        subscription: Subscriptions, 
        tariff: Tariffs, 
        payment: Payments
    ) -> Dict[str, Any]:
        """Продление существующей подписки"""
        try:
            # Получаем пользователя
            user = await self._get_user(subscription.user_id)
            
            # Продлеваем подписку в Remnawave
            vpn_result = await self.vpn_integration.process_subscription_renewal(
                vpn_user_id=user.remnawave_user_id,
                additional_days=tariff.duration_days
            )
            
            # Обновляем локальную запись
            from datetime import timedelta
            subscription.end_date = subscription.end_date + timedelta(days=tariff.duration_days)
            subscription.is_active = True
            
            await self.db_session.commit()
            
            return {
                'type': 'subscription_extended',
                'subscription_id': str(subscription.id),
                'vpn_user_id': user.remnawave_user_id,
                'additional_days': tariff.duration_days,
                'new_end_date': subscription.end_date.isoformat()
            }
            
        except Exception as e:
            await self.db_session.rollback()
            raise
    
    async def handle_subscription_expiry(self, subscription_id: UUID) -> bool:
        """Обработка истечения подписки"""
        try:
            # Получаем подписку
            subscription = await self._get_subscription(subscription_id)
            if not subscription:
                return False
            
            # Получаем пользователя
            user = await self._get_user(subscription.user_id)
            if not user or not user.remnawave_user_id:
                return False
            
            # Приостанавливаем доступ в Remnawave
            result = await self.vpn_integration.handle_subscription_expiry(
                user.remnawave_user_id
            )
            
            # Обновляем локальную запись
            subscription.is_active = False
            await self.db_session.commit()
            
            return result
            
        except Exception as e:
            self.logger.error(f"Ошибка обработки истечения подписки {subscription_id}: {e}")
            return False
    
    async def get_subscription_info(self, user_id: UUID) -> Optional[Dict[str, Any]]:
        """Получение информации о подписке пользователя"""
        try:
            # Получаем пользователя
            user = await self._get_user(user_id)
            if not user or not user.remnawave_user_id:
                return None
            
            # Получаем информацию из Remnawave
            vpn_info = await self.vpn_integration.get_user_subscription_info(
                user.remnawave_user_id
            )
            
            # Получаем локальную подписку
            local_subscription = await self._get_active_subscription(user_id)
            
            return {
                'local_subscription': {
                    'id': str(local_subscription.id) if local_subscription else None,
                    'end_date': local_subscription.end_date.isoformat() if local_subscription else None,
                    'is_active': local_subscription.is_active if local_subscription else False
                },
                'vpn_info': vpn_info
            }
            
        except Exception as e:
            self.logger.error(f"Ошибка получения информации о подписке для {user_id}: {e}")
            return None
    
    # Приватные методы
    
    async def _get_user(self, user_id: UUID) -> Optional[VPNUsers]:
        """Получение пользователя по ID"""
        result = await self.db_session.execute(
            select(VPNUsers).where(VPNUsers.id == user_id)
        )
        return result.scalar_one_or_none()
    
    async def _get_tariff(self, tariff_id: UUID) -> Optional[Tariffs]:
        """Получение тарифа по ID"""
        result = await self.db_session.execute(
            select(Tariffs).where(Tariffs.id == tariff_id)
        )
        return result.scalar_one_or_none()
    
    async def _get_subscription(self, subscription_id: UUID) -> Optional[Subscriptions]:
        """Получение подписки по ID"""
        result = await self.db_session.execute(
            select(Subscriptions).where(Subscriptions.id == subscription_id)
        )
        return result.scalar_one_or_none()
    
    async def _get_active_subscription(self, user_id: UUID) -> Optional[Subscriptions]:
        """Получение активной подписки пользователя"""
        from datetime import datetime, timezone
        
        result = await self.db_session.execute(
            select(Subscriptions).where(
                Subscriptions.user_id == user_id,
                Subscriptions.is_active == True,
                Subscriptions.end_date > datetime.now(timezone.utc)
            ).order_by(Subscriptions.end_date.desc())
        )
        return result.scalar_one_or_none()
