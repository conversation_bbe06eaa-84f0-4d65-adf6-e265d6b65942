"""
UnveilVPN Shop - Analytics Service
Сервис для финансовой аналитики и отчетности
"""

import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, timezone, timedelta
from decimal import Decimal

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_, text
from sqlalchemy.orm import selectinload

from ..db.models import Payments, VPNUsers, Tariffs, Refunds, Promocodes
from ..common.exceptions import BusinessLogicError


class AnalyticsService:
    """
    Сервис для финансовой аналитики
    """
    
    def __init__(self, db_session: AsyncSession):
        self.db_session = db_session
        self.logger = logging.getLogger(self.__class__.__name__)
    
    async def get_revenue_analytics(
        self,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        group_by: str = 'day'  # day, week, month
    ) -> Dict[str, Any]:
        """Аналитика доходов"""
        try:
            # Устанавливаем период по умолчанию (последние 30 дней)
            if not start_date:
                start_date = datetime.now(timezone.utc) - timedelta(days=30)
            if not end_date:
                end_date = datetime.now(timezone.utc)
            
            # Базовый запрос для успешных платежей
            base_query = (
                select(Payments)
                .where(and_(
                    Payments.status == 'completed',
                    Payments.paid_at >= start_date,
                    Payments.paid_at <= end_date
                ))
            )
            
            result = await self.db_session.execute(base_query)
            payments = result.scalars().all()
            
            # Общая статистика
            total_revenue = sum(payment.final_amount for payment in payments)
            total_payments = len(payments)
            avg_payment = total_revenue / total_payments if total_payments > 0 else 0
            
            # Группировка по периодам
            revenue_by_period = await self._group_revenue_by_period(payments, group_by)
            
            # Разбивка по валютам
            currency_breakdown = {}
            for payment in payments:
                currency = payment.currency
                if currency not in currency_breakdown:
                    currency_breakdown[currency] = {'count': 0, 'amount': Decimal('0')}
                currency_breakdown[currency]['count'] += 1
                currency_breakdown[currency]['amount'] += payment.final_amount
            
            # Разбивка по методам оплаты
            method_breakdown = {}
            for payment in payments:
                method = payment.payment_method
                if method not in method_breakdown:
                    method_breakdown[method] = {'count': 0, 'amount': Decimal('0')}
                method_breakdown[method]['count'] += 1
                method_breakdown[method]['amount'] += payment.final_amount
            
            return {
                'period': {
                    'start_date': start_date.isoformat(),
                    'end_date': end_date.isoformat(),
                    'group_by': group_by
                },
                'summary': {
                    'total_revenue': float(total_revenue),
                    'total_payments': total_payments,
                    'average_payment': float(avg_payment)
                },
                'revenue_by_period': revenue_by_period,
                'currency_breakdown': {
                    currency: {
                        'count': stats['count'],
                        'amount': float(stats['amount'])
                    }
                    for currency, stats in currency_breakdown.items()
                },
                'method_breakdown': {
                    method: {
                        'count': stats['count'],
                        'amount': float(stats['amount'])
                    }
                    for method, stats in method_breakdown.items()
                }
            }
            
        except Exception as e:
            self.logger.error(f"Ошибка получения аналитики доходов: {e}")
            raise BusinessLogicError(f"Не удалось получить аналитику доходов: {e}")
    
    async def get_tariff_analytics(
        self,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """Аналитика по тарифам"""
        try:
            # Устанавливаем период по умолчанию
            if not start_date:
                start_date = datetime.now(timezone.utc) - timedelta(days=30)
            if not end_date:
                end_date = datetime.now(timezone.utc)
            
            # Запрос платежей с тарифами
            query = (
                select(Payments)
                .options(selectinload(Payments.tariff))
                .where(and_(
                    Payments.status == 'completed',
                    Payments.paid_at >= start_date,
                    Payments.paid_at <= end_date
                ))
            )
            
            result = await self.db_session.execute(query)
            payments = result.scalars().all()
            
            # Группировка по тарифам
            tariff_stats = {}
            for payment in payments:
                tariff = payment.tariff
                tariff_id = str(tariff.id)
                
                if tariff_id not in tariff_stats:
                    tariff_stats[tariff_id] = {
                        'name': tariff.name,
                        'count': 0,
                        'revenue': Decimal('0'),
                        'duration_days': tariff.duration_days
                    }
                
                tariff_stats[tariff_id]['count'] += 1
                tariff_stats[tariff_id]['revenue'] += payment.final_amount
            
            # Сортируем по доходу
            sorted_tariffs = sorted(
                tariff_stats.items(),
                key=lambda x: x[1]['revenue'],
                reverse=True
            )
            
            # Топ-5 тарифов
            top_tariffs = []
            for tariff_id, stats in sorted_tariffs[:5]:
                top_tariffs.append({
                    'tariff_id': tariff_id,
                    'name': stats['name'],
                    'sales_count': stats['count'],
                    'revenue': float(stats['revenue']),
                    'duration_days': stats['duration_days'],
                    'avg_revenue_per_sale': float(stats['revenue'] / stats['count']) if stats['count'] > 0 else 0
                })
            
            return {
                'period': {
                    'start_date': start_date.isoformat(),
                    'end_date': end_date.isoformat()
                },
                'total_tariffs_sold': len(tariff_stats),
                'top_tariffs': top_tariffs,
                'all_tariffs': {
                    tariff_id: {
                        'name': stats['name'],
                        'sales_count': stats['count'],
                        'revenue': float(stats['revenue']),
                        'duration_days': stats['duration_days']
                    }
                    for tariff_id, stats in tariff_stats.items()
                }
            }
            
        except Exception as e:
            self.logger.error(f"Ошибка получения аналитики тарифов: {e}")
            raise BusinessLogicError(f"Не удалось получить аналитику тарифов: {e}")
    
    async def get_user_analytics(
        self,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """Аналитика пользователей"""
        try:
            # Устанавливаем период по умолчанию
            if not start_date:
                start_date = datetime.now(timezone.utc) - timedelta(days=30)
            if not end_date:
                end_date = datetime.now(timezone.utc)
            
            # Новые пользователи за период
            new_users_query = (
                select(func.count(VPNUsers.id))
                .where(and_(
                    VPNUsers.created_at >= start_date,
                    VPNUsers.created_at <= end_date
                ))
            )
            new_users_result = await self.db_session.execute(new_users_query)
            new_users_count = new_users_result.scalar()
            
            # Активные пользователи (с платежами за период)
            active_users_query = (
                select(func.count(func.distinct(Payments.user_id)))
                .where(and_(
                    Payments.status == 'completed',
                    Payments.paid_at >= start_date,
                    Payments.paid_at <= end_date
                ))
            )
            active_users_result = await self.db_session.execute(active_users_query)
            active_users_count = active_users_result.scalar()
            
            # Общее количество пользователей
            total_users_query = select(func.count(VPNUsers.id))
            total_users_result = await self.db_session.execute(total_users_query)
            total_users_count = total_users_result.scalar()
            
            # Пользователи с повторными покупками
            repeat_customers_query = text("""
                SELECT COUNT(DISTINCT user_id) 
                FROM (
                    SELECT user_id, COUNT(*) as payment_count
                    FROM payments 
                    WHERE status = 'completed' 
                    AND paid_at >= :start_date 
                    AND paid_at <= :end_date
                    GROUP BY user_id
                    HAVING COUNT(*) > 1
                ) as repeat_customers
            """)
            
            repeat_customers_result = await self.db_session.execute(
                repeat_customers_query,
                {'start_date': start_date, 'end_date': end_date}
            )
            repeat_customers_count = repeat_customers_result.scalar()
            
            # Средний доход на пользователя (ARPU)
            revenue_query = (
                select(func.sum(Payments.final_amount))
                .where(and_(
                    Payments.status == 'completed',
                    Payments.paid_at >= start_date,
                    Payments.paid_at <= end_date
                ))
            )
            revenue_result = await self.db_session.execute(revenue_query)
            total_revenue = revenue_result.scalar() or Decimal('0')
            
            arpu = float(total_revenue / active_users_count) if active_users_count > 0 else 0
            
            return {
                'period': {
                    'start_date': start_date.isoformat(),
                    'end_date': end_date.isoformat()
                },
                'user_metrics': {
                    'total_users': total_users_count,
                    'new_users': new_users_count,
                    'active_users': active_users_count,
                    'repeat_customers': repeat_customers_count,
                    'user_retention_rate': (repeat_customers_count / active_users_count * 100) if active_users_count > 0 else 0
                },
                'revenue_metrics': {
                    'total_revenue': float(total_revenue),
                    'arpu': arpu,
                    'revenue_per_new_user': float(total_revenue / new_users_count) if new_users_count > 0 else 0
                }
            }
            
        except Exception as e:
            self.logger.error(f"Ошибка получения аналитики пользователей: {e}")
            raise BusinessLogicError(f"Не удалось получить аналитику пользователей: {e}")
    
    async def get_promocode_analytics(
        self,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """Аналитика промокодов"""
        try:
            # Устанавливаем период по умолчанию
            if not start_date:
                start_date = datetime.now(timezone.utc) - timedelta(days=30)
            if not end_date:
                end_date = datetime.now(timezone.utc)
            
            # Платежи с промокодами за период
            query = (
                select(Payments)
                .options(selectinload(Payments.promocode))
                .where(and_(
                    Payments.status == 'completed',
                    Payments.paid_at >= start_date,
                    Payments.paid_at <= end_date,
                    Payments.promocode_id.isnot(None)
                ))
            )
            
            result = await self.db_session.execute(query)
            payments_with_promocodes = result.scalars().all()
            
            # Группировка по промокодам
            promocode_stats = {}
            total_discount = Decimal('0')
            
            for payment in payments_with_promocodes:
                promocode = payment.promocode
                code = promocode.code
                
                if code not in promocode_stats:
                    promocode_stats[code] = {
                        'usage_count': 0,
                        'total_discount': Decimal('0'),
                        'revenue_impact': Decimal('0')
                    }
                
                promocode_stats[code]['usage_count'] += 1
                promocode_stats[code]['total_discount'] += payment.discount_amount
                promocode_stats[code]['revenue_impact'] += payment.final_amount
                total_discount += payment.discount_amount
            
            # Сортируем по использованию
            sorted_promocodes = sorted(
                promocode_stats.items(),
                key=lambda x: x[1]['usage_count'],
                reverse=True
            )
            
            return {
                'period': {
                    'start_date': start_date.isoformat(),
                    'end_date': end_date.isoformat()
                },
                'summary': {
                    'total_promocode_usage': len(payments_with_promocodes),
                    'total_discount_given': float(total_discount),
                    'unique_promocodes_used': len(promocode_stats)
                },
                'top_promocodes': [
                    {
                        'code': code,
                        'usage_count': stats['usage_count'],
                        'total_discount': float(stats['total_discount']),
                        'revenue_impact': float(stats['revenue_impact'])
                    }
                    for code, stats in sorted_promocodes[:10]
                ],
                'all_promocodes': {
                    code: {
                        'usage_count': stats['usage_count'],
                        'total_discount': float(stats['total_discount']),
                        'revenue_impact': float(stats['revenue_impact'])
                    }
                    for code, stats in promocode_stats.items()
                }
            }
            
        except Exception as e:
            self.logger.error(f"Ошибка получения аналитики промокодов: {e}")
            raise BusinessLogicError(f"Не удалось получить аналитику промокодов: {e}")
    
    async def _group_revenue_by_period(
        self,
        payments: List[Payments],
        group_by: str
    ) -> List[Dict[str, Any]]:
        """Группировка доходов по периодам"""
        try:
            revenue_by_period = {}
            
            for payment in payments:
                if not payment.paid_at:
                    continue
                
                # Определяем ключ группировки
                if group_by == 'day':
                    period_key = payment.paid_at.date().isoformat()
                elif group_by == 'week':
                    # Начало недели (понедельник)
                    week_start = payment.paid_at.date() - timedelta(days=payment.paid_at.weekday())
                    period_key = week_start.isoformat()
                elif group_by == 'month':
                    period_key = payment.paid_at.strftime('%Y-%m')
                else:
                    period_key = payment.paid_at.date().isoformat()
                
                if period_key not in revenue_by_period:
                    revenue_by_period[period_key] = {
                        'period': period_key,
                        'revenue': Decimal('0'),
                        'payment_count': 0
                    }
                
                revenue_by_period[period_key]['revenue'] += payment.final_amount
                revenue_by_period[period_key]['payment_count'] += 1
            
            # Сортируем по периоду
            sorted_periods = sorted(revenue_by_period.items())
            
            return [
                {
                    'period': period,
                    'revenue': float(stats['revenue']),
                    'payment_count': stats['payment_count']
                }
                for period, stats in sorted_periods
            ]
            
        except Exception as e:
            self.logger.error(f"Ошибка группировки доходов по периодам: {e}")
            return []
