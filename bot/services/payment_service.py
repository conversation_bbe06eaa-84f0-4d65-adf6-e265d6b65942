"""
UnveilVPN Shop - Payment Service
Сервис для работы с платежами
"""

import logging
from typing import List, Optional, Dict, Any
from uuid import UUID
from decimal import Decimal
from datetime import datetime, timezone, timedelta

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete, func, and_, or_
from sqlalchemy.orm import selectinload

from ..db.models import Payments, VPNUsers, Tariffs, Promocodes
from ..common.exceptions import NotFoundError, ValidationError, BusinessLogicError


class PaymentService:
    """
    Сервис для работы с платежами
    """
    
    def __init__(self, db_session: AsyncSession):
        self.db_session = db_session
        self.logger = logging.getLogger(self.__class__.__name__)
    
    async def create_payment(
        self,
        user_id: UUID,
        tariff_id: UUID,
        payment_method: str,
        promocode_id: Optional[UUID] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Payments:
        """Создание нового платежа"""
        try:
            # Получаем пользователя и тариф
            user = await self._get_user(user_id)
            tariff = await self._get_tariff(tariff_id)
            
            if not user:
                raise NotFoundError(f"Пользователь с ID {user_id} не найден")
            if not tariff or not tariff.is_active:
                raise NotFoundError(f"Тариф с ID {tariff_id} не найден или неактивен")
            
            # Валидация метода оплаты
            if payment_method not in ['yookassa', 'cryptomus', 'telegram_stars']:
                raise ValidationError(f"Неподдерживаемый метод оплаты: {payment_method}")
            
            # Определяем валюту и цену
            currency, original_amount = await self._get_price_for_method(tariff, payment_method)
            
            if original_amount <= 0:
                raise ValidationError(f"Цена для метода {payment_method} не установлена")
            
            # Применяем промокод если есть
            discount_amount = Decimal('0')
            if promocode_id:
                discount_amount = await self._apply_promocode(
                    promocode_id, user_id, tariff_id, original_amount
                )
            
            # Применяем скидку тарифа
            tariff_discount = Decimal(str(tariff.discount_percent)) / 100
            tariff_discount_amount = original_amount * tariff_discount
            
            # Общая скидка
            total_discount = discount_amount + tariff_discount_amount
            final_amount = max(original_amount - total_discount, Decimal('1'))  # Минимум 1 единица валюты
            
            # Создаем платеж
            payment = Payments(
                user_id=user_id,
                tariff_id=tariff_id,
                payment_method=payment_method,
                status='pending',
                original_amount=original_amount,
                discount_amount=total_discount,
                final_amount=final_amount,
                currency=currency,
                promocode_id=promocode_id,
                payment_metadata=metadata or {},
                expires_at=datetime.now(timezone.utc) + timedelta(hours=1)  # Платеж действителен 1 час
            )
            
            self.db_session.add(payment)
            await self.db_session.flush()
            
            self.logger.info(f"Создан платеж: {payment.id} для пользователя {user_id}")
            return payment
            
        except (NotFoundError, ValidationError):
            raise
        except Exception as e:
            self.logger.error(f"Ошибка создания платежа: {e}")
            raise BusinessLogicError(f"Не удалось создать платеж: {e}")
    
    async def get_payment_by_id(self, payment_id: UUID) -> Optional[Payments]:
        """Получение платежа по ID"""
        try:
            query = (
                select(Payments)
                .options(
                    selectinload(Payments.user),
                    selectinload(Payments.tariff),
                    selectinload(Payments.promocode)
                )
                .where(Payments.id == payment_id)
            )
            result = await self.db_session.execute(query)
            return result.scalar_one_or_none()
            
        except Exception as e:
            self.logger.error(f"Ошибка получения платежа {payment_id}: {e}")
            raise BusinessLogicError(f"Не удалось получить платеж: {e}")
    
    async def get_payment_by_external_id(self, external_id: str, payment_method: str) -> Optional[Payments]:
        """Получение платежа по внешнему ID"""
        try:
            query = (
                select(Payments)
                .options(
                    selectinload(Payments.user),
                    selectinload(Payments.tariff)
                )
                .where(and_(
                    Payments.external_payment_id == external_id,
                    Payments.payment_method == payment_method
                ))
            )
            result = await self.db_session.execute(query)
            return result.scalar_one_or_none()
            
        except Exception as e:
            self.logger.error(f"Ошибка получения платежа по внешнему ID {external_id}: {e}")
            raise BusinessLogicError(f"Не удалось получить платеж: {e}")
    
    async def update_payment_status(
        self,
        payment_id: UUID,
        new_status: str,
        external_payment_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Payments:
        """Обновление статуса платежа"""
        try:
            payment = await self.get_payment_by_id(payment_id)
            if not payment:
                raise NotFoundError(f"Платеж с ID {payment_id} не найден")
            
            old_status = payment.status
            
            # Обновляем статус
            payment.status = new_status
            
            # Устанавливаем внешний ID если предоставлен
            if external_payment_id:
                payment.external_payment_id = external_payment_id
            
            # Устанавливаем время оплаты для успешных платежей
            if new_status == 'completed' and not payment.paid_at:
                payment.paid_at = datetime.now(timezone.utc)
            
            # Обновляем метаданные
            if metadata:
                current_metadata = dict(payment.payment_metadata) if payment.payment_metadata else {}
                current_metadata.update(metadata)
                
                # Добавляем историю изменений статуса
                status_history = current_metadata.get('status_history', [])
                status_history.append({
                    'from_status': old_status,
                    'to_status': new_status,
                    'changed_at': datetime.now(timezone.utc).isoformat(),
                    'metadata': metadata
                })
                current_metadata['status_history'] = status_history
                
                payment.payment_metadata = current_metadata
            
            await self.db_session.flush()
            
            self.logger.info(f"Обновлен статус платежа {payment_id}: {old_status} -> {new_status}")
            
            # Если платеж успешен, активируем подписку
            if new_status == 'completed':
                await self._activate_subscription(payment)
            
            return payment
            
        except (NotFoundError, ValidationError):
            raise
        except Exception as e:
            self.logger.error(f"Ошибка обновления статуса платежа {payment_id}: {e}")
            raise BusinessLogicError(f"Не удалось обновить статус платежа: {e}")
    
    async def get_user_payments(
        self,
        user_id: UUID,
        status: Optional[str] = None,
        limit: int = 50,
        offset: int = 0
    ) -> List[Payments]:
        """Получение платежей пользователя"""
        try:
            query = (
                select(Payments)
                .options(
                    selectinload(Payments.tariff),
                    selectinload(Payments.promocode)
                )
                .where(Payments.user_id == user_id)
            )
            
            if status:
                query = query.where(Payments.status == status)
            
            query = query.order_by(Payments.created_at.desc()).limit(limit).offset(offset)
            
            result = await self.db_session.execute(query)
            return result.scalars().all()
            
        except Exception as e:
            self.logger.error(f"Ошибка получения платежей пользователя {user_id}: {e}")
            raise BusinessLogicError(f"Не удалось получить платежи: {e}")
    
    async def get_expired_payments(self, limit: int = 100) -> List[Payments]:
        """Получение просроченных платежей"""
        try:
            now = datetime.now(timezone.utc)
            
            query = (
                select(Payments)
                .where(and_(
                    Payments.status == 'pending',
                    Payments.expires_at < now
                ))
                .limit(limit)
            )
            
            result = await self.db_session.execute(query)
            return result.scalars().all()
            
        except Exception as e:
            self.logger.error(f"Ошибка получения просроченных платежей: {e}")
            raise BusinessLogicError(f"Не удалось получить просроченные платежи: {e}")
    
    async def cancel_expired_payments(self) -> int:
        """Отмена просроченных платежей"""
        try:
            expired_payments = await self.get_expired_payments()
            cancelled_count = 0
            
            for payment in expired_payments:
                await self.update_payment_status(
                    payment.id,
                    'failed',
                    metadata={'cancellation_reason': 'expired'}
                )
                cancelled_count += 1
            
            self.logger.info(f"Отменено просроченных платежей: {cancelled_count}")
            return cancelled_count
            
        except Exception as e:
            self.logger.error(f"Ошибка отмены просроченных платежей: {e}")
            raise BusinessLogicError(f"Не удалось отменить просроченные платежи: {e}")
    
    async def _get_user(self, user_id: UUID) -> Optional[VPNUsers]:
        """Получение пользователя"""
        query = select(VPNUsers).where(VPNUsers.id == user_id)
        result = await self.db_session.execute(query)
        return result.scalar_one_or_none()
    
    async def _get_tariff(self, tariff_id: UUID) -> Optional[Tariffs]:
        """Получение тарифа"""
        query = select(Tariffs).where(Tariffs.id == tariff_id)
        result = await self.db_session.execute(query)
        return result.scalar_one_or_none()
    
    async def _get_price_for_method(self, tariff: Tariffs, payment_method: str) -> tuple[str, Decimal]:
        """Получение цены и валюты для метода оплаты"""
        method_mapping = {
            'yookassa': ('RUB', 'rub'),
            'cryptomus': ('USD', 'usd'),
            'telegram_stars': ('STARS', 'stars')
        }
        
        if payment_method not in method_mapping:
            raise ValidationError(f"Неподдерживаемый метод оплаты: {payment_method}")
        
        currency, price_key = method_mapping[payment_method]
        price = tariff.prices.get(price_key, 0)
        
        return currency, Decimal(str(price))
    
    async def _apply_promocode(
        self,
        promocode_id: UUID,
        user_id: UUID,
        tariff_id: UUID,
        amount: Decimal
    ) -> Decimal:
        """Применение промокода"""
        try:
            from .promocode_service import PromocodeService

            promocode_service = PromocodeService(self.db_session)

            # Получаем промокод
            promocode = await promocode_service.get_promocode_by_id(promocode_id)
            if not promocode:
                raise ValidationError("Промокод не найден")

            # Валидируем промокод
            is_valid, message, _ = await promocode_service.validate_promocode(
                code=promocode.code,
                user_id=user_id,
                tariff_id=tariff_id,
                purchase_amount=amount
            )

            if not is_valid:
                raise ValidationError(message)

            # Рассчитываем скидку
            discount_amount, final_amount = promocode.calculate_discount(amount)

            self.logger.info(f"Применен промокод {promocode.code}: скидка {discount_amount}")
            return discount_amount
            
        except ValidationError:
            raise
        except Exception as e:
            self.logger.error(f"Ошибка применения промокода {promocode_id}: {e}")
            return Decimal('0')
    
    async def _activate_subscription(self, payment: Payments):
        """Активация подписки после успешной оплаты через Remnawave"""
        try:
            # Импортируем сервисы
            from .payment_vpn_integration import PaymentVPNIntegration
            from .vpn_panel_service import VPNPanelService
            from ..common.config import BotConfig

            # Получаем конфигурацию
            config = BotConfig.from_env()

            # Создаем VPN панель сервис
            vpn_service = VPNPanelService(
                panel_url=config.remnawave_panel_url,
                api_key=config.remnawave_api_key
            )

            # Создаем интеграционный сервис
            vpn_integration = PaymentVPNIntegration(self.db_session, vpn_service)

            # Активируем подписку
            subscription_result = await vpn_integration.activate_subscription_after_payment(payment)

            self.logger.info(f"Активирована VPN подписка: {subscription_result}")

            # Обновляем статистику пользователя
            user = payment.user
            if user and user.user_metadata:
                metadata = dict(user.user_metadata)
                stats = metadata.get('statistics', {})
                stats['total_purchases'] = stats.get('total_purchases', 0) + 1
                stats['total_spent'] = float(stats.get('total_spent', 0)) + float(payment.final_amount)
                metadata['statistics'] = stats
                user.user_metadata = metadata

            # Обрабатываем реферальные бонусы
            await self._process_referral_bonuses(payment)

            self.logger.info(f"Активирована подписка для платежа {payment.id}")

        except Exception as e:
            self.logger.error(f"Ошибка активации подписки для платежа {payment.id}: {e}")
            # Не прерываем процесс, так как платеж уже прошел

    async def get_payments_count_for_today(self) -> int:
        """Получение количества платежей за сегодня"""
        try:
            today = datetime.now(timezone.utc).date()
            start_of_day = datetime.combine(today, datetime.min.time(), tzinfo=timezone.utc)
            
            query = (
                select(func.count(Payments.id))
                .where(and_(
                    Payments.status == 'completed',
                    Payments.paid_at >= start_of_day
                ))
            )
            
            result = await self.db_session.execute(query)
            return result.scalar_one() or 0
            
        except Exception as e:
            self.logger.error(f"Ошибка получения количества платежей за сегодня: {e}")
            raise BusinessLogicError(f"Не удалось получить количество платежей: {e}")

    async def get_all_payments(self, limit: int = 100, offset: int = 0) -> List[Payments]:
        """Получение всех платежей"""
        try:
            query = (
                select(Payments)
                .options(
                    selectinload(Payments.user),
                    selectinload(Payments.tariff)
                )
                .order_by(Payments.created_at.desc())
                .limit(limit)
                .offset(offset)
            )
            
            result = await self.db_session.execute(query)
            return result.scalars().all()
            
        except Exception as e:
            self.logger.error(f"Ошибка получения всех платежей: {e}")
            raise BusinessLogicError(f"Не удалось получить платежи: {e}")
