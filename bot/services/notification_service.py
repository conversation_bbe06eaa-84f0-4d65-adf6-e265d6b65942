"""
UnveilVPN Shop - Notification Service
Сервис для отправки уведомлений и рассылок
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, timezone, timedelta
from uuid import UUID

from aiogram import Bot
from aiogram.exceptions import TelegramBadRequest, TelegramForbiddenError
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_, desc

from ..db.models import VPNUsers, NotificationLogs
from ..common.exceptions import BusinessLogicError


class NotificationService:
    """
    Сервис для отправки уведомлений и рассылок
    """
    
    def __init__(self, db_session: AsyncSession, bot: Bot = None):
        self.db_session = db_session
        self.bot = bot
        self.logger = logging.getLogger(self.__class__.__name__)
    
    async def send_broadcast(
        self,
        message: str,
        sent_by_id: Optional[UUID] = None,
        target_filter: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Отправка рассылки пользователям"""
        try:
            if not self.bot:
                raise BusinessLogicError("Bot instance not provided")
            
            # Получаем список пользователей для рассылки
            users = await self._get_broadcast_users(target_filter)
            
            sent_count = 0
            error_count = 0
            blocked_count = 0
            
            # Отправляем сообщения порциями
            batch_size = 30  # Telegram rate limit
            for i in range(0, len(users), batch_size):
                batch = users[i:i + batch_size]
                
                # Отправляем сообщения в батче
                tasks = []
                for user in batch:
                    task = self._send_message_to_user(user.telegram_id, message)
                    tasks.append(task)
                
                # Ждем выполнения всех задач в батче
                results = await asyncio.gather(*tasks, return_exceptions=True)
                
                # Обрабатываем результаты
                for result in results:
                    if result is True:
                        sent_count += 1
                    elif isinstance(result, TelegramForbiddenError):
                        blocked_count += 1
                    else:
                        error_count += 1
                
                # Пауза между батчами
                if i + batch_size < len(users):
                    await asyncio.sleep(1)
            
            # Логируем рассылку
            await self._log_broadcast(
                message=message,
                sent_by_id=sent_by_id,
                total_users=len(users),
                sent_count=sent_count,
                error_count=error_count,
                blocked_count=blocked_count
            )
            
            return {
                'total': len(users),
                'sent': sent_count,
                'errors': error_count,
                'blocked': blocked_count
            }
            
        except Exception as e:
            self.logger.error(f"Ошибка отправки рассылки: {e}")
            raise BusinessLogicError(f"Не удалось отправить рассылку: {e}")
    
    async def send_notification_to_user(
        self,
        user_id: UUID,
        message: str,
        notification_type: str = "info"
    ) -> bool:
        """Отправка уведомления конкретному пользователю"""
        try:
            if not self.bot:
                raise BusinessLogicError("Bot instance not provided")
            
            # Получаем пользователя
            query = select(VPNUsers).where(VPNUsers.id == user_id)
            result = await self.db_session.execute(query)
            user = result.scalar_one_or_none()
            
            if not user:
                return False
            
            # Отправляем сообщение
            success = await self._send_message_to_user(user.telegram_id, message)
            
            # Логируем уведомление
            await self._log_notification(
                user_id=user_id,
                message=message,
                notification_type=notification_type,
                success=success
            )
            
            return success
            
        except Exception as e:
            self.logger.error(f"Ошибка отправки уведомления пользователю {user_id}: {e}")
            return False
    
    async def send_payment_notification(
        self,
        user_id: UUID,
        payment_data: Dict[str, Any]
    ) -> bool:
        """Отправка уведомления об оплате с информацией о Remnawave подписке"""
        try:
            amount = payment_data.get('amount', 0)
            currency = payment_data.get('currency', 'RUB')
            tariff_name = payment_data.get('tariff_name', 'Неизвестный тариф')
            vpn_user_id = payment_data.get('vpn_user_id')
            configs_count = len(payment_data.get('configs', []))

            currency_symbol = {'RUB': '₽', 'USD': '$', 'EUR': '€', 'STARS': '⭐'}.get(currency, currency)

            message = (
                f"✅ <b>Оплата прошла успешно!</b>\n\n"
                f"💰 Сумма: {amount} {currency_symbol}\n"
                f"📦 Тариф: {tariff_name}\n\n"
                f"🎉 Ваша VPN подписка активирована в Remnawave!\n"
            )

            if vpn_user_id:
                message += f"🆔 ID пользователя: <code>{vpn_user_id}</code>\n"

            if configs_count > 0:
                message += f"⚙️ Создано конфигураций: {configs_count}\n"

            message += f"\n📱 Получите конфигурации в разделе 'Моя подписка'."

            return await self.send_notification_to_user(
                user_id=user_id,
                message=message,
                notification_type="payment_success"
            )
            
        except Exception as e:
            self.logger.error(f"Ошибка отправки уведомления об оплате: {e}")
            return False
    
    async def send_subscription_expiry_warning(
        self,
        user_id: UUID,
        days_left: int
    ) -> bool:
        """Отправка предупреждения об истечении подписки"""
        try:
            if days_left <= 0:
                message = (
                    f"⚠️ <b>Ваша подписка истекла!</b>\n\n"
                    f"Для продолжения использования VPN необходимо продлить подписку.\n\n"
                    f"💡 Продлите сейчас и получите скидку!"
                )
            elif days_left == 1:
                message = (
                    f"⚠️ <b>Подписка истекает завтра!</b>\n\n"
                    f"Не забудьте продлить подписку, чтобы не потерять доступ к VPN.\n\n"
                    f"🔄 Продлить подписку можно в разделе 'Моя подписка'."
                )
            else:
                message = (
                    f"⏰ <b>Подписка истекает через {days_left} дн.</b>\n\n"
                    f"Рекомендуем продлить подписку заранее.\n\n"
                    f"🔄 Продлить подписку можно в разделе 'Моя подписка'."
                )
            
            return await self.send_notification_to_user(
                user_id=user_id,
                message=message,
                notification_type="subscription_expiry"
            )
            
        except Exception as e:
            self.logger.error(f"Ошибка отправки предупреждения об истечении: {e}")
            return False

    async def send_remnawave_subscription_notification(
        self,
        user_id: UUID,
        subscription_data: Dict[str, Any]
    ) -> bool:
        """Отправка уведомления о Remnawave подписке"""
        try:
            subscription_type = subscription_data.get('type', 'new_subscription')
            vpn_user_id = subscription_data.get('vpn_user_id', 'N/A')
            configs = subscription_data.get('configs', [])
            end_date = subscription_data.get('end_date', '')

            if subscription_type == 'new_subscription':
                message = (
                    f"🎉 <b>VPN подписка активирована!</b>\n\n"
                    f"🆔 ID в Remnawave: <code>{vpn_user_id}</code>\n"
                    f"⚙️ Создано конфигураций: {len(configs)}\n"
                    f"📅 Действует до: {end_date[:10] if end_date else 'Не указано'}\n\n"
                    f"📱 Получите конфигурации в разделе 'Моя подписка'."
                )
            elif subscription_type == 'subscription_extended':
                additional_days = subscription_data.get('additional_days', 0)
                message = (
                    f"🔄 <b>Подписка продлена!</b>\n\n"
                    f"🆔 ID в Remnawave: <code>{vpn_user_id}</code>\n"
                    f"➕ Добавлено дней: {additional_days}\n"
                    f"📅 Новая дата окончания: {end_date[:10] if end_date else 'Не указано'}\n\n"
                    f"✅ Доступ к VPN восстановлен!"
                )
            else:
                message = (
                    f"ℹ️ <b>Обновление подписки</b>\n\n"
                    f"🆔 ID в Remnawave: <code>{vpn_user_id}</code>\n"
                    f"📅 Статус обновлен"
                )

            return await self.send_notification_to_user(
                user_id=user_id,
                message=message,
                notification_type="remnawave_subscription"
            )

        except Exception as e:
            self.logger.error(f"Ошибка отправки уведомления о Remnawave подписке: {e}")
            return False

    async def send_vpn_config_notification(
        self,
        user_id: UUID,
        config_data: Dict[str, Any]
    ) -> bool:
        """Отправка уведомления о новой VPN конфигурации"""
        try:
            device_name = config_data.get('device_name', 'Неизвестное устройство')
            protocol = config_data.get('protocol', 'unknown')
            config_id = config_data.get('config_id', 'N/A')

            message = (
                f"⚙️ <b>Создана новая конфигурация!</b>\n\n"
                f"📱 Устройство: {device_name}\n"
                f"🔐 Протокол: {protocol.upper()}\n"
                f"🆔 ID конфигурации: <code>{config_id}</code>\n\n"
                f"📥 Скачайте конфигурацию в разделе 'Моя подписка'."
            )

            return await self.send_notification_to_user(
                user_id=user_id,
                message=message,
                notification_type="vpn_config"
            )

        except Exception as e:
            self.logger.error(f"Ошибка отправки уведомления о VPN конфигурации: {e}")
            return False
    
    async def send_referral_notification(
        self,
        user_id: UUID,
        referral_data: Dict[str, Any]
    ) -> bool:
        """Отправка уведомления о реферальном доходе"""
        try:
            earnings = referral_data.get('earnings', 0)
            referred_user = referral_data.get('referred_user', 'Пользователь')
            
            message = (
                f"💰 <b>Новый реферальный доход!</b>\n\n"
                f"👤 Пользователь: {referred_user}\n"
                f"💵 Заработано: {earnings} ₽\n\n"
                f"🎉 Продолжайте приглашать друзей и зарабатывать!"
            )
            
            return await self.send_notification_to_user(
                user_id=user_id,
                message=message,
                notification_type="referral_earnings"
            )
            
        except Exception as e:
            self.logger.error(f"Ошибка отправки уведомления о реферале: {e}")
            return False
    
    async def send_system_notification(
        self,
        message: str,
        target_users: List[UUID] = None,
        notification_type: str = "system"
    ) -> Dict[str, Any]:
        """Отправка системного уведомления"""
        try:
            if target_users:
                # Отправляем конкретным пользователям
                sent_count = 0
                for user_id in target_users:
                    success = await self.send_notification_to_user(
                        user_id=user_id,
                        message=message,
                        notification_type=notification_type
                    )
                    if success:
                        sent_count += 1
                
                return {
                    'total': len(target_users),
                    'sent': sent_count,
                    'errors': len(target_users) - sent_count
                }
            else:
                # Отправляем всем пользователям
                return await self.send_broadcast(
                    message=message,
                    target_filter={'notification_type': notification_type}
                )
            
        except Exception as e:
            self.logger.error(f"Ошибка отправки системного уведомления: {e}")
            raise BusinessLogicError(f"Не удалось отправить системное уведомление: {e}")
    
    async def get_notification_stats(self, days: int = 7) -> Dict[str, Any]:
        """Получение статистики уведомлений"""
        try:
            start_date = datetime.now(timezone.utc) - timedelta(days=days)
            
            # Общее количество уведомлений
            total_query = select(func.count(NotificationLogs.id)).where(
                NotificationLogs.created_at >= start_date
            )
            total_result = await self.db_session.execute(total_query)
            total_notifications = total_result.scalar() or 0
            
            # Успешные уведомления
            success_query = select(func.count(NotificationLogs.id)).where(
                and_(
                    NotificationLogs.created_at >= start_date,
                    NotificationLogs.success == True
                )
            )
            success_result = await self.db_session.execute(success_query)
            successful_notifications = success_result.scalar() or 0
            
            # По типам уведомлений
            types_query = select(
                NotificationLogs.notification_type,
                func.count(NotificationLogs.id)
            ).where(
                NotificationLogs.created_at >= start_date
            ).group_by(NotificationLogs.notification_type)
            
            types_result = await self.db_session.execute(types_query)
            by_type = {
                notification_type: count for notification_type, count in types_result.fetchall()
            }
            
            success_rate = (successful_notifications / total_notifications * 100) if total_notifications > 0 else 0
            
            return {
                'total_notifications': total_notifications,
                'successful_notifications': successful_notifications,
                'success_rate': success_rate,
                'by_type': by_type,
                'period_days': days
            }
            
        except Exception as e:
            self.logger.error(f"Ошибка получения статистики уведомлений: {e}")
            raise BusinessLogicError(f"Не удалось получить статистику уведомлений: {e}")
    
    # Приватные методы
    
    async def _get_broadcast_users(self, target_filter: Dict[str, Any] = None) -> List[VPNUsers]:
        """Получение списка пользователей для рассылки"""
        try:
            query = select(VPNUsers).where(VPNUsers.is_blocked == False)
            
            # Применяем фильтры
            if target_filter:
                if target_filter.get('has_vpn'):
                    query = query.where(VPNUsers.vpn_id.isnot(None))
                elif target_filter.get('no_vpn'):
                    query = query.where(VPNUsers.vpn_id.is_(None))
                
                if target_filter.get('has_referrals'):
                    query = query.where(VPNUsers.referral_code.isnot(None))
            
            result = await self.db_session.execute(query)
            return result.scalars().all()
            
        except Exception as e:
            self.logger.error(f"Ошибка получения пользователей для рассылки: {e}")
            return []
    
    async def _send_message_to_user(self, telegram_id: int, message: str) -> bool:
        """Отправка сообщения конкретному пользователю"""
        try:
            await self.bot.send_message(
                chat_id=telegram_id,
                text=message,
                parse_mode='HTML'
            )
            return True
            
        except TelegramForbiddenError:
            # Пользователь заблокировал бота
            self.logger.info(f"User {telegram_id} blocked the bot")
            return False
        except TelegramBadRequest as e:
            # Другие ошибки Telegram
            self.logger.warning(f"Telegram error for user {telegram_id}: {e}")
            return False
        except Exception as e:
            # Общие ошибки
            self.logger.error(f"Error sending message to user {telegram_id}: {e}")
            return False
    
    async def _log_broadcast(
        self,
        message: str,
        sent_by_id: Optional[UUID],
        total_users: int,
        sent_count: int,
        error_count: int,
        blocked_count: int
    ):
        """Логирование рассылки"""
        try:
            log_entry = NotificationLogs(
                notification_type="broadcast",
                message=message[:500],  # Ограничиваем длину
                sent_by_id=sent_by_id,
                success=True,
                metadata={
                    'total_users': total_users,
                    'sent_count': sent_count,
                    'error_count': error_count,
                    'blocked_count': blocked_count
                },
                created_at=datetime.now(timezone.utc)
            )
            
            self.db_session.add(log_entry)
            await self.db_session.commit()
            
        except Exception as e:
            self.logger.error(f"Ошибка логирования рассылки: {e}")
    
    async def _log_notification(
        self,
        user_id: UUID,
        message: str,
        notification_type: str,
        success: bool
    ):
        """Логирование уведомления"""
        try:
            log_entry = NotificationLogs(
                user_id=user_id,
                notification_type=notification_type,
                message=message[:500],  # Ограничиваем длину
                success=success,
                created_at=datetime.now(timezone.utc)
            )
            
            self.db_session.add(log_entry)
            await self.db_session.commit()
            
        except Exception as e:
            self.logger.error(f"Ошибка логирования уведомления: {e}")
