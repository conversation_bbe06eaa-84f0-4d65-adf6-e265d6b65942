"""
UnveilVPN Shop - Refund Service
Сервис для работы с возвратами платежей
"""

import logging
from typing import List, Optional, Dict, Any
from uuid import UUID
from decimal import Decimal
from datetime import datetime, timezone, timedelta

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, and_, func
from sqlalchemy.orm import selectinload

from ..db.models import Payments, VPNUsers, Refunds
from ..common.exceptions import NotFoundError, ValidationError, BusinessLogicError
from .payment_manager import PaymentManager


class RefundService:
    """
    Сервис для работы с возвратами
    """
    
    def __init__(self, db_session: AsyncSession, payment_manager: PaymentManager):
        self.db_session = db_session
        self.payment_manager = payment_manager
        self.logger = logging.getLogger(self.__class__.__name__)
    
    async def create_refund(
        self,
        payment_id: UUID,
        amount: Optional[Decimal] = None,
        reason: str = "Возврат по запросу пользователя",
        admin_id: Optional[UUID] = None,
        auto_refund: bool = False
    ) -> Dict[str, Any]:
        """Создание возврата"""
        try:
            # Получаем платеж
            payment = await self._get_payment_with_details(payment_id)
            if not payment:
                raise NotFoundError(f"Платеж {payment_id} не найден")
            
            # Проверяем возможность возврата
            await self._validate_refund_eligibility(payment, amount)
            
            # Определяем сумму возврата
            refund_amount = amount or payment.final_amount
            
            # Выполняем возврат через платежную систему
            refund_success = await self.payment_manager.refund_payment(
                payment_id=payment_id,
                amount=float(refund_amount),
                reason=reason
            )
            
            if not refund_success:
                raise BusinessLogicError("Не удалось выполнить возврат через платежную систему")
            
            # Создаем запись о возврате
            refund_record = await self._create_refund_record(
                payment=payment,
                amount=refund_amount,
                reason=reason,
                admin_id=admin_id,
                auto_refund=auto_refund
            )
            
            # Отзываем доступ к VPN если полный возврат
            if refund_amount >= payment.final_amount:
                await self._revoke_vpn_access(payment)
            
            await self.db_session.commit()
            
            self.logger.info(f"Создан возврат {refund_record.id} для платежа {payment_id}")
            
            return {
                'refund_id': str(refund_record.id),
                'payment_id': str(payment_id),
                'amount': float(refund_amount),
                'currency': payment.currency,
                'status': 'completed',
                'reason': reason
            }
            
        except (NotFoundError, ValidationError, BusinessLogicError):
            await self.db_session.rollback()
            raise
        except Exception as e:
            await self.db_session.rollback()
            self.logger.error(f"Ошибка создания возврата для платежа {payment_id}: {e}")
            raise BusinessLogicError(f"Не удалось создать возврат: {e}")
    
    async def process_automatic_refunds(self) -> Dict[str, Any]:
        """Обработка автоматических возвратов"""
        try:
            # Получаем платежи, подлежащие автоматическому возврату
            eligible_payments = await self._get_auto_refund_eligible_payments()
            
            processed_count = 0
            failed_count = 0
            total_amount = Decimal('0')
            
            for payment in eligible_payments:
                try:
                    result = await self.create_refund(
                        payment_id=payment.id,
                        reason="Автоматический возврат по политике возвратов",
                        auto_refund=True
                    )
                    
                    processed_count += 1
                    total_amount += Decimal(str(result['amount']))
                    
                except Exception as e:
                    failed_count += 1
                    self.logger.error(f"Ошибка автоматического возврата для платежа {payment.id}: {e}")
            
            self.logger.info(f"Обработано автоматических возвратов: {processed_count}, ошибок: {failed_count}")
            
            return {
                'processed_count': processed_count,
                'failed_count': failed_count,
                'total_amount': float(total_amount),
                'eligible_payments': len(eligible_payments)
            }
            
        except Exception as e:
            self.logger.error(f"Ошибка обработки автоматических возвратов: {e}")
            raise BusinessLogicError(f"Не удалось обработать автоматические возвраты: {e}")
    
    async def get_refund_by_id(self, refund_id: UUID) -> Optional[Refunds]:
        """Получение возврата по ID"""
        try:
            query = (
                select(Refunds)
                .options(
                    selectinload(Refunds.payment).selectinload(Payments.user),
                    selectinload(Refunds.payment).selectinload(Payments.tariff)
                )
                .where(Refunds.id == refund_id)
            )
            result = await self.db_session.execute(query)
            return result.scalar_one_or_none()
            
        except Exception as e:
            self.logger.error(f"Ошибка получения возврата {refund_id}: {e}")
            raise BusinessLogicError(f"Не удалось получить возврат: {e}")
    
    async def get_payment_refunds(self, payment_id: UUID) -> List[Refunds]:
        """Получение всех возвратов для платежа"""
        try:
            query = (
                select(Refunds)
                .where(Refunds.payment_id == payment_id)
                .order_by(Refunds.created_at.desc())
            )
            result = await self.db_session.execute(query)
            return result.scalars().all()
            
        except Exception as e:
            self.logger.error(f"Ошибка получения возвратов для платежа {payment_id}: {e}")
            raise BusinessLogicError(f"Не удалось получить возвраты: {e}")
    
    async def get_user_refunds(
        self,
        user_id: UUID,
        limit: int = 50,
        offset: int = 0
    ) -> List[Refunds]:
        """Получение возвратов пользователя"""
        try:
            query = (
                select(Refunds)
                .join(Payments)
                .options(
                    selectinload(Refunds.payment).selectinload(Payments.tariff)
                )
                .where(Payments.user_id == user_id)
                .order_by(Refunds.created_at.desc())
                .limit(limit)
                .offset(offset)
            )
            result = await self.db_session.execute(query)
            return result.scalars().all()
            
        except Exception as e:
            self.logger.error(f"Ошибка получения возвратов пользователя {user_id}: {e}")
            raise BusinessLogicError(f"Не удалось получить возвраты пользователя: {e}")
    
    async def get_refund_statistics(
        self,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """Получение статистики возвратов"""
        try:
            # Базовый запрос
            query = select(Refunds)
            
            # Фильтрация по датам
            if start_date:
                query = query.where(Refunds.created_at >= start_date)
            if end_date:
                query = query.where(Refunds.created_at <= end_date)
            
            # Выполняем запрос
            result = await self.db_session.execute(query)
            refunds = result.scalars().all()
            
            # Подсчитываем статистику
            total_refunds = len(refunds)
            total_amount = sum(refund.amount for refund in refunds)
            auto_refunds = len([r for r in refunds if r.auto_refund])
            manual_refunds = total_refunds - auto_refunds
            
            # Группировка по валютам
            currency_stats = {}
            for refund in refunds:
                currency = refund.payment.currency
                if currency not in currency_stats:
                    currency_stats[currency] = {'count': 0, 'amount': Decimal('0')}
                currency_stats[currency]['count'] += 1
                currency_stats[currency]['amount'] += refund.amount
            
            # Группировка по причинам
            reason_stats = {}
            for refund in refunds:
                reason = refund.reason or 'Не указана'
                reason_stats[reason] = reason_stats.get(reason, 0) + 1
            
            return {
                'total_refunds': total_refunds,
                'total_amount': float(total_amount),
                'auto_refunds': auto_refunds,
                'manual_refunds': manual_refunds,
                'currency_breakdown': {
                    currency: {
                        'count': stats['count'],
                        'amount': float(stats['amount'])
                    }
                    for currency, stats in currency_stats.items()
                },
                'reason_breakdown': reason_stats,
                'period': {
                    'start_date': start_date.isoformat() if start_date else None,
                    'end_date': end_date.isoformat() if end_date else None
                }
            }
            
        except Exception as e:
            self.logger.error(f"Ошибка получения статистики возвратов: {e}")
            raise BusinessLogicError(f"Не удалось получить статистику возвратов: {e}")
    
    # Приватные методы
    
    async def _get_payment_with_details(self, payment_id: UUID) -> Optional[Payments]:
        """Получение платежа с деталями"""
        query = (
            select(Payments)
            .options(
                selectinload(Payments.user),
                selectinload(Payments.tariff)
            )
            .where(Payments.id == payment_id)
        )
        result = await self.db_session.execute(query)
        return result.scalar_one_or_none()
    
    async def _validate_refund_eligibility(self, payment: Payments, amount: Optional[Decimal]):
        """Проверка возможности возврата"""
        # Проверяем статус платежа
        if payment.status != 'completed':
            raise ValidationError("Можно вернуть только завершенные платежы")
        
        # Проверяем, не был ли уже возврат
        existing_refunds = await self.get_payment_refunds(payment.id)
        total_refunded = sum(refund.amount for refund in existing_refunds)
        
        if total_refunded >= payment.final_amount:
            raise ValidationError("Платеж уже полностью возвращен")
        
        # Проверяем сумму возврата
        if amount:
            if amount <= 0:
                raise ValidationError("Сумма возврата должна быть положительной")
            
            if total_refunded + amount > payment.final_amount:
                raise ValidationError("Сумма возврата превышает оставшуюся сумму платежа")
        
        # Проверяем временные ограничения (например, 30 дней)
        refund_deadline = payment.paid_at + timedelta(days=30) if payment.paid_at else None
        if refund_deadline and datetime.now(timezone.utc) > refund_deadline:
            raise ValidationError("Срок для возврата истек (30 дней с момента оплаты)")
    
    async def _create_refund_record(
        self,
        payment: Payments,
        amount: Decimal,
        reason: str,
        admin_id: Optional[UUID],
        auto_refund: bool
    ) -> Refunds:
        """Создание записи о возврате"""
        refund = Refunds(
            payment_id=payment.id,
            amount=amount,
            currency=payment.currency,
            reason=reason,
            admin_id=admin_id,
            auto_refund=auto_refund,
            refund_metadata={
                'original_amount': float(payment.final_amount),
                'payment_method': payment.payment_method,
                'processed_at': datetime.now(timezone.utc).isoformat()
            }
        )
        
        self.db_session.add(refund)
        await self.db_session.flush()
        
        return refund
    
    async def _revoke_vpn_access(self, payment: Payments):
        """Отзыв доступа к VPN"""
        try:
            # Здесь будет логика отзыва доступа в VPN панели
            # Пока заглушка
            
            # Обновляем статистику пользователя
            user = payment.user
            if user and user.user_metadata:
                metadata = dict(user.user_metadata)
                stats = metadata.get('statistics', {})
                stats['total_refunds'] = stats.get('total_refunds', 0) + 1
                metadata['statistics'] = stats
                user.user_metadata = metadata
            
            self.logger.info(f"Отозван VPN доступ для платежа {payment.id}")
            
        except Exception as e:
            self.logger.error(f"Ошибка отзыва VPN доступа для платежа {payment.id}: {e}")
            # Не прерываем процесс возврата из-за ошибки отзыва доступа
    
    async def _get_auto_refund_eligible_payments(self) -> List[Payments]:
        """Получение платежей, подлежащих автоматическому возврату"""
        try:
            # Критерии для автоматического возврата:
            # 1. Платеж завершен более 24 часов назад
            # 2. Пользователь не активировал VPN
            # 3. Еще не было возврата
            
            cutoff_time = datetime.now(timezone.utc) - timedelta(hours=24)
            
            query = (
                select(Payments)
                .options(selectinload(Payments.user))
                .where(and_(
                    Payments.status == 'completed',
                    Payments.paid_at < cutoff_time,
                    # Добавить условие проверки активации VPN
                ))
            )
            
            result = await self.db_session.execute(query)
            payments = result.scalars().all()
            
            # Фильтруем платежи без возвратов
            eligible_payments = []
            for payment in payments:
                existing_refunds = await self.get_payment_refunds(payment.id)
                if not existing_refunds:
                    eligible_payments.append(payment)
            
            return eligible_payments
            
        except Exception as e:
            self.logger.error(f"Ошибка получения платежей для автоматического возврата: {e}")
            return []
