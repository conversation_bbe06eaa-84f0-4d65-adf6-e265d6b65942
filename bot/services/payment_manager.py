"""
UnveilVPN Shop - Payment Manager
Менеджер для управления платежными провайдерами
"""

import logging
from typing import Dict, Any, Optional, List
from uuid import UUID

from sqlalchemy.ext.asyncio import AsyncSession

from .payment_service import PaymentService
from .payment_providers import (
    BasePaymentProvider,
    YooKassaProvider,
    CryptomusProvider,
    TelegramStarsProvider,
    PaymentResult,
    WebhookResult
)
from ..db.models import Payments
from ..common.exceptions import PaymentError, ValidationError, NotFoundError


class PaymentManager:
    """
    Менеджер платежных провайдеров
    Управляет всеми платежными системами и обеспечивает единый интерфейс
    """
    
    def __init__(self, config: Dict[str, Any], db_session: AsyncSession):
        self.config = config
        self.db_session = db_session
        self.payment_service = PaymentService(db_session)
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # Инициализируем провайдеры
        self.providers: Dict[str, BasePaymentProvider] = {}
        self._init_providers()
    
    def _init_providers(self):
        """Инициализация платежных провайдеров"""
        try:
            # YooKassa
            yookassa_config = self.config.get('yookassa', {})
            if yookassa_config.get('shop_id') and yookassa_config.get('secret_key'):
                try:
                    self.providers['yookassa'] = YooKassaProvider(yookassa_config)
                    self.logger.info("YooKassa провайдер инициализирован")
                except Exception as e:
                    self.logger.error(f"Ошибка инициализации YooKassa: {e}")
            
            # Cryptomus
            cryptomus_config = self.config.get('cryptomus', {})
            if cryptomus_config.get('merchant_id') and cryptomus_config.get('api_key'):
                try:
                    self.providers['cryptomus'] = CryptomusProvider(cryptomus_config)
                    self.logger.info("Cryptomus провайдер инициализирован")
                except Exception as e:
                    self.logger.error(f"Ошибка инициализации Cryptomus: {e}")
            
            # Telegram Stars
            telegram_config = self.config.get('telegram_stars', {})
            if telegram_config.get('bot_token'):
                try:
                    self.providers['telegram_stars'] = TelegramStarsProvider(telegram_config)
                    self.logger.info("Telegram Stars провайдер инициализирован")
                except Exception as e:
                    self.logger.error(f"Ошибка инициализации Telegram Stars: {e}")
            
            if not self.providers:
                self.logger.warning("Ни один платежный провайдер не был инициализирован")
            else:
                self.logger.info(f"Инициализировано провайдеров: {list(self.providers.keys())}")
                
        except Exception as e:
            self.logger.error(f"Ошибка инициализации провайдеров: {e}")
    
    def get_available_methods(self) -> List[str]:
        """Получение списка доступных методов оплаты"""
        return list(self.providers.keys())
    
    def is_method_available(self, method: str) -> bool:
        """Проверка доступности метода оплаты"""
        return method in self.providers and self.providers[method].is_configured()
    
    async def create_payment(
        self,
        user_id: UUID,
        tariff_id: UUID,
        payment_method: str,
        promocode_id: Optional[UUID] = None,
        metadata: Optional[Dict[str, Any]] = None,
        return_url: Optional[str] = None,
        webhook_url: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Создание платежа
        
        Returns:
            Dict с информацией о созданном платеже
        """
        try:
            # Проверяем доступность метода оплаты
            if not self.is_method_available(payment_method):
                raise ValidationError(f"Метод оплаты {payment_method} недоступен")
            
            # Создаем платеж в БД
            payment = await self.payment_service.create_payment(
                user_id=user_id,
                tariff_id=tariff_id,
                payment_method=payment_method,
                promocode_id=promocode_id,
                metadata=metadata
            )
            
            # Создаем платеж в платежной системе
            provider = self.providers[payment_method]
            result = await provider.create_payment(
                payment=payment,
                return_url=return_url,
                webhook_url=webhook_url
            )
            
            if result.success:
                # Обновляем платеж с данными от провайдера
                await self.payment_service.update_payment_status(
                    payment.id,
                    'pending',
                    external_payment_id=result.external_payment_id,
                    metadata=result.metadata
                )
                
                return {
                    'success': True,
                    'payment_id': str(payment.id),
                    'payment_url': result.payment_url,
                    'external_payment_id': result.external_payment_id,
                    'amount': float(payment.final_amount),
                    'currency': payment.currency,
                    'method': payment_method
                }
            else:
                # Отмечаем платеж как неудачный
                await self.payment_service.update_payment_status(
                    payment.id,
                    'failed',
                    metadata={'error': result.error_message}
                )
                
                raise PaymentError(f"Ошибка создания платежа: {result.error_message}")
        
        except (ValidationError, PaymentError, NotFoundError):
            raise
        except Exception as e:
            self.logger.error(f"Ошибка создания платежа: {e}")
            raise PaymentError(f"Не удалось создать платеж: {e}")
    
    async def process_webhook(
        self,
        payment_method: str,
        webhook_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Обработка webhook от платежной системы
        
        Returns:
            Dict с результатом обработки
        """
        try:
            # Проверяем наличие провайдера
            if payment_method not in self.providers:
                raise ValidationError(f"Неизвестный метод оплаты: {payment_method}")
            
            provider = self.providers[payment_method]
            
            # Обрабатываем webhook
            result = await provider.process_webhook(webhook_data)
            
            if result.success and result.external_payment_id:
                # Находим платеж в БД
                payment = await self.payment_service.get_payment_by_external_id(
                    result.external_payment_id,
                    payment_method
                )
                
                if payment:
                    # Обновляем статус платежа
                    await self.payment_service.update_payment_status(
                        payment.id,
                        result.payment_status,
                        metadata=result.metadata
                    )
                    
                    return {
                        'success': True,
                        'payment_id': str(payment.id),
                        'status': result.payment_status,
                        'message': 'Webhook обработан успешно'
                    }
                else:
                    self.logger.warning(f"Платеж не найден для external_id: {result.external_payment_id}")
                    return {
                        'success': False,
                        'message': 'Платеж не найден'
                    }
            else:
                return {
                    'success': False,
                    'message': result.error_message or 'Ошибка обработки webhook'
                }
        
        except Exception as e:
            self.logger.error(f"Ошибка обработки webhook {payment_method}: {e}")
            return {
                'success': False,
                'message': f'Ошибка обработки webhook: {e}'
            }
    
    async def check_payment_status(self, payment_id: UUID) -> Dict[str, Any]:
        """Проверка статуса платежа"""
        try:
            payment = await self.payment_service.get_payment_by_id(payment_id)
            if not payment:
                raise NotFoundError(f"Платеж {payment_id} не найден")
            
            # Если платеж уже завершен, возвращаем текущий статус
            if payment.status in ['completed', 'failed', 'refunded']:
                return {
                    'payment_id': str(payment.id),
                    'status': payment.status,
                    'amount': float(payment.final_amount),
                    'currency': payment.currency
                }
            
            # Проверяем статус в платежной системе
            if payment.external_payment_id and payment.payment_method in self.providers:
                provider = self.providers[payment.payment_method]
                current_status = await provider.check_payment_status(payment.external_payment_id)
                
                # Обновляем статус если изменился
                if current_status != payment.status:
                    await self.payment_service.update_payment_status(
                        payment.id,
                        current_status,
                        metadata={'status_check_at': 'manual'}
                    )
                    payment.status = current_status
            
            return {
                'payment_id': str(payment.id),
                'status': payment.status,
                'amount': float(payment.final_amount),
                'currency': payment.currency
            }
        
        except (NotFoundError, ValidationError):
            raise
        except Exception as e:
            self.logger.error(f"Ошибка проверки статуса платежа {payment_id}: {e}")
            raise PaymentError(f"Не удалось проверить статус платежа: {e}")
    
    async def refund_payment(
        self,
        payment_id: UUID,
        amount: Optional[float] = None,
        reason: Optional[str] = None
    ) -> bool:
        """Возврат платежа"""
        try:
            payment = await self.payment_service.get_payment_by_id(payment_id)
            if not payment:
                raise NotFoundError(f"Платеж {payment_id} не найден")
            
            if payment.status != 'completed':
                raise ValidationError("Можно вернуть только завершенные платежи")
            
            if not payment.external_payment_id:
                raise ValidationError("Отсутствует внешний ID платежа")
            
            # Выполняем возврат через провайдера
            if payment.payment_method in self.providers:
                provider = self.providers[payment.payment_method]
                
                refund_amount = None
                if amount is not None:
                    refund_amount = Decimal(str(amount))
                
                success = await provider.refund_payment(
                    payment.external_payment_id,
                    refund_amount,
                    reason
                )
                
                if success:
                    # Обновляем статус платежа
                    await self.payment_service.update_payment_status(
                        payment.id,
                        'refunded',
                        metadata={
                            'refund_reason': reason,
                            'refund_amount': amount or float(payment.final_amount),
                            'refunded_at': datetime.now(timezone.utc).isoformat()
                        }
                    )
                    
                    self.logger.info(f"Платеж {payment_id} успешно возвращен")
                    return True
                else:
                    self.logger.error(f"Ошибка возврата платежа {payment_id}")
                    return False
            else:
                raise ValidationError(f"Провайдер {payment.payment_method} недоступен")
        
        except (NotFoundError, ValidationError):
            raise
        except Exception as e:
            self.logger.error(f"Ошибка возврата платежа {payment_id}: {e}")
            raise PaymentError(f"Не удалось вернуть платеж: {e}")
    
    async def get_payment_statistics(self) -> Dict[str, Any]:
        """Получение статистики платежей"""
        try:
            # Здесь будет логика получения статистики
            # Пока заглушка
            return {
                'total_payments': 0,
                'successful_payments': 0,
                'failed_payments': 0,
                'total_amount': 0,
                'methods_stats': {}
            }
        
        except Exception as e:
            self.logger.error(f"Ошибка получения статистики платежей: {e}")
            return {}
    
    async def cleanup_expired_payments(self) -> int:
        """Очистка просроченных платежей"""
        try:
            return await self.payment_service.cancel_expired_payments()
        except Exception as e:
            self.logger.error(f"Ошибка очистки просроченных платежей: {e}")
            return 0
