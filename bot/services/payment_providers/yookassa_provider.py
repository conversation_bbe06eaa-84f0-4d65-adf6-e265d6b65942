"""
UnveilVPN Shop - YooKassa Payment Provider
Провайдер для интеграции с Yoo<PERSON> (банковские карты, СБП)
"""

import asyncio
from typing import Dict, Any, Optional
from decimal import Decimal
import aiohttp
import json
import base64
from datetime import datetime, timezone

from .base_provider import BasePaymentProvider, PaymentResult, WebhookResult
from ...db.models import Payments
from ...common.exceptions import PaymentError


class YooKassaProvider(BasePaymentProvider):
    """
    Провайдер для YooKassa
    Поддерживает банковские карты, СБП, электронные кошельки
    """
    
    API_URL = "https://api.yookassa.ru/v3"
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.shop_id = config.get('shop_id')
        self.secret_key = config.get('secret_key')
        self.auth_header = self._create_auth_header()
    
    def _validate_config(self) -> None:
        """Валидация конфигурации YooKassa"""
        required_fields = ['shop_id', 'secret_key']
        
        for field in required_fields:
            if not self.config.get(field):
                raise ValueError(f"YooKassa: отсутствует обязательное поле {field}")
    
    def _create_auth_header(self) -> str:
        """Создание заголовка авторизации"""
        credentials = f"{self.shop_id}:{self.secret_key}"
        encoded_credentials = base64.b64encode(credentials.encode()).decode()
        return f"Basic {encoded_credentials}"
    
    async def create_payment(
        self,
        payment: Payments,
        return_url: Optional[str] = None,
        webhook_url: Optional[str] = None
    ) -> PaymentResult:
        """Создание платежа в YooKassa"""
        try:
            # Формируем данные платежа
            payment_data = {
                "amount": {
                    "value": self._format_amount(payment.final_amount, payment.currency),
                    "currency": payment.currency
                },
                "confirmation": {
                    "type": "redirect",
                    "return_url": return_url or "https://t.me/unveilvpn_bot"
                },
                "capture": True,
                "description": f"Подписка VPN - {payment.tariff.name}",
                "metadata": {
                    "payment_id": str(payment.id),
                    "user_id": str(payment.user_id),
                    "tariff_id": str(payment.tariff_id)
                }
            }
            
            # Добавляем чек для соответствия 54-ФЗ
            if payment.currency == "RUB":
                payment_data["receipt"] = self._create_receipt(payment)
            
            # Отправляем запрос к YooKassa
            headers = {
                "Authorization": self.auth_header,
                "Content-Type": "application/json",
                "Idempotence-Key": str(payment.id)  # Для идемпотентности
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.API_URL}/payments",
                    json=payment_data,
                    headers=headers
                ) as response:
                    
                    if response.status == 200:
                        result_data = await response.json()
                        
                        self._log_payment_action(
                            'create',
                            payment.id,
                            result_data.get('id'),
                            {'status': result_data.get('status')}
                        )
                        
                        return PaymentResult(
                            success=True,
                            payment_url=result_data['confirmation']['confirmation_url'],
                            external_payment_id=result_data['id'],
                            metadata={
                                'yookassa_status': result_data.get('status'),
                                'created_at': result_data.get('created_at')
                            }
                        )
                    else:
                        error_data = await response.json()
                        error_message = error_data.get('description', 'Неизвестная ошибка YooKassa')
                        
                        self.logger.error(f"YooKassa API error: {response.status} - {error_message}")
                        
                        return PaymentResult(
                            success=False,
                            error_message=error_message
                        )
        
        except Exception as e:
            self.logger.error(f"Ошибка создания платежа YooKassa: {e}")
            return PaymentResult(
                success=False,
                error_message=f"Ошибка создания платежа: {e}"
            )
    
    async def check_payment_status(self, external_payment_id: str) -> str:
        """Проверка статуса платежа в YooKassa"""
        try:
            headers = {
                "Authorization": self.auth_header,
                "Content-Type": "application/json"
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{self.API_URL}/payments/{external_payment_id}",
                    headers=headers
                ) as response:
                    
                    if response.status == 200:
                        result_data = await response.json()
                        yookassa_status = result_data.get('status', 'pending')
                        return self._normalize_yookassa_status(yookassa_status)
                    else:
                        self.logger.error(f"Ошибка проверки статуса YooKassa: {response.status}")
                        return 'pending'
        
        except Exception as e:
            self.logger.error(f"Ошибка проверки статуса платежа YooKassa: {e}")
            return 'pending'
    
    async def process_webhook(self, webhook_data: Dict[str, Any]) -> WebhookResult:
        """Обработка webhook от YooKassa"""
        try:
            # Извлекаем данные о платеже
            event_type = webhook_data.get('event')
            payment_data = webhook_data.get('object', {})
            
            if event_type not in ['payment.succeeded', 'payment.canceled', 'refund.succeeded']:
                return WebhookResult(
                    success=False,
                    error_message=f"Неподдерживаемый тип события: {event_type}"
                )
            
            external_payment_id = payment_data.get('id')
            if not external_payment_id:
                return WebhookResult(
                    success=False,
                    error_message="Отсутствует ID платежа в webhook"
                )
            
            # Определяем статус
            yookassa_status = payment_data.get('status', 'pending')
            normalized_status = self._normalize_yookassa_status(yookassa_status)
            
            # Формируем метаданные
            metadata = {
                'webhook_event': event_type,
                'yookassa_status': yookassa_status,
                'payment_method': payment_data.get('payment_method', {}),
                'captured_at': payment_data.get('captured_at'),
                'processed_at': datetime.now(timezone.utc).isoformat()
            }
            
            self._log_payment_action(
                'webhook',
                payment_data.get('metadata', {}).get('payment_id', 'unknown'),
                external_payment_id,
                {'event': event_type, 'status': yookassa_status}
            )
            
            return WebhookResult(
                success=True,
                payment_status=normalized_status,
                external_payment_id=external_payment_id,
                metadata=metadata
            )
        
        except Exception as e:
            self.logger.error(f"Ошибка обработки webhook YooKassa: {e}")
            return WebhookResult(
                success=False,
                error_message=f"Ошибка обработки webhook: {e}"
            )
    
    async def refund_payment(
        self,
        external_payment_id: str,
        amount: Optional[Decimal] = None,
        reason: Optional[str] = None
    ) -> bool:
        """Возврат платежа в YooKassa"""
        try:
            # Сначала получаем информацию о платеже
            payment_info = await self._get_payment_info(external_payment_id)
            if not payment_info:
                return False
            
            # Формируем данные возврата
            refund_data = {
                "payment_id": external_payment_id,
                "amount": {
                    "value": self._format_amount(
                        amount or Decimal(payment_info['amount']['value']),
                        payment_info['amount']['currency']
                    ),
                    "currency": payment_info['amount']['currency']
                }
            }
            
            if reason:
                refund_data["description"] = reason
            
            headers = {
                "Authorization": self.auth_header,
                "Content-Type": "application/json",
                "Idempotence-Key": f"refund_{external_payment_id}_{int(datetime.now().timestamp())}"
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.API_URL}/refunds",
                    json=refund_data,
                    headers=headers
                ) as response:
                    
                    if response.status == 200:
                        result_data = await response.json()
                        
                        self._log_payment_action(
                            'refund',
                            'unknown',
                            external_payment_id,
                            {'refund_id': result_data.get('id'), 'status': result_data.get('status')}
                        )
                        
                        return True
                    else:
                        error_data = await response.json()
                        self.logger.error(f"Ошибка возврата YooKassa: {error_data}")
                        return False
        
        except Exception as e:
            self.logger.error(f"Ошибка возврата платежа YooKassa: {e}")
            return False
    
    async def _get_payment_info(self, external_payment_id: str) -> Optional[Dict[str, Any]]:
        """Получение информации о платеже"""
        try:
            headers = {
                "Authorization": self.auth_header,
                "Content-Type": "application/json"
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{self.API_URL}/payments/{external_payment_id}",
                    headers=headers
                ) as response:
                    
                    if response.status == 200:
                        return await response.json()
                    else:
                        return None
        
        except Exception as e:
            self.logger.error(f"Ошибка получения информации о платеже: {e}")
            return None
    
    def _create_receipt(self, payment: Payments) -> Dict[str, Any]:
        """Создание чека для 54-ФЗ"""
        return {
            "customer": {
                "email": self.config.get('receipt_email', '<EMAIL>')
            },
            "items": [
                {
                    "description": f"Подписка VPN - {payment.tariff.name}",
                    "quantity": "1",
                    "amount": {
                        "value": self._format_amount(payment.final_amount, payment.currency),
                        "currency": payment.currency
                    },
                    "vat_code": "1"  # НДС не облагается
                }
            ]
        }
    
    def _normalize_yookassa_status(self, yookassa_status: str) -> str:
        """Нормализация статуса YooKassa"""
        status_mapping = {
            'pending': 'pending',
            'waiting_for_capture': 'pending',
            'succeeded': 'completed',
            'canceled': 'failed',
            'refunded': 'refunded'
        }
        
        return status_mapping.get(yookassa_status.lower(), 'pending')
