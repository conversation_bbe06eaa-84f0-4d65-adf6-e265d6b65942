"""
UnveilVPN Shop - Telegram Stars Payment Provider
Провайдер для интеграции с Telegram Stars
"""

from typing import Dict, Any, Optional
from decimal import Decimal
from datetime import datetime, timezone

from aiogram import Bot
from aiogram.types import LabeledPrice, InlineKeyboardMarkup, InlineKeyboardButton

from .base_provider import BasePaymentProvider, PaymentResult, WebhookResult
from ...db.models import Payments
from ...common.exceptions import PaymentError


class TelegramStarsProvider(BasePaymentProvider):
    """
    Провайдер для Telegram Stars
    Использует встроенную платежную систему Telegram
    """
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.bot_token = config.get('bot_token')
        self.provider_token = config.get('provider_token', '')  # Для Stars может быть пустым
        self.bot = None
    
    def _validate_config(self) -> None:
        """Валидация конфигурации Telegram Stars"""
        required_fields = ['bot_token']
        
        for field in required_fields:
            if not self.config.get(field):
                raise ValueError(f"Telegram Stars: отсутствует обязательное поле {field}")
    
    def _get_bot(self) -> Bot:
        """Получение экземпляра бота"""
        if not self.bot:
            self.bot = Bot(token=self.bot_token)
        return self.bot
    
    async def create_payment(
        self,
        payment: Payments,
        return_url: Optional[str] = None,
        webhook_url: Optional[str] = None
    ) -> PaymentResult:
        """Создание платежа через Telegram Stars"""
        try:
            bot = self._get_bot()
            
            # Формируем описание товара
            title = f"VPN подписка - {payment.tariff.name}"
            description = f"Подписка на {payment.tariff.duration_days} дней"
            
            # Цена в Stars (должна быть целым числом)
            stars_amount = int(payment.final_amount)
            
            # Создаем инвойс
            prices = [LabeledPrice(label=title, amount=stars_amount)]
            
            # Формируем payload с информацией о платеже
            payload = f"payment_{payment.id}"
            
            # Создаем клавиатуру с кнопкой оплаты
            keyboard = InlineKeyboardMarkup(inline_keyboard=[
                [InlineKeyboardButton(
                    text=f"💳 Оплатить {stars_amount} ⭐",
                    pay=True
                )]
            ])
            
            # Отправляем инвойс пользователю
            invoice_message = await bot.send_invoice(
                chat_id=payment.user.telegram_id,
                title=title,
                description=description,
                payload=payload,
                provider_token=self.provider_token,
                currency="XTR",  # Telegram Stars
                prices=prices,
                reply_markup=keyboard
            )
            
            self._log_payment_action(
                'create',
                payment.id,
                payload,
                {'stars_amount': stars_amount, 'message_id': invoice_message.message_id}
            )
            
            return PaymentResult(
                success=True,
                payment_url=None,  # Для Stars URL не нужен
                external_payment_id=payload,
                metadata={
                    'invoice_message_id': invoice_message.message_id,
                    'stars_amount': stars_amount,
                    'created_at': datetime.now(timezone.utc).isoformat()
                }
            )
        
        except Exception as e:
            self.logger.error(f"Ошибка создания платежа Telegram Stars: {e}")
            return PaymentResult(
                success=False,
                error_message=f"Ошибка создания платежа: {e}"
            )
    
    async def check_payment_status(self, external_payment_id: str) -> str:
        """
        Проверка статуса платежа Telegram Stars
        Для Stars статус обновляется только через webhook
        """
        # Для Telegram Stars нет API для проверки статуса
        # Статус обновляется только через pre_checkout_query и successful_payment
        return 'pending'
    
    async def process_webhook(self, webhook_data: Dict[str, Any]) -> WebhookResult:
        """Обработка webhook от Telegram Stars"""
        try:
            # Определяем тип события
            if 'pre_checkout_query' in webhook_data:
                return await self._process_pre_checkout(webhook_data['pre_checkout_query'])
            elif 'successful_payment' in webhook_data:
                return await self._process_successful_payment(webhook_data['successful_payment'])
            else:
                return WebhookResult(
                    success=False,
                    error_message="Неподдерживаемый тип события Telegram"
                )
        
        except Exception as e:
            self.logger.error(f"Ошибка обработки webhook Telegram Stars: {e}")
            return WebhookResult(
                success=False,
                error_message=f"Ошибка обработки webhook: {e}"
            )
    
    async def _process_pre_checkout(self, pre_checkout_data: Dict[str, Any]) -> WebhookResult:
        """Обработка pre_checkout_query"""
        try:
            query_id = pre_checkout_data.get('id')
            payload = pre_checkout_data.get('invoice_payload')
            
            if not payload or not payload.startswith('payment_'):
                return WebhookResult(
                    success=False,
                    error_message="Неверный payload в pre_checkout_query"
                )
            
            # Извлекаем ID платежа
            payment_id = payload.replace('payment_', '')
            
            # Автоматически подтверждаем pre_checkout
            bot = self._get_bot()
            await bot.answer_pre_checkout_query(
                pre_checkout_query_id=query_id,
                ok=True
            )
            
            self._log_payment_action(
                'pre_checkout',
                payment_id,
                payload,
                {'query_id': query_id}
            )
            
            return WebhookResult(
                success=True,
                payment_status='pending',  # Еще не завершен
                external_payment_id=payload,
                metadata={
                    'pre_checkout_query_id': query_id,
                    'processed_at': datetime.now(timezone.utc).isoformat()
                }
            )
        
        except Exception as e:
            self.logger.error(f"Ошибка обработки pre_checkout: {e}")
            return WebhookResult(
                success=False,
                error_message=f"Ошибка pre_checkout: {e}"
            )
    
    async def _process_successful_payment(self, payment_data: Dict[str, Any]) -> WebhookResult:
        """Обработка successful_payment"""
        try:
            payload = payment_data.get('invoice_payload')
            telegram_payment_charge_id = payment_data.get('telegram_payment_charge_id')
            provider_payment_charge_id = payment_data.get('provider_payment_charge_id')
            
            if not payload or not payload.startswith('payment_'):
                return WebhookResult(
                    success=False,
                    error_message="Неверный payload в successful_payment"
                )
            
            # Извлекаем ID платежа
            payment_id = payload.replace('payment_', '')
            
            # Формируем метаданные
            metadata = {
                'telegram_payment_charge_id': telegram_payment_charge_id,
                'provider_payment_charge_id': provider_payment_charge_id,
                'total_amount': payment_data.get('total_amount'),
                'currency': payment_data.get('currency'),
                'processed_at': datetime.now(timezone.utc).isoformat()
            }
            
            self._log_payment_action(
                'successful_payment',
                payment_id,
                payload,
                {
                    'telegram_charge_id': telegram_payment_charge_id,
                    'amount': payment_data.get('total_amount')
                }
            )
            
            return WebhookResult(
                success=True,
                payment_status='completed',
                external_payment_id=payload,
                metadata=metadata
            )
        
        except Exception as e:
            self.logger.error(f"Ошибка обработки successful_payment: {e}")
            return WebhookResult(
                success=False,
                error_message=f"Ошибка successful_payment: {e}"
            )
    
    async def refund_payment(
        self,
        external_payment_id: str,
        amount: Optional[Decimal] = None,
        reason: Optional[str] = None
    ) -> bool:
        """
        Возврат платежа Telegram Stars
        Примечание: Telegram Stars поддерживает возвраты через API
        """
        try:
            bot = self._get_bot()
            
            # Извлекаем telegram_payment_charge_id из метаданных платежа
            # Это должно быть сохранено при обработке successful_payment
            
            # Для демонстрации используем заглушку
            # В реальной реализации нужно получить telegram_payment_charge_id из БД
            telegram_payment_charge_id = "fake_charge_id"
            
            # Выполняем возврат
            result = await bot.refund_star_payment(
                user_id=0,  # Нужно получить из БД
                telegram_payment_charge_id=telegram_payment_charge_id
            )
            
            if result:
                self._log_payment_action(
                    'refund',
                    'unknown',
                    external_payment_id,
                    {'telegram_charge_id': telegram_payment_charge_id}
                )
                
                return True
            else:
                return False
        
        except Exception as e:
            self.logger.error(f"Ошибка возврата платежа Telegram Stars: {e}")
            return False
    
    async def send_payment_notification(
        self,
        user_id: int,
        payment: Payments,
        success: bool = True
    ):
        """Отправка уведомления о платеже"""
        try:
            bot = self._get_bot()
            
            if success:
                text = (
                    f"✅ <b>Платеж успешно завершен!</b>\n\n"
                    f"Тариф: {payment.tariff.name}\n"
                    f"Сумма: {int(payment.final_amount)} ⭐\n"
                    f"Продолжительность: {payment.tariff.duration_days} дней\n\n"
                    f"Ваша подписка активирована!"
                )
            else:
                text = (
                    f"❌ <b>Ошибка платежа</b>\n\n"
                    f"Платеж не был завершен. Попробуйте еще раз или обратитесь в поддержку."
                )
            
            await bot.send_message(
                chat_id=user_id,
                text=text,
                parse_mode='HTML'
            )
        
        except Exception as e:
            self.logger.error(f"Ошибка отправки уведомления о платеже: {e}")
    
    async def close(self):
        """Закрытие соединения с ботом"""
        if self.bot:
            await self.bot.session.close()
