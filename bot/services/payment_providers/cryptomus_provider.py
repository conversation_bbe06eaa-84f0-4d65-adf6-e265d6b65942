"""
UnveilVPN Shop - Cryptomus Payment Provider
Провайдер для интеграции с Cryptomus (криптовалютные платежи)
"""

import hashlib
import hmac
import base64
from typing import Dict, Any, Optional
from decimal import Decimal
import aiohttp
import json
from datetime import datetime, timezone

from .base_provider import BasePaymentProvider, PaymentResult, WebhookResult
from ...db.models import Payments
from ...common.exceptions import PaymentError


class CryptomusProvider(BasePaymentProvider):
    """
    Провайдер для Cryptomus
    Поддерживает различные криптовалюты
    """
    
    API_URL = "https://api.cryptomus.com/v1"
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.merchant_id = config.get('merchant_id')
        self.api_key = config.get('api_key')
    
    def _validate_config(self) -> None:
        """Валидация конфигурации Cryptomus"""
        required_fields = ['merchant_id', 'api_key']
        
        for field in required_fields:
            if not self.config.get(field):
                raise ValueError(f"Cryptomus: отсутствует обязательное поле {field}")
    
    def _create_signature(self, data: Dict[str, Any]) -> str:
        """Создание подписи для запроса"""
        # Сортируем данные по ключам
        sorted_data = dict(sorted(data.items()))
        
        # Создаем строку для подписи
        sign_string = base64.b64encode(json.dumps(sorted_data).encode()).decode()
        
        # Создаем HMAC подпись
        signature = hmac.new(
            self.api_key.encode(),
            sign_string.encode(),
            hashlib.md5
        ).hexdigest()
        
        return signature
    
    async def create_payment(
        self,
        payment: Payments,
        return_url: Optional[str] = None,
        webhook_url: Optional[str] = None
    ) -> PaymentResult:
        """Создание платежа в Cryptomus"""
        try:
            # Формируем данные платежа
            payment_data = {
                "amount": self._format_amount(payment.final_amount, payment.currency),
                "currency": payment.currency,
                "order_id": self._generate_order_id(payment.id),
                "lifetime": 1800,  # 30 минут
                "url_callback": webhook_url,
                "url_return": return_url or "https://t.me/unveilvpn_bot",
                "is_payment_multiple": False,
                "additional_data": json.dumps({
                    "payment_id": str(payment.id),
                    "user_id": str(payment.user_id),
                    "tariff_id": str(payment.tariff_id)
                })
            }
            
            # Создаем подпись
            signature = self._create_signature(payment_data)
            
            headers = {
                "merchant": self.merchant_id,
                "sign": signature,
                "Content-Type": "application/json"
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.API_URL}/payment",
                    json=payment_data,
                    headers=headers
                ) as response:
                    
                    if response.status == 200:
                        result_data = await response.json()
                        
                        if result_data.get('state') == 0:  # Успех
                            payment_info = result_data['result']
                            
                            self._log_payment_action(
                                'create',
                                payment.id,
                                payment_info.get('uuid'),
                                {'order_id': payment_info.get('order_id')}
                            )
                            
                            return PaymentResult(
                                success=True,
                                payment_url=payment_info['url'],
                                external_payment_id=payment_info['uuid'],
                                metadata={
                                    'cryptomus_order_id': payment_info.get('order_id'),
                                    'created_at': datetime.now(timezone.utc).isoformat()
                                }
                            )
                        else:
                            error_message = result_data.get('message', 'Неизвестная ошибка Cryptomus')
                            
                            self.logger.error(f"Cryptomus API error: {error_message}")
                            
                            return PaymentResult(
                                success=False,
                                error_message=error_message
                            )
                    else:
                        error_text = await response.text()
                        self.logger.error(f"Cryptomus HTTP error: {response.status} - {error_text}")
                        
                        return PaymentResult(
                            success=False,
                            error_message=f"Ошибка HTTP: {response.status}"
                        )
        
        except Exception as e:
            self.logger.error(f"Ошибка создания платежа Cryptomus: {e}")
            return PaymentResult(
                success=False,
                error_message=f"Ошибка создания платежа: {e}"
            )
    
    async def check_payment_status(self, external_payment_id: str) -> str:
        """Проверка статуса платежа в Cryptomus"""
        try:
            payment_data = {
                "uuid": external_payment_id
            }
            
            signature = self._create_signature(payment_data)
            
            headers = {
                "merchant": self.merchant_id,
                "sign": signature,
                "Content-Type": "application/json"
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.API_URL}/payment/info",
                    json=payment_data,
                    headers=headers
                ) as response:
                    
                    if response.status == 200:
                        result_data = await response.json()
                        
                        if result_data.get('state') == 0:
                            payment_info = result_data['result']
                            cryptomus_status = payment_info.get('payment_status', 'check')
                            return self._normalize_cryptomus_status(cryptomus_status)
                        else:
                            self.logger.error(f"Ошибка проверки статуса Cryptomus: {result_data.get('message')}")
                            return 'pending'
                    else:
                        self.logger.error(f"Ошибка проверки статуса Cryptomus: {response.status}")
                        return 'pending'
        
        except Exception as e:
            self.logger.error(f"Ошибка проверки статуса платежа Cryptomus: {e}")
            return 'pending'
    
    async def process_webhook(self, webhook_data: Dict[str, Any]) -> WebhookResult:
        """Обработка webhook от Cryptomus"""
        try:
            # Проверяем подпись webhook
            if not self._verify_webhook_signature(webhook_data):
                return WebhookResult(
                    success=False,
                    error_message="Неверная подпись webhook"
                )
            
            # Извлекаем данные о платеже
            external_payment_id = webhook_data.get('uuid')
            if not external_payment_id:
                return WebhookResult(
                    success=False,
                    error_message="Отсутствует UUID платежа в webhook"
                )
            
            # Определяем статус
            cryptomus_status = webhook_data.get('status', 'check')
            normalized_status = self._normalize_cryptomus_status(cryptomus_status)
            
            # Формируем метаданные
            metadata = {
                'webhook_type': webhook_data.get('type'),
                'cryptomus_status': cryptomus_status,
                'currency': webhook_data.get('currency'),
                'amount': webhook_data.get('amount'),
                'network': webhook_data.get('network'),
                'txid': webhook_data.get('txid'),
                'processed_at': datetime.now(timezone.utc).isoformat()
            }
            
            # Извлекаем дополнительные данные
            additional_data = webhook_data.get('additional_data')
            if additional_data:
                try:
                    parsed_data = json.loads(additional_data)
                    metadata.update(parsed_data)
                except json.JSONDecodeError:
                    pass
            
            self._log_payment_action(
                'webhook',
                metadata.get('payment_id', 'unknown'),
                external_payment_id,
                {'status': cryptomus_status, 'type': webhook_data.get('type')}
            )
            
            return WebhookResult(
                success=True,
                payment_status=normalized_status,
                external_payment_id=external_payment_id,
                metadata=metadata
            )
        
        except Exception as e:
            self.logger.error(f"Ошибка обработки webhook Cryptomus: {e}")
            return WebhookResult(
                success=False,
                error_message=f"Ошибка обработки webhook: {e}"
            )
    
    async def refund_payment(
        self,
        external_payment_id: str,
        amount: Optional[Decimal] = None,
        reason: Optional[str] = None
    ) -> bool:
        """
        Возврат платежа в Cryptomus
        Примечание: Cryptomus не поддерживает автоматические возвраты криптовалют
        """
        self.logger.warning(f"Возврат криптовалютного платежа {external_payment_id} требует ручной обработки")
        return False
    
    def _verify_webhook_signature(self, webhook_data: Dict[str, Any]) -> bool:
        """Проверка подписи webhook"""
        try:
            received_signature = webhook_data.pop('sign', '')
            calculated_signature = self._create_signature(webhook_data)
            
            # Возвращаем подпись обратно
            webhook_data['sign'] = received_signature
            
            return hmac.compare_digest(received_signature, calculated_signature)
        
        except Exception as e:
            self.logger.error(f"Ошибка проверки подписи webhook: {e}")
            return False
    
    def _normalize_cryptomus_status(self, cryptomus_status: str) -> str:
        """Нормализация статуса Cryptomus"""
        status_mapping = {
            'check': 'pending',
            'process': 'pending',
            'confirm_check': 'pending',
            'paid': 'completed',
            'paid_over': 'completed',
            'fail': 'failed',
            'cancel': 'failed',
            'system_fail': 'failed',
            'refund_process': 'refunded',
            'refund_fail': 'failed',
            'refund_paid': 'refunded'
        }
        
        return status_mapping.get(cryptomus_status.lower(), 'pending')
