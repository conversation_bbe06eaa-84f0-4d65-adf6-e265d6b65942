"""
UnveilVPN Shop - Base Payment Provider
Базовый класс для провайдеров платежных систем
"""

import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
from decimal import Decimal
from uuid import UUID

from ...db.models import Payments


class PaymentResult:
    """Результат создания платежа"""
    
    def __init__(
        self,
        success: bool,
        payment_url: Optional[str] = None,
        external_payment_id: Optional[str] = None,
        error_message: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ):
        self.success = success
        self.payment_url = payment_url
        self.external_payment_id = external_payment_id
        self.error_message = error_message
        self.metadata = metadata or {}


class WebhookResult:
    """Результат обработки webhook"""
    
    def __init__(
        self,
        success: bool,
        payment_status: Optional[str] = None,
        external_payment_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
        error_message: Optional[str] = None
    ):
        self.success = success
        self.payment_status = payment_status
        self.external_payment_id = external_payment_id
        self.metadata = metadata or {}
        self.error_message = error_message


class BasePaymentProvider(ABC):
    """
    Базовый класс для провайдеров платежных систем
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
        self._validate_config()
    
    @abstractmethod
    def _validate_config(self) -> None:
        """Валидация конфигурации провайдера"""
        pass
    
    @abstractmethod
    async def create_payment(
        self,
        payment: Payments,
        return_url: Optional[str] = None,
        webhook_url: Optional[str] = None
    ) -> PaymentResult:
        """
        Создание платежа в платежной системе
        
        Args:
            payment: Объект платежа из БД
            return_url: URL для возврата после оплаты
            webhook_url: URL для webhook уведомлений
            
        Returns:
            PaymentResult: Результат создания платежа
        """
        pass
    
    @abstractmethod
    async def check_payment_status(self, external_payment_id: str) -> str:
        """
        Проверка статуса платежа в платежной системе
        
        Args:
            external_payment_id: ID платежа в платежной системе
            
        Returns:
            str: Статус платежа ('pending', 'completed', 'failed', 'refunded')
        """
        pass
    
    @abstractmethod
    async def process_webhook(self, webhook_data: Dict[str, Any]) -> WebhookResult:
        """
        Обработка webhook от платежной системы
        
        Args:
            webhook_data: Данные webhook
            
        Returns:
            WebhookResult: Результат обработки webhook
        """
        pass
    
    @abstractmethod
    async def refund_payment(
        self,
        external_payment_id: str,
        amount: Optional[Decimal] = None,
        reason: Optional[str] = None
    ) -> bool:
        """
        Возврат платежа
        
        Args:
            external_payment_id: ID платежа в платежной системе
            amount: Сумма возврата (None для полного возврата)
            reason: Причина возврата
            
        Returns:
            bool: Успешность операции
        """
        pass
    
    def _normalize_status(self, provider_status: str) -> str:
        """
        Нормализация статуса платежа к внутреннему формату
        
        Args:
            provider_status: Статус от провайдера
            
        Returns:
            str: Нормализованный статус
        """
        # Базовая реализация, переопределяется в наследниках
        status_mapping = {
            'pending': 'pending',
            'waiting_for_capture': 'pending',
            'succeeded': 'completed',
            'canceled': 'failed',
            'failed': 'failed',
            'refunded': 'refunded'
        }
        
        return status_mapping.get(provider_status.lower(), 'pending')
    
    def _format_amount(self, amount: Decimal, currency: str) -> str:
        """
        Форматирование суммы для платежной системы
        
        Args:
            amount: Сумма
            currency: Валюта
            
        Returns:
            str: Отформатированная сумма
        """
        if currency.upper() in ['RUB', 'USD', 'EUR']:
            return f"{amount:.2f}"
        elif currency.upper() == 'STARS':
            return str(int(amount))
        else:
            return str(amount)
    
    def _generate_order_id(self, payment_id: UUID) -> str:
        """
        Генерация ID заказа для платежной системы
        
        Args:
            payment_id: ID платежа в нашей системе
            
        Returns:
            str: ID заказа
        """
        return f"unveilvpn_{payment_id}"
    
    def _log_payment_action(
        self,
        action: str,
        payment_id: UUID,
        external_payment_id: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        """
        Логирование действий с платежами
        
        Args:
            action: Действие (create, update, refund, etc.)
            payment_id: ID платежа
            external_payment_id: Внешний ID платежа
            details: Дополнительные детали
        """
        log_data = {
            'action': action,
            'payment_id': str(payment_id),
            'provider': self.__class__.__name__
        }
        
        if external_payment_id:
            log_data['external_payment_id'] = external_payment_id
        
        if details:
            log_data.update(details)
        
        self.logger.info(f"Payment action: {log_data}")
    
    def is_configured(self) -> bool:
        """
        Проверка корректности конфигурации провайдера
        
        Returns:
            bool: True если провайдер настроен корректно
        """
        try:
            self._validate_config()
            return True
        except Exception:
            return False
