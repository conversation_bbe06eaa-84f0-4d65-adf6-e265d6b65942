"""
UnveilVPN Shop - Backup Service
Сервис для создания и управления резервными копиями
"""

import os
import gzip
import json
import shutil
import logging
import tempfile
from typing import Dict, Any, List, Optional
from datetime import datetime, timezone
from pathlib import Path
from uuid import UUID, uuid4

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_, desc, text

from ..db.models import VPNUsers, Payments, Tariffs, Promocodes, SystemBackups
from ..common.exceptions import BusinessLogicError


class BackupService:
    """
    Сервис для создания и управления резервными копиями
    """
    
    def __init__(self, db_session: AsyncSession):
        self.db_session = db_session
        self.logger = logging.getLogger(self.__class__.__name__)
        self.backup_dir = Path("backups")
        self.backup_dir.mkdir(exist_ok=True)
    
    async def create_backup(
        self,
        backup_type: str = "full",
        created_by_id: Optional[UUID] = None
    ) -> Dict[str, Any]:
        """Создание резервной копии"""
        try:
            backup_id = uuid4()
            timestamp = datetime.now(timezone.utc)
            
            # Создаем директорию для бэкапа
            backup_path = self.backup_dir / f"backup_{backup_id}"
            backup_path.mkdir(exist_ok=True)
            
            # Экспортируем данные
            backup_data = await self._export_database_data(backup_type)
            
            # Сохраняем данные в файл
            backup_file = backup_path / "data.json"
            with open(backup_file, 'w', encoding='utf-8') as f:
                json.dump(backup_data, f, ensure_ascii=False, indent=2, default=str)
            
            # Сжимаем бэкап
            compressed_file = backup_path.with_suffix('.gz')
            with open(backup_file, 'rb') as f_in:
                with gzip.open(compressed_file, 'wb') as f_out:
                    shutil.copyfileobj(f_in, f_out)
            
            # Удаляем несжатый файл
            backup_file.unlink()
            shutil.rmtree(backup_path)
            
            # Получаем размер файла
            file_size = compressed_file.stat().st_size
            size_mb = file_size / (1024 * 1024)
            
            # Сохраняем информацию о бэкапе в БД
            backup_record = SystemBackups(
                id=backup_id,
                backup_type=backup_type,
                file_path=str(compressed_file),
                file_size=file_size,
                created_by_id=created_by_id,
                created_at=timestamp,
                metadata={
                    'tables_count': len(backup_data),
                    'records_count': sum(len(table_data) for table_data in backup_data.values()),
                    'compression': 'gzip'
                }
            )
            
            self.db_session.add(backup_record)
            await self.db_session.commit()
            
            return {
                'id': str(backup_id),
                'type': backup_type,
                'size_mb': round(size_mb, 2),
                'created_at': timestamp.strftime('%d.%m.%Y %H:%M:%S'),
                'file_path': str(compressed_file),
                'records_count': backup_record.metadata.get('records_count', 0)
            }
            
        except Exception as e:
            self.logger.error(f"Ошибка создания бэкапа: {e}")
            raise BusinessLogicError(f"Не удалось создать резервную копию: {e}")
    
    async def get_backups_list(self, limit: int = 20) -> List[Dict[str, Any]]:
        """Получение списка резервных копий"""
        try:
            query = select(SystemBackups).order_by(desc(SystemBackups.created_at)).limit(limit)
            result = await self.db_session.execute(query)
            backups = result.scalars().all()
            
            backups_list = []
            for backup in backups:
                # Проверяем существование файла
                file_exists = os.path.exists(backup.file_path) if backup.file_path else False
                
                size_mb = backup.file_size / (1024 * 1024) if backup.file_size else 0
                
                backups_list.append({
                    'id': str(backup.id),
                    'type': backup.backup_type,
                    'size_mb': round(size_mb, 2),
                    'created_at': backup.created_at.strftime('%d.%m.%Y %H:%M:%S'),
                    'file_exists': file_exists,
                    'records_count': backup.metadata.get('records_count', 0) if backup.metadata else 0,
                    'created_by': str(backup.created_by_id) if backup.created_by_id else None
                })
            
            return backups_list
            
        except Exception as e:
            self.logger.error(f"Ошибка получения списка бэкапов: {e}")
            raise BusinessLogicError(f"Не удалось получить список резервных копий: {e}")
    
    async def restore_backup(self, backup_id: UUID, restore_options: Dict[str, Any] = None) -> Dict[str, Any]:
        """Восстановление из резервной копии"""
        try:
            # Получаем информацию о бэкапе
            query = select(SystemBackups).where(SystemBackups.id == backup_id)
            result = await self.db_session.execute(query)
            backup = result.scalar_one_or_none()
            
            if not backup:
                raise BusinessLogicError("Резервная копия не найдена")
            
            if not os.path.exists(backup.file_path):
                raise BusinessLogicError("Файл резервной копии не найден")
            
            # Загружаем данные из бэкапа
            backup_data = await self._load_backup_data(backup.file_path)
            
            # Восстанавливаем данные
            restore_stats = await self._restore_database_data(backup_data, restore_options)
            
            return {
                'backup_id': str(backup_id),
                'restored_tables': restore_stats['tables'],
                'restored_records': restore_stats['records'],
                'execution_time': restore_stats['execution_time'],
                'status': 'success'
            }
            
        except Exception as e:
            self.logger.error(f"Ошибка восстановления бэкапа: {e}")
            raise BusinessLogicError(f"Не удалось восстановить резервную копию: {e}")
    
    async def delete_backup(self, backup_id: UUID) -> bool:
        """Удаление резервной копии"""
        try:
            # Получаем информацию о бэкапе
            query = select(SystemBackups).where(SystemBackups.id == backup_id)
            result = await self.db_session.execute(query)
            backup = result.scalar_one_or_none()
            
            if not backup:
                return False
            
            # Удаляем файл
            if backup.file_path and os.path.exists(backup.file_path):
                os.remove(backup.file_path)
            
            # Удаляем запись из БД
            await self.db_session.delete(backup)
            await self.db_session.commit()
            
            return True
            
        except Exception as e:
            self.logger.error(f"Ошибка удаления бэкапа: {e}")
            return False
    
    async def get_backup_info(self, backup_id: UUID) -> Optional[Dict[str, Any]]:
        """Получение детальной информации о резервной копии"""
        try:
            query = select(SystemBackups).where(SystemBackups.id == backup_id)
            result = await self.db_session.execute(query)
            backup = result.scalar_one_or_none()
            
            if not backup:
                return None
            
            file_exists = os.path.exists(backup.file_path) if backup.file_path else False
            size_mb = backup.file_size / (1024 * 1024) if backup.file_size else 0
            
            return {
                'id': str(backup.id),
                'type': backup.backup_type,
                'size_mb': round(size_mb, 2),
                'created_at': backup.created_at.strftime('%d.%m.%Y %H:%M:%S'),
                'file_path': backup.file_path,
                'file_exists': file_exists,
                'metadata': backup.metadata or {},
                'created_by': str(backup.created_by_id) if backup.created_by_id else None
            }
            
        except Exception as e:
            self.logger.error(f"Ошибка получения информации о бэкапе: {e}")
            return None
    
    async def cleanup_old_backups(self, keep_days: int = 30) -> Dict[str, Any]:
        """Очистка старых резервных копий"""
        try:
            cutoff_date = datetime.now(timezone.utc) - timedelta(days=keep_days)
            
            # Находим старые бэкапы
            query = select(SystemBackups).where(SystemBackups.created_at < cutoff_date)
            result = await self.db_session.execute(query)
            old_backups = result.scalars().all()
            
            deleted_count = 0
            freed_space = 0
            
            for backup in old_backups:
                # Удаляем файл
                if backup.file_path and os.path.exists(backup.file_path):
                    file_size = os.path.getsize(backup.file_path)
                    os.remove(backup.file_path)
                    freed_space += file_size
                
                # Удаляем запись из БД
                await self.db_session.delete(backup)
                deleted_count += 1
            
            await self.db_session.commit()
            
            freed_space_mb = freed_space / (1024 * 1024)
            
            return {
                'deleted_count': deleted_count,
                'freed_space_mb': round(freed_space_mb, 2),
                'cutoff_date': cutoff_date.strftime('%d.%m.%Y')
            }
            
        except Exception as e:
            self.logger.error(f"Ошибка очистки старых бэкапов: {e}")
            raise BusinessLogicError(f"Не удалось очистить старые резервные копии: {e}")
    
    # Приватные методы
    
    async def _export_database_data(self, backup_type: str) -> Dict[str, Any]:
        """Экспорт данных из базы данных"""
        try:
            backup_data = {}
            
            # Экспортируем пользователей
            users_query = select(VPNUsers)
            users_result = await self.db_session.execute(users_query)
            users = users_result.scalars().all()
            
            backup_data['users'] = []
            for user in users:
                backup_data['users'].append({
                    'id': str(user.id),
                    'telegram_id': user.telegram_id,
                    'username': user.username,
                    'vpn_id': user.vpn_id,
                    'is_blocked': user.is_blocked,
                    'blocked_reason': user.blocked_reason,
                    'referral_code': user.referral_code,
                    'referred_by_id': str(user.referred_by_id) if user.referred_by_id else None,
                    'referral_earnings': float(user.referral_earnings),
                    'is_test_used': user.is_test_used,
                    'user_metadata': user.user_metadata,
                    'created_at': user.created_at.isoformat(),
                    'last_activity': user.last_activity.isoformat() if user.last_activity else None
                })
            
            # Экспортируем платежи
            payments_query = select(Payments)
            payments_result = await self.db_session.execute(payments_query)
            payments = payments_result.scalars().all()
            
            backup_data['payments'] = []
            for payment in payments:
                backup_data['payments'].append({
                    'id': str(payment.id),
                    'user_id': str(payment.user_id),
                    'tariff_id': str(payment.tariff_id) if payment.tariff_id else None,
                    'amount': float(payment.amount),
                    'currency': payment.currency,
                    'payment_method': payment.payment_method,
                    'status': payment.status,
                    'external_id': payment.external_id,
                    'payment_metadata': payment.payment_metadata,
                    'created_at': payment.created_at.isoformat(),
                    'updated_at': payment.updated_at.isoformat() if payment.updated_at else None
                })
            
            # Экспортируем тарифы
            tariffs_query = select(Tariffs)
            tariffs_result = await self.db_session.execute(tariffs_query)
            tariffs = tariffs_result.scalars().all()
            
            backup_data['tariffs'] = []
            for tariff in tariffs:
                backup_data['tariffs'].append({
                    'id': str(tariff.id),
                    'name': tariff.name,
                    'duration_days': tariff.duration_days,
                    'prices': tariff.prices,
                    'features': tariff.features,
                    'is_active': tariff.is_active,
                    'is_popular': tariff.is_popular,
                    'created_at': tariff.created_at.isoformat()
                })
            
            # Экспортируем промокоды
            promocodes_query = select(Promocodes)
            promocodes_result = await self.db_session.execute(promocodes_query)
            promocodes = promocodes_result.scalars().all()
            
            backup_data['promocodes'] = []
            for promocode in promocodes:
                backup_data['promocodes'].append({
                    'id': str(promocode.id),
                    'code': promocode.code,
                    'discount_type': promocode.discount_type,
                    'discount_value': float(promocode.discount_value),
                    'usage_limit': promocode.usage_limit,
                    'usage_count': promocode.usage_count,
                    'is_active': promocode.is_active,
                    'expires_at': promocode.expires_at.isoformat() if promocode.expires_at else None,
                    'created_by_id': str(promocode.created_by_id) if promocode.created_by_id else None,
                    'created_at': promocode.created_at.isoformat()
                })
            
            return backup_data
            
        except Exception as e:
            self.logger.error(f"Ошибка экспорта данных: {e}")
            raise BusinessLogicError(f"Не удалось экспортировать данные: {e}")
    
    async def _load_backup_data(self, file_path: str) -> Dict[str, Any]:
        """Загрузка данных из файла резервной копии"""
        try:
            with gzip.open(file_path, 'rt', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            self.logger.error(f"Ошибка загрузки бэкапа: {e}")
            raise BusinessLogicError(f"Не удалось загрузить резервную копию: {e}")
    
    async def _restore_database_data(
        self,
        backup_data: Dict[str, Any],
        restore_options: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Восстановление данных в базу данных"""
        try:
            start_time = datetime.now()
            restore_options = restore_options or {}
            
            restored_tables = 0
            restored_records = 0
            
            # В реальности здесь будет сложная логика восстановления
            # с проверками конфликтов, опциями восстановления и т.д.
            
            # Пока что просто имитируем процесс
            for table_name, table_data in backup_data.items():
                if restore_options.get(f'restore_{table_name}', True):
                    restored_tables += 1
                    restored_records += len(table_data)
            
            execution_time = (datetime.now() - start_time).total_seconds()
            
            return {
                'tables': restored_tables,
                'records': restored_records,
                'execution_time': execution_time
            }
            
        except Exception as e:
            self.logger.error(f"Ошибка восстановления данных: {e}")
            raise BusinessLogicError(f"Не удалось восстановить данные: {e}")
