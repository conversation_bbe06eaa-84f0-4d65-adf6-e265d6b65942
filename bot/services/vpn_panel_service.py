"""
UnveilVPN Shop - VPN Panel Service (Remnawave)
Сервис для интеграции с Remnawave VPN панелью управления
"""

import logging
import aiohttp
import json
from typing import Dict, Any, Optional, List

from ..common.exceptions import VPNPanelError, BusinessLogicError
from ..utils.remnawave_api import RemnaWave, RemnaWaveAPIError


class VPNPanelService:
    """
    Сервис для работы с Remnawave VPN панелью управления
    """

    def __init__(self, panel_url: str, api_key: str, timeout: int = 30):
        self.panel_url = panel_url.rstrip('/')
        self.api_key = api_key
        self.timeout = timeout
        self.logger = logging.getLogger(self.__class__.__name__)

        # Инициализация Remnawave API
        self.remnawave = RemnaWave(
            panel_url=panel_url,
            api_key=api_key,
            timeout=timeout
        )

        # Заголовки для API запросов (для обратной совместимости)
        self.headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json',
            'User-Agent': 'UnveilVPN-Shop/1.0'
        }
    
    async def create_user(
        self,
        telegram_id: int,
        username: Optional[str] = None,
        email: Optional[str] = None,
        protocols: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """Создание пользователя в Remnawave панели"""
        try:
            data = {
                'telegram_id': telegram_id,
                'username': username or f'user_{telegram_id}',
                'email': email or f'user_{telegram_id}@unveilvpn.local',
                'protocols': protocols or ['vless', 'vmess']
            }

            response = await self.remnawave.create_user(data)

            self.logger.info(f"Создан пользователь VPN для Telegram ID {telegram_id}")
            return response

        except RemnaWaveAPIError as e:
            self.logger.error(f"Ошибка создания пользователя VPN для {telegram_id}: {e}")
            raise VPNPanelError(f"Не удалось создать пользователя VPN: {e}")
        except Exception as e:
            self.logger.error(f"Неожиданная ошибка создания пользователя VPN для {telegram_id}: {e}")
            raise VPNPanelError(f"Не удалось создать пользователя VPN: {e}")
    
    async def get_user(self, vpn_user_id: str) -> Optional[Dict[str, Any]]:
        """Получение информации о пользователе VPN из Remnawave"""
        try:
            response = await self.remnawave.get_user(vpn_user_id)
            return response

        except RemnaWaveAPIError as e:
            if "404" in str(e):
                return None
            raise VPNPanelError(f"Ошибка получения пользователя VPN: {e}")
        except Exception as e:
            self.logger.error(f"Ошибка получения пользователя VPN {vpn_user_id}: {e}")
            raise VPNPanelError(f"Не удалось получить пользователя VPN: {e}")
    
    async def create_subscription(
        self,
        vpn_user_id: str,
        tariff_name: str,
        duration_days: int,
        traffic_limit_gb: Optional[int] = None
    ) -> Dict[str, Any]:
        """Создание подписки для пользователя в Remnawave"""
        try:
            response = await self.remnawave.create_subscription(
                user_id=vpn_user_id,
                tariff_name=tariff_name,
                duration_days=duration_days,
                traffic_limit_gb=traffic_limit_gb
            )

            self.logger.info(f"Создана подписка для пользователя {vpn_user_id}")
            return response

        except RemnaWaveAPIError as e:
            self.logger.error(f"Ошибка создания подписки для {vpn_user_id}: {e}")
            raise VPNPanelError(f"Не удалось создать подписку: {e}")
        except Exception as e:
            self.logger.error(f"Неожиданная ошибка создания подписки для {vpn_user_id}: {e}")
            raise VPNPanelError(f"Не удалось создать подписку: {e}")
    
    async def extend_subscription(
        self,
        vpn_user_id: str,
        additional_days: int
    ) -> Dict[str, Any]:
        """Продление подписки пользователя в Remnawave"""
        try:
            response = await self.remnawave.extend_subscription(
                user_id=vpn_user_id,
                additional_days=additional_days
            )

            self.logger.info(f"Продлена подписка для пользователя {vpn_user_id} на {additional_days} дней")
            return response

        except RemnaWaveAPIError as e:
            self.logger.error(f"Ошибка продления подписки для {vpn_user_id}: {e}")
            raise VPNPanelError(f"Не удалось продлить подписку: {e}")
        except Exception as e:
            self.logger.error(f"Неожиданная ошибка продления подписки для {vpn_user_id}: {e}")
            raise VPNPanelError(f"Не удалось продлить подписку: {e}")
    
    async def get_subscription_info(self, vpn_user_id: str) -> Optional[Dict[str, Any]]:
        """Получение информации о подписке из Remnawave"""
        try:
            response = await self.remnawave.get_subscription(vpn_user_id)
            return response

        except RemnaWaveAPIError as e:
            if "404" in str(e):
                return None
            raise VPNPanelError(f"Ошибка получения подписки: {e}")
        except Exception as e:
            self.logger.error(f"Ошибка получения подписки для {vpn_user_id}: {e}")
            raise VPNPanelError(f"Не удалось получить информацию о подписке: {e}")

    async def get_user_configs(self, vpn_user_id: str) -> List[Dict[str, Any]]:
        """Получение конфигураций пользователя из Remnawave"""
        try:
            response = await self.remnawave.get_user_configs(vpn_user_id)
            return response

        except RemnaWaveAPIError as e:
            self.logger.error(f"Ошибка получения конфигураций для {vpn_user_id}: {e}")
            raise VPNPanelError(f"Не удалось получить конфигурации: {e}")
        except Exception as e:
            self.logger.error(f"Неожиданная ошибка получения конфигураций для {vpn_user_id}: {e}")
            raise VPNPanelError(f"Не удалось получить конфигурации: {e}")
    
    async def generate_config(
        self,
        vpn_user_id: str,
        device_name: str,
        protocol: str = 'vless'
    ) -> Dict[str, Any]:
        """Генерация новой конфигурации в Remnawave"""
        try:
            response = await self.remnawave.create_config(
                user_id=vpn_user_id,
                device_name=device_name,
                protocol=protocol
            )

            self.logger.info(f"Сгенерирована конфигурация {protocol} для {vpn_user_id}")
            return response
            
        except RemnaWaveAPIError as e:
            self.logger.error(f"Ошибка генерации конфигурации для {vpn_user_id}: {e}")
            raise VPNPanelError(f"Не удалось сгенерировать конфигурацию: {e}")
        except Exception as e:
            self.logger.error(f"Неожиданная ошибка генерации конфигурации для {vpn_user_id}: {e}")
            raise VPNPanelError(f"Не удалось сгенерировать конфигурацию: {e}")

    async def revoke_config(self, config_id: str) -> bool:
        """Отзыв конфигурации в Remnawave"""
        try:
            result = await self.remnawave.delete_config(config_id)

            self.logger.info(f"Отозвана конфигурация {config_id}")
            return result

        except RemnaWaveAPIError as e:
            self.logger.error(f"Ошибка отзыва конфигурации {config_id}: {e}")
            raise VPNPanelError(f"Не удалось отозвать конфигурацию: {e}")
        except Exception as e:
            self.logger.error(f"Неожиданная ошибка отзыва конфигурации {config_id}: {e}")
            raise VPNPanelError(f"Не удалось отозвать конфигурацию: {e}")

    async def suspend_user(self, vpn_user_id: str, reason: str = "Payment expired") -> bool:
        """Приостановка доступа пользователя в Remnawave"""
        try:
            result = await self.remnawave.suspend_user(vpn_user_id, reason)
            
            self.logger.info(f"Приостановлен доступ для пользователя {vpn_user_id}")
            return result

        except RemnaWaveAPIError as e:
            self.logger.error(f"Ошибка приостановки пользователя {vpn_user_id}: {e}")
            raise VPNPanelError(f"Не удалось приостановить пользователя: {e}")
        except Exception as e:
            self.logger.error(f"Неожиданная ошибка приостановки пользователя {vpn_user_id}: {e}")
            raise VPNPanelError(f"Не удалось приостановить пользователя: {e}")

    async def reactivate_user(self, vpn_user_id: str) -> bool:
        """Восстановление доступа пользователя в Remnawave"""
        try:
            result = await self.remnawave.activate_user(vpn_user_id)

            self.logger.info(f"Восстановлен доступ для пользователя {vpn_user_id}")
            return result

        except RemnaWaveAPIError as e:
            self.logger.error(f"Ошибка восстановления пользователя {vpn_user_id}: {e}")
            raise VPNPanelError(f"Не удалось восстановить пользователя: {e}")
        except Exception as e:
            self.logger.error(f"Неожиданная ошибка восстановления пользователя {vpn_user_id}: {e}")
            raise VPNPanelError(f"Не удалось восстановить пользователя: {e}")

    async def get_user_statistics(self, vpn_user_id: str) -> Dict[str, Any]:
        """Получение статистики использования из Remnawave"""
        try:
            response = await self.remnawave.get_user_stats(vpn_user_id)
            return response

        except RemnaWaveAPIError as e:
            self.logger.error(f"Ошибка получения статистики для {vpn_user_id}: {e}")
            raise VPNPanelError(f"Не удалось получить статистику: {e}")
        except Exception as e:
            self.logger.error(f"Неожиданная ошибка получения статистики для {vpn_user_id}: {e}")
            raise VPNPanelError(f"Не удалось получить статистику: {e}")

    async def check_connection(self) -> bool:
        """Проверка соединения с Remnawave панелью"""
        try:
            return await self.remnawave.check_connection()
        except Exception as e:
            self.logger.error(f"Ошибка проверки соединения с Remnawave: {e}")
            return False

    async def get_servers(self) -> List[Dict[str, Any]]:
        """Получение списка серверов из Remnawave"""
        try:
            return await self.remnawave.get_servers()
        except RemnaWaveAPIError as e:
            self.logger.error(f"Ошибка получения списка серверов: {e}")
            raise VPNPanelError(f"Не удалось получить список серверов: {e}")
        except Exception as e:
            self.logger.error(f"Неожиданная ошибка получения списка серверов: {e}")
            raise VPNPanelError(f"Не удалось получить список серверов: {e}")
    
    async def get_server_list(self) -> List[Dict[str, Any]]:
        """Получение списка доступных серверов"""
        try:
            response = await self._make_request('GET', '/api/servers')
            return response.get('servers', [])
            
        except Exception as e:
            self.logger.error(f"Ошибка получения списка серверов: {e}")
            raise VPNPanelError(f"Не удалось получить список серверов: {e}")
    
    async def check_connection(self) -> bool:
        """Проверка соединения с VPN панелью"""
        try:
            await self._make_request('GET', '/api/health')
            return True
            
        except Exception as e:
            self.logger.error(f"Ошибка соединения с VPN панелью: {e}")
            return False
    
    # Методы для обратной совместимости (deprecated)
    # Эти методы будут удалены после полного перехода на Remnawave

    async def _make_request(
        self,
        method: str,
        endpoint: str,
        data: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Выполнение HTTP запроса к VPN панели"""
        url = f"{self.panel_url}{endpoint}"
        
        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout)) as session:
            try:
                kwargs = {
                    'headers': self.headers,
                    'ssl': False  # Для разработки, в продакшене использовать SSL
                }
                
                if data:
                    kwargs['json'] = data
                
                async with session.request(method, url, **kwargs) as response:
                    response.raise_for_status()
                    
                    if response.content_type == 'application/json':
                        return await response.json()
                    else:
                        return {'success': True, 'data': await response.text()}
                        
            except aiohttp.ClientError as e:
                self.logger.error(f"HTTP ошибка при запросе к {url}: {e}")
                raise VPNPanelError(f"Ошибка соединения с VPN панелью: {e}")
            except json.JSONDecodeError as e:
                self.logger.error(f"Ошибка декодирования JSON ответа от {url}: {e}")
                raise VPNPanelError(f"Некорректный ответ от VPN панели: {e}")


class VPNPanelIntegration:
    """
    Интеграция с Remnawave VPN панелью для автоматизации процессов
    """

    def __init__(self, panel_service: VPNPanelService):
        self.panel_service = panel_service
        self.logger = logging.getLogger(self.__class__.__name__)
    
    async def setup_user_subscription(
        self,
        telegram_id: int,
        tariff_name: str,
        duration_days: int,
        username: Optional[str] = None,
        protocols: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """Полная настройка подписки для нового пользователя в Remnawave"""
        try:
            # Создаем пользователя в Remnawave панели
            vpn_user = await self.panel_service.create_user(
                telegram_id=telegram_id,
                username=username,
                protocols=protocols or ['vless', 'vmess']
            )

            vpn_user_id = vpn_user.get('id')
            if not vpn_user_id:
                raise VPNPanelError("Не удалось получить ID созданного пользователя")

            # Создаем подписку
            subscription = await self.panel_service.create_subscription(
                vpn_user_id=vpn_user_id,
                tariff_name=tariff_name,
                duration_days=duration_days
            )

            # Генерируем конфигурации для всех протоколов
            configs = []
            for protocol in (protocols or ['vless', 'vmess']):
                try:
                    config = await self.panel_service.generate_config(
                        vpn_user_id=vpn_user_id,
                        device_name=f'Default-{protocol.upper()}',
                        protocol=protocol
                    )
                    configs.append(config)
                except Exception as e:
                    self.logger.warning(f"Не удалось создать конфигурацию {protocol}: {e}")

            return {
                'vpn_user_id': vpn_user_id,
                'subscription': subscription,
                'configs': configs,
                'primary_config': configs[0] if configs else None
            }

        except VPNPanelError:
            raise
        except Exception as e:
            self.logger.error(f"Ошибка настройки подписки для {telegram_id}: {e}")
            raise BusinessLogicError(f"Не удалось настроить VPN подписку: {e}")
    
    async def process_subscription_renewal(
        self,
        vpn_user_id: str,
        additional_days: int
    ) -> Dict[str, Any]:
        """Обработка продления подписки"""
        try:
            # Продлеваем подписку
            result = await self.panel_service.extend_subscription(
                vpn_user_id=vpn_user_id,
                additional_days=additional_days
            )
            
            # Если пользователь был приостановлен, восстанавливаем доступ
            await self.panel_service.reactivate_user(vpn_user_id)
            
            return result
            
        except Exception as e:
            self.logger.error(f"Ошибка продления подписки для {vpn_user_id}: {e}")
            raise BusinessLogicError(f"Не удалось продлить подписку: {e}")
    
    async def handle_subscription_expiry(self, vpn_user_id: str) -> bool:
        """Обработка истечения подписки в Remnawave"""
        try:
            # Приостанавливаем доступ
            result = await self.panel_service.suspend_user(
                vpn_user_id=vpn_user_id,
                reason="Subscription expired"
            )

            return result

        except Exception as e:
            self.logger.error(f"Ошибка обработки истечения подписки для {vpn_user_id}: {e}")
            return False

    async def get_user_subscription_info(self, vpn_user_id: str) -> Optional[Dict[str, Any]]:
        """Получение полной информации о подписке пользователя"""
        try:
            # Получаем информацию о пользователе
            user_info = await self.panel_service.get_user(vpn_user_id)
            if not user_info:
                return None

            # Получаем информацию о подписке
            subscription_info = await self.panel_service.get_subscription_info(vpn_user_id)

            # Получаем конфигурации
            configs = await self.panel_service.get_user_configs(vpn_user_id)

            # Получаем статистику
            stats = await self.panel_service.get_user_statistics(vpn_user_id)

            return {
                'user': user_info,
                'subscription': subscription_info,
                'configs': configs,
                'statistics': stats
            }

        except Exception as e:
            self.logger.error(f"Ошибка получения информации о подписке для {vpn_user_id}: {e}")
            return None

    async def create_additional_config(
        self,
        vpn_user_id: str,
        device_name: str,
        protocol: str = 'vless'
    ) -> Optional[Dict[str, Any]]:
        """Создание дополнительной конфигурации для пользователя"""
        try:
            config = await self.panel_service.generate_config(
                vpn_user_id=vpn_user_id,
                device_name=device_name,
                protocol=protocol
            )

            self.logger.info(f"Создана дополнительная конфигурация {protocol} для {vpn_user_id}")
            return config

        except Exception as e:
            self.logger.error(f"Ошибка создания дополнительной конфигурации: {e}")
            return None
