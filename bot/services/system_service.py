"""
UnveilVPN Shop - System Service
Сервис для системного управления и мониторинга
"""

import os
import psutil
import logging
import tempfile
from typing import Dict, Any, List, Optional
from datetime import datetime, timezone, timedelta
from pathlib import Path

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_, desc, text

from ..db.models import VPNUsers, Payments, SystemLogs
from ..common.exceptions import BusinessLogicError


class SystemService:
    """
    Сервис для системного управления и мониторинга
    """
    
    def __init__(self, db_session: AsyncSession):
        self.db_session = db_session
        self.logger = logging.getLogger(self.__class__.__name__)
    
    async def get_system_info(self) -> Dict[str, Any]:
        """Получение основной системной информации"""
        try:
            # CPU информация
            cpu_usage = psutil.cpu_percent(interval=1)
            cpu_count = psutil.cpu_count()
            
            # Память
            memory = psutil.virtual_memory()
            memory_usage = memory.percent
            memory_total = memory.total / (1024**3)  # ГБ
            memory_used = memory.used / (1024**3)  # ГБ
            
            # Диск
            disk = psutil.disk_usage('/')
            disk_usage = (disk.used / disk.total) * 100
            disk_total = disk.total / (1024**3)  # ГБ
            disk_used = disk.used / (1024**3)  # ГБ
            
            # Uptime
            boot_time = psutil.boot_time()
            uptime_seconds = datetime.now().timestamp() - boot_time
            uptime_hours = int(uptime_seconds // 3600)
            uptime_minutes = int((uptime_seconds % 3600) // 60)
            uptime = f"{uptime_hours}ч {uptime_minutes}м"
            
            return {
                'cpu_usage': cpu_usage,
                'cpu_count': cpu_count,
                'memory_usage': memory_usage,
                'memory_total': memory_total,
                'memory_used': memory_used,
                'disk_usage': disk_usage,
                'disk_total': disk_total,
                'disk_used': disk_used,
                'uptime': uptime,
                'uptime_seconds': uptime_seconds
            }
            
        except Exception as e:
            self.logger.error(f"Ошибка получения системной информации: {e}")
            raise BusinessLogicError(f"Не удалось получить системную информацию: {e}")
    
    async def get_monitoring_data(self) -> Dict[str, Any]:
        """Получение расширенных данных мониторинга"""
        try:
            # Базовая системная информация
            system_info = await self.get_system_info()
            
            # Сетевая статистика
            network = psutil.net_io_counters()
            network_info = {
                'bytes_sent': network.bytes_sent / (1024**2),  # МБ
                'bytes_recv': network.bytes_recv / (1024**2),  # МБ
                'packets_sent': network.packets_sent,
                'packets_recv': network.packets_recv
            }
            
            # Топ процессов по CPU
            processes = []
            for proc in psutil.process_iter(['pid', 'name', 'cpu_percent']):
                try:
                    proc_info = proc.info
                    if proc_info['cpu_percent'] > 0:
                        processes.append(proc_info)
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    pass
            
            # Сортируем по CPU usage
            processes = sorted(processes, key=lambda x: x['cpu_percent'], reverse=True)
            
            # Статус сервисов (заглушка)
            services_status = {
                'database': 'running',
                'redis': 'running',
                'nginx': 'running',
                'bot': 'running'
            }
            
            return {
                **system_info,
                'network': network_info,
                'processes': processes[:10],
                'services_status': services_status
            }
            
        except Exception as e:
            self.logger.error(f"Ошибка получения данных мониторинга: {e}")
            raise BusinessLogicError(f"Не удалось получить данные мониторинга: {e}")
    
    async def get_recent_logs(self, limit: int = 50) -> List[Dict[str, Any]]:
        """Получение последних системных логов"""
        try:
            # Пытаемся получить логи из базы данных
            query = select(SystemLogs).order_by(desc(SystemLogs.created_at)).limit(limit)
            result = await self.db_session.execute(query)
            logs = result.scalars().all()
            
            logs_data = []
            for log in logs:
                logs_data.append({
                    'id': str(log.id),
                    'level': log.level,
                    'message': log.message,
                    'source': log.source,
                    'timestamp': log.created_at.strftime('%d.%m.%Y %H:%M:%S'),
                    'created_at': log.created_at
                })
            
            return logs_data
            
        except Exception as e:
            self.logger.error(f"Ошибка получения логов: {e}")
            # Возвращаем заглушку если нет таблицы логов
            return [
                {
                    'id': '1',
                    'level': 'INFO',
                    'message': 'Система запущена успешно',
                    'source': 'system',
                    'timestamp': datetime.now().strftime('%d.%m.%Y %H:%M:%S'),
                    'created_at': datetime.now()
                }
            ]
    
    async def get_error_logs(self, limit: int = 20) -> List[Dict[str, Any]]:
        """Получение логов ошибок"""
        try:
            # Пытаемся получить логи ошибок из базы данных
            query = select(SystemLogs).where(
                SystemLogs.level.in_(['ERROR', 'CRITICAL'])
            ).order_by(desc(SystemLogs.created_at)).limit(limit)
            
            result = await self.db_session.execute(query)
            logs = result.scalars().all()
            
            error_logs = []
            for log in logs:
                error_logs.append({
                    'id': str(log.id),
                    'level': log.level,
                    'message': log.message,
                    'source': log.source,
                    'timestamp': log.created_at.strftime('%d.%m.%Y %H:%M:%S'),
                    'created_at': log.created_at
                })
            
            return error_logs
            
        except Exception as e:
            self.logger.error(f"Ошибка получения логов ошибок: {e}")
            # Возвращаем пустой список если нет таблицы логов
            return []
    
    async def clear_cache(self) -> Dict[str, Any]:
        """Очистка системного кэша"""
        try:
            start_time = datetime.now()
            
            # Получаем информацию о памяти до очистки
            memory_before = psutil.virtual_memory()
            
            # Очистка кэша (заглушка - в реальности здесь будет очистка Redis, файлового кэша и т.д.)
            freed_memory = 0
            deleted_files = 0
            
            # Имитация очистки
            import time
            time.sleep(1)  # Имитация работы
            
            # Получаем информацию о памяти после очистки
            memory_after = psutil.virtual_memory()
            
            # Вычисляем освобожденную память (приблизительно)
            freed_memory = max(0, (memory_before.used - memory_after.used) / (1024**2))  # МБ
            
            execution_time = (datetime.now() - start_time).total_seconds()
            
            # Логируем операцию
            await self._log_system_event(
                level='INFO',
                message=f'Кэш очищен. Освобождено {freed_memory:.1f} МБ памяти',
                source='system_service'
            )
            
            return {
                'freed_memory': freed_memory,
                'deleted_files': deleted_files,
                'execution_time': execution_time
            }
            
        except Exception as e:
            self.logger.error(f"Ошибка очистки кэша: {e}")
            raise BusinessLogicError(f"Не удалось очистить кэш: {e}")
    
    async def restart_services(self) -> Dict[str, Any]:
        """Перезапуск системных сервисов"""
        try:
            start_time = datetime.now()
            
            # В реальности здесь будет перезапуск сервисов
            # Пока что имитируем
            services_restarted = ['redis', 'nginx', 'bot_workers']
            
            import time
            time.sleep(2)  # Имитация перезапуска
            
            execution_time = (datetime.now() - start_time).total_seconds()
            
            # Логируем операцию
            await self._log_system_event(
                level='INFO',
                message=f'Сервисы перезапущены: {", ".join(services_restarted)}',
                source='system_service'
            )
            
            return {
                'services_restarted': services_restarted,
                'execution_time': execution_time,
                'status': 'success'
            }
            
        except Exception as e:
            self.logger.error(f"Ошибка перезапуска сервисов: {e}")
            raise BusinessLogicError(f"Не удалось перезапустить сервисы: {e}")
    
    async def export_logs(self, days: int = 7) -> Optional[str]:
        """Экспорт логов в файл"""
        try:
            # Получаем логи за указанный период
            start_date = datetime.now(timezone.utc) - timedelta(days=days)
            
            query = select(SystemLogs).where(
                SystemLogs.created_at >= start_date
            ).order_by(desc(SystemLogs.created_at))
            
            result = await self.db_session.execute(query)
            logs = result.scalars().all()
            
            # Создаем временный файл
            temp_file = tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt')
            
            # Записываем заголовок
            temp_file.write(f"UnveilVPN Shop - System Logs Export\n")
            temp_file.write(f"Export Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            temp_file.write(f"Period: Last {days} days\n")
            temp_file.write(f"Total Records: {len(logs)}\n")
            temp_file.write("=" * 80 + "\n\n")
            
            # Записываем логи
            for log in logs:
                temp_file.write(f"[{log.created_at.strftime('%Y-%m-%d %H:%M:%S')}] ")
                temp_file.write(f"{log.level} | {log.source} | {log.message}\n")
            
            temp_file.close()
            
            return temp_file.name
            
        except Exception as e:
            self.logger.error(f"Ошибка экспорта логов: {e}")
            return None
    
    async def get_database_stats(self) -> Dict[str, Any]:
        """Получение статистики базы данных"""
        try:
            # Количество пользователей
            users_query = select(func.count(VPNUsers.id))
            users_result = await self.db_session.execute(users_query)
            total_users = users_result.scalar() or 0
            
            # Количество платежей
            payments_query = select(func.count(Payments.id))
            payments_result = await self.db_session.execute(payments_query)
            total_payments = payments_result.scalar() or 0
            
            # Размер базы данных (заглушка)
            db_size_mb = 0
            
            # Количество таблиц (заглушка)
            tables_count = 10
            
            return {
                'total_users': total_users,
                'total_payments': total_payments,
                'db_size_mb': db_size_mb,
                'tables_count': tables_count
            }
            
        except Exception as e:
            self.logger.error(f"Ошибка получения статистики БД: {e}")
            raise BusinessLogicError(f"Не удалось получить статистику базы данных: {e}")
    
    async def check_system_health(self) -> Dict[str, Any]:
        """Проверка состояния системы"""
        try:
            health_status = {
                'overall': 'healthy',
                'issues': [],
                'warnings': []
            }
            
            # Проверяем использование CPU
            cpu_usage = psutil.cpu_percent(interval=1)
            if cpu_usage > 80:
                health_status['issues'].append(f'Высокая загрузка CPU: {cpu_usage:.1f}%')
                health_status['overall'] = 'warning'
            elif cpu_usage > 60:
                health_status['warnings'].append(f'Повышенная загрузка CPU: {cpu_usage:.1f}%')
            
            # Проверяем использование памяти
            memory = psutil.virtual_memory()
            if memory.percent > 85:
                health_status['issues'].append(f'Высокое использование памяти: {memory.percent:.1f}%')
                health_status['overall'] = 'critical'
            elif memory.percent > 70:
                health_status['warnings'].append(f'Повышенное использование памяти: {memory.percent:.1f}%')
            
            # Проверяем использование диска
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            if disk_percent > 90:
                health_status['issues'].append(f'Мало места на диске: {disk_percent:.1f}%')
                health_status['overall'] = 'critical'
            elif disk_percent > 80:
                health_status['warnings'].append(f'Заканчивается место на диске: {disk_percent:.1f}%')
            
            # Проверяем подключение к базе данных
            try:
                await self.db_session.execute(select(1))
                health_status['database'] = 'connected'
            except Exception:
                health_status['issues'].append('Нет подключения к базе данных')
                health_status['overall'] = 'critical'
                health_status['database'] = 'disconnected'
            
            return health_status
            
        except Exception as e:
            self.logger.error(f"Ошибка проверки состояния системы: {e}")
            return {
                'overall': 'error',
                'issues': [f'Ошибка проверки: {str(e)}'],
                'warnings': []
            }
    
    # Приватные методы
    
    async def _log_system_event(self, level: str, message: str, source: str):
        """Логирование системного события"""
        try:
            # Пытаемся записать в базу данных
            log_entry = SystemLogs(
                level=level,
                message=message,
                source=source,
                created_at=datetime.now(timezone.utc)
            )
            
            self.db_session.add(log_entry)
            await self.db_session.commit()
            
        except Exception as e:
            # Если не удалось записать в БД, логируем в файл
            self.logger.info(f"[{level}] {source}: {message}")
    
    async def _get_process_info(self, pid: int) -> Optional[Dict[str, Any]]:
        """Получение информации о процессе"""
        try:
            process = psutil.Process(pid)
            return {
                'pid': pid,
                'name': process.name(),
                'cpu_percent': process.cpu_percent(),
                'memory_percent': process.memory_percent(),
                'status': process.status(),
                'create_time': process.create_time()
            }
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            return None
