"""
UnveilVPN Shop - Promocode Service
Сервис для работы с промокодами
"""

import logging
import secrets
import string
from typing import List, Optional, Dict, Any, Tuple
from uuid import UUID
from decimal import Decimal
from datetime import datetime, timezone, timedelta

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, func, and_, or_, desc
from sqlalchemy.orm import selectinload, joinedload

from ..db.models import Promocodes, PromocodeUsage, VPNUsers, Tariffs, Payments
from ..common.exceptions import NotFoundError, ValidationError, BusinessLogicError


class PromocodeService:
    """
    Сервис для работы с промокодами
    """
    
    def __init__(self, db_session: AsyncSession):
        self.db_session = db_session
        self.logger = logging.getLogger(self.__class__.__name__)
    
    async def create_promocode(
        self,
        code: str,
        discount_type: str,
        discount_value: Decimal,
        name: Optional[str] = None,
        description: Optional[str] = None,
        usage_limit: Optional[int] = None,
        usage_limit_per_user: int = 1,
        starts_at: Optional[datetime] = None,
        expires_at: Optional[datetime] = None,
        applicable_tariffs: Optional[List[UUID]] = None,
        min_purchase_amount: Optional[Decimal] = None,
        is_public: bool = False,
        created_by_id: Optional[UUID] = None
    ) -> Promocodes:
        """Создание нового промокода"""
        try:
            # Валидация данных
            await self._validate_promocode_data(
                code, discount_type, discount_value, starts_at, expires_at
            )
            
            # Проверяем уникальность кода
            existing = await self.get_promocode_by_code(code)
            if existing:
                raise ValidationError(f"Промокод с кодом '{code}' уже существует")
            
            # Создаем промокод
            promocode = Promocodes(
                code=code.upper().strip(),
                name=name,
                description=description,
                discount_type=discount_type,
                discount_value=discount_value,
                usage_limit=usage_limit,
                usage_limit_per_user=usage_limit_per_user,
                starts_at=starts_at,
                expires_at=expires_at,
                applicable_tariffs=applicable_tariffs,
                min_purchase_amount=min_purchase_amount,
                is_public=is_public,
                created_by_id=created_by_id
            )
            
            self.db_session.add(promocode)
            await self.db_session.flush()
            
            self.logger.info(f"Создан промокод: {promocode.code} (ID: {promocode.id})")
            return promocode
            
        except (ValidationError, BusinessLogicError):
            raise
        except Exception as e:
            self.logger.error(f"Ошибка создания промокода: {e}")
            raise BusinessLogicError(f"Не удалось создать промокод: {e}")
    
    async def get_promocode_by_id(self, promocode_id: UUID) -> Optional[Promocodes]:
        """Получение промокода по ID"""
        try:
            query = (
                select(Promocodes)
                .options(
                    selectinload(Promocodes.created_by),
                    selectinload(Promocodes.usage_history)
                )
                .where(Promocodes.id == promocode_id)
            )
            result = await self.db_session.execute(query)
            return result.scalar_one_or_none()
            
        except Exception as e:
            self.logger.error(f"Ошибка получения промокода {promocode_id}: {e}")
            raise BusinessLogicError(f"Не удалось получить промокод: {e}")
    
    async def get_promocode_by_code(self, code: str) -> Optional[Promocodes]:
        """Получение промокода по коду"""
        try:
            normalized_code = code.upper().strip()
            
            query = (
                select(Promocodes)
                .options(
                    selectinload(Promocodes.created_by),
                    selectinload(Promocodes.usage_history)
                )
                .where(Promocodes.code == normalized_code)
            )
            result = await self.db_session.execute(query)
            return result.scalar_one_or_none()
            
        except Exception as e:
            self.logger.error(f"Ошибка получения промокода по коду {code}: {e}")
            raise BusinessLogicError(f"Не удалось получить промокод: {e}")
    
    async def validate_promocode(
        self,
        code: str,
        user_id: UUID,
        tariff_id: Optional[UUID] = None,
        purchase_amount: Optional[Decimal] = None
    ) -> Tuple[bool, str, Optional[Promocodes]]:
        """Валидация промокода для использования"""
        try:
            # Получаем промокод
            promocode = await self.get_promocode_by_code(code)
            if not promocode:
                return False, "Промокод не найден", None
            
            # Базовая валидация
            is_valid, message = promocode.is_valid(user_id, tariff_id, purchase_amount)
            if not is_valid:
                return False, message, promocode
            
            # Проверяем лимит использований на пользователя
            user_usage_count = await self._get_user_promocode_usage_count(promocode.id, user_id)
            if user_usage_count >= promocode.usage_limit_per_user:
                return False, f"Вы уже использовали этот промокод максимальное количество раз ({promocode.usage_limit_per_user})", promocode
            
            return True, "Промокод действителен", promocode
            
        except Exception as e:
            self.logger.error(f"Ошибка валидации промокода {code}: {e}")
            return False, "Ошибка проверки промокода", None
    
    async def apply_promocode(
        self,
        promocode_id: UUID,
        user_id: UUID,
        payment_id: UUID,
        original_amount: Decimal
    ) -> Dict[str, Any]:
        """Применение промокода к платежу"""
        try:
            # Получаем промокод
            promocode = await self.get_promocode_by_id(promocode_id)
            if not promocode:
                raise NotFoundError(f"Промокод {promocode_id} не найден")
            
            # Рассчитываем скидку
            discount_amount, final_amount = promocode.calculate_discount(original_amount)
            
            # Создаем запись об использовании
            usage = PromocodeUsage(
                promocode_id=promocode_id,
                user_id=user_id,
                payment_id=payment_id,
                discount_amount=discount_amount,
                original_amount=original_amount,
                final_amount=final_amount
            )
            
            self.db_session.add(usage)
            
            # Увеличиваем счетчик использований
            promocode.usage_count += 1
            
            await self.db_session.flush()
            
            self.logger.info(f"Применен промокод {promocode.code} к платежу {payment_id}")
            
            return {
                'promocode_id': str(promocode_id),
                'code': promocode.code,
                'discount_amount': float(discount_amount),
                'original_amount': float(original_amount),
                'final_amount': float(final_amount),
                'discount_type': promocode.discount_type,
                'discount_value': float(promocode.discount_value)
            }
            
        except (NotFoundError, ValidationError):
            raise
        except Exception as e:
            self.logger.error(f"Ошибка применения промокода {promocode_id}: {e}")
            raise BusinessLogicError(f"Не удалось применить промокод: {e}")
    
    async def get_all_promocodes(
        self,
        active_only: bool = False,
        public_only: bool = False,
        created_by_id: Optional[UUID] = None,
        limit: int = 100,
        offset: int = 0
    ) -> List[Promocodes]:
        """Получение списка промокодов"""
        try:
            query = (
                select(Promocodes)
                .options(selectinload(Promocodes.created_by))
            )
            
            # Фильтры
            if active_only:
                query = query.where(Promocodes.is_active == True)
            
            if public_only:
                query = query.where(Promocodes.is_public == True)
            
            if created_by_id:
                query = query.where(Promocodes.created_by_id == created_by_id)
            
            # Сортировка и пагинация
            query = query.order_by(desc(Promocodes.created_at)).limit(limit).offset(offset)
            
            result = await self.db_session.execute(query)
            return result.scalars().all()
            
        except Exception as e:
            self.logger.error(f"Ошибка получения списка промокодов: {e}")
            raise BusinessLogicError(f"Не удалось получить промокоды: {e}")
    
    async def get_public_promocodes(self) -> List[Promocodes]:
        """Получение публичных активных промокодов"""
        try:
            now = datetime.now(timezone.utc)
            
            query = (
                select(Promocodes)
                .where(and_(
                    Promocodes.is_active == True,
                    Promocodes.is_public == True,
                    or_(
                        Promocodes.starts_at.is_(None),
                        Promocodes.starts_at <= now
                    ),
                    or_(
                        Promocodes.expires_at.is_(None),
                        Promocodes.expires_at > now
                    ),
                    or_(
                        Promocodes.usage_limit.is_(None),
                        Promocodes.usage_count < Promocodes.usage_limit
                    )
                ))
                .order_by(desc(Promocodes.created_at))
            )
            
            result = await self.db_session.execute(query)
            return result.scalars().all()
            
        except Exception as e:
            self.logger.error(f"Ошибка получения публичных промокодов: {e}")
            raise BusinessLogicError(f"Не удалось получить публичные промокоды: {e}")
    
    async def update_promocode(
        self,
        promocode_id: UUID,
        **update_data
    ) -> Promocodes:
        """Обновление промокода"""
        try:
            promocode = await self.get_promocode_by_id(promocode_id)
            if not promocode:
                raise NotFoundError(f"Промокод {promocode_id} не найден")
            
            # Валидация обновляемых данных
            if 'code' in update_data:
                new_code = update_data['code'].upper().strip()
                if new_code != promocode.code:
                    existing = await self.get_promocode_by_code(new_code)
                    if existing:
                        raise ValidationError(f"Промокод с кодом '{new_code}' уже существует")
                    update_data['code'] = new_code
            
            # Обновляем поля
            for field, value in update_data.items():
                if hasattr(promocode, field):
                    setattr(promocode, field, value)
            
            await self.db_session.flush()
            
            self.logger.info(f"Обновлен промокод {promocode.code}")
            return promocode
            
        except (NotFoundError, ValidationError):
            raise
        except Exception as e:
            self.logger.error(f"Ошибка обновления промокода {promocode_id}: {e}")
            raise BusinessLogicError(f"Не удалось обновить промокод: {e}")
    
    async def delete_promocode(self, promocode_id: UUID) -> bool:
        """Удаление промокода (мягкое удаление - деактивация)"""
        try:
            promocode = await self.get_promocode_by_id(promocode_id)
            if not promocode:
                raise NotFoundError(f"Промокод {promocode_id} не найден")
            
            # Мягкое удаление - деактивация
            promocode.is_active = False
            await self.db_session.flush()
            
            self.logger.info(f"Деактивирован промокод {promocode.code}")
            return True
            
        except (NotFoundError, ValidationError):
            raise
        except Exception as e:
            self.logger.error(f"Ошибка удаления промокода {promocode_id}: {e}")
            raise BusinessLogicError(f"Не удалось удалить промокод: {e}")
    
    async def get_promocode_statistics(
        self,
        promocode_id: Optional[UUID] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """Получение статистики использования промокодов"""
        try:
            query = select(PromocodeUsage).options(
                joinedload(PromocodeUsage.promocode),
                joinedload(PromocodeUsage.user)
            )
            
            # Фильтры
            if promocode_id:
                query = query.where(PromocodeUsage.promocode_id == promocode_id)
            
            if start_date:
                query = query.where(PromocodeUsage.used_at >= start_date)
            
            if end_date:
                query = query.where(PromocodeUsage.used_at <= end_date)
            
            result = await self.db_session.execute(query)
            usage_records = result.scalars().all()
            
            # Подсчитываем статистику
            total_usage = len(usage_records)
            total_discount = sum(record.discount_amount for record in usage_records)
            total_original = sum(record.original_amount for record in usage_records)
            
            # Группировка по промокодам
            promocode_stats = {}
            for record in usage_records:
                code = record.promocode.code
                if code not in promocode_stats:
                    promocode_stats[code] = {
                        'usage_count': 0,
                        'total_discount': Decimal('0'),
                        'total_original': Decimal('0')
                    }
                
                promocode_stats[code]['usage_count'] += 1
                promocode_stats[code]['total_discount'] += record.discount_amount
                promocode_stats[code]['total_original'] += record.original_amount
            
            return {
                'total_usage': total_usage,
                'total_discount': float(total_discount),
                'total_original': float(total_original),
                'discount_percentage': float(total_discount / total_original * 100) if total_original > 0 else 0,
                'promocode_breakdown': {
                    code: {
                        'usage_count': stats['usage_count'],
                        'total_discount': float(stats['total_discount']),
                        'total_original': float(stats['total_original'])
                    }
                    for code, stats in promocode_stats.items()
                }
            }
            
        except Exception as e:
            self.logger.error(f"Ошибка получения статистики промокодов: {e}")
            raise BusinessLogicError(f"Не удалось получить статистику: {e}")
    
    async def generate_promocode(
        self,
        prefix: str = "",
        length: int = 8,
        discount_type: str = "percent",
        discount_value: Decimal = Decimal('10'),
        expires_in_days: int = 30,
        usage_limit: Optional[int] = None,
        created_by_id: Optional[UUID] = None
    ) -> Promocodes:
        """Генерация случайного промокода"""
        try:
            # Генерируем уникальный код
            max_attempts = 10
            for _ in range(max_attempts):
                code = self._generate_code(prefix, length)
                
                # Проверяем уникальность
                existing = await self.get_promocode_by_code(code)
                if not existing:
                    break
            else:
                raise BusinessLogicError("Не удалось сгенерировать уникальный код")
            
            # Устанавливаем дату истечения
            expires_at = datetime.now(timezone.utc) + timedelta(days=expires_in_days)
            
            # Создаем промокод
            promocode = await self.create_promocode(
                code=code,
                discount_type=discount_type,
                discount_value=discount_value,
                expires_at=expires_at,
                usage_limit=usage_limit,
                created_by_id=created_by_id,
                name=f"Автогенерированный промокод {code}"
            )
            
            return promocode
            
        except Exception as e:
            self.logger.error(f"Ошибка генерации промокода: {e}")
            raise BusinessLogicError(f"Не удалось сгенерировать промокод: {e}")
    
    # Приватные методы
    
    async def _validate_promocode_data(
        self,
        code: str,
        discount_type: str,
        discount_value: Decimal,
        starts_at: Optional[datetime],
        expires_at: Optional[datetime]
    ):
        """Валидация данных промокода"""
        # Валидация кода
        if not code or len(code.strip()) < 3:
            raise ValidationError("Код промокода должен содержать минимум 3 символа")
        
        if len(code.strip()) > 50:
            raise ValidationError("Код промокода не может быть длиннее 50 символов")
        
        # Валидация типа скидки
        valid_types = ['percent', 'fixed', 'days']
        if discount_type not in valid_types:
            raise ValidationError(f"Неверный тип скидки. Допустимые: {valid_types}")
        
        # Валидация значения скидки
        if discount_value <= 0:
            raise ValidationError("Значение скидки должно быть положительным")
        
        if discount_type == 'percent' and discount_value > 100:
            raise ValidationError("Процентная скидка не может быть больше 100%")
        
        # Валидация дат
        if starts_at and expires_at and starts_at >= expires_at:
            raise ValidationError("Дата начала должна быть раньше даты окончания")
    
    async def _get_user_promocode_usage_count(self, promocode_id: UUID, user_id: UUID) -> int:
        """Получение количества использований промокода пользователем"""
        query = (
            select(func.count(PromocodeUsage.id))
            .where(and_(
                PromocodeUsage.promocode_id == promocode_id,
                PromocodeUsage.user_id == user_id
            ))
        )
        result = await self.db_session.execute(query)
        return result.scalar() or 0
    
    def _generate_code(self, prefix: str = "", length: int = 8) -> str:
        """Генерация случайного кода"""
        alphabet = string.ascii_uppercase + string.digits
        # Исключаем похожие символы
        alphabet = alphabet.replace('0', '').replace('O', '').replace('I', '').replace('1')
        
        suffix_length = length - len(prefix)
        if suffix_length <= 0:
            suffix_length = 4
        
        suffix = ''.join(secrets.choice(alphabet) for _ in range(suffix_length))
        return f"{prefix}{suffix}".upper()
