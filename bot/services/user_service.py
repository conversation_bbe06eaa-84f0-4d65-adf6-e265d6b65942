"""
UnveilVPN Shop - Сервис управления пользователями
"""

from typing import Optional, List
from sqlalchemy.orm import Session
from sqlalchemy import select
from datetime import datetime, timedelta

from bot.db.models import VPNUsers, Subscriptions
from bot.common.exceptions import NotFoundError, ValidationError


class UserService:
    """Сервис для работы с пользователями"""
    
    def __init__(self, session: Session):
        self.session = session
    
    def get_user_by_telegram_id(self, telegram_id: int) -> Optional[VPNUsers]:
        """Получение пользователя по Telegram ID"""
        return self.session.query(VPNUsers).filter(
            VPNUsers.telegram_id == telegram_id
        ).first()
    
    def create_user(
        self,
        telegram_id: int,
        username: Optional[str] = None,
        first_name: Optional[str] = None,
        last_name: Optional[str] = None,
        language_code: str = "ru"
    ) -> VPNUsers:
        """Создание нового пользователя"""
        
        # Проверяем, что пользователь не существует
        existing_user = self.get_user_by_telegram_id(telegram_id)
        if existing_user:
            raise ValidationError(f"Пользователь с ID {telegram_id} уже существует")
        
        user = VPNUsers(
            telegram_id=telegram_id,
            username=username,
            first_name=first_name,
            last_name=last_name,
            language_code=language_code,
            registration_date=datetime.utcnow()
        )
        
        self.session.add(user)
        self.session.commit()
        self.session.refresh(user)
        
        return user
    
    def update_user(
        self,
        telegram_id: int,
        username: Optional[str] = None,
        first_name: Optional[str] = None,
        last_name: Optional[str] = None,
        language_code: Optional[str] = None
    ) -> VPNUsers:
        """Обновление данных пользователя"""
        
        user = self.get_user_by_telegram_id(telegram_id)
        if not user:
            raise NotFoundError(f"Пользователь с ID {telegram_id} не найден")
        
        if username is not None:
            user.username = username
        if first_name is not None:
            user.first_name = first_name
        if last_name is not None:
            user.last_name = last_name
        if language_code is not None:
            user.language_code = language_code
        
        user.last_activity = datetime.utcnow()
        
        self.session.commit()
        self.session.refresh(user)
        
        return user
    
    def get_or_create_user(
        self,
        telegram_id: int,
        username: Optional[str] = None,
        first_name: Optional[str] = None,
        last_name: Optional[str] = None,
        language_code: str = "ru"
    ) -> tuple[VPNUsers, bool]:
        """Получение или создание пользователя"""
        
        user = self.get_user_by_telegram_id(telegram_id)
        if user:
            # Обновляем активность
            user.last_activity = datetime.utcnow()
            self.session.commit()
            return user, False
        
        # Создаем нового пользователя
        user = self.create_user(
            telegram_id=telegram_id,
            username=username,
            first_name=first_name,
            last_name=last_name,
            language_code=language_code
        )
        
        return user, True
    
    def get_user_subscription(self, telegram_id: int) -> Optional[Subscriptions]:
        """Получение активной подписки пользователя"""
        user = self.get_user_by_telegram_id(telegram_id)
        if not user:
            return None
        
        # Ищем активную подписку
        active_subscription = self.session.query(Subscriptions).filter(
            Subscriptions.user_id == user.id,
            Subscriptions.end_date > datetime.utcnow()
        ).first()
        
        return active_subscription
    
    def has_active_subscription(self, telegram_id: int) -> bool:
        """Проверка наличия активной подписки"""
        subscription = self.get_user_subscription(telegram_id)
        return subscription is not None
    
    def get_subscription_days_left(self, telegram_id: int) -> int:
        """Получение количества дней до окончания подписки"""
        subscription = self.get_user_subscription(telegram_id)
        if not subscription:
            return 0
        
        days_left = (subscription.end_date - datetime.utcnow()).days
        return max(0, days_left)
    
    def update_user_activity(self, telegram_id: int):
        """Обновление времени последней активности пользователя"""
        user = self.get_user_by_telegram_id(telegram_id)
        if user:
            user.last_activity = datetime.utcnow()
            self.session.commit()
    
    def get_users_count(self) -> int:
        """Получение общего количества пользователей"""
        return self.session.query(VPNUsers).count()
    
    def get_active_users_count(self, days: int = 30) -> int:
        """Получение количества активных пользователей за период"""
        cutoff_date = datetime.utcnow() - timedelta(days=days)
        return self.session.query(VPNUsers).filter(
            VPNUsers.last_activity >= cutoff_date
        ).count()
    
    def get_users_with_active_subscriptions(self) -> List[VPNUsers]:
        """Получение пользователей с активными подписками"""
        return self.session.query(VPNUsers).join(Subscriptions).filter(
            Subscriptions.end_date > datetime.utcnow()
        ).all()

    def get_all_users(self) -> List[VPNUsers]:
        """Получение всех пользователей"""
        return self.session.query(VPNUsers).all()
    
    def get_all_users(self) -> List[VPNUsers]:
        """Получение всех пользователей"""
        return self.session.query(VPNUsers).all()
