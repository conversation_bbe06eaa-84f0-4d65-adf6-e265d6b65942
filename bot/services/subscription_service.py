"""
UnveilVPN Shop - Subscription Service
Сервис для управления подписками пользователей
"""

import logging
from typing import Dict, Any, Optional, List
from uuid import UUID
from datetime import datetime, timezone, timedelta

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, and_, or_
from sqlalchemy.orm import selectinload

from ..db.models import VPNUsers, Tariffs, Payments, Subscriptions, VPNConfigs
from ..common.exceptions import NotFoundError, ValidationError, BusinessLogicError
from .vpn_panel_service import VPNPanelService, VPNPanelIntegration
from ..utils.remnawave_api import generate_remnawave_subscription, RemnaWaveAPIError


class SubscriptionService:
    """
    Сервис для управления подписками
    """
    
    def __init__(self, db_session: AsyncSession, vpn_panel_service: VPNPanelService):
        self.db_session = db_session
        self.vpn_panel_service = vpn_panel_service
        self.vpn_integration = VPNPanelIntegration(vpn_panel_service)
        self.logger = logging.getLogger(self.__class__.__name__)

    async def create_remnawave_subscription(
        self,
        user_id: UUID,
        tariff_id: UUID,
        payment_id: Optional[UUID] = None
    ) -> Dict[str, Any]:
        """Создание подписки через Remnawave API"""
        try:
            # Получаем пользователя и тариф
            user = await self._get_user_by_id(user_id)
            if not user:
                raise NotFoundError(f"Пользователь {user_id} не найден")

            tariff = await self._get_tariff_by_id(tariff_id)
            if not tariff:
                raise NotFoundError(f"Тариф {tariff_id} не найден")

            # Создаем пользователя в Remnawave если его еще нет
            if not user.remnawave_user_id:
                remnawave_user = await self.vpn_panel_service.create_user(
                    telegram_id=user.telegram_id,
                    username=user.username,
                    email=f"user_{user.telegram_id}@unveilvpn.local"
                )
                user.remnawave_user_id = remnawave_user.get('id')
                await self.db_session.commit()

            # Создаем подписку в Remnawave
            remnawave_subscription = await self.vpn_panel_service.create_subscription(
                vpn_user_id=user.remnawave_user_id,
                tariff_name=tariff.name,
                duration_days=tariff.duration_days,
                traffic_limit_gb=tariff.features.get('traffic_limit_gb')
            )

            # Создаем запись подписки в локальной БД
            subscription = Subscriptions(
                user_id=user.id,
                tariff_id=tariff.id,
                start_date=datetime.now(timezone.utc),
                end_date=datetime.now(timezone.utc) + timedelta(days=tariff.duration_days),
                remnawave_subscription_id=remnawave_subscription.get('id'),
                traffic_limit_gb=tariff.features.get('traffic_limit_gb'),
                is_active=True
            )

            self.db_session.add(subscription)
            await self.db_session.commit()

            # Создаем конфигурации по умолчанию
            await self._create_default_configs(user, subscription)

            self.logger.info(f"Создана Remnawave подписка для пользователя {user.telegram_id}")

            return {
                'subscription_id': str(subscription.id),
                'remnawave_subscription_id': subscription.remnawave_subscription_id,
                'user_id': str(user.id),
                'tariff_name': tariff.name,
                'end_date': subscription.end_date.isoformat(),
                'is_active': subscription.is_active
            }

        except RemnaWaveAPIError as e:
            self.logger.error(f"Ошибка Remnawave API при создании подписки: {e}")
            raise BusinessLogicError(f"Не удалось создать VPN подписку: {e}")
        except Exception as e:
            self.logger.error(f"Ошибка создания Remnawave подписки для {user_id}: {e}")
            raise BusinessLogicError(f"Не удалось создать подписку: {e}")

    async def _create_default_configs(self, user: VPNUsers, subscription: Subscriptions):
        """Создание конфигураций по умолчанию"""
        try:
            protocols = ['vless', 'vmess']  # Можно получить из конфигурации

            for protocol in protocols:
                # Создаем конфигурацию в Remnawave
                config = await self.vpn_panel_service.generate_config(
                    vpn_user_id=user.remnawave_user_id,
                    device_name=f"Default-{protocol.upper()}",
                    protocol=protocol
                )

                # Сохраняем в локальной БД
                vpn_config = VPNConfigs(
                    user_id=user.id,
                    remnawave_config_id=config.get('id'),
                    device_name=f"Default-{protocol.upper()}",
                    protocol=protocol,
                    config_url=config.get('config_url'),
                    is_active=True
                )

                self.db_session.add(vpn_config)

            await self.db_session.commit()

        except Exception as e:
            self.logger.error(f"Ошибка создания конфигураций по умолчанию: {e}")
            # Не прерываем процесс, конфигурации можно создать позже
    
    async def activate_subscription(
        self,
        user_id: UUID,
        tariff_id: UUID,
        payment_id: Optional[UUID] = None
    ) -> Dict[str, Any]:
        """Активация подписки для пользователя"""
        try:
            # Получаем пользователя и тариф
            user = await self._get_user_by_id(user_id)
            if not user:
                raise NotFoundError(f"Пользователь {user_id} не найден")
            
            tariff = await self._get_tariff_by_id(tariff_id)
            if not tariff:
                raise NotFoundError(f"Тариф {tariff_id} не найден")
            
            # Проверяем, есть ли уже VPN пользователь
            if user.vpn_id:
                # Продлеваем существующую подписку
                result = await self._extend_existing_subscription(user, tariff)
            else:
                # Создаем новую подписку
                result = await self._create_new_subscription(user, tariff)
            
            # Обновляем информацию в базе данных
            await self._update_user_subscription_info(user, tariff, result)
            
            # Обновляем статистику
            await self._update_user_statistics(user, tariff, payment_id)
            
            await self.db_session.flush()
            
            self.logger.info(f"Активирована подписка для пользователя {user_id}")
            return result
            
        except (NotFoundError, ValidationError):
            raise
        except Exception as e:
            self.logger.error(f"Ошибка активации подписки для {user_id}: {e}")
            raise BusinessLogicError(f"Не удалось активировать подписку: {e}")
    
    async def get_subscription_info(self, user_id: UUID) -> Optional[Dict[str, Any]]:
        """Получение информации о подписке пользователя"""
        try:
            user = await self._get_user_by_id(user_id)
            if not user or not user.vpn_id:
                return None
            
            # Получаем информацию из VPN панели
            vpn_info = await self.vpn_panel_service.get_subscription_info(user.vpn_id)
            if not vpn_info:
                return None
            
            # Получаем статистику использования
            stats = await self.vpn_panel_service.get_user_statistics(user.vpn_id)
            
            # Получаем конфигурации
            configs = await self.vpn_panel_service.get_user_configs(user.vpn_id)
            
            return {
                'user_id': str(user_id),
                'vpn_user_id': user.vpn_id,
                'subscription': vpn_info,
                'statistics': stats,
                'configs': configs,
                'is_active': vpn_info.get('is_active', False),
                'expires_at': vpn_info.get('expires_at'),
                'traffic_used': stats.get('traffic_used_gb', 0),
                'traffic_limit': vpn_info.get('traffic_limit_gb')
            }
            
        except Exception as e:
            self.logger.error(f"Ошибка получения информации о подписке для {user_id}: {e}")
            raise BusinessLogicError(f"Не удалось получить информацию о подписке: {e}")
    
    async def generate_config(
        self,
        user_id: UUID,
        device_name: str,
        protocol: str = 'wireguard'
    ) -> Dict[str, Any]:
        """Генерация новой конфигурации для пользователя"""
        try:
            user = await self._get_user_by_id(user_id)
            if not user or not user.vpn_id:
                raise ValidationError("У пользователя нет активной подписки")
            
            # Проверяем активность подписки
            subscription_info = await self.vpn_panel_service.get_subscription_info(user.vpn_id)
            if not subscription_info or not subscription_info.get('is_active'):
                raise ValidationError("Подписка неактивна")
            
            # Генерируем конфигурацию
            config = await self.vpn_panel_service.generate_config(
                vpn_user_id=user.vpn_id,
                device_name=device_name,
                protocol=protocol
            )
            
            self.logger.info(f"Сгенерирована конфигурация {protocol} для пользователя {user_id}")
            return config
            
        except (NotFoundError, ValidationError):
            raise
        except Exception as e:
            self.logger.error(f"Ошибка генерации конфигурации для {user_id}: {e}")
            raise BusinessLogicError(f"Не удалось сгенерировать конфигурацию: {e}")
    
    async def revoke_config(self, user_id: UUID, config_id: str) -> bool:
        """Отзыв конфигурации пользователя"""
        try:
            user = await self._get_user_by_id(user_id)
            if not user or not user.vpn_id:
                raise ValidationError("У пользователя нет активной подписки")
            
            # Отзываем конфигурацию
            success = await self.vpn_panel_service.revoke_config(user.vpn_id, config_id)
            
            if success:
                self.logger.info(f"Отозвана конфигурация {config_id} для пользователя {user_id}")
            
            return success
            
        except (NotFoundError, ValidationError):
            raise
        except Exception as e:
            self.logger.error(f"Ошибка отзыва конфигурации {config_id} для {user_id}: {e}")
            raise BusinessLogicError(f"Не удалось отозвать конфигурацию: {e}")
    
    async def suspend_subscription(self, user_id: UUID, reason: str = "Manual suspension") -> bool:
        """Приостановка подписки пользователя"""
        try:
            user = await self._get_user_by_id(user_id)
            if not user or not user.vpn_id:
                raise ValidationError("У пользователя нет активной подписки")
            
            # Приостанавливаем в VPN панели
            success = await self.vpn_panel_service.suspend_user(user.vpn_id, reason)
            
            if success:
                self.logger.info(f"Приостановлена подписка для пользователя {user_id}")
            
            return success
            
        except (NotFoundError, ValidationError):
            raise
        except Exception as e:
            self.logger.error(f"Ошибка приостановки подписки для {user_id}: {e}")
            raise BusinessLogicError(f"Не удалось приостановить подписку: {e}")
    
    async def reactivate_subscription(self, user_id: UUID) -> bool:
        """Восстановление подписки пользователя"""
        try:
            user = await self._get_user_by_id(user_id)
            if not user or not user.vpn_id:
                raise ValidationError("У пользователя нет подписки")
            
            # Восстанавливаем в VPN панели
            success = await self.vpn_panel_service.reactivate_user(user.vpn_id)
            
            if success:
                self.logger.info(f"Восстановлена подписка для пользователя {user_id}")
            
            return success
            
        except (NotFoundError, ValidationError):
            raise
        except Exception as e:
            self.logger.error(f"Ошибка восстановления подписки для {user_id}: {e}")
            raise BusinessLogicError(f"Не удалось восстановить подписку: {e}")
    
    async def check_expired_subscriptions(self) -> List[Dict[str, Any]]:
        """Проверка и обработка истекших подписок"""
        try:
            # Получаем пользователей с VPN подписками
            query = (
                select(VPNUsers)
                .where(VPNUsers.vpn_id.isnot(None))
            )
            result = await self.db_session.execute(query)
            users = result.scalars().all()
            
            expired_users = []
            
            for user in users:
                try:
                    # Проверяем статус подписки в VPN панели
                    subscription_info = await self.vpn_panel_service.get_subscription_info(user.vpn_id)
                    
                    if subscription_info:
                        expires_at = subscription_info.get('expires_at')
                        if expires_at:
                            expires_date = datetime.fromisoformat(expires_at.replace('Z', '+00:00'))
                            if expires_date <= datetime.now(timezone.utc):
                                # Подписка истекла
                                await self.vpn_integration.handle_subscription_expiry(user.vpn_id)
                                expired_users.append({
                                    'user_id': str(user.id),
                                    'telegram_id': user.telegram_id,
                                    'vpn_user_id': user.vpn_id,
                                    'expired_at': expires_at
                                })
                
                except Exception as e:
                    self.logger.error(f"Ошибка проверки подписки для пользователя {user.id}: {e}")
                    continue
            
            if expired_users:
                self.logger.info(f"Обработано {len(expired_users)} истекших подписок")
            
            return expired_users
            
        except Exception as e:
            self.logger.error(f"Ошибка проверки истекших подписок: {e}")
            raise BusinessLogicError(f"Не удалось проверить истекшие подписки: {e}")
    
    # Приватные методы
    
    async def _get_user_by_id(self, user_id: UUID) -> Optional[VPNUsers]:
        """Получение пользователя по ID"""
        query = select(VPNUsers).where(VPNUsers.id == user_id)
        result = await self.db_session.execute(query)
        return result.scalar_one_or_none()
    
    async def _get_tariff_by_id(self, tariff_id: UUID) -> Optional[Tariffs]:
        """Получение тарифа по ID"""
        query = select(Tariffs).where(Tariffs.id == tariff_id)
        result = await self.db_session.execute(query)
        return result.scalar_one_or_none()
    
    async def _create_new_subscription(self, user: VPNUsers, tariff: Tariffs) -> Dict[str, Any]:
        """Создание новой подписки"""
        result = await self.vpn_integration.setup_user_subscription(
            telegram_id=user.telegram_id,
            tariff_name=tariff.name,
            duration_days=tariff.duration_days,
            username=user.username
        )
        
        # Сохраняем VPN ID пользователя
        user.vpn_id = result['vpn_user_id']
        
        return result
    
    async def _extend_existing_subscription(self, user: VPNUsers, tariff: Tariffs) -> Dict[str, Any]:
        """Продление существующей подписки"""
        result = await self.vpn_integration.process_subscription_renewal(
            vpn_user_id=user.vpn_id,
            additional_days=tariff.duration_days
        )
        
        return {
            'vpn_user_id': user.vpn_id,
            'subscription': result,
            'extended': True
        }
    
    async def _update_user_subscription_info(
        self,
        user: VPNUsers,
        tariff: Tariffs,
        vpn_result: Dict[str, Any]
    ):
        """Обновление информации о подписке в базе данных"""
        # Обновляем метаданные пользователя
        if not user.user_metadata:
            user.user_metadata = {}
        
        metadata = dict(user.user_metadata)
        
        # Информация о последней подписке
        metadata['last_subscription'] = {
            'tariff_id': str(tariff.id),
            'tariff_name': tariff.name,
            'activated_at': datetime.now(timezone.utc).isoformat(),
            'duration_days': tariff.duration_days
        }
        
        user.user_metadata = metadata
    
    async def _update_user_statistics(
        self,
        user: VPNUsers,
        tariff: Tariffs,
        payment_id: Optional[UUID]
    ):
        """Обновление статистики пользователя"""
        if not user.user_metadata:
            user.user_metadata = {}
        
        metadata = dict(user.user_metadata)
        stats = metadata.get('statistics', {})
        
        # Обновляем статистику
        stats['total_subscriptions'] = stats.get('total_subscriptions', 0) + 1
        stats['total_days_purchased'] = stats.get('total_days_purchased', 0) + tariff.duration_days
        stats['last_subscription_date'] = datetime.now(timezone.utc).isoformat()
        
        if payment_id:
            stats['last_payment_id'] = str(payment_id)
        
        metadata['statistics'] = stats
        user.user_metadata = metadata
