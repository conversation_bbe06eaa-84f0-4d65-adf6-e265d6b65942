"""
UnveilVPN Shop - Referral Service
Сервис для работы с реферальной системой
"""

import logging
import secrets
import string
from typing import List, Optional, Dict, Any, Tuple
from uuid import UUID
from decimal import Decimal
from datetime import datetime, timezone

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, func, and_, or_, text
from sqlalchemy.orm import selectinload, joinedload

from ..db.models import VPNUsers, Referrals, Payments
from ..common.exceptions import NotFoundError, ValidationError, BusinessLogicError


class ReferralService:
    """
    Сервис для работы с реферальной системой
    """
    
    # Процентные ставки по уровням
    REFERRAL_RATES = {
        1: Decimal('0.10'),  # 10% с первого уровня
        2: Decimal('0.05'),  # 5% со второго уровня
        3: Decimal('0.02')   # 2% с третьего уровня
    }
    
    def __init__(self, db_session: AsyncSession):
        self.db_session = db_session
        self.logger = logging.getLogger(self.__class__.__name__)
    
    async def generate_referral_code(self, user_id: UUID) -> str:
        """Генерация уникального реферального кода"""
        try:
            # Получаем пользователя
            user = await self._get_user_by_id(user_id)
            if not user:
                raise NotFoundError(f"Пользователь {user_id} не найден")
            
            # Если у пользователя уже есть код, возвращаем его
            if user.referral_code:
                return user.referral_code
            
            # Генерируем новый код
            max_attempts = 10
            for _ in range(max_attempts):
                code = self._generate_code()
                
                # Проверяем уникальность
                if await self._is_code_unique(code):
                    # Сохраняем код
                    user.referral_code = code
                    await self.db_session.flush()
                    
                    self.logger.info(f"Сгенерирован реферальный код {code} для пользователя {user_id}")
                    return code
            
            raise BusinessLogicError("Не удалось сгенерировать уникальный реферальный код")
            
        except (NotFoundError, ValidationError):
            raise
        except Exception as e:
            self.logger.error(f"Ошибка генерации реферального кода для {user_id}: {e}")
            raise BusinessLogicError(f"Не удалось сгенерировать реферальный код: {e}")
    
    async def register_referral(self, referral_code: str, new_user_id: UUID) -> bool:
        """Регистрация нового реферала"""
        try:
            # Получаем реферера по коду
            referrer = await self._get_user_by_referral_code(referral_code)
            if not referrer:
                raise ValidationError(f"Реферальный код {referral_code} не найден")
            
            # Получаем нового пользователя
            new_user = await self._get_user_by_id(new_user_id)
            if not new_user:
                raise NotFoundError(f"Пользователь {new_user_id} не найден")
            
            # Проверяем, что пользователь еще не является рефералом
            if new_user.referred_by_id:
                raise ValidationError("Пользователь уже является рефералом")
            
            # Проверяем, что пользователь не пытается стать рефералом самого себя
            if referrer.id == new_user_id:
                raise ValidationError("Нельзя стать рефералом самого себя")
            
            # Устанавливаем связь
            new_user.referred_by_id = referrer.id
            
            # Создаем записи в таблице рефералов для всех уровней
            await self._create_referral_chain(referrer.id, new_user_id)
            
            # Обновляем статистику реферера
            await self._update_referrer_stats(referrer.id)
            
            await self.db_session.flush()
            
            self.logger.info(f"Зарегистрирован реферал {new_user_id} для {referrer.id}")
            return True
            
        except (NotFoundError, ValidationError):
            raise
        except Exception as e:
            self.logger.error(f"Ошибка регистрации реферала {new_user_id} по коду {referral_code}: {e}")
            raise BusinessLogicError(f"Не удалось зарегистрировать реферала: {e}")
    
    async def process_payment_bonus(self, payment_id: UUID) -> Dict[str, Any]:
        """Обработка бонусов за платеж реферала"""
        try:
            # Получаем платеж с пользователем
            payment = await self._get_payment_with_user(payment_id)
            if not payment:
                raise NotFoundError(f"Платеж {payment_id} не найден")
            
            if payment.status != 'completed':
                raise ValidationError("Бонусы начисляются только за завершенные платежи")
            
            user = payment.user
            if not user.referred_by_id:
                # Пользователь не является рефералом
                return {'processed': False, 'reason': 'User is not a referral'}
            
            # Получаем все реферальные связи для этого пользователя
            referral_relations = await self._get_user_referral_relations(user.id)
            
            bonuses_processed = []
            total_bonus = Decimal('0')
            
            for relation in referral_relations:
                # Рассчитываем бонус
                bonus_rate = self.REFERRAL_RATES.get(relation.level, Decimal('0'))
                bonus_amount = payment.final_amount * bonus_rate
                
                if bonus_amount > 0:
                    # Начисляем бонус
                    await self._add_bonus_to_relation(relation, bonus_amount, payment)
                    
                    bonuses_processed.append({
                        'referrer_id': str(relation.referrer_id),
                        'level': relation.level,
                        'bonus_amount': float(bonus_amount),
                        'bonus_rate': float(bonus_rate)
                    })
                    
                    total_bonus += bonus_amount
            
            await self.db_session.flush()
            
            self.logger.info(f"Обработаны бонусы за платеж {payment_id}: {len(bonuses_processed)} начислений")
            
            return {
                'processed': True,
                'payment_id': str(payment_id),
                'total_bonus': float(total_bonus),
                'bonuses': bonuses_processed
            }
            
        except (NotFoundError, ValidationError):
            raise
        except Exception as e:
            self.logger.error(f"Ошибка обработки бонусов за платеж {payment_id}: {e}")
            raise BusinessLogicError(f"Не удалось обработать бонусы: {e}")
    
    async def get_user_referral_stats(self, user_id: UUID) -> Dict[str, Any]:
        """Получение статистики рефералов пользователя"""
        try:
            user = await self._get_user_by_id(user_id)
            if not user:
                raise NotFoundError(f"Пользователь {user_id} не найден")
            
            # Получаем реферальные связи где пользователь - реферер
            referrer_relations = await self._get_referrer_relations(user_id)
            
            # Группируем по уровням
            stats_by_level = {}
            total_referrals = 0
            total_earnings = Decimal('0')
            
            for relation in referrer_relations:
                level = relation.level
                if level not in stats_by_level:
                    stats_by_level[level] = {
                        'count': 0,
                        'earnings': Decimal('0'),
                        'pending': Decimal('0'),
                        'paid': Decimal('0')
                    }
                
                stats_by_level[level]['count'] += 1
                stats_by_level[level]['earnings'] += relation.bonus_earned
                stats_by_level[level]['pending'] += relation.bonus_pending
                stats_by_level[level]['paid'] += relation.bonus_paid
                
                total_referrals += 1
                total_earnings += relation.bonus_earned
            
            # Получаем информацию о том, кто пригласил этого пользователя
            invited_by = None
            if user.referred_by_id:
                referrer = await self._get_user_by_id(user.referred_by_id)
                if referrer:
                    invited_by = {
                        'user_id': str(referrer.id),
                        'telegram_id': referrer.telegram_id,
                        'username': referrer.username,
                        'referral_code': referrer.referral_code
                    }
            
            return {
                'user_id': str(user_id),
                'referral_code': user.referral_code,
                'total_referrals': total_referrals,
                'total_earnings': float(total_earnings),
                'invited_by': invited_by,
                'stats_by_level': {
                    str(level): {
                        'count': stats['count'],
                        'earnings': float(stats['earnings']),
                        'pending': float(stats['pending']),
                        'paid': float(stats['paid'])
                    }
                    for level, stats in stats_by_level.items()
                }
            }
            
        except (NotFoundError, ValidationError):
            raise
        except Exception as e:
            self.logger.error(f"Ошибка получения статистики рефералов для {user_id}: {e}")
            raise BusinessLogicError(f"Не удалось получить статистику рефералов: {e}")
    
    async def get_referral_tree(self, user_id: UUID, max_depth: int = 3) -> Dict[str, Any]:
        """Получение дерева рефералов"""
        try:
            # Используем рекурсивный CTE запрос для построения дерева
            query = text("""
                WITH RECURSIVE referral_tree AS (
                    -- Базовый случай: прямые рефералы
                    SELECT 
                        r.referrer_id,
                        r.referred_id,
                        r.level,
                        r.bonus_earned,
                        r.total_purchases,
                        u.username,
                        u.telegram_id,
                        1 as depth
                    FROM referrals r
                    JOIN vpnusers u ON r.referred_id = u.id
                    WHERE r.referrer_id = :user_id AND r.level = 1
                    
                    UNION ALL
                    
                    -- Рекурсивный случай: рефералы рефералов
                    SELECT 
                        rt.referred_id as referrer_id,
                        r.referred_id,
                        r.level,
                        r.bonus_earned,
                        r.total_purchases,
                        u.username,
                        u.telegram_id,
                        rt.depth + 1
                    FROM referral_tree rt
                    JOIN referrals r ON rt.referred_id = r.referrer_id
                    JOIN vpnusers u ON r.referred_id = u.id
                    WHERE rt.depth < :max_depth AND r.level = 1
                )
                SELECT * FROM referral_tree ORDER BY depth, username
            """)
            
            result = await self.db_session.execute(query, {
                'user_id': str(user_id),
                'max_depth': max_depth
            })
            
            tree_data = result.fetchall()
            
            # Строим дерево
            tree = {}
            for row in tree_data:
                depth = row.depth
                if depth not in tree:
                    tree[depth] = []
                
                tree[depth].append({
                    'user_id': str(row.referred_id),
                    'username': row.username,
                    'telegram_id': row.telegram_id,
                    'level': row.level,
                    'bonus_earned': float(row.bonus_earned),
                    'total_purchases': row.total_purchases
                })
            
            return {
                'user_id': str(user_id),
                'max_depth': max_depth,
                'tree': tree
            }
            
        except Exception as e:
            self.logger.error(f"Ошибка получения дерева рефералов для {user_id}: {e}")
            raise BusinessLogicError(f"Не удалось получить дерево рефералов: {e}")
    
    # Приватные методы
    
    def _generate_code(self, length: int = 8) -> str:
        """Генерация случайного кода"""
        alphabet = string.ascii_uppercase + string.digits
        # Исключаем похожие символы
        alphabet = alphabet.replace('0', '').replace('O', '').replace('I', '').replace('1', '')
        return ''.join(secrets.choice(alphabet) for _ in range(length))
    
    async def _is_code_unique(self, code: str) -> bool:
        """Проверка уникальности кода"""
        query = select(VPNUsers).where(VPNUsers.referral_code == code)
        result = await self.db_session.execute(query)
        return result.scalar_one_or_none() is None
    
    async def _get_user_by_id(self, user_id: UUID) -> Optional[VPNUsers]:
        """Получение пользователя по ID"""
        query = select(VPNUsers).where(VPNUsers.id == user_id)
        result = await self.db_session.execute(query)
        return result.scalar_one_or_none()
    
    async def _get_user_by_referral_code(self, code: str) -> Optional[VPNUsers]:
        """Получение пользователя по реферальному коду"""
        query = select(VPNUsers).where(VPNUsers.referral_code == code)
        result = await self.db_session.execute(query)
        return result.scalar_one_or_none()
    
    async def _get_payment_with_user(self, payment_id: UUID) -> Optional[Payments]:
        """Получение платежа с пользователем"""
        query = (
            select(Payments)
            .options(selectinload(Payments.user))
            .where(Payments.id == payment_id)
        )
        result = await self.db_session.execute(query)
        return result.scalar_one_or_none()
    
    async def _create_referral_chain(self, referrer_id: UUID, referred_id: UUID):
        """Создание цепочки реферальных связей"""
        # Создаем связь первого уровня
        referral_1 = Referrals(
            referrer_id=referrer_id,
            referred_id=referred_id,
            level=1
        )
        self.db_session.add(referral_1)
        
        # Ищем реферера второго уровня
        level_2_referrer = await self._get_user_by_id(referrer_id)
        if level_2_referrer and level_2_referrer.referred_by_id:
            referral_2 = Referrals(
                referrer_id=level_2_referrer.referred_by_id,
                referred_id=referred_id,
                level=2
            )
            self.db_session.add(referral_2)
            
            # Ищем реферера третьего уровня
            level_3_referrer = await self._get_user_by_id(level_2_referrer.referred_by_id)
            if level_3_referrer and level_3_referrer.referred_by_id:
                referral_3 = Referrals(
                    referrer_id=level_3_referrer.referred_by_id,
                    referred_id=referred_id,
                    level=3
                )
                self.db_session.add(referral_3)
    
    async def _get_user_referral_relations(self, user_id: UUID) -> List[Referrals]:
        """Получение всех реферальных связей для пользователя (как реферала)"""
        query = (
            select(Referrals)
            .where(Referrals.referred_id == user_id)
            .order_by(Referrals.level)
        )
        result = await self.db_session.execute(query)
        return result.scalars().all()
    
    async def _get_referrer_relations(self, user_id: UUID) -> List[Referrals]:
        """Получение всех реферальных связей где пользователь - реферер"""
        query = (
            select(Referrals)
            .where(Referrals.referrer_id == user_id)
            .order_by(Referrals.level, Referrals.created_at)
        )
        result = await self.db_session.execute(query)
        return result.scalars().all()
    
    async def _add_bonus_to_relation(self, relation: Referrals, bonus_amount: Decimal, payment: Payments):
        """Добавление бонуса к реферальной связи"""
        relation.bonus_earned += bonus_amount
        relation.bonus_pending += bonus_amount
        relation.total_purchases += 1
        relation.total_amount += payment.final_amount
        relation.last_earning_at = datetime.now(timezone.utc)
        
        # Обновляем статистику реферера
        await self._update_referrer_earnings(relation.referrer_id, bonus_amount)
    
    async def _update_referrer_earnings(self, referrer_id: UUID, bonus_amount: Decimal):
        """Обновление заработка реферера"""
        query = (
            update(VPNUsers)
            .where(VPNUsers.id == referrer_id)
            .values(referral_earnings=VPNUsers.referral_earnings + bonus_amount)
        )
        await self.db_session.execute(query)
    
    async def _update_referrer_stats(self, referrer_id: UUID):
        """Обновление статистики реферера"""
        user = await self._get_user_by_id(referrer_id)
        if user and user.user_metadata:
            metadata = dict(user.user_metadata)
            stats = metadata.get('statistics', {})
            stats['referrals_count'] = stats.get('referrals_count', 0) + 1
            metadata['statistics'] = stats
            user.user_metadata = metadata
