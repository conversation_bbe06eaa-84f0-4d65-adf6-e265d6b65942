"""
UnveilVPN Shop - Support Service
Сервис для работы с системой поддержки
"""

import logging
import secrets
import string
from typing import List, Optional, Dict, Any, Tuple
from uuid import UUID
from datetime import datetime, timezone, timedelta

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, func, and_, or_, desc
from sqlalchemy.orm import selectinload, joinedload

from ..db.models import SupportTickets, VPNUsers
from ..common.exceptions import NotFoundError, ValidationError, BusinessLogicError


class SupportService:
    """
    Сервис для работы с системой поддержки
    """
    
    # Категории тикетов
    CATEGORIES = {
        'technical': 'Технические проблемы',
        'billing': 'Вопросы по оплате',
        'account': 'Проблемы с аккаунтом',
        'connection': 'Проблемы с подключением',
        'feature': 'Запрос функций',
        'other': 'Другое'
    }
    
    # Приоритеты
    PRIORITIES = {
        'low': 'Низкий',
        'medium': 'Средний',
        'high': 'Высокий',
        'urgent': 'Критический'
    }
    
    # Статусы
    STATUSES = {
        'open': 'Открыт',
        'in_progress': 'В работе',
        'waiting_user': 'Ожидает пользователя',
        'closed': 'Закрыт'
    }
    
    def __init__(self, db_session: AsyncSession):
        self.db_session = db_session
        self.logger = logging.getLogger(self.__class__.__name__)
    
    async def create_ticket(
        self,
        user_id: UUID,
        subject: str,
        message: str,
        category: str = 'other',
        priority: str = 'medium',
        metadata: Optional[Dict[str, Any]] = None
    ) -> SupportTickets:
        """Создание нового тикета поддержки"""
        try:
            # Валидация данных
            await self._validate_ticket_data(subject, category, priority)
            
            # Генерируем номер тикета
            ticket_number = await self._generate_ticket_number()
            
            # Создаем тикет
            ticket = SupportTickets(
                ticket_number=ticket_number,
                user_id=user_id,
                subject=subject.strip(),
                category=category,
                priority=priority,
                metadata=metadata or {}
            )
            
            # Добавляем первое сообщение
            ticket.add_message(message, user_id, is_internal=False)
            
            self.db_session.add(ticket)
            await self.db_session.flush()
            
            self.logger.info(f"Создан тикет {ticket.ticket_number} для пользователя {user_id}")
            return ticket
            
        except (ValidationError, BusinessLogicError):
            raise
        except Exception as e:
            self.logger.error(f"Ошибка создания тикета: {e}")
            raise BusinessLogicError(f"Не удалось создать тикет: {e}")
    
    async def get_ticket_by_id(self, ticket_id: UUID) -> Optional[SupportTickets]:
        """Получение тикета по ID"""
        try:
            query = (
                select(SupportTickets)
                .options(
                    selectinload(SupportTickets.user),
                    selectinload(SupportTickets.assigned_to)
                )
                .where(SupportTickets.id == ticket_id)
            )
            result = await self.db_session.execute(query)
            return result.scalar_one_or_none()
            
        except Exception as e:
            self.logger.error(f"Ошибка получения тикета {ticket_id}: {e}")
            raise BusinessLogicError(f"Не удалось получить тикет: {e}")
    
    async def get_ticket_by_number(self, ticket_number: str) -> Optional[SupportTickets]:
        """Получение тикета по номеру"""
        try:
            query = (
                select(SupportTickets)
                .options(
                    selectinload(SupportTickets.user),
                    selectinload(SupportTickets.assigned_to)
                )
                .where(SupportTickets.ticket_number == ticket_number)
            )
            result = await self.db_session.execute(query)
            return result.scalar_one_or_none()
            
        except Exception as e:
            self.logger.error(f"Ошибка получения тикета {ticket_number}: {e}")
            raise BusinessLogicError(f"Не удалось получить тикет: {e}")
    
    async def add_message_to_ticket(
        self,
        ticket_id: UUID,
        message: str,
        author_id: UUID,
        is_internal: bool = False,
        attachments: Optional[List[str]] = None
    ) -> SupportTickets:
        """Добавление сообщения в тикет"""
        try:
            ticket = await self.get_ticket_by_id(ticket_id)
            if not ticket:
                raise NotFoundError(f"Тикет {ticket_id} не найден")
            
            if ticket.status == 'closed':
                raise ValidationError("Нельзя добавлять сообщения в закрытый тикет")
            
            # Добавляем сообщение
            ticket.add_message(message, author_id, is_internal)
            
            # Обновляем статус если нужно
            if not is_internal and ticket.status == 'waiting_user':
                ticket.status = 'in_progress'
            elif is_internal and ticket.status == 'open':
                ticket.status = 'in_progress'
            
            await self.db_session.flush()
            
            self.logger.info(f"Добавлено сообщение в тикет {ticket.ticket_number}")
            return ticket
            
        except (NotFoundError, ValidationError):
            raise
        except Exception as e:
            self.logger.error(f"Ошибка добавления сообщения в тикет {ticket_id}: {e}")
            raise BusinessLogicError(f"Не удалось добавить сообщение: {e}")
    
    async def update_ticket_status(
        self,
        ticket_id: UUID,
        new_status: str,
        updated_by_id: UUID,
        reason: Optional[str] = None
    ) -> SupportTickets:
        """Обновление статуса тикета"""
        try:
            ticket = await self.get_ticket_by_id(ticket_id)
            if not ticket:
                raise NotFoundError(f"Тикет {ticket_id} не найден")
            
            if new_status not in self.STATUSES:
                raise ValidationError(f"Неверный статус: {new_status}")
            
            old_status = ticket.status
            ticket.status = new_status
            
            # Добавляем системное сообщение
            status_message = f"Статус изменен с '{self.STATUSES[old_status]}' на '{self.STATUSES[new_status]}'"
            if reason:
                status_message += f". Причина: {reason}"
            
            ticket.add_message(status_message, updated_by_id, is_internal=True)
            
            # Если тикет закрывается
            if new_status == 'closed':
                ticket.closed_at = datetime.now(timezone.utc)
            
            await self.db_session.flush()
            
            self.logger.info(f"Статус тикета {ticket.ticket_number} изменен на {new_status}")
            return ticket
            
        except (NotFoundError, ValidationError):
            raise
        except Exception as e:
            self.logger.error(f"Ошибка обновления статуса тикета {ticket_id}: {e}")
            raise BusinessLogicError(f"Не удалось обновить статус тикета: {e}")
    
    async def assign_ticket(
        self,
        ticket_id: UUID,
        assigned_to_id: UUID,
        assigned_by_id: UUID
    ) -> SupportTickets:
        """Назначение тикета сотруднику"""
        try:
            ticket = await self.get_ticket_by_id(ticket_id)
            if not ticket:
                raise NotFoundError(f"Тикет {ticket_id} не найден")
            
            # Проверяем, что назначаемый пользователь существует
            assignee = await self._get_user_by_id(assigned_to_id)
            if not assignee:
                raise NotFoundError(f"Пользователь {assigned_to_id} не найден")
            
            old_assignee_id = ticket.assigned_to_id
            ticket.assigned_to_id = assigned_to_id
            
            # Добавляем системное сообщение
            if old_assignee_id:
                message = f"Тикет переназначен на {assignee.username or assignee.telegram_id}"
            else:
                message = f"Тикет назначен на {assignee.username or assignee.telegram_id}"
            
            ticket.add_message(message, assigned_by_id, is_internal=True)
            
            # Обновляем статус если нужно
            if ticket.status == 'open':
                ticket.status = 'in_progress'
            
            await self.db_session.flush()
            
            self.logger.info(f"Тикет {ticket.ticket_number} назначен на {assigned_to_id}")
            return ticket
            
        except (NotFoundError, ValidationError):
            raise
        except Exception as e:
            self.logger.error(f"Ошибка назначения тикета {ticket_id}: {e}")
            raise BusinessLogicError(f"Не удалось назначить тикет: {e}")
    
    async def get_tickets(
        self,
        user_id: Optional[UUID] = None,
        assigned_to_id: Optional[UUID] = None,
        status: Optional[str] = None,
        category: Optional[str] = None,
        priority: Optional[str] = None,
        limit: int = 50,
        offset: int = 0
    ) -> List[SupportTickets]:
        """Получение списка тикетов с фильтрацией"""
        try:
            query = (
                select(SupportTickets)
                .options(
                    selectinload(SupportTickets.user),
                    selectinload(SupportTickets.assigned_to)
                )
            )
            
            # Фильтры
            if user_id:
                query = query.where(SupportTickets.user_id == user_id)
            
            if assigned_to_id:
                query = query.where(SupportTickets.assigned_to_id == assigned_to_id)
            
            if status:
                query = query.where(SupportTickets.status == status)
            
            if category:
                query = query.where(SupportTickets.category == category)
            
            if priority:
                query = query.where(SupportTickets.priority == priority)
            
            # Сортировка и пагинация
            query = query.order_by(desc(SupportTickets.created_at)).limit(limit).offset(offset)
            
            result = await self.db_session.execute(query)
            return result.scalars().all()
            
        except Exception as e:
            self.logger.error(f"Ошибка получения списка тикетов: {e}")
            raise BusinessLogicError(f"Не удалось получить тикеты: {e}")
    
    async def get_overdue_tickets(self) -> List[SupportTickets]:
        """Получение просроченных тикетов"""
        try:
            # Получаем все открытые тикеты
            query = (
                select(SupportTickets)
                .options(
                    selectinload(SupportTickets.user),
                    selectinload(SupportTickets.assigned_to)
                )
                .where(SupportTickets.status.in_(['open', 'in_progress', 'waiting_user']))
            )
            
            result = await self.db_session.execute(query)
            tickets = result.scalars().all()
            
            # Фильтруем просроченные
            overdue_tickets = [ticket for ticket in tickets if ticket.is_overdue]
            
            return overdue_tickets
            
        except Exception as e:
            self.logger.error(f"Ошибка получения просроченных тикетов: {e}")
            raise BusinessLogicError(f"Не удалось получить просроченные тикеты: {e}")
    
    async def get_support_statistics(
        self,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """Получение статистики поддержки"""
        try:
            # Устанавливаем период по умолчанию (последние 30 дней)
            if not start_date:
                start_date = datetime.now(timezone.utc) - timedelta(days=30)
            if not end_date:
                end_date = datetime.now(timezone.utc)
            
            # Базовый запрос
            base_query = (
                select(SupportTickets)
                .where(and_(
                    SupportTickets.created_at >= start_date,
                    SupportTickets.created_at <= end_date
                ))
            )
            
            result = await self.db_session.execute(base_query)
            tickets = result.scalars().all()
            
            # Подсчитываем статистику
            total_tickets = len(tickets)
            
            # Группировка по статусам
            status_stats = {}
            for status in self.STATUSES:
                status_stats[status] = len([t for t in tickets if t.status == status])
            
            # Группировка по категориям
            category_stats = {}
            for category in self.CATEGORIES:
                category_stats[category] = len([t for t in tickets if t.category == category])
            
            # Группировка по приоритетам
            priority_stats = {}
            for priority in self.PRIORITIES:
                priority_stats[priority] = len([t for t in tickets if t.priority == priority])
            
            # Просроченные тикеты
            overdue_count = len([t for t in tickets if t.is_overdue])
            
            # Среднее время ответа (заглушка)
            avg_response_time = 4.5  # часов
            
            return {
                'period': {
                    'start_date': start_date.isoformat(),
                    'end_date': end_date.isoformat()
                },
                'summary': {
                    'total_tickets': total_tickets,
                    'overdue_tickets': overdue_count,
                    'avg_response_time_hours': avg_response_time
                },
                'by_status': status_stats,
                'by_category': category_stats,
                'by_priority': priority_stats
            }
            
        except Exception as e:
            self.logger.error(f"Ошибка получения статистики поддержки: {e}")
            raise BusinessLogicError(f"Не удалось получить статистику: {e}")
    
    # Приватные методы
    
    async def _validate_ticket_data(self, subject: str, category: str, priority: str):
        """Валидация данных тикета"""
        if not subject or len(subject.strip()) < 5:
            raise ValidationError("Тема тикета должна содержать минимум 5 символов")
        
        if len(subject.strip()) > 500:
            raise ValidationError("Тема тикета не может быть длиннее 500 символов")
        
        if category not in self.CATEGORIES:
            raise ValidationError(f"Неверная категория. Допустимые: {list(self.CATEGORIES.keys())}")
        
        if priority not in self.PRIORITIES:
            raise ValidationError(f"Неверный приоритет. Допустимые: {list(self.PRIORITIES.keys())}")
    
    async def _generate_ticket_number(self) -> str:
        """Генерация уникального номера тикета"""
        max_attempts = 10
        
        for _ in range(max_attempts):
            # Генерируем номер в формате YYYYMMDD-XXXX
            date_part = datetime.now(timezone.utc).strftime("%Y%m%d")
            random_part = ''.join(secrets.choice(string.digits) for _ in range(4))
            ticket_number = f"{date_part}-{random_part}"
            
            # Проверяем уникальность
            existing = await self.get_ticket_by_number(ticket_number)
            if not existing:
                return ticket_number
        
        raise BusinessLogicError("Не удалось сгенерировать уникальный номер тикета")
    
    async def _get_user_by_id(self, user_id: UUID) -> Optional[VPNUsers]:
        """Получение пользователя по ID"""
        query = select(VPNUsers).where(VPNUsers.id == user_id)
        result = await self.db_session.execute(query)
        return result.scalar_one_or_none()
