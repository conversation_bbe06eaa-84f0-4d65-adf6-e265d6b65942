"""
UnveilVPN Shop - FastAPI Main Application
"""

import os
import logging
from contextlib import asynccontextmanager
from typing import Dict, Any

from fastapi import Fast<PERSON><PERSON>, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
from fastapi.openapi.docs import get_swagger_ui_html
from fastapi.openapi.utils import get_openapi

from bot.common.config import BotConfig
from bot.db.session import init_db, close_db
from bot.api.routes import tariffs, payments, users, health
from bot.api.routers import public as public_router
from bot.common.exceptions import BotException


# Настройка логирования
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Управление жизненным циклом приложения"""
    # Startup
    logger.info("Starting UnveilVPN Shop API...")
    
    try:
        # Инициализация базы данных
        await init_db()
        logger.info("Database initialized successfully")
        
        yield
        
    except Exception as e:
        logger.error(f"Failed to initialize application: {e}")
        raise
    finally:
        # Shutdown
        logger.info("Shutting down UnveilVPN Shop API...")
        await close_db()
        logger.info("Database connections closed")


# Создание FastAPI приложения
app = FastAPI(
    title="UnveilVPN Shop API",
    description="API для системы продажи VPN подписок",
    version="1.0.0",
    docs_url="/docs" if os.getenv("ENVIRONMENT") != "production" else None,
    redoc_url="/redoc" if os.getenv("ENVIRONMENT") != "production" else None,
    lifespan=lifespan
)

# Настройка CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=os.getenv("CORS_ORIGINS", "http://localhost:3000").split(","),
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE"],
    allow_headers=["*"],
)

# Настройка доверенных хостов
trusted_hosts = os.getenv("TRUSTED_HOSTS", "localhost,127.0.0.1").split(",")
app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=trusted_hosts
)


@app.exception_handler(BotException)
async def bot_exception_handler(request: Request, exc: BotException):
    """Обработчик исключений бота"""
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": exc.error_code,
            "message": exc.message,
            "details": exc.details
        }
    )


@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """Обработчик HTTP исключений"""
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": "HTTP_ERROR",
            "message": exc.detail,
            "status_code": exc.status_code
        }
    )


@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """Обработчик общих исключений"""
    logger.error(f"Unhandled exception: {exc}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={
            "error": "INTERNAL_SERVER_ERROR",
            "message": "Внутренняя ошибка сервера"
        }
    )


# Подключение маршрутов
app.include_router(health.router, prefix="/health", tags=["Health"])
app.include_router(tariffs.router, prefix="/tariffs", tags=["Tariffs"])
app.include_router(payments.router, prefix="/payments", tags=["Payments"])
app.include_router(users.router, prefix="/users", tags=["Users"])
app.include_router(public_router.router, prefix="/v1/public", tags=["Public"])


@app.get("/")
async def root():
    """Корневой endpoint"""
    return {
        "message": "UnveilVPN Shop API",
        "version": "1.0.0",
        "status": "running"
    }


@app.get("/info")
async def info():
    """Информация о API"""
    return {
        "name": "UnveilVPN Shop API",
        "version": "1.0.0",
        "environment": os.getenv("ENVIRONMENT", "development"),
        "features": [
            "Управление тарифами",
            "Обработка платежей",
            "Управление пользователями",
            "Реферальная система",
            "Система промокодов",
            "Поддержка клиентов"
        ]
    }

@app.get("/api/config")
async def api_config():
    """Конфигурация API"""
    return {
        "version": "2.0.0",
        "api_name": "UnveilVPN Shop API",
        "remnawave_enabled": True,
        "features": ["payments", "vpn", "subscriptions", "tariffs"],
        "supported_currencies": ["rub", "usd", "stars"],
        "payment_methods": ["yookassa", "cryptomus", "telegram_stars"]
    }


# Кастомная OpenAPI схема
def custom_openapi():
    if app.openapi_schema:
        return app.openapi_schema
    
    openapi_schema = get_openapi(
        title="UnveilVPN Shop API",
        version="1.0.0",
        description="Комплексная система продажи VPN подписок",
        routes=app.routes,
    )
    
    # Добавляем дополнительную информацию
    openapi_schema["info"]["contact"] = {
        "name": "UnveilVPN Support",
        "email": "<EMAIL>"
    }
    
    openapi_schema["info"]["license"] = {
        "name": "MIT",
        "url": "https://opensource.org/licenses/MIT"
    }
    
    app.openapi_schema = openapi_schema
    return app.openapi_schema


app.openapi = custom_openapi


if __name__ == "__main__":
    import uvicorn
    
    # Настройки для разработки
    uvicorn.run(
        "bot.api.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
