"""
UnveilVPN Shop - Users Router
API роутер для работы с пользователями
"""

from fastapi import APIRouter

router = APIRouter()


@router.get("/")
async def get_users():
    """Получение списка пользователей"""
    # Заглушка
    return {"message": "Users list endpoint - not implemented yet"}


@router.get("/{user_id}")
async def get_user(user_id: str):
    """Получение пользователя по ID"""
    # Заглушка
    return {"message": f"User {user_id} endpoint - not implemented yet"}
