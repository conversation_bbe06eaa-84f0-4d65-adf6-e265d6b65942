"""
UnveilVPN Shop - Tariffs API Router
API роутер для работы с тарифами
"""

from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, Query, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from ..dependencies import get_db_session, get_current_user
from ..models import TariffResponse, TariffCreate, TariffUpdate, BaseResponse
from ..exceptions import NotFoundError, ValidationError, BusinessLogicError
from ...services.tariff_service import TariffService
from ...db.models import VPNUsers

router = APIRouter()


@router.get("/", response_model=List[TariffResponse])
async def get_tariffs(
    active_only: bool = Query(True, description="Только активные тарифы"),
    include_test: bool = Query(True, description="Включить тестовые тарифы"),
    sort_by: str = Query("sort_order", description="Сортировка: sort_order, price_asc, price_desc, duration, popular"),
    db: AsyncSession = Depends(get_db_session)
):
    """Получение списка тарифов"""
    try:
        tariff_service = TariffService(db)
        tariffs = await tariff_service.get_all_tariffs(
            active_only=active_only,
            include_test=include_test,
            sort_by=sort_by
        )
        
        return [TariffResponse.from_orm(tariff) for tariff in tariffs]
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка получения тарифов: {str(e)}"
        )


@router.get("/popular", response_model=List[TariffResponse])
async def get_popular_tariffs(
    limit: int = Query(3, ge=1, le=10, description="Количество популярных тарифов"),
    db: AsyncSession = Depends(get_db_session)
):
    """Получение популярных тарифов"""
    try:
        tariff_service = TariffService(db)
        tariffs = await tariff_service.get_popular_tariffs(limit=limit)
        
        return [TariffResponse.from_orm(tariff) for tariff in tariffs]
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка получения популярных тарифов: {str(e)}"
        )


@router.get("/search", response_model=List[TariffResponse])
async def search_tariffs(
    q: str = Query(..., min_length=2, description="Поисковый запрос"),
    db: AsyncSession = Depends(get_db_session)
):
    """Поиск тарифов"""
    try:
        tariff_service = TariffService(db)
        tariffs = await tariff_service.search_tariffs(q)
        
        return [TariffResponse.from_orm(tariff) for tariff in tariffs]
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка поиска тарифов: {str(e)}"
        )


@router.get("/duration/{duration_days}", response_model=List[TariffResponse])
async def get_tariffs_by_duration(
    duration_days: int,
    db: AsyncSession = Depends(get_db_session)
):
    """Получение тарифов по продолжительности"""
    try:
        if duration_days <= 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Продолжительность должна быть положительным числом"
            )
        
        tariff_service = TariffService(db)
        tariffs = await tariff_service.get_tariffs_by_duration(duration_days)
        
        return [TariffResponse.from_orm(tariff) for tariff in tariffs]
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка получения тарифов: {str(e)}"
        )


@router.get("/{tariff_id}", response_model=TariffResponse)
async def get_tariff(
    tariff_id: UUID,
    db: AsyncSession = Depends(get_db_session)
):
    """Получение тарифа по ID"""
    try:
        tariff_service = TariffService(db)
        tariff = await tariff_service.get_tariff_by_id(tariff_id)
        
        if not tariff:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Тариф с ID {tariff_id} не найден"
            )
        
        return TariffResponse.from_orm(tariff)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка получения тарифа: {str(e)}"
        )


@router.post("/", response_model=TariffResponse, status_code=status.HTTP_201_CREATED)
async def create_tariff(
    tariff_data: TariffCreate,
    db: AsyncSession = Depends(get_db_session),
    current_user: VPNUsers = Depends(get_current_user)
):
    """Создание нового тарифа (только для администраторов)"""
    try:
        # Проверка прав администратора
        if not current_user.is_admin:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Недостаточно прав для создания тарифа"
            )
        
        tariff_service = TariffService(db)
        tariff = await tariff_service.create_tariff(tariff_data.dict())
        
        await db.commit()
        
        return TariffResponse.from_orm(tariff)
        
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка создания тарифа: {str(e)}"
        )


@router.put("/{tariff_id}", response_model=TariffResponse)
async def update_tariff(
    tariff_id: UUID,
    tariff_data: TariffUpdate,
    db: AsyncSession = Depends(get_db_session),
    current_user: VPNUsers = Depends(get_current_user)
):
    """Обновление тарифа (только для администраторов)"""
    try:
        # Проверка прав администратора
        if not current_user.is_admin:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Недостаточно прав для обновления тарифа"
            )
        
        tariff_service = TariffService(db)
        
        # Фильтруем только заполненные поля
        update_data = {k: v for k, v in tariff_data.dict().items() if v is not None}
        
        if not update_data:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Нет данных для обновления"
            )
        
        tariff = await tariff_service.update_tariff(tariff_id, update_data)
        
        await db.commit()
        
        return TariffResponse.from_orm(tariff)
        
    except NotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка обновления тарифа: {str(e)}"
        )


@router.delete("/{tariff_id}", response_model=BaseResponse)
async def delete_tariff(
    tariff_id: UUID,
    soft_delete: bool = Query(True, description="Мягкое удаление (деактивация)"),
    db: AsyncSession = Depends(get_db_session),
    current_user: VPNUsers = Depends(get_current_user)
):
    """Удаление тарифа (только для администраторов)"""
    try:
        # Проверка прав администратора
        if not current_user.is_admin:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Недостаточно прав для удаления тарифа"
            )
        
        tariff_service = TariffService(db)
        success = await tariff_service.delete_tariff(tariff_id, soft_delete=soft_delete)
        
        await db.commit()
        
        action = "деактивирован" if soft_delete else "удален"
        return BaseResponse(
            success=success,
            message=f"Тариф успешно {action}"
        )
        
    except NotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except BusinessLogicError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка удаления тарифа: {str(e)}"
        )


@router.post("/{tariff_id}/calculate-price")
async def calculate_tariff_price(
    tariff_id: UUID,
    currency: str = Query("rub", description="Валюта для расчета"),
    promocode: Optional[str] = Query(None, description="Промокод для скидки"),
    db: AsyncSession = Depends(get_db_session)
):
    """Расчет цены тарифа с учетом скидок"""
    try:
        tariff_service = TariffService(db)
        
        # Получаем скидку по промокоду (если есть)
        promocode_discount = 0
        if promocode:
            # Здесь будет логика проверки промокода
            # Пока заглушка
            pass
        
        price_info = await tariff_service.calculate_price_with_discount(
            tariff_id=tariff_id,
            currency=currency,
            promocode_discount=promocode_discount
        )
        
        return {
            "success": True,
            "data": price_info
        }
        
    except NotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка расчета цены: {str(e)}"
        )
