"""
UnveilVPN Shop - Support API Router
API endpoints для работы с системой поддержки
"""

from typing import List, Optional, Dict, Any
from uuid import UUID
from datetime import datetime

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession

from ..dependencies import get_db_session, get_current_user, require_admin
from ..models import (
    SupportTicketCreate, SupportTicketResponse, SupportTicketUpdate,
    SupportMessageCreate, SupportTicketListResponse, SupportStatsResponse,
    BaseResponse
)
from ...services.support_service import SupportService
from ...common.exceptions import NotFoundError, ValidationError, BusinessLogicError

router = APIRouter(prefix="/support", tags=["support"])


def get_support_service(db_session: AsyncSession = Depends(get_db_session)) -> SupportService:
    """Получение сервиса поддержки"""
    return SupportService(db_session)


@router.post("/tickets", response_model=SupportTicketResponse)
async def create_ticket(
    ticket_data: SupportTicketCreate,
    db_session: AsyncSession = Depends(get_db_session),
    support_service: SupportService = Depends(get_support_service),
    current_user = Depends(get_current_user)
):
    """Создание нового тикета поддержки"""
    try:
        ticket = await support_service.create_ticket(
            user_id=current_user.id,
            subject=ticket_data.subject,
            message=ticket_data.message,
            category=ticket_data.category,
            priority=ticket_data.priority,
            metadata=ticket_data.metadata
        )
        
        await db_session.commit()
        
        return SupportTicketResponse.from_orm(ticket)
    
    except (ValidationError, BusinessLogicError) as e:
        await db_session.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        await db_session.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка создания тикета: {e}"
        )


@router.get("/tickets", response_model=SupportTicketListResponse)
async def get_tickets(
    status_filter: Optional[str] = Query(None, alias="status", description="Фильтр по статусу"),
    category: Optional[str] = Query(None, description="Фильтр по категории"),
    priority: Optional[str] = Query(None, description="Фильтр по приоритету"),
    assigned_to_id: Optional[UUID] = Query(None, description="Фильтр по назначенному сотруднику"),
    limit: int = Query(50, ge=1, le=100, description="Количество тикетов"),
    offset: int = Query(0, ge=0, description="Смещение"),
    support_service: SupportService = Depends(get_support_service),
    current_user = Depends(get_current_user)
):
    """Получение списка тикетов"""
    try:
        # Для обычных пользователей показываем только их тикеты
        user_id = None if current_user.is_admin else current_user.id
        
        tickets = await support_service.get_tickets(
            user_id=user_id,
            assigned_to_id=assigned_to_id,
            status=status_filter,
            category=category,
            priority=priority,
            limit=limit,
            offset=offset
        )
        
        return SupportTicketListResponse(
            success=True,
            tickets=[SupportTicketResponse.from_orm(t) for t in tickets],
            total=len(tickets),
            message="Тикеты получены успешно"
        )
    
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка получения тикетов: {e}"
        )


@router.get("/tickets/{ticket_id}", response_model=SupportTicketResponse)
async def get_ticket(
    ticket_id: UUID,
    support_service: SupportService = Depends(get_support_service),
    current_user = Depends(get_current_user)
):
    """Получение тикета по ID"""
    try:
        ticket = await support_service.get_ticket_by_id(ticket_id)
        
        if not ticket:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Тикет не найден"
            )
        
        # Проверяем права доступа
        if not current_user.is_admin and ticket.user_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Недостаточно прав для просмотра тикета"
            )
        
        return SupportTicketResponse.from_orm(ticket)
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка получения тикета: {e}"
        )


@router.post("/tickets/{ticket_id}/messages", response_model=SupportTicketResponse)
async def add_message_to_ticket(
    ticket_id: UUID,
    message_data: SupportMessageCreate,
    db_session: AsyncSession = Depends(get_db_session),
    support_service: SupportService = Depends(get_support_service),
    current_user = Depends(get_current_user)
):
    """Добавление сообщения в тикет"""
    try:
        # Проверяем доступ к тикету
        ticket = await support_service.get_ticket_by_id(ticket_id)
        if not ticket:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Тикет не найден"
            )
        
        # Проверяем права
        if not current_user.is_admin and ticket.user_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Недостаточно прав для добавления сообщения"
            )
        
        # Только админы могут создавать внутренние сообщения
        is_internal = message_data.is_internal and current_user.is_admin
        
        updated_ticket = await support_service.add_message_to_ticket(
            ticket_id=ticket_id,
            message=message_data.message,
            author_id=current_user.id,
            is_internal=is_internal,
            attachments=message_data.attachments
        )
        
        await db_session.commit()
        
        return SupportTicketResponse.from_orm(updated_ticket)
    
    except HTTPException:
        raise
    except (NotFoundError, ValidationError, BusinessLogicError) as e:
        await db_session.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        await db_session.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка добавления сообщения: {e}"
        )


@router.put("/tickets/{ticket_id}/status", response_model=SupportTicketResponse)
async def update_ticket_status(
    ticket_id: UUID,
    update_data: SupportTicketUpdate,
    db_session: AsyncSession = Depends(get_db_session),
    support_service: SupportService = Depends(get_support_service),
    current_user = Depends(require_admin)
):
    """Обновление статуса тикета (только для админов)"""
    try:
        updated_ticket = await support_service.update_ticket_status(
            ticket_id=ticket_id,
            new_status=update_data.status,
            updated_by_id=current_user.id,
            reason=update_data.reason
        )
        
        await db_session.commit()
        
        return SupportTicketResponse.from_orm(updated_ticket)
    
    except NotFoundError as e:
        await db_session.rollback()
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except (ValidationError, BusinessLogicError) as e:
        await db_session.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        await db_session.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка обновления статуса тикета: {e}"
        )


@router.put("/tickets/{ticket_id}/assign", response_model=SupportTicketResponse)
async def assign_ticket(
    ticket_id: UUID,
    assigned_to_id: UUID,
    db_session: AsyncSession = Depends(get_db_session),
    support_service: SupportService = Depends(get_support_service),
    current_user = Depends(require_admin)
):
    """Назначение тикета сотруднику (только для админов)"""
    try:
        updated_ticket = await support_service.assign_ticket(
            ticket_id=ticket_id,
            assigned_to_id=assigned_to_id,
            assigned_by_id=current_user.id
        )
        
        await db_session.commit()
        
        return SupportTicketResponse.from_orm(updated_ticket)
    
    except NotFoundError as e:
        await db_session.rollback()
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except (ValidationError, BusinessLogicError) as e:
        await db_session.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        await db_session.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка назначения тикета: {e}"
        )


@router.get("/tickets/overdue", response_model=SupportTicketListResponse)
async def get_overdue_tickets(
    support_service: SupportService = Depends(get_support_service),
    current_user = Depends(require_admin)
):
    """Получение просроченных тикетов (только для админов)"""
    try:
        tickets = await support_service.get_overdue_tickets()
        
        return SupportTicketListResponse(
            success=True,
            tickets=[SupportTicketResponse.from_orm(t) for t in tickets],
            total=len(tickets),
            message="Просроченные тикеты получены успешно"
        )
    
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка получения просроченных тикетов: {e}"
        )


@router.get("/stats", response_model=SupportStatsResponse)
async def get_support_statistics(
    start_date: Optional[datetime] = Query(None, description="Дата начала периода"),
    end_date: Optional[datetime] = Query(None, description="Дата окончания периода"),
    support_service: SupportService = Depends(get_support_service),
    current_user = Depends(require_admin)
):
    """Получение статистики поддержки (только для админов)"""
    try:
        stats = await support_service.get_support_statistics(
            start_date=start_date,
            end_date=end_date
        )
        
        return SupportStatsResponse(
            success=True,
            stats=stats,
            message="Статистика получена успешно"
        )
    
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка получения статистики: {e}"
        )


@router.get("/categories", response_model=Dict[str, str])
async def get_support_categories():
    """Получение списка категорий поддержки"""
    return SupportService.CATEGORIES


@router.get("/priorities", response_model=Dict[str, str])
async def get_support_priorities():
    """Получение списка приоритетов"""
    return SupportService.PRIORITIES


@router.get("/statuses", response_model=Dict[str, str])
async def get_support_statuses():
    """Получение списка статусов"""
    return SupportService.STATUSES
