"""
UnveilVPN Shop - Promocodes API Router
API endpoints для работы с промокодами
"""

from typing import List, Optional, Dict, Any
from uuid import UUID
from datetime import datetime

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession

from ..dependencies import get_db_session, get_current_user, require_admin
from ..models import (
    PromocodeCreate, PromocodeUpdate, PromocodeResponse, BaseResponse,
    PromocodeValidationRequest, PromocodeValidationResponse,
    PromocodeStatsResponse, PromocodeListResponse
)
from ...services.promocode_service import PromocodeService
from ...common.exceptions import NotFoundError, ValidationError, BusinessLogicError

router = APIRouter(prefix="/promocodes", tags=["promocodes"])


def get_promocode_service(db_session: AsyncSession = Depends(get_db_session)) -> PromocodeService:
    """Получение сервиса промокодов"""
    return PromocodeService(db_session)


@router.post("/", response_model=PromocodeResponse)
async def create_promocode(
    promocode_data: PromocodeCreate,
    db_session: AsyncSession = Depends(get_db_session),
    promocode_service: PromocodeService = Depends(get_promocode_service),
    current_user = Depends(require_admin)
):
    """Создание нового промокода (только для админов)"""
    try:
        promocode = await promocode_service.create_promocode(
            code=promocode_data.code,
            discount_type=promocode_data.discount_type,
            discount_value=promocode_data.discount_value,
            name=promocode_data.name,
            description=promocode_data.description,
            usage_limit=promocode_data.usage_limit,
            usage_limit_per_user=promocode_data.usage_limit_per_user,
            starts_at=promocode_data.starts_at,
            expires_at=promocode_data.expires_at,
            applicable_tariffs=promocode_data.applicable_tariffs,
            min_purchase_amount=promocode_data.min_purchase_amount,
            is_public=promocode_data.is_public,
            created_by_id=current_user.id
        )
        
        await db_session.commit()
        
        return PromocodeResponse.from_orm(promocode)
    
    except (ValidationError, BusinessLogicError) as e:
        await db_session.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        await db_session.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка создания промокода: {e}"
        )


@router.get("/", response_model=PromocodeListResponse)
async def get_promocodes(
    active_only: bool = Query(False, description="Только активные промокоды"),
    public_only: bool = Query(False, description="Только публичные промокоды"),
    limit: int = Query(100, ge=1, le=1000, description="Количество промокодов"),
    offset: int = Query(0, ge=0, description="Смещение"),
    promocode_service: PromocodeService = Depends(get_promocode_service),
    current_user = Depends(get_current_user)
):
    """Получение списка промокодов"""
    try:
        # Для обычных пользователей показываем только публичные
        if not current_user.is_admin:
            public_only = True
        
        promocodes = await promocode_service.get_all_promocodes(
            active_only=active_only,
            public_only=public_only,
            limit=limit,
            offset=offset
        )
        
        return PromocodeListResponse(
            success=True,
            promocodes=[PromocodeResponse.from_orm(p) for p in promocodes],
            total=len(promocodes),
            message="Промокоды получены успешно"
        )
    
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка получения промокодов: {e}"
        )


@router.get("/public", response_model=PromocodeListResponse)
async def get_public_promocodes(
    promocode_service: PromocodeService = Depends(get_promocode_service)
):
    """Получение публичных активных промокодов"""
    try:
        promocodes = await promocode_service.get_public_promocodes()
        
        return PromocodeListResponse(
            success=True,
            promocodes=[PromocodeResponse.from_orm(p) for p in promocodes],
            total=len(promocodes),
            message="Публичные промокоды получены успешно"
        )
    
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка получения публичных промокодов: {e}"
        )


@router.get("/{promocode_id}", response_model=PromocodeResponse)
async def get_promocode(
    promocode_id: UUID,
    promocode_service: PromocodeService = Depends(get_promocode_service),
    current_user = Depends(get_current_user)
):
    """Получение промокода по ID"""
    try:
        promocode = await promocode_service.get_promocode_by_id(promocode_id)
        
        if not promocode:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Промокод не найден"
            )
        
        # Проверяем права доступа
        if not current_user.is_admin and not promocode.is_public:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Недостаточно прав для просмотра промокода"
            )
        
        return PromocodeResponse.from_orm(promocode)
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка получения промокода: {e}"
        )


@router.post("/validate", response_model=PromocodeValidationResponse)
async def validate_promocode(
    validation_data: PromocodeValidationRequest,
    promocode_service: PromocodeService = Depends(get_promocode_service),
    current_user = Depends(get_current_user)
):
    """Валидация промокода"""
    try:
        is_valid, message, promocode = await promocode_service.validate_promocode(
            code=validation_data.code,
            user_id=current_user.id,
            tariff_id=validation_data.tariff_id,
            purchase_amount=validation_data.purchase_amount
        )
        
        response_data = {
            'valid': is_valid,
            'message': message
        }
        
        if is_valid and promocode:
            # Рассчитываем скидку для предварительного просмотра
            if validation_data.purchase_amount:
                discount_amount, final_amount = promocode.calculate_discount(
                    validation_data.purchase_amount
                )
                response_data.update({
                    'discount_amount': float(discount_amount),
                    'final_amount': float(final_amount),
                    'discount_type': promocode.discount_type,
                    'discount_value': float(promocode.discount_value)
                })
        
        return PromocodeValidationResponse(
            success=True,
            validation=response_data,
            message="Валидация выполнена успешно"
        )
    
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка валидации промокода: {e}"
        )


@router.put("/{promocode_id}", response_model=PromocodeResponse)
async def update_promocode(
    promocode_id: UUID,
    update_data: PromocodeUpdate,
    db_session: AsyncSession = Depends(get_db_session),
    promocode_service: PromocodeService = Depends(get_promocode_service),
    current_user = Depends(require_admin)
):
    """Обновление промокода (только для админов)"""
    try:
        # Подготавливаем данные для обновления
        update_dict = update_data.dict(exclude_unset=True)
        
        promocode = await promocode_service.update_promocode(
            promocode_id=promocode_id,
            **update_dict
        )
        
        await db_session.commit()
        
        return PromocodeResponse.from_orm(promocode)
    
    except NotFoundError as e:
        await db_session.rollback()
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except (ValidationError, BusinessLogicError) as e:
        await db_session.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        await db_session.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка обновления промокода: {e}"
        )


@router.delete("/{promocode_id}", response_model=BaseResponse)
async def delete_promocode(
    promocode_id: UUID,
    db_session: AsyncSession = Depends(get_db_session),
    promocode_service: PromocodeService = Depends(get_promocode_service),
    current_user = Depends(require_admin)
):
    """Удаление промокода (только для админов)"""
    try:
        success = await promocode_service.delete_promocode(promocode_id)
        
        if success:
            await db_session.commit()
            return BaseResponse(
                success=True,
                message="Промокод успешно удален"
            )
        else:
            return BaseResponse(
                success=False,
                message="Не удалось удалить промокод"
            )
    
    except NotFoundError as e:
        await db_session.rollback()
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        await db_session.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка удаления промокода: {e}"
        )


@router.get("/stats/overview", response_model=PromocodeStatsResponse)
async def get_promocode_statistics(
    promocode_id: Optional[UUID] = Query(None, description="ID конкретного промокода"),
    start_date: Optional[datetime] = Query(None, description="Дата начала периода"),
    end_date: Optional[datetime] = Query(None, description="Дата окончания периода"),
    promocode_service: PromocodeService = Depends(get_promocode_service),
    current_user = Depends(require_admin)
):
    """Получение статистики использования промокодов (только для админов)"""
    try:
        stats = await promocode_service.get_promocode_statistics(
            promocode_id=promocode_id,
            start_date=start_date,
            end_date=end_date
        )
        
        return PromocodeStatsResponse(
            success=True,
            stats=stats,
            message="Статистика получена успешно"
        )
    
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка получения статистики: {e}"
        )


@router.post("/generate", response_model=PromocodeResponse)
async def generate_promocode(
    prefix: str = Query("", description="Префикс для кода"),
    length: int = Query(8, ge=4, le=20, description="Длина кода"),
    discount_type: str = Query("percent", description="Тип скидки"),
    discount_value: float = Query(10, gt=0, description="Значение скидки"),
    expires_in_days: int = Query(30, gt=0, description="Срок действия в днях"),
    usage_limit: Optional[int] = Query(None, description="Лимит использований"),
    db_session: AsyncSession = Depends(get_db_session),
    promocode_service: PromocodeService = Depends(get_promocode_service),
    current_user = Depends(require_admin)
):
    """Генерация случайного промокода (только для админов)"""
    try:
        from decimal import Decimal
        
        promocode = await promocode_service.generate_promocode(
            prefix=prefix,
            length=length,
            discount_type=discount_type,
            discount_value=Decimal(str(discount_value)),
            expires_in_days=expires_in_days,
            usage_limit=usage_limit,
            created_by_id=current_user.id
        )
        
        await db_session.commit()
        
        return PromocodeResponse.from_orm(promocode)
    
    except (ValidationError, BusinessLogicError) as e:
        await db_session.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        await db_session.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка генерации промокода: {e}"
        )
