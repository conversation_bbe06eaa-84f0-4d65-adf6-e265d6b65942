"""
UnveilVPN Shop - Referrals API Router
API endpoints для работы с реферальной системой
"""

from typing import Dict, Any, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from ..dependencies import get_db_session, get_current_user, require_admin
from ..models import (
    ReferralCodeResponse, ReferralRegisterRequest, ReferralStatsResponse,
    ReferralTreeResponse, BaseResponse
)
from ...services.referral_service import ReferralService
from ...common.exceptions import NotFoundError, ValidationError, BusinessLogicError

router = APIRouter(prefix="/referrals", tags=["referrals"])


def get_referral_service(db_session: AsyncSession = Depends(get_db_session)) -> ReferralService:
    """Получение сервиса рефералов"""
    return ReferralService(db_session)


@router.post("/generate-code", response_model=ReferralCodeResponse)
async def generate_referral_code(
    db_session: AsyncSession = Depends(get_db_session),
    referral_service: ReferralService = Depends(get_referral_service),
    current_user = Depends(get_current_user)
):
    """Генерация реферального кода для пользователя"""
    try:
        code = await referral_service.generate_referral_code(current_user.id)
        await db_session.commit()
        
        return ReferralCodeResponse(
            success=True,
            referral_code=code,
            message="Реферальный код сгенерирован успешно"
        )
    
    except (NotFoundError, ValidationError, BusinessLogicError) as e:
        await db_session.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        await db_session.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка генерации реферального кода: {e}"
        )


@router.post("/register", response_model=BaseResponse)
async def register_referral(
    request: ReferralRegisterRequest,
    db_session: AsyncSession = Depends(get_db_session),
    referral_service: ReferralService = Depends(get_referral_service),
    current_user = Depends(get_current_user)
):
    """Регистрация пользователя как реферала"""
    try:
        success = await referral_service.register_referral(
            referral_code=request.referral_code,
            new_user_id=current_user.id
        )
        
        if success:
            await db_session.commit()
            return BaseResponse(
                success=True,
                message="Реферальная связь установлена успешно"
            )
        else:
            return BaseResponse(
                success=False,
                message="Не удалось установить реферальную связь"
            )
    
    except (NotFoundError, ValidationError, BusinessLogicError) as e:
        await db_session.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        await db_session.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка регистрации реферала: {e}"
        )


@router.get("/stats", response_model=ReferralStatsResponse)
async def get_referral_stats(
    referral_service: ReferralService = Depends(get_referral_service),
    current_user = Depends(get_current_user)
):
    """Получение статистики рефералов пользователя"""
    try:
        stats = await referral_service.get_user_referral_stats(current_user.id)
        
        return ReferralStatsResponse(
            success=True,
            stats=stats,
            message="Статистика получена успешно"
        )
    
    except (NotFoundError, ValidationError, BusinessLogicError) as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка получения статистики: {e}"
        )


@router.get("/stats/{user_id}", response_model=ReferralStatsResponse)
async def get_user_referral_stats(
    user_id: UUID,
    referral_service: ReferralService = Depends(get_referral_service),
    current_user = Depends(require_admin)
):
    """Получение статистики рефералов пользователя (только для админов)"""
    try:
        stats = await referral_service.get_user_referral_stats(user_id)
        
        return ReferralStatsResponse(
            success=True,
            stats=stats,
            message="Статистика получена успешно"
        )
    
    except (NotFoundError, ValidationError, BusinessLogicError) as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка получения статистики: {e}"
        )


@router.get("/tree", response_model=ReferralTreeResponse)
async def get_referral_tree(
    max_depth: int = 3,
    referral_service: ReferralService = Depends(get_referral_service),
    current_user = Depends(get_current_user)
):
    """Получение дерева рефералов пользователя"""
    try:
        if max_depth < 1 or max_depth > 5:
            raise ValidationError("Глубина дерева должна быть от 1 до 5")
        
        tree = await referral_service.get_referral_tree(current_user.id, max_depth)
        
        return ReferralTreeResponse(
            success=True,
            tree=tree,
            message="Дерево рефералов получено успешно"
        )
    
    except (NotFoundError, ValidationError, BusinessLogicError) as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка получения дерева рефералов: {e}"
        )


@router.get("/tree/{user_id}", response_model=ReferralTreeResponse)
async def get_user_referral_tree(
    user_id: UUID,
    max_depth: int = 3,
    referral_service: ReferralService = Depends(get_referral_service),
    current_user = Depends(require_admin)
):
    """Получение дерева рефералов пользователя (только для админов)"""
    try:
        if max_depth < 1 or max_depth > 5:
            raise ValidationError("Глубина дерева должна быть от 1 до 5")
        
        tree = await referral_service.get_referral_tree(user_id, max_depth)
        
        return ReferralTreeResponse(
            success=True,
            tree=tree,
            message="Дерево рефералов получено успешно"
        )
    
    except (NotFoundError, ValidationError, BusinessLogicError) as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка получения дерева рефералов: {e}"
        )


@router.post("/process-payment-bonus/{payment_id}", response_model=BaseResponse)
async def process_payment_bonus(
    payment_id: UUID,
    db_session: AsyncSession = Depends(get_db_session),
    referral_service: ReferralService = Depends(get_referral_service),
    current_user = Depends(require_admin)
):
    """Обработка бонусов за платеж (только для админов)"""
    try:
        result = await referral_service.process_payment_bonus(payment_id)
        
        if result['processed']:
            await db_session.commit()
            return BaseResponse(
                success=True,
                message=f"Обработано {len(result['bonuses'])} бонусов на сумму {result['total_bonus']}"
            )
        else:
            return BaseResponse(
                success=False,
                message=result.get('reason', 'Бонусы не были обработаны')
            )
    
    except (NotFoundError, ValidationError, BusinessLogicError) as e:
        await db_session.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        await db_session.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка обработки бонусов: {e}"
        )


@router.get("/rates", response_model=Dict[str, float])
async def get_referral_rates():
    """Получение процентных ставок реферальной системы"""
    return {
        "level_1": float(ReferralService.REFERRAL_RATES[1]),
        "level_2": float(ReferralService.REFERRAL_RATES[2]),
        "level_3": float(ReferralService.REFERRAL_RATES[3])
    }


@router.get("/validate-code/{code}", response_model=BaseResponse)
async def validate_referral_code(
    code: str,
    referral_service: ReferralService = Depends(get_referral_service)
):
    """Валидация реферального кода"""
    try:
        # Проверяем существование кода
        user = await referral_service._get_user_by_referral_code(code)
        
        if user:
            return BaseResponse(
                success=True,
                message="Реферальный код действителен"
            )
        else:
            return BaseResponse(
                success=False,
                message="Реферальный код не найден"
            )
    
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка валидации кода: {e}"
        )
