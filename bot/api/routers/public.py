"""
UnveilVPN Shop - Public API Router
Публичный роутер для API
"""

from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession

from ...services.tariff_service import TariffService
from ...db.session import get_async_session

router = APIRouter(redirect_slashes=False)


@router.get("/tariffs")
async def get_public_tariffs(db: AsyncSession = Depends(get_async_session)):
    """Получение списка активных тарифов для лендинга"""
    tariff_service = TariffService(db)
    tariffs = await tariff_service.get_active_tariffs(include_test=False)
    return [t.to_dict() for t in tariffs]


@router.get("/trial/info")
async def get_trial_info():
    """Получение информации о пробном периоде"""
    return {
        "success": True,
        "data": {
            "duration_days": 7,
            "description": "Попробуйте UnveilVPN бесплатно в течение 7 дней",
            "terms": [
                "Полный доступ ко всем серверам",
                "Безлимитный трафик",
                "Поддержка всех устройств",
                "Отмена в любое время"
            ],
            "enabled": True
        }
    }


@router.get("/faq")
async def get_faq():
    """Получение списка часто задаваемых вопросов"""
    return {
        "success": True,
        "data": {
            "success": True,
            "data": [
                {
                    "id": "1",
                    "question": "Что такое VPN и зачем он нужен?",
                    "answer": "VPN (Virtual Private Network) создает зашифрованное соединение между вашим устройством и интернетом, защищая вашу онлайн-активность от перехвата и обеспечивая анонимность. Он нужен для защиты конфиденциальности, обхода географических ограничений и безопасного использования публичного Wi-Fi сетей.",
                    "category": "general"
                },
                {
                    "id": "2",
                    "question": "Как работает 7-дневный пробный период?",
                    "answer": "Вы можете активировать пробный период через наш Telegram бот. Вам будет предоставлен полный доступ ко всем функциям UnveilVPN на 7 дней без необходимости привязки банковской карты. По истечении периода доступ автоматически прекратится, если вы не оформите подписку.",
                    "category": "trial"
                },
                {
                    "id": "3",
                    "question": "На скольких устройствах можно использовать VPN?",
                    "answer": "Вы можете использовать UnveilVPN одновременно на 10 устройствах. Мы поддерживаем Windows, macOS, iOS, Android и Linux.",
                    "category": "devices"
                },
                {
                    "id": "4",
                    "question": "Какие способы оплаты доступны?",
                    "answer": "Мы принимаем оплату через YooKassa (банковские карты, ЮMoney, SberPay) и Cryptomus (криптовалюты). Также доступна оплата через Telegram Stars.",
                    "category": "payment"
                },
                {
                    "id": "5",
                    "question": "Можно ли вернуть деньги?",
                    "answer": "Да, мы предоставляем 30-дневную гарантию возврата средств. Если вы не удовлетворены нашим сервисом, вы можете запросить полный возврат в течение 30 дней с момента покупки.",
                    "category": "refund"
                }
            ]
        }
    }
