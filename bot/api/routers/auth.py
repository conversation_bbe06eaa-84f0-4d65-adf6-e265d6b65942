"""
UnveilVPN Shop - Auth Router
API роутер для аутентификации
"""

from fastapi import APIRouter

router = APIRouter()


@router.post("/login")
async def login():
    """Вход в систему"""
    # Заглушка для аутентификации
    return {"message": "Auth endpoint - not implemented yet"}


@router.post("/logout")
async def logout():
    """Выход из системы"""
    # Заглушка для выхода
    return {"message": "Logout endpoint - not implemented yet"}


@router.get("/me")
async def get_current_user_info():
    """Получение информации о текущем пользователе"""
    # Заглушка для получения информации о пользователе
    return {"message": "User info endpoint - not implemented yet"}
