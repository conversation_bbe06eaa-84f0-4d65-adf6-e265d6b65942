"""
UnveilVPN Shop - Payments API Router
API endpoints для работы с платежами
"""

from typing import Dict, Any, Optional, List
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, status, Request
from fastapi.responses import J<PERSON><PERSON>esponse
from sqlalchemy.ext.asyncio import AsyncSession

from ..dependencies import get_db_session, get_current_user, get_admin_user
from ..models import (
    PaymentCreate, PaymentResponse, PaymentListResponse,
    WebhookResponse, RefundRequest, PaymentStatsResponse
)
from ...services.payment_manager import PaymentManager
from ...common.exceptions import NotFoundError, ValidationError, PaymentError
from ...common.config import get_config

router = APIRouter(prefix="/payments", tags=["payments"])


def get_payment_manager(db_session: AsyncSession = Depends(get_db_session)) -> PaymentManager:
    """Получение менеджера платежей"""
    config = get_config()
    payment_config = {
        'yookassa': {
            'shop_id': config.yookassa_shop_id,
            'secret_key': config.yookassa_secret_key,
            'receipt_email': config.receipt_email
        },
        'cryptomus': {
            'merchant_id': config.cryptomus_merchant_id,
            'api_key': config.cryptomus_api_key
        },
        'telegram_stars': {
            'bot_token': config.bot_token,
            'provider_token': config.telegram_stars_provider_token
        }
    }
    return PaymentManager(payment_config, db_session)


@router.get("/methods", response_model=List[str])
async def get_available_payment_methods(
    payment_manager: PaymentManager = Depends(get_payment_manager)
):
    """Получение списка доступных методов оплаты"""
    return payment_manager.get_available_methods()


@router.post("/create", response_model=PaymentResponse)
async def create_payment(
    payment_data: PaymentCreate,
    request: Request,
    db_session: AsyncSession = Depends(get_db_session),
    payment_manager: PaymentManager = Depends(get_payment_manager)
):
    """Создание нового платежа"""
    try:
        # Формируем URLs
        base_url = str(request.base_url).rstrip('/')
        return_url = f"{base_url}/payment/success"
        webhook_url = f"{base_url}/webhooks/payments/{payment_data.payment_method}"

        # Создаем платеж
        result = await payment_manager.create_payment(
            user_id=payment_data.user_id,
            tariff_id=payment_data.tariff_id,
            payment_method=payment_data.payment_method,
            promocode_id=payment_data.promocode_id,
            metadata={
                'ip_address': request.client.host,
                'user_agent': request.headers.get('user-agent'),
                'created_via': 'api'
            },
            return_url=return_url,
            webhook_url=webhook_url
        )

        await db_session.commit()

        return PaymentResponse(
            success=True,
            payment_id=result['payment_id'],
            payment_url=result.get('payment_url'),
            external_payment_id=result.get('external_payment_id'),
            amount=result['amount'],
            currency=result['currency'],
            method=result['method'],
            message="Платеж создан успешно"
        )

    except (ValidationError, PaymentError) as e:
        await db_session.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        await db_session.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка создания платежа: {e}"
        )


@router.get("/{payment_id}", response_model=PaymentResponse)
async def get_payment_status(
    payment_id: UUID,
    payment_manager: PaymentManager = Depends(get_payment_manager)
):
    """Получение статуса платежа"""
    try:
        result = await payment_manager.check_payment_status(payment_id)

        return PaymentResponse(
            success=True,
            payment_id=result['payment_id'],
            status=result['status'],
            amount=result['amount'],
            currency=result['currency'],
            message="Статус получен успешно"
        )

    except NotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка получения статуса: {e}"
        )


@router.get("/user/{user_id}", response_model=PaymentListResponse)
async def get_user_payments(
    user_id: UUID,
    status_filter: Optional[str] = None,
    limit: int = 50,
    offset: int = 0,
    db_session: AsyncSession = Depends(get_db_session),
    current_user = Depends(get_current_user)
):
    """Получение платежей пользователя"""
    try:
        # Проверяем права доступа
        if str(current_user.id) != str(user_id) and not current_user.is_admin:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Недостаточно прав для просмотра платежей"
            )

        from ...services.payment_service import PaymentService
        payment_service = PaymentService(db_session)

        payments = await payment_service.get_user_payments(
            user_id=user_id,
            status=status_filter,
            limit=limit,
            offset=offset
        )

        payments_data = []
        for payment in payments:
            payments_data.append({
                'payment_id': str(payment.id),
                'tariff_name': payment.tariff.name,
                'amount': float(payment.final_amount),
                'currency': payment.currency,
                'status': payment.status,
                'method': payment.payment_method,
                'created_at': payment.created_at.isoformat(),
                'paid_at': payment.paid_at.isoformat() if payment.paid_at else None
            })

        return PaymentListResponse(
            success=True,
            payments=payments_data,
            total=len(payments_data),
            message="Платежи получены успешно"
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка получения платежей: {e}"
        )


# Webhook endpoints

@router.post("/webhooks/yookassa", response_model=WebhookResponse)
async def yookassa_webhook(
    request: Request,
    db_session: AsyncSession = Depends(get_db_session),
    payment_manager: PaymentManager = Depends(get_payment_manager)
):
    """Webhook для YooKassa"""
    try:
        webhook_data = await request.json()

        result = await payment_manager.process_webhook('yookassa', webhook_data)

        if result['success']:
            await db_session.commit()

        return WebhookResponse(
            success=result['success'],
            message=result['message']
        )

    except Exception as e:
        await db_session.rollback()
        return WebhookResponse(
            success=False,
            message=f"Ошибка обработки webhook: {e}"
        )


@router.post("/webhooks/cryptomus", response_model=WebhookResponse)
async def cryptomus_webhook(
    request: Request,
    db_session: AsyncSession = Depends(get_db_session),
    payment_manager: PaymentManager = Depends(get_payment_manager)
):
    """Webhook для Cryptomus"""
    try:
        webhook_data = await request.json()

        result = await payment_manager.process_webhook('cryptomus', webhook_data)

        if result['success']:
            await db_session.commit()

        return WebhookResponse(
            success=result['success'],
            message=result['message']
        )

    except Exception as e:
        await db_session.rollback()
        return WebhookResponse(
            success=False,
            message=f"Ошибка обработки webhook: {e}"
        )


@router.post("/webhooks/telegram_stars", response_model=WebhookResponse)
async def telegram_stars_webhook(
    request: Request,
    db_session: AsyncSession = Depends(get_db_session),
    payment_manager: PaymentManager = Depends(get_payment_manager)
):
    """Webhook для Telegram Stars"""
    try:
        webhook_data = await request.json()

        result = await payment_manager.process_webhook('telegram_stars', webhook_data)

        if result['success']:
            await db_session.commit()

        return WebhookResponse(
            success=result['success'],
            message=result['message']
        )

    except Exception as e:
        await db_session.rollback()
        return WebhookResponse(
            success=False,
            message=f"Ошибка обработки webhook: {e}"
        )
