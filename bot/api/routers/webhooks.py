"""
UnveilVPN Shop - Webhooks Router
API роутер для обработки webhooks
"""

from fastapi import APIRouter

router = APIRouter()


@router.post("/yookassa")
async def yookassa_webhook():
    """Webhook для YooKassa"""
    # Заглушка
    return {"message": "YooKassa webhook - not implemented yet"}


@router.post("/cryptomus")
async def cryptomus_webhook():
    """Webhook для Cryptomus"""
    # Заглушка
    return {"message": "Cryptomus webhook - not implemented yet"}


@router.post("/telegram_stars")
async def telegram_stars_webhook():
    """Webhook для Telegram Stars"""
    # Заглушка
    return {"message": "Telegram Stars webhook - not implemented yet"}
