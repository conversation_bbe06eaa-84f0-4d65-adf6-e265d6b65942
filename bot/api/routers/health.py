"""
UnveilVPN Shop - Health Check Router
API роутер для проверки состояния системы
"""

from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text

from ..dependencies import get_db_session
from ..models import BaseResponse

router = APIRouter()


@router.get("/", response_model=BaseResponse)
async def health_check():
    """Базовая проверка состояния API"""
    return BaseResponse(
        success=True,
        message="API работает нормально"
    )


@router.get("/db", response_model=BaseResponse)
async def database_health_check(db: AsyncSession = Depends(get_db_session)):
    """Проверка состояния базы данных"""
    try:
        # Простой запрос к БД
        result = await db.execute(text("SELECT 1"))
        result.scalar()
        
        return BaseResponse(
            success=True,
            message="База данных доступна"
        )
        
    except Exception as e:
        return BaseResponse(
            success=False,
            message=f"Ошибка подключения к базе данных: {str(e)}"
        )


@router.get("/detailed")
async def detailed_health_check(db: AsyncSession = Depends(get_db_session)):
    """Детальная проверка состояния системы"""
    try:
        # Проверка БД
        db_status = "ok"
        try:
            result = await db.execute(text("SELECT 1"))
            result.scalar()
        except Exception as e:
            db_status = f"error: {str(e)}"
        
        # Здесь можно добавить проверки других сервисов
        # Redis, внешние API и т.д.
        
        return {
            "success": True,
            "services": {
                "api": "ok",
                "database": db_status,
                "redis": "not_configured",  # Заглушка
                "external_services": "not_checked"  # Заглушка
            },
            "timestamp": "2024-12-24T12:00:00Z"  # Здесь будет реальное время
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "timestamp": "2024-12-24T12:00:00Z"
        }
