"""
UnveilVPN Shop - API Configuration
Конфигурация для FastAPI сервера
"""

import os
from dataclasses import dataclass
from typing import List, Optional


@dataclass
class APIConfig:
    """
    Конфигурация API сервера
    """
    
    # Основные настройки
    host: str = "0.0.0.0"
    port: int = 8000
    debug: bool = False
    
    # База данных
    db_host: str = "localhost"
    db_port: int = 5432
    db_name: str = "unveilvpn"
    db_user: str = "unveilvpn_user"
    db_pass: str = ""
    
    # Redis (для кэширования и rate limiting)
    redis_url: Optional[str] = None
    
    # Безопасность
    secret_key: str = "your-secret-key-change-in-production"
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
    
    # CORS
    allowed_origins: List[str] = None
    allowed_hosts: List[str] = None
    
    # Rate Limiting
    rate_limit_enabled: bool = True
    rate_limit_calls: int = 100
    rate_limit_period: int = 60  # секунды
    
    # Логирование
    log_level: str = "INFO"
    
    # Внешние сервисы - Remnawave VPN Panel
    remnawave_panel_url: Optional[str] = None
    remnawave_api_key: Optional[str] = None
    remnawave_subscription_url: Optional[str] = None


    
    # Платежные системы
    yookassa_shop_id: Optional[str] = None
    yookassa_secret_key: Optional[str] = None
    cryptomus_merchant_id: Optional[str] = None
    cryptomus_api_key: Optional[str] = None
    
    # Webhook настройки
    webhook_secret: Optional[str] = None
    
    def __post_init__(self):
        """Инициализация после создания объекта"""
        if self.allowed_origins is None:
            self.allowed_origins = ["*"] if self.debug else []
        if self.allowed_hosts is None:
            self.allowed_hosts = ["*"] if self.debug else []
    
    @property
    def database_url(self) -> str:
        """URL для подключения к базе данных"""
        return f"postgresql+asyncpg://{self.db_user}:{self.db_pass}@{self.db_host}:{self.db_port}/{self.db_name}"
    
    @classmethod
    def from_env(cls) -> 'APIConfig':
        """Создание конфигурации из переменных окружения"""
        
        # Парсим списки
        allowed_origins = []
        origins_str = os.getenv('API_ALLOWED_ORIGINS', '')
        if origins_str:
            allowed_origins = [origin.strip() for origin in origins_str.split(',') if origin.strip()]
        
        allowed_hosts = []
        hosts_str = os.getenv('API_ALLOWED_HOSTS', '')
        if hosts_str:
            allowed_hosts = [host.strip() for host in hosts_str.split(',') if host.strip()]
        
        return cls(
            # Основные настройки
            host=os.getenv('API_HOST', '0.0.0.0'),
            port=int(os.getenv('API_PORT', 8000)),
            debug=os.getenv('API_DEBUG', 'false').lower() == 'true',
            
            # База данных
            db_host=os.getenv('DB_HOST', 'localhost'),
            db_port=int(os.getenv('DB_PORT', 5432)),
            db_name=os.getenv('DB_NAME', 'unveilvpn'),
            db_user=os.getenv('DB_USER', 'unveilvpn_user'),
            db_pass=os.getenv('DB_PASS', ''),
            
            # Redis
            redis_url=os.getenv('REDIS_URL'),
            
            # Безопасность
            secret_key=os.getenv('API_SECRET_KEY', 'your-secret-key-change-in-production'),
            algorithm=os.getenv('API_ALGORITHM', 'HS256'),
            access_token_expire_minutes=int(os.getenv('API_ACCESS_TOKEN_EXPIRE_MINUTES', 30)),
            
            # CORS
            allowed_origins=allowed_origins,
            allowed_hosts=allowed_hosts,
            
            # Rate Limiting
            rate_limit_enabled=os.getenv('API_RATE_LIMIT_ENABLED', 'true').lower() == 'true',
            rate_limit_calls=int(os.getenv('API_RATE_LIMIT_CALLS', 100)),
            rate_limit_period=int(os.getenv('API_RATE_LIMIT_PERIOD', 60)),
            
            # Логирование
            log_level=os.getenv('API_LOG_LEVEL', 'INFO'),
            
            # Внешние сервисы - Remnawave VPN Panel
            remnawave_panel_url=os.getenv('REMNAWAVE_PANEL_URL'),
            remnawave_api_key=os.getenv('REMNAWAVE_API_KEY'),
            remnawave_subscription_url=os.getenv('REMNAWAVE_SUBSCRIPTION_URL'),


            
            # Платежные системы
            yookassa_shop_id=os.getenv('YOOKASSA_SHOP_ID'),
            yookassa_secret_key=os.getenv('YOOKASSA_SECRET_KEY'),
            cryptomus_merchant_id=os.getenv('CRYPTOMUS_MERCHANT_ID'),
            cryptomus_api_key=os.getenv('CRYPTOMUS_API_KEY'),
            
            # Webhook
            webhook_secret=os.getenv('API_WEBHOOK_SECRET')
        )
    
    def validate(self) -> bool:
        """Валидация конфигурации"""
        errors = []
        
        if not self.secret_key or self.secret_key == 'your-secret-key-change-in-production':
            if not self.debug:
                errors.append("Необходимо установить безопасный API_SECRET_KEY для продакшена")
        
        if not self.db_host:
            errors.append("Отсутствует хост базы данных")
        
        if not self.db_name:
            errors.append("Отсутствует имя базы данных")
        
        if not self.db_user:
            errors.append("Отсутствует пользователь базы данных")
        
        if self.port < 1 or self.port > 65535:
            errors.append(f"Неверный порт: {self.port}")
        
        if errors:
            raise ValueError(f"Ошибки конфигурации API: {'; '.join(errors)}")
        
        return True
    
    def to_dict(self) -> dict:
        """Преобразование в словарь (без секретных данных)"""
        return {
            'host': self.host,
            'port': self.port,
            'debug': self.debug,
            'db_host': self.db_host,
            'db_port': self.db_port,
            'db_name': self.db_name,
            'db_user': self.db_user,
            'redis_url': bool(self.redis_url),
            'secret_key': self.secret_key[:10] + '...' if self.secret_key else None,
            'algorithm': self.algorithm,
            'access_token_expire_minutes': self.access_token_expire_minutes,
            'allowed_origins_count': len(self.allowed_origins),
            'allowed_hosts_count': len(self.allowed_hosts),
            'rate_limit_enabled': self.rate_limit_enabled,
            'rate_limit_calls': self.rate_limit_calls,
            'rate_limit_period': self.rate_limit_period,
            'log_level': self.log_level,
            'remnawave_panel_url': self.remnawave_panel_url,
            'yookassa_configured': bool(self.yookassa_shop_id and self.yookassa_secret_key),
            'cryptomus_configured': bool(self.cryptomus_merchant_id and self.cryptomus_api_key),
            'webhook_secret_configured': bool(self.webhook_secret)
        }


# Глобальная конфигурация
config: Optional[APIConfig] = None


def get_config() -> APIConfig:
    """Получение глобальной конфигурации"""
    global config
    if config is None:
        config = APIConfig.from_env()
        config.validate()
    return config
