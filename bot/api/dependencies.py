"""
UnveilVPN Shop - API Dependencies
Зависимости для FastAPI endpoints
"""

import logging
from typing import AsyncGenerator, Optional

from fastapi import Depends, HTTPException, status
from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker

from .config import get_config
from .exceptions import AuthenticationError
from ..db.models import VPNUsers

# Настройка базы данных
config = get_config()
engine = create_async_engine(
    config.database_url,
    echo=config.debug,
    pool_pre_ping=True,
    pool_recycle=3600
)

AsyncSessionLocal = sessionmaker(
    engine,
    class_=AsyncSession,
    expire_on_commit=False
)

# Настройка аутентификации
security = HTTPBearer(auto_error=False)
logger = logging.getLogger(__name__)


async def get_db_session() -> AsyncGenerator[AsyncSession, None]:
    """Получение сессии базы данных"""
    async with AsyncSessionLocal() as session:
        try:
            yield session
        except Exception as e:
            logger.error(f"Database session error: {e}")
            await session.rollback()
            raise
        finally:
            await session.close()


async def get_current_user(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security),
    db: AsyncSession = Depends(get_db_session)
) -> Optional[VPNUsers]:
    """Получение текущего пользователя (опционально)"""
    if not credentials:
        return None
    
    try:
        # Здесь будет логика проверки токена и получения пользователя
        # Пока возвращаем заглушку
        
        # В реальной реализации:
        # 1. Декодируем JWT токен
        # 2. Извлекаем user_id
        # 3. Получаем пользователя из БД
        # 4. Проверяем права доступа
        
        return None  # Заглушка
        
    except Exception as e:
        logger.error(f"Authentication error: {e}")
        raise AuthenticationError("Неверный токен аутентификации")


async def get_current_user_required(
    current_user: Optional[VPNUsers] = Depends(get_current_user)
) -> VPNUsers:
    """Получение текущего пользователя (обязательно)"""
    if not current_user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Требуется аутентификация",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    return current_user


async def get_admin_user(
    current_user: VPNUsers = Depends(get_current_user_required)
) -> VPNUsers:
    """Получение пользователя с правами администратора"""
    if not getattr(current_user, 'is_admin', False):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Недостаточно прав доступа"
        )
    
    return current_user


async def get_support_user(
    current_user: VPNUsers = Depends(get_current_user_required)
) -> VPNUsers:
    """Получение пользователя с правами поддержки"""
    if not (getattr(current_user, 'is_admin', False) or 
            getattr(current_user, 'is_support', False)):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Недостаточно прав доступа"
        )
    
    return current_user


# Дополнительные зависимости для валидации

async def validate_pagination(
    page: int = 1,
    per_page: int = 20
) -> dict:
    """Валидация параметров пагинации"""
    if page < 1:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Номер страницы должен быть больше 0"
        )
    
    if per_page < 1 or per_page > 100:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Размер страницы должен быть от 1 до 100"
        )
    
    return {
        "page": page,
        "per_page": per_page,
        "offset": (page - 1) * per_page
    }


async def validate_currency(currency: str = "rub") -> str:
    """Валидация валюты"""
    allowed_currencies = ["rub", "usd", "stars"]
    
    if currency.lower() not in allowed_currencies:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Неподдерживаемая валюта. Доступные: {', '.join(allowed_currencies)}"
        )
    
    return currency.lower()


# Кэширование (заглушка для будущей реализации)

class CacheManager:
    """Менеджер кэширования"""
    
    def __init__(self):
        self.cache = {}  # В реальной реализации будет Redis
    
    async def get(self, key: str):
        """Получение значения из кэша"""
        return self.cache.get(key)
    
    async def set(self, key: str, value, expire: int = 300):
        """Установка значения в кэш"""
        self.cache[key] = value
        # В реальной реализации будет установка TTL
    
    async def delete(self, key: str):
        """Удаление значения из кэша"""
        self.cache.pop(key, None)
    
    async def clear(self):
        """Очистка кэша"""
        self.cache.clear()


# Глобальный экземпляр кэша
cache_manager = CacheManager()


async def get_cache_manager() -> CacheManager:
    """Получение менеджера кэширования"""
    return cache_manager
