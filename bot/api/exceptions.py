"""
UnveilVPN Shop - API Exceptions
Исключения для FastAPI приложения
"""

from typing import Optional, Dict, Any
from fastapi import HTTPException, status


class APIException(HTTPException):
    """Базовое исключение для API"""
    
    def __init__(
        self,
        status_code: int,
        message: str,
        error_code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        super().__init__(status_code=status_code, detail=message)


class ValidationError(APIException):
    """Ошибка валидации"""
    
    def __init__(
        self,
        message: str = "Ошибка валидации данных",
        field: Optional[str] = None,
        value: Optional[Any] = None
    ):
        details = {}
        if field:
            details['field'] = field
        if value is not None:
            details['value'] = str(value)
        
        super().__init__(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            message=message,
            error_code="VALIDATION_ERROR",
            details=details
        )


class NotFoundError(APIException):
    """Ошибка "не найдено" """
    
    def __init__(
        self,
        message: str = "Ресурс не найден",
        resource_type: Optional[str] = None,
        resource_id: Optional[str] = None
    ):
        details = {}
        if resource_type:
            details['resource_type'] = resource_type
        if resource_id:
            details['resource_id'] = resource_id
        
        super().__init__(
            status_code=status.HTTP_404_NOT_FOUND,
            message=message,
            error_code="NOT_FOUND",
            details=details
        )


class AuthenticationError(APIException):
    """Ошибка аутентификации"""
    
    def __init__(self, message: str = "Требуется аутентификация"):
        super().__init__(
            status_code=status.HTTP_401_UNAUTHORIZED,
            message=message,
            error_code="AUTHENTICATION_ERROR"
        )


class AuthorizationError(APIException):
    """Ошибка авторизации"""
    
    def __init__(self, message: str = "Недостаточно прав доступа"):
        super().__init__(
            status_code=status.HTTP_403_FORBIDDEN,
            message=message,
            error_code="AUTHORIZATION_ERROR"
        )


class ConflictError(APIException):
    """Ошибка конфликта"""
    
    def __init__(
        self,
        message: str = "Конфликт данных",
        conflict_field: Optional[str] = None
    ):
        details = {}
        if conflict_field:
            details['conflict_field'] = conflict_field
        
        super().__init__(
            status_code=status.HTTP_409_CONFLICT,
            message=message,
            error_code="CONFLICT_ERROR",
            details=details
        )


class RateLimitError(APIException):
    """Ошибка превышения лимита запросов"""
    
    def __init__(
        self,
        message: str = "Превышен лимит запросов",
        retry_after: Optional[int] = None
    ):
        details = {}
        if retry_after:
            details['retry_after'] = retry_after
        
        super().__init__(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            message=message,
            error_code="RATE_LIMIT_ERROR",
            details=details
        )


class ExternalServiceError(APIException):
    """Ошибка внешнего сервиса"""
    
    def __init__(
        self,
        message: str = "Ошибка внешнего сервиса",
        service_name: Optional[str] = None,
        service_status_code: Optional[int] = None
    ):
        details = {}
        if service_name:
            details['service_name'] = service_name
        if service_status_code:
            details['service_status_code'] = service_status_code
        
        super().__init__(
            status_code=status.HTTP_502_BAD_GATEWAY,
            message=message,
            error_code="EXTERNAL_SERVICE_ERROR",
            details=details
        )


class PaymentError(APIException):
    """Ошибка платежа"""
    
    def __init__(
        self,
        message: str = "Ошибка обработки платежа",
        payment_method: Optional[str] = None,
        payment_id: Optional[str] = None
    ):
        details = {}
        if payment_method:
            details['payment_method'] = payment_method
        if payment_id:
            details['payment_id'] = payment_id
        
        super().__init__(
            status_code=status.HTTP_402_PAYMENT_REQUIRED,
            message=message,
            error_code="PAYMENT_ERROR",
            details=details
        )


class DatabaseError(APIException):
    """Ошибка базы данных"""
    
    def __init__(
        self,
        message: str = "Ошибка базы данных",
        operation: Optional[str] = None
    ):
        details = {}
        if operation:
            details['operation'] = operation
        
        super().__init__(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            message=message,
            error_code="DATABASE_ERROR",
            details=details
        )


class BusinessLogicError(APIException):
    """Ошибка бизнес-логики"""
    
    def __init__(
        self,
        message: str,
        operation: Optional[str] = None
    ):
        details = {}
        if operation:
            details['operation'] = operation
        
        super().__init__(
            status_code=status.HTTP_400_BAD_REQUEST,
            message=message,
            error_code="BUSINESS_LOGIC_ERROR",
            details=details
        )


# Функции для быстрого создания исключений

def not_found(resource_type: str, resource_id: str = None) -> NotFoundError:
    """Создание ошибки "не найдено" """
    message = f"{resource_type} не найден"
    if resource_id:
        message += f" (ID: {resource_id})"
    return NotFoundError(message, resource_type, resource_id)


def validation_error(message: str, field: str = None, value: Any = None) -> ValidationError:
    """Создание ошибки валидации"""
    return ValidationError(message, field, value)


def auth_error(message: str = "Требуется аутентификация") -> AuthenticationError:
    """Создание ошибки аутентификации"""
    return AuthenticationError(message)


def forbidden(message: str = "Недостаточно прав доступа") -> AuthorizationError:
    """Создание ошибки авторизации"""
    return AuthorizationError(message)


def conflict(message: str, field: str = None) -> ConflictError:
    """Создание ошибки конфликта"""
    return ConflictError(message, field)


def rate_limit(retry_after: int = None) -> RateLimitError:
    """Создание ошибки превышения лимита"""
    return RateLimitError(retry_after=retry_after)


def external_service_error(service: str, status_code: int = None) -> ExternalServiceError:
    """Создание ошибки внешнего сервиса"""
    message = f"Ошибка сервиса {service}"
    return ExternalServiceError(message, service, status_code)


def payment_error(message: str, method: str = None, payment_id: str = None) -> PaymentError:
    """Создание ошибки платежа"""
    return PaymentError(message, method, payment_id)


def business_error(message: str, operation: str = None) -> BusinessLogicError:
    """Создание ошибки бизнес-логики"""
    return BusinessLogicError(message, operation)
