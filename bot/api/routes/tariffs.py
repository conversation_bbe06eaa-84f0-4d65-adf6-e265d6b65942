"""
UnveilVPN Shop - Tariffs API Routes
"""

from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query, Path
from sqlalchemy.orm import Session
from pydantic import BaseModel

from bot.db.session import get_session
from bot.db.models import Tariffs
from bot.services.tariff_service import TariffService
from bot.common.exceptions import NotFoundError

router = APIRouter()


class TariffResponse(BaseModel):
    """Схема ответа для тарифа"""
    id: str
    name: str
    description: str
    duration_days: int
    prices: dict
    features: dict
    is_active: bool
    sort_order: int
    discount_percent: float
    
    class Config:
        from_attributes = True


class TariffListResponse(BaseModel):
    """Схема ответа для списка тарифов"""
    tariffs: List[TariffResponse]
    total: int
    active_count: int


@router.get("/", response_model=TariffListResponse)
async def get_tariffs(
    active_only: bool = Query(True, description="Показывать только активные тарифы"),
    db: Session = Depends(get_session)
):
    """Получение списка тарифов"""
    try:
        tariff_service = TariffService(db)
        
        if active_only:
            tariffs = tariff_service.get_active_tariffs()
        else:
            tariffs = tariff_service.get_all_tariffs()
        
        # Подсчитываем статистику
        all_tariffs = tariff_service.get_all_tariffs()
        active_count = len([t for t in all_tariffs if t.is_active])
        
        return TariffListResponse(
            tariffs=[TariffResponse.model_validate(t) for t in tariffs],
            total=len(all_tariffs),
            active_count=active_count
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Ошибка получения тарифов: {str(e)}"
        )


@router.get("/{tariff_id}", response_model=TariffResponse)
async def get_tariff(
    tariff_id: UUID,
    db: Session = Depends(get_session)
):
    """Получение конкретного тарифа"""
    try:
        tariff_service = TariffService(db)
        tariff = tariff_service.get_tariff_by_id(tariff_id)
        
        if not tariff:
            raise HTTPException(
                status_code=404,
                detail=f"Тариф с ID {tariff_id} не найден"
            )
        
        return TariffResponse.model_validate(tariff)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Ошибка получения тарифа: {str(e)}"
        )


@router.get("/by-duration/{duration_days}")
async def get_tariffs_by_duration(
    duration_days: int,
    db: Session = Depends(get_session)
):
    """Получение тарифов по длительности"""
    try:
        tariff_service = TariffService(db)
        tariffs = tariff_service.get_tariffs_by_duration(duration_days)
        
        return {
            "duration_days": duration_days,
            "tariffs": [TariffResponse.model_validate(t) for t in tariffs],
            "count": len(tariffs)
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Ошибка получения тарифов: {str(e)}"
        )


@router.get("/prices/{currency}")
async def get_tariff_prices(
    currency: str = Path(..., regex="^(rub|usd|stars)$", description="Валюта: rub, usd, stars"),
    db: Session = Depends(get_session)
):
    """Получение цен тарифов в определенной валюте"""
    try:
        tariff_service = TariffService(db)
        tariffs = tariff_service.get_active_tariffs()
        
        prices = []
        for tariff in tariffs:
            price = tariff.prices.get(currency)
            if price is not None:
                prices.append({
                    "tariff_id": str(tariff.id),
                    "name": tariff.name,
                    "duration_days": tariff.duration_days,
                    "price": price,
                    "currency": currency,
                    "discount_percent": tariff.discount_percent
                })
        
        return {
            "currency": currency,
            "prices": prices,
            "count": len(prices)
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Ошибка получения цен: {str(e)}"
        )


@router.get("/popular/")
async def get_popular_tariffs(
    limit: int = Query(3, ge=1, le=10, description="Количество популярных тарифов"),
    db: Session = Depends(get_session)
):
    """Получение популярных тарифов"""
    try:
        tariff_service = TariffService(db)
        tariffs = tariff_service.get_popular_tariffs(limit)
        
        return {
            "popular_tariffs": [TariffResponse.model_validate(t) for t in tariffs],
            "count": len(tariffs)
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Ошибка получения популярных тарифов: {str(e)}"
        )


@router.get("/stats/")
async def get_tariff_stats(db: Session = Depends(get_session)):
    """Получение статистики по тарифам"""
    try:
        tariff_service = TariffService(db)
        
        all_tariffs = tariff_service.get_all_tariffs()
        active_tariffs = tariff_service.get_active_tariffs()
        
        # Группировка по длительности
        duration_stats = {}
        for tariff in active_tariffs:
            duration = tariff.duration_days
            if duration not in duration_stats:
                duration_stats[duration] = {
                    "count": 0,
                    "min_price_rub": None,
                    "max_price_rub": None
                }
            
            duration_stats[duration]["count"] += 1
            
            price_rub = tariff.prices.get("rub")
            if price_rub:
                if duration_stats[duration]["min_price_rub"] is None:
                    duration_stats[duration]["min_price_rub"] = price_rub
                    duration_stats[duration]["max_price_rub"] = price_rub
                else:
                    duration_stats[duration]["min_price_rub"] = min(
                        duration_stats[duration]["min_price_rub"], price_rub
                    )
                    duration_stats[duration]["max_price_rub"] = max(
                        duration_stats[duration]["max_price_rub"], price_rub
                    )
        
        return {
            "total_tariffs": len(all_tariffs),
            "active_tariffs": len(active_tariffs),
            "inactive_tariffs": len(all_tariffs) - len(active_tariffs),
            "duration_stats": duration_stats,
            "available_currencies": ["rub", "usd", "stars"]
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Ошибка получения статистики: {str(e)}"
        )
