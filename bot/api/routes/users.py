"""
UnveilVPN Shop - Users API Routes
"""

from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from pydantic import BaseModel

from bot.db.session import get_session
from bot.db.models import VPNUsers
from bot.services.user_service import UserService
from bot.common.exceptions import NotFoundError

router = APIRouter()


class UserResponse(BaseModel):
    """Схема ответа для пользователя"""
    id: str
    telegram_id: int
    username: Optional[str]
    first_name: Optional[str]
    last_name: Optional[str]
    language_code: Optional[str]
    is_active: bool
    is_premium: bool
    created_at: str
    
    class Config:
        from_attributes = True


class UserStatsResponse(BaseModel):
    """Схема ответа для статистики пользователей"""
    total_users: int
    active_users: int
    premium_users: int
    new_users_today: int
    new_users_week: int
    new_users_month: int


@router.get("/stats", response_model=UserStatsResponse)
async def get_user_stats(db: Session = Depends(get_session)):
    """Получение статистики пользователей"""
    try:
        user_service = UserService(db)
        stats = user_service.get_user_statistics()
        
        return UserStatsResponse(**stats)
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Ошибка получения статистики: {str(e)}"
        )


@router.get("/", response_model=List[UserResponse])
async def get_users(
    skip: int = Query(0, ge=0, description="Количество пропускаемых записей"),
    limit: int = Query(100, ge=1, le=1000, description="Максимальное количество записей"),
    active_only: bool = Query(False, description="Только активные пользователи"),
    premium_only: bool = Query(False, description="Только премиум пользователи"),
    db: Session = Depends(get_session)
):
    """Получение списка пользователей"""
    try:
        user_service = UserService(db)
        
        filters = {}
        if active_only:
            filters['is_active'] = True
        if premium_only:
            filters['is_premium'] = True
        
        users = user_service.get_users_list(
            skip=skip,
            limit=limit,
            filters=filters
        )
        
        return [UserResponse.model_validate(user) for user in users]
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Ошибка получения пользователей: {str(e)}"
        )


@router.get("/{user_id}", response_model=UserResponse)
async def get_user(
    user_id: UUID,
    db: Session = Depends(get_session)
):
    """Получение конкретного пользователя"""
    try:
        user_service = UserService(db)
        user = user_service.get_user_by_id(user_id)
        
        if not user:
            raise HTTPException(
                status_code=404,
                detail=f"Пользователь с ID {user_id} не найден"
            )
        
        return UserResponse.model_validate(user)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Ошибка получения пользователя: {str(e)}"
        )


@router.get("/telegram/{telegram_id}", response_model=UserResponse)
async def get_user_by_telegram_id(
    telegram_id: int,
    db: Session = Depends(get_session)
):
    """Получение пользователя по Telegram ID"""
    try:
        user_service = UserService(db)
        user = user_service.get_user_by_telegram_id(telegram_id)
        
        if not user:
            raise HTTPException(
                status_code=404,
                detail=f"Пользователь с Telegram ID {telegram_id} не найден"
            )
        
        return UserResponse.model_validate(user)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Ошибка получения пользователя: {str(e)}"
        )


@router.get("/search/")
async def search_users(
    query: str = Query(..., min_length=2, description="Поисковый запрос"),
    limit: int = Query(50, ge=1, le=100, description="Максимальное количество результатов"),
    db: Session = Depends(get_session)
):
    """Поиск пользователей"""
    try:
        user_service = UserService(db)
        users = user_service.search_users(query, limit)
        
        return {
            "query": query,
            "results": [UserResponse.model_validate(user) for user in users],
            "count": len(users)
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Ошибка поиска пользователей: {str(e)}"
        )


@router.get("/activity/recent")
async def get_recent_activity(
    hours: int = Query(24, ge=1, le=168, description="Количество часов для анализа"),
    db: Session = Depends(get_session)
):
    """Получение недавней активности пользователей"""
    try:
        user_service = UserService(db)
        activity = user_service.get_recent_activity(hours)
        
        return {
            "period_hours": hours,
            "activity": activity
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Ошибка получения активности: {str(e)}"
        )


@router.get("/growth/")
async def get_user_growth(
    days: int = Query(30, ge=1, le=365, description="Количество дней для анализа"),
    db: Session = Depends(get_session)
):
    """Получение статистики роста пользователей"""
    try:
        user_service = UserService(db)
        growth_data = user_service.get_user_growth_stats(days)
        
        return {
            "period_days": days,
            "growth_data": growth_data
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Ошибка получения статистики роста: {str(e)}"
        )


@router.get("/countries/")
async def get_users_by_country(db: Session = Depends(get_session)):
    """Получение распределения пользователей по странам"""
    try:
        user_service = UserService(db)
        countries_data = user_service.get_users_by_country()
        
        return {
            "countries": countries_data,
            "total_countries": len(countries_data)
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Ошибка получения данных по странам: {str(e)}"
        )
