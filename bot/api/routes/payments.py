"""
UnveilVPN Shop - Payments API Routes
"""

from typing import List, Optional
from uuid import UUID
from decimal import Decimal

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from pydantic import BaseModel

from bot.db.session import get_session
from bot.db.models import Payments
from bot.services.payment_service import PaymentService
from bot.common.exceptions import NotFoundError

router = APIRouter()


class PaymentResponse(BaseModel):
    """Схема ответа для платежа"""
    id: str
    user_id: str
    tariff_id: str
    amount: float
    currency: str
    payment_method: str
    status: str
    created_at: str
    
    class Config:
        from_attributes = True


class PaymentStatsResponse(BaseModel):
    """Схема ответа для статистики платежей"""
    total_payments: int
    successful_payments: int
    failed_payments: int
    pending_payments: int
    total_revenue: dict
    revenue_by_method: dict
    revenue_by_currency: dict


@router.get("/stats", response_model=PaymentStatsResponse)
async def get_payment_stats(
    days: int = Query(30, ge=1, le=365, description="Количество дней для анализа"),
    db: Session = Depends(get_session)
):
    """Получение статистики платежей"""
    try:
        payment_service = PaymentService(db)
        stats = payment_service.get_payment_statistics(days)
        
        return PaymentStatsResponse(**stats)
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Ошибка получения статистики платежей: {str(e)}"
        )


@router.get("/", response_model=List[PaymentResponse])
async def get_payments(
    skip: int = Query(0, ge=0, description="Количество пропускаемых записей"),
    limit: int = Query(100, ge=1, le=1000, description="Максимальное количество записей"),
    status: Optional[str] = Query(None, description="Фильтр по статусу"),
    payment_method: Optional[str] = Query(None, description="Фильтр по способу оплаты"),
    db: Session = Depends(get_session)
):
    """Получение списка платежей"""
    try:
        payment_service = PaymentService(db)
        
        filters = {}
        if status:
            filters['status'] = status
        if payment_method:
            filters['payment_method'] = payment_method
        
        payments = payment_service.get_payments_list(
            skip=skip,
            limit=limit,
            filters=filters
        )
        
        return [PaymentResponse.model_validate(payment) for payment in payments]
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Ошибка получения платежей: {str(e)}"
        )


@router.get("/{payment_id}", response_model=PaymentResponse)
async def get_payment(
    payment_id: UUID,
    db: Session = Depends(get_session)
):
    """Получение конкретного платежа"""
    try:
        payment_service = PaymentService(db)
        payment = payment_service.get_payment_by_id(payment_id)
        
        if not payment:
            raise HTTPException(
                status_code=404,
                detail=f"Платеж с ID {payment_id} не найден"
            )
        
        return PaymentResponse.model_validate(payment)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Ошибка получения платежа: {str(e)}"
        )


@router.get("/user/{user_id}")
async def get_user_payments(
    user_id: UUID,
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100),
    db: Session = Depends(get_session)
):
    """Получение платежей пользователя"""
    try:
        payment_service = PaymentService(db)
        payments = payment_service.get_user_payments(user_id, skip, limit)
        
        return {
            "user_id": str(user_id),
            "payments": [PaymentResponse.model_validate(payment) for payment in payments],
            "count": len(payments)
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Ошибка получения платежей пользователя: {str(e)}"
        )


@router.get("/revenue/daily")
async def get_daily_revenue(
    days: int = Query(30, ge=1, le=365, description="Количество дней"),
    db: Session = Depends(get_session)
):
    """Получение ежедневной выручки"""
    try:
        payment_service = PaymentService(db)
        revenue_data = payment_service.get_daily_revenue(days)
        
        return {
            "period_days": days,
            "revenue_data": revenue_data
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Ошибка получения данных о выручке: {str(e)}"
        )


@router.get("/methods/stats")
async def get_payment_methods_stats(
    days: int = Query(30, ge=1, le=365),
    db: Session = Depends(get_session)
):
    """Получение статистики по способам оплаты"""
    try:
        payment_service = PaymentService(db)
        methods_stats = payment_service.get_payment_methods_stats(days)
        
        return {
            "period_days": days,
            "methods_stats": methods_stats
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Ошибка получения статистики способов оплаты: {str(e)}"
        )


@router.get("/conversion/")
async def get_conversion_stats(
    days: int = Query(30, ge=1, le=365),
    db: Session = Depends(get_session)
):
    """Получение статистики конверсии"""
    try:
        payment_service = PaymentService(db)
        conversion_data = payment_service.get_conversion_stats(days)
        
        return {
            "period_days": days,
            "conversion_data": conversion_data
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Ошибка получения статистики конверсии: {str(e)}"
        )


@router.get("/failed/")
async def get_failed_payments(
    days: int = Query(7, ge=1, le=30),
    limit: int = Query(100, ge=1, le=500),
    db: Session = Depends(get_session)
):
    """Получение неудачных платежей"""
    try:
        payment_service = PaymentService(db)
        failed_payments = payment_service.get_failed_payments(days, limit)
        
        return {
            "period_days": days,
            "failed_payments": [PaymentResponse.model_validate(payment) for payment in failed_payments],
            "count": len(failed_payments)
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Ошибка получения неудачных платежей: {str(e)}"
        )
