"""
UnveilVPN Shop - Health Check Routes
"""

import time
import psutil
from typing import Dict, Any
from datetime import datetime

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from bot.db.session import get_async_session

router = APIRouter(redirect_slashes=False)


@router.get("")
async def health_check():
    """Базовая проверка здоровья сервиса"""
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "service": "UnveilVPN Shop API"
    }

@router.get("/db")
async def health_check_db(session: AsyncSession = Depends(get_async_session)):
    """Проверка подключения к базе данных"""
    try:
        # Выполняем простой запрос к БД
        await session.execute(text("SELECT 1"))
        return {
            "status": "connected",
            "type": "postgresql",
            "timestamp": datetime.utcnow().isoformat()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Database connection failed: {str(e)}")

@router.get("/redis")
async def health_check_redis():
    """Проверка подключения к Redis"""
    try:
        # Простая проверка Redis (заглушка)
        return {
            "status": "connected",
            "type": "redis",
            "timestamp": datetime.utcnow().isoformat()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Redis connection failed: {str(e)}")

@router.get("/remnawave")
async def health_check_remnawave():
    """Проверка подключения к Remnawave"""
    try:
        # Проверка Remnawave (заглушка для тестового окружения)
        return {
            "status": "connected",
            "type": "remnawave",
            "timestamp": datetime.utcnow().isoformat(),
            "note": "Test environment - actual connection not verified"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Remnawave connection failed: {str(e)}")


@router.get("/detailed")
async def detailed_health_check(db: AsyncSession = Depends(get_async_session)):
    """Детальная проверка здоровья всех компонентов"""
    start_time = time.time()
    
    health_data = {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "service": "UnveilVPN Shop API",
        "version": "1.0.0",
        "checks": {}
    }
    
    # Проверка базы данных
    try:
        db_start = time.time()
        await db.execute(text("SELECT 1"))
        db_time = time.time() - db_start
        
        health_data["checks"]["database"] = {
            "status": "healthy",
            "response_time_ms": round(db_time * 1000, 2),
            "details": "PostgreSQL connection successful"
        }
    except Exception as e:
        health_data["checks"]["database"] = {
            "status": "unhealthy",
            "error": str(e),
            "details": "PostgreSQL connection failed"
        }
        health_data["status"] = "unhealthy"
    
    # Проверка системных ресурсов
    try:
        cpu_percent = psutil.cpu_percent(interval=0.1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        health_data["checks"]["system"] = {
            "status": "healthy",
            "cpu_percent": cpu_percent,
            "memory_percent": memory.percent,
            "disk_percent": disk.percent,
            "details": "System resources within normal limits"
        }
        
        # Предупреждения о высокой нагрузке
        if cpu_percent > 80 or memory.percent > 80 or disk.percent > 80:
            health_data["checks"]["system"]["status"] = "warning"
            health_data["checks"]["system"]["details"] = "High resource usage detected"
            
    except Exception as e:
        health_data["checks"]["system"] = {
            "status": "error",
            "error": str(e),
            "details": "Failed to get system metrics"
        }
    
    # Общее время ответа
    total_time = time.time() - start_time
    health_data["response_time_ms"] = round(total_time * 1000, 2)
    
    # Определяем общий статус
    if any(check.get("status") == "unhealthy" for check in health_data["checks"].values()):
        health_data["status"] = "unhealthy"
        raise HTTPException(status_code=503, detail=health_data)
    elif any(check.get("status") == "warning" for check in health_data["checks"].values()):
        health_data["status"] = "warning"
    
    return health_data


@router.get("/ready")
async def readiness_check(db: AsyncSession = Depends(get_async_session)):
    """Проверка готовности сервиса к обработке запросов"""
    try:
        # Проверяем подключение к БД
        await db.execute(text("SELECT 1"))
        
        return {
            "status": "ready",
            "timestamp": datetime.utcnow().isoformat(),
            "message": "Service is ready to handle requests"
        }
    except Exception as e:
        raise HTTPException(
            status_code=503,
            detail={
                "status": "not_ready",
                "timestamp": datetime.utcnow().isoformat(),
                "error": str(e),
                "message": "Service is not ready to handle requests"
            }
        )


@router.get("/live")
async def liveness_check():
    """Проверка жизнеспособности сервиса"""
    return {
        "status": "alive",
        "timestamp": datetime.utcnow().isoformat(),
        "message": "Service is alive"
    }
