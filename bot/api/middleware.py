"""
UnveilVPN Shop - API Middleware
Middleware для FastAPI приложения
"""

import time
import logging
from typing import Callable
from collections import defaultdict

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import JSONResponse

logger = logging.getLogger(__name__)


class LoggingMiddleware(BaseHTTPMiddleware):
    """Middleware для логирования запросов"""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        start_time = time.time()
        
        # Логируем входящий запрос
        logger.info(f"Incoming request: {request.method} {request.url}")
        
        try:
            response = await call_next(request)
            
            # Вычисляем время обработки
            process_time = time.time() - start_time
            
            # Логируем ответ
            logger.info(
                f"Request completed: {request.method} {request.url} "
                f"- Status: {response.status_code} - Time: {process_time:.3f}s"
            )
            
            # Добавляем заголовок с временем обработки
            response.headers["X-Process-Time"] = str(process_time)
            
            return response
            
        except Exception as e:
            process_time = time.time() - start_time
            logger.error(
                f"Request failed: {request.method} {request.url} "
                f"- Error: {str(e)} - Time: {process_time:.3f}s"
            )
            raise


class SecurityMiddleware(BaseHTTPMiddleware):
    """Middleware для безопасности"""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        response = await call_next(request)
        
        # Добавляем заголовки безопасности
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        
        # CSP для API (строгий)
        response.headers["Content-Security-Policy"] = "default-src 'none'"
        
        return response


class RateLimitMiddleware(BaseHTTPMiddleware):
    """Middleware для ограничения частоты запросов"""
    
    def __init__(self, app, calls: int = 100, period: int = 60):
        super().__init__(app)
        self.calls = calls
        self.period = period
        self.requests = defaultdict(list)
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Получаем IP клиента
        client_ip = self._get_client_ip(request)
        
        # Проверяем лимит
        if await self._is_rate_limited(client_ip):
            return JSONResponse(
                status_code=429,
                content={
                    "error": True,
                    "message": "Превышен лимит запросов",
                    "error_code": "RATE_LIMIT_EXCEEDED",
                    "retry_after": self.period
                },
                headers={"Retry-After": str(self.period)}
            )
        
        # Записываем запрос
        await self._record_request(client_ip)
        
        return await call_next(request)
    
    def _get_client_ip(self, request: Request) -> str:
        """Получение IP адреса клиента"""
        # Проверяем заголовки прокси
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        # Возвращаем IP из соединения
        return request.client.host if request.client else "unknown"
    
    async def _is_rate_limited(self, client_ip: str) -> bool:
        """Проверка превышения лимита"""
        now = time.time()
        
        # Очищаем старые запросы
        self.requests[client_ip] = [
            req_time for req_time in self.requests[client_ip]
            if now - req_time < self.period
        ]
        
        # Проверяем лимит
        return len(self.requests[client_ip]) >= self.calls
    
    async def _record_request(self, client_ip: str):
        """Запись времени запроса"""
        self.requests[client_ip].append(time.time())


class ErrorHandlerMiddleware(BaseHTTPMiddleware):
    """Middleware для обработки ошибок"""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        try:
            return await call_next(request)
            
        except Exception as e:
            logger.error(f"Unhandled exception in API: {e}", exc_info=True)
            
            # Возвращаем стандартизированный ответ об ошибке
            return JSONResponse(
                status_code=500,
                content={
                    "error": True,
                    "message": "Внутренняя ошибка сервера",
                    "error_code": "INTERNAL_SERVER_ERROR"
                }
            )


class CacheMiddleware(BaseHTTPMiddleware):
    """Middleware для кэширования ответов"""
    
    def __init__(self, app, cache_ttl: int = 300):
        super().__init__(app)
        self.cache_ttl = cache_ttl
        self.cache = {}  # В реальной реализации будет Redis
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Кэшируем только GET запросы
        if request.method != "GET":
            return await call_next(request)
        
        # Создаем ключ кэша
        cache_key = self._create_cache_key(request)
        
        # Проверяем кэш
        cached_response = await self._get_from_cache(cache_key)
        if cached_response:
            logger.debug(f"Cache hit for {request.url}")
            return cached_response
        
        # Выполняем запрос
        response = await call_next(request)
        
        # Кэшируем успешные ответы
        if response.status_code == 200:
            await self._save_to_cache(cache_key, response)
            logger.debug(f"Cached response for {request.url}")
        
        return response
    
    def _create_cache_key(self, request: Request) -> str:
        """Создание ключа кэша"""
        # Включаем URL и query параметры
        return f"api_cache:{request.url}"
    
    async def _get_from_cache(self, key: str):
        """Получение из кэша"""
        cached_data = self.cache.get(key)
        if not cached_data:
            return None
        
        # Проверяем TTL
        if time.time() - cached_data["timestamp"] > self.cache_ttl:
            del self.cache[key]
            return None
        
        # Возвращаем кэшированный ответ
        return JSONResponse(
            content=cached_data["content"],
            status_code=cached_data["status_code"],
            headers={"X-Cache": "HIT"}
        )
    
    async def _save_to_cache(self, key: str, response: Response):
        """Сохранение в кэш"""
        try:
            # Читаем содержимое ответа
            body = b""
            async for chunk in response.body_iterator:
                body += chunk
            
            # Парсим JSON
            import json
            content = json.loads(body.decode())
            
            # Сохраняем в кэш
            self.cache[key] = {
                "content": content,
                "status_code": response.status_code,
                "timestamp": time.time()
            }
            
            # Пересоздаем response с новым body
            response.body_iterator = iter([body])
            response.headers["X-Cache"] = "MISS"
            
        except Exception as e:
            logger.error(f"Error caching response: {e}")


class MetricsMiddleware(BaseHTTPMiddleware):
    """Middleware для сбора метрик"""
    
    def __init__(self, app):
        super().__init__(app)
        self.metrics = {
            "requests_total": 0,
            "requests_by_method": defaultdict(int),
            "requests_by_status": defaultdict(int),
            "response_times": [],
            "errors_total": 0
        }
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        start_time = time.time()
        
        try:
            response = await call_next(request)
            
            # Записываем метрики
            process_time = time.time() - start_time
            
            self.metrics["requests_total"] += 1
            self.metrics["requests_by_method"][request.method] += 1
            self.metrics["requests_by_status"][response.status_code] += 1
            self.metrics["response_times"].append(process_time)
            
            # Ограничиваем размер списка времен ответа
            if len(self.metrics["response_times"]) > 1000:
                self.metrics["response_times"] = self.metrics["response_times"][-500:]
            
            return response
            
        except Exception as e:
            self.metrics["errors_total"] += 1
            raise
    
    def get_metrics(self) -> dict:
        """Получение метрик"""
        response_times = self.metrics["response_times"]
        
        return {
            "requests_total": self.metrics["requests_total"],
            "requests_by_method": dict(self.metrics["requests_by_method"]),
            "requests_by_status": dict(self.metrics["requests_by_status"]),
            "errors_total": self.metrics["errors_total"],
            "avg_response_time": sum(response_times) / len(response_times) if response_times else 0,
            "max_response_time": max(response_times) if response_times else 0,
            "min_response_time": min(response_times) if response_times else 0
        }


# Глобальный экземпляр для метрик
metrics_middleware = None


def get_metrics_middleware() -> MetricsMiddleware:
    """Получение middleware метрик"""
    global metrics_middleware
    return metrics_middleware
