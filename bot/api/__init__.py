"""
UnveilVPN Shop - FastAPI Server Package
API сервер для интеграции с лендингом и внешними сервисами
"""

from .app import create_app
from .config import APIConfig
from .dependencies import get_db_session, get_current_user
from .exceptions import APIException, ValidationError, NotFoundError
from .models import (
    BaseResponse,
    ErrorResponse,
    UserResponse,
    TariffResponse,
    PaymentResponse
)

__all__ = [
    'create_app',
    'APIConfig',
    'get_db_session',
    'get_current_user',
    'APIException',
    'ValidationError',
    'NotFoundError',
    'BaseResponse',
    'ErrorResponse',
    'UserResponse',
    'TariffResponse',
    'PaymentResponse'
]
