"""
UnveilVPN Shop - FastAPI Application
Основное приложение FastAPI с настройкой middleware и роутеров
"""

import logging
from contextlib import asynccontextmanager
from typing import AsyncGenerator

from fastapi import FastAPI, Request, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException

from .config import APIConfig
from .exceptions import APIException
from .middleware import (
    LoggingMiddleware,
    SecurityMiddleware,
    RateLimitMiddleware,
    ErrorHandlerMiddleware,
    MetricsMiddleware
)
from .routers import (
    health_router,
    auth_router,
    users_router,
    tariffs_router,
    payments_router,
    webhooks_router
)


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    """Управление жизненным циклом приложения"""
    # Startup
    logging.info("🚀 FastAPI server starting up...")
    
    # Здесь можно добавить инициализацию ресурсов
    # Например, подключение к внешним сервисам
    
    yield
    
    # Shutdown
    logging.info("🛑 FastAPI server shutting down...")
    
    # Здесь можно добавить очистку ресурсов


def create_app(config: APIConfig = None) -> FastAPI:
    """
    Создание и настройка FastAPI приложения
    """
    if config is None:
        config = APIConfig.from_env()
    
    # Создаем приложение
    app = FastAPI(
        title="UnveilVPN Shop API",
        description="API для системы продажи VPN подписок",
        version="1.0.0",
        docs_url="/docs" if config.debug else None,
        redoc_url="/redoc" if config.debug else None,
        openapi_url="/openapi.json" if config.debug else None,
        lifespan=lifespan
    )
    
    # Настраиваем логирование
    _setup_logging(config)
    
    # Добавляем middleware
    _setup_middleware(app, config)
    
    # Добавляем обработчики ошибок
    _setup_exception_handlers(app)
    
    # Подключаем роутеры
    _setup_routers(app)
    
    return app


def _setup_logging(config: APIConfig):
    """Настройка логирования"""
    logging.basicConfig(
        level=getattr(logging, config.log_level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )


def _setup_middleware(app: FastAPI, config: APIConfig):
    """Настройка middleware"""
    
    # CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=config.allowed_origins,
        allow_credentials=True,
        allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
        allow_headers=["*"],
    )
    
    # Trusted hosts middleware
    if config.allowed_hosts:
        app.add_middleware(
            TrustedHostMiddleware,
            allowed_hosts=config.allowed_hosts
        )
    
    # Custom middleware
    app.add_middleware(ErrorHandlerMiddleware)
    app.add_middleware(SecurityMiddleware)
    app.add_middleware(LoggingMiddleware)

    # Metrics middleware
    global metrics_middleware
    from .middleware import metrics_middleware
    metrics_middleware = MetricsMiddleware(app)
    app.add_middleware(MetricsMiddleware)

    if config.rate_limit_enabled:
        app.add_middleware(
            RateLimitMiddleware,
            calls=config.rate_limit_calls,
            period=config.rate_limit_period
        )


def _setup_exception_handlers(app: FastAPI):
    """Настройка обработчиков ошибок"""
    
    @app.exception_handler(APIException)
    async def api_exception_handler(_: Request, exc: APIException):
        """Обработчик API исключений"""
        return JSONResponse(
            status_code=exc.status_code,
            content={
                "error": True,
                "message": exc.message,
                "error_code": exc.error_code,
                "details": exc.details
            }
        )
    
    @app.exception_handler(StarletteHTTPException)
    async def http_exception_handler(_: Request, exc: StarletteHTTPException):
        """Обработчик HTTP исключений"""
        return JSONResponse(
            status_code=exc.status_code,
            content={
                "error": True,
                "message": exc.detail,
                "error_code": f"HTTP_{exc.status_code}"
            }
        )
    
    @app.exception_handler(RequestValidationError)
    async def validation_exception_handler(_: Request, exc: RequestValidationError):
        """Обработчик ошибок валидации"""
        return JSONResponse(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            content={
                "error": True,
                "message": "Ошибка валидации данных",
                "error_code": "VALIDATION_ERROR",
                "details": exc.errors()
            }
        )
    
    @app.exception_handler(Exception)
    async def general_exception_handler(_: Request, exc: Exception):
        """Обработчик общих исключений"""
        logging.error(f"Unhandled exception: {exc}", exc_info=True)

        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={
                "error": True,
                "message": "Внутренняя ошибка сервера",
                "error_code": "INTERNAL_SERVER_ERROR"
            }
        )


def _setup_routers(app: FastAPI):
    """Подключение роутеров"""
    
    # Базовые роутеры
    app.include_router(health_router, prefix="/health", tags=["Health"])
    
    # API v1
    api_v1_prefix = "/api/v1"
    
    app.include_router(auth_router, prefix=f"{api_v1_prefix}/auth", tags=["Authentication"])
    app.include_router(users_router, prefix=f"{api_v1_prefix}/users", tags=["Users"])
    app.include_router(tariffs_router, prefix=f"{api_v1_prefix}/tariffs", tags=["Tariffs"])
    app.include_router(payments_router, prefix=f"{api_v1_prefix}/payments", tags=["Payments"])
    app.include_router(webhooks_router, prefix=f"{api_v1_prefix}/webhooks", tags=["Webhooks"])


# Создаем экземпляр приложения для запуска
app = create_app()


if __name__ == "__main__":
    import uvicorn
    
    config = APIConfig.from_env()
    
    uvicorn.run(
        "bot.api.app:app",
        host=config.host,
        port=config.port,
        reload=config.debug,
        log_level=config.log_level.lower()
    )
