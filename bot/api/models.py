"""
UnveilVPN Shop - API Models
Pydantic модели для API запросов и ответов
"""

from datetime import datetime
from typing import Optional, Dict, Any, List, Union
from decimal import Decimal

from pydantic import BaseModel, Field, validator
from pydantic.types import UUID4


class BaseResponse(BaseModel):
    """Базовая модель ответа"""
    success: bool = True
    message: Optional[str] = None
    timestamp: datetime = Field(default_factory=datetime.utcnow)


class ErrorResponse(BaseResponse):
    """Модель ответа с ошибкой"""
    success: bool = False
    error_code: Optional[str] = None
    details: Optional[Dict[str, Any]] = None


class PaginationMeta(BaseModel):
    """Метаданные пагинации"""
    page: int = Field(ge=1)
    per_page: int = Field(ge=1, le=100)
    total: int = Field(ge=0)
    pages: int = Field(ge=0)
    has_next: bool
    has_prev: bool


class PaginatedResponse(BaseResponse):
    """Базовая модель для пагинированных ответов"""
    meta: PaginationMeta
    data: List[Any]


# User Models

class UserBase(BaseModel):
    """Базовая модель пользователя"""
    telegram_id: int = Field(..., description="Telegram ID пользователя")
    username: Optional[str] = Field(None, description="Username в Telegram")
    first_name: Optional[str] = Field(None, description="Имя пользователя")
    last_name: Optional[str] = Field(None, description="Фамилия пользователя")
    language_code: str = Field("ru", description="Код языка")


class UserCreate(UserBase):
    """Модель для создания пользователя"""
    pass


class UserUpdate(BaseModel):
    """Модель для обновления пользователя"""
    username: Optional[str] = None
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    language_code: Optional[str] = None
    is_active: Optional[bool] = None
    is_banned: Optional[bool] = None
    ban_reason: Optional[str] = None


class UserResponse(UserBase):
    """Модель ответа с данными пользователя"""
    id: UUID4
    vpn_id: Optional[str] = None
    is_test_used: bool = False
    is_active: bool = True
    is_banned: bool = False
    ban_reason: Optional[str] = None
    referral_code: Optional[str] = None
    referred_by_id: Optional[UUID4] = None
    referral_earnings: Decimal = Decimal('0')
    user_metadata: Dict[str, Any] = {}
    created_at: datetime
    updated_at: datetime
    last_activity: Optional[datetime] = None
    
    class Config:
        from_attributes = True


# Tariff Models

class TariffBase(BaseModel):
    """Базовая модель тарифа"""
    name: str = Field(..., description="Название тарифа")
    description: Optional[str] = Field(None, description="Описание тарифа")
    duration_days: int = Field(..., gt=0, description="Продолжительность в днях")
    prices: Dict[str, Union[int, float]] = Field(..., description="Цены в разных валютах")
    features: Dict[str, Any] = Field(default_factory=dict, description="Характеристики тарифа")
    is_active: bool = Field(True, description="Активен ли тариф")
    is_popular: bool = Field(False, description="Популярный тариф")
    is_test_available: bool = Field(True, description="Доступен ли тестовый период")
    sort_order: int = Field(0, description="Порядок сортировки")
    discount_percent: int = Field(0, ge=0, le=100, description="Процент скидки")
    is_promo: bool = Field(False, description="Промо тариф")
    promo_text: Optional[str] = Field(None, description="Текст промо")


class TariffCreate(TariffBase):
    """Модель для создания тарифа"""
    
    @validator('prices')
    def validate_prices(cls, v):
        """Валидация цен"""
        required_currencies = ['rub', 'usd', 'stars']
        for currency in required_currencies:
            if currency not in v:
                v[currency] = 0
            if not isinstance(v[currency], (int, float)) or v[currency] < 0:
                raise ValueError(f"Цена в валюте {currency} должна быть неотрицательным числом")
        return v


class TariffUpdate(BaseModel):
    """Модель для обновления тарифа"""
    name: Optional[str] = None
    description: Optional[str] = None
    duration_days: Optional[int] = Field(None, gt=0)
    prices: Optional[Dict[str, Union[int, float]]] = None
    features: Optional[Dict[str, Any]] = None
    is_active: Optional[bool] = None
    is_popular: Optional[bool] = None
    is_test_available: Optional[bool] = None
    sort_order: Optional[int] = None
    discount_percent: Optional[int] = Field(None, ge=0, le=100)
    is_promo: Optional[bool] = None
    promo_text: Optional[str] = None


class TariffResponse(TariffBase):
    """Модель ответа с данными тарифа"""
    id: UUID4
    version: int = 1
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


# Payment Models

class PaymentBase(BaseModel):
    """Базовая модель платежа"""
    user_id: UUID4 = Field(..., description="ID пользователя")
    tariff_id: UUID4 = Field(..., description="ID тарифа")
    payment_method: str = Field(..., description="Метод оплаты")
    original_amount: Decimal = Field(..., description="Оригинальная сумма")
    discount_amount: Decimal = Field(Decimal('0'), description="Сумма скидки")
    final_amount: Decimal = Field(..., description="Итоговая сумма")
    currency: str = Field(..., description="Валюта")


class PaymentCreate(PaymentBase):
    """Модель для создания платежа"""
    promocode_id: Optional[UUID4] = Field(None, description="ID промокода")
    payment_metadata: Dict[str, Any] = Field(default_factory=dict, description="Метаданные платежа")


class PaymentUpdate(BaseModel):
    """Модель для обновления платежа"""
    status: Optional[str] = None
    external_payment_id: Optional[str] = None
    order_id: Optional[str] = None
    payment_metadata: Optional[Dict[str, Any]] = None
    paid_at: Optional[datetime] = None
    expires_at: Optional[datetime] = None


class PaymentResponse(BaseResponse):
    """Ответ с информацией о платеже"""
    payment_id: Optional[UUID4] = Field(None, description="ID платежа")
    payment_url: Optional[str] = Field(None, description="URL для оплаты")
    external_payment_id: Optional[str] = Field(None, description="Внешний ID платежа")
    status: Optional[str] = Field(None, description="Статус платежа")
    amount: Optional[Decimal] = Field(None, description="Сумма платежа")
    currency: Optional[str] = Field(None, description="Валюта платежа")
    method: Optional[str] = Field(None, description="Метод оплаты")


class PaymentDetailResponse(PaymentBase):
    """Детальная модель ответа с данными платежа"""
    id: UUID4
    status: str = "pending"
    external_payment_id: Optional[str] = None
    order_id: Optional[str] = None
    payment_metadata: Dict[str, Any] = {}
    promocode_id: Optional[UUID4] = None
    created_at: datetime
    updated_at: datetime
    paid_at: Optional[datetime] = None
    expires_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class PaymentListResponse(BaseResponse):
    """Ответ со списком платежей"""
    payments: List[Dict[str, Any]] = Field(default_factory=list, description="Список платежей")
    total: int = Field(0, description="Общее количество платежей")


class WebhookResponse(BaseResponse):
    """Ответ на webhook"""
    pass


class RefundRequest(BaseModel):
    """Запрос на возврат платежа"""
    amount: Optional[Decimal] = Field(None, description="Сумма возврата (None для полного возврата)")
    reason: Optional[str] = Field(None, description="Причина возврата")


class PaymentStatsResponse(BaseResponse):
    """Ответ со статистикой платежей"""
    stats: Dict[str, Any] = Field(default_factory=dict, description="Статистика платежей")


# Referral Models

class ReferralCodeResponse(BaseResponse):
    """Ответ с реферальным кодом"""
    referral_code: Optional[str] = Field(None, description="Реферальный код")


class ReferralRegisterRequest(BaseModel):
    """Запрос на регистрацию реферала"""
    referral_code: str = Field(..., description="Реферальный код")


class ReferralStatsResponse(BaseResponse):
    """Ответ со статистикой рефералов"""
    stats: Dict[str, Any] = Field(default_factory=dict, description="Статистика рефералов")


class ReferralTreeResponse(BaseResponse):
    """Ответ с деревом рефералов"""
    tree: Dict[str, Any] = Field(default_factory=dict, description="Дерево рефералов")


# Promocode Models

class PromocodeBase(BaseModel):
    """Базовая модель промокода"""
    code: str = Field(..., description="Код промокода")
    name: Optional[str] = Field(None, description="Название промокода")
    description: Optional[str] = Field(None, description="Описание промокода")
    discount_type: str = Field(..., description="Тип скидки")
    discount_value: Decimal = Field(..., description="Значение скидки")
    usage_limit: Optional[int] = Field(None, description="Лимит использований")
    usage_limit_per_user: int = Field(1, description="Лимит использований на пользователя")
    starts_at: Optional[datetime] = Field(None, description="Дата начала действия")
    expires_at: Optional[datetime] = Field(None, description="Дата окончания действия")
    applicable_tariffs: Optional[List[UUID4]] = Field(None, description="Применимые тарифы")
    min_purchase_amount: Optional[Decimal] = Field(None, description="Минимальная сумма покупки")
    is_active: bool = Field(True, description="Активен ли промокод")
    is_public: bool = Field(False, description="Публичный промокод")


class PromocodeCreate(PromocodeBase):
    """Модель для создания промокода"""
    created_by_id: Optional[UUID4] = Field(None, description="ID создателя")


class PromocodeUpdate(BaseModel):
    """Модель для обновления промокода"""
    name: Optional[str] = None
    description: Optional[str] = None
    discount_type: Optional[str] = None
    discount_value: Optional[Decimal] = None
    usage_limit: Optional[int] = None
    usage_limit_per_user: Optional[int] = None
    starts_at: Optional[datetime] = None
    expires_at: Optional[datetime] = None
    applicable_tariffs: Optional[List[UUID4]] = None
    min_purchase_amount: Optional[Decimal] = None
    is_active: Optional[bool] = None
    is_public: Optional[bool] = None


class PromocodeResponse(PromocodeBase):
    """Модель ответа с данными промокода"""
    id: UUID4
    usage_count: int = 0
    created_by_id: Optional[UUID4] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class PromocodeValidationRequest(BaseModel):
    """Запрос на валидацию промокода"""
    code: str = Field(..., description="Код промокода")
    tariff_id: Optional[UUID4] = Field(None, description="ID тарифа")
    purchase_amount: Optional[Decimal] = Field(None, description="Сумма покупки")


class PromocodeValidationResponse(BaseResponse):
    """Ответ валидации промокода"""
    validation: Dict[str, Any] = Field(default_factory=dict, description="Результат валидации")


class PromocodeListResponse(BaseResponse):
    """Ответ со списком промокодов"""
    promocodes: List[PromocodeResponse] = Field(default_factory=list, description="Список промокодов")
    total: int = Field(0, description="Общее количество")


class PromocodeStatsResponse(BaseResponse):
    """Ответ со статистикой промокодов"""
    stats: Dict[str, Any] = Field(default_factory=dict, description="Статистика промокодов")


# Support Models

class SupportTicketCreate(BaseModel):
    """Модель создания тикета поддержки"""
    subject: str = Field(..., min_length=5, max_length=500, description="Тема тикета")
    message: str = Field(..., min_length=10, description="Сообщение")
    category: str = Field(default="other", description="Категория тикета")
    priority: str = Field(default="medium", description="Приоритет тикета")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Дополнительные данные")


class SupportMessageCreate(BaseModel):
    """Модель создания сообщения в тикете"""
    message: str = Field(..., min_length=1, description="Текст сообщения")
    is_internal: bool = Field(default=False, description="Внутреннее сообщение")
    attachments: Optional[List[str]] = Field(None, description="Вложения")


class SupportTicketUpdate(BaseModel):
    """Модель обновления тикета"""
    status: str = Field(..., description="Новый статус тикета")
    reason: Optional[str] = Field(None, description="Причина изменения")


class SupportTicketResponse(BaseModel):
    """Модель ответа с данными тикета"""
    id: UUID4
    ticket_number: str
    user_id: UUID4
    subject: str
    category: str
    priority: str
    status: str
    messages: List[Dict[str, Any]] = Field(default_factory=list)
    metadata: Dict[str, Any] = Field(default_factory=dict)
    assigned_to_id: Optional[UUID4] = None
    created_at: datetime
    updated_at: datetime
    closed_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class SupportTicketListResponse(BaseResponse):
    """Ответ со списком тикетов"""
    tickets: List[SupportTicketResponse] = Field(default_factory=list, description="Список тикетов")
    total: int = Field(0, description="Общее количество")


class SupportStatsResponse(BaseResponse):
    """Ответ со статистикой поддержки"""
    stats: Dict[str, Any] = Field(default_factory=dict, description="Статистика поддержки")


# Statistics Models

class UserStats(BaseModel):
    """Статистика пользователей"""
    total_users: int
    active_users: int
    banned_users: int
    new_users_today: int
    new_users_week: int
    new_users_month: int


class PaymentStats(BaseModel):
    """Статистика платежей"""
    total_payments: int
    completed_payments: int
    pending_payments: int
    failed_payments: int
    total_revenue: Decimal
    revenue_today: Decimal
    revenue_week: Decimal
    revenue_month: Decimal


class TariffStats(BaseModel):
    """Статистика тарифов"""
    total_tariffs: int
    active_tariffs: int
    popular_tariffs: int
    most_purchased_tariff: Optional[str] = None


class GeneralStats(BaseModel):
    """Общая статистика"""
    users: UserStats
    payments: PaymentStats
    tariffs: TariffStats


# Webhook Models

class WebhookEvent(BaseModel):
    """Модель webhook события"""
    event_type: str = Field(..., description="Тип события")
    event_data: Dict[str, Any] = Field(..., description="Данные события")
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    signature: Optional[str] = Field(None, description="Подпись для верификации")


# Auth Models

class TokenData(BaseModel):
    """Данные токена"""
    user_id: Optional[UUID4] = None
    telegram_id: Optional[int] = None
    scopes: List[str] = []


class Token(BaseModel):
    """Токен доступа"""
    access_token: str
    token_type: str = "bearer"
    expires_in: int
