"""
UnveilVPN Shop - Remnawave Subscription Renewal Notifications
Задача для отправки уведомлений о продлении подписки через Remnawave
"""

import asyncio
import logging
from datetime import datetime, timezone, timedelta
from typing import List, Dict, Any

from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from sqlalchemy import select

from ..db.models import VPNUsers, Subscriptions
from ..services.notification_service import NotificationService
from ..services.payment_vpn_integration import PaymentVPNIntegration
from ..services.vpn_panel_service import VPNPanelService
from ..common.config import BotConfig

import glv

logger = logging.getLogger(__name__)


async def notify_users_to_renew_sub():
    """Уведомление пользователей о необходимости продления подписки"""
    try:
        # Получаем конфигурацию
        config = BotConfig.from_env()

        # Создаем подключение к БД
        engine = create_async_engine(config.database_url)
        async_session = sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)

        # Создаем сервисы
        vpn_service = VPNPanelService(
            panel_url=config.remnawave_panel_url,
            api_key=config.remnawave_api_key
        )

        async with async_session() as session:
            notification_service = NotificationService(session, glv.bot)
            vpn_integration = PaymentVPNIntegration(session, vpn_service)

            # Получаем пользователей для уведомления
            users_to_notify = await get_remnawave_users_to_notify(session)

            logger.info(f"Найдено {len(users_to_notify)} пользователей для уведомления")

            sent_count = 0
            for user_data in users_to_notify:
                try:
                    success = await send_renewal_notification(
                        notification_service,
                        user_data
                    )
                    if success:
                        sent_count += 1

                except Exception as e:
                    logger.error(f"Ошибка отправки уведомления пользователю {user_data['user_id']}: {e}")
                    continue

            logger.info(f"Отправлено уведомлений: {sent_count}/{len(users_to_notify)}")

        await engine.dispose()

    except Exception as e:
        logger.error(f"Ошибка в задаче уведомлений о продлении: {e}")


async def get_remnawave_users_to_notify(session: AsyncSession) -> List[Dict[str, Any]]:
    """Получение пользователей Remnawave для уведомления"""
    try:
        # Получаем подписки, которые истекают в ближайшие 1-2 дня
        tomorrow = datetime.now(timezone.utc) + timedelta(days=1)
        day_after_tomorrow = datetime.now(timezone.utc) + timedelta(days=2)

        query = select(Subscriptions, VPNUsers).join(
            VPNUsers, Subscriptions.user_id == VPNUsers.id
        ).where(
            Subscriptions.is_active == True,
            Subscriptions.end_date >= tomorrow,
            Subscriptions.end_date <= day_after_tomorrow,
            VPNUsers.remnawave_user_id.isnot(None)
        )

        result = await session.execute(query)
        rows = result.fetchall()

        users_to_notify = []
        for subscription, user in rows:
            days_left = (subscription.end_date - datetime.now(timezone.utc)).days

            users_to_notify.append({
                'user_id': user.id,
                'telegram_id': user.telegram_id,
                'username': user.username,
                'subscription_id': subscription.id,
                'end_date': subscription.end_date,
                'days_left': days_left,
                'remnawave_user_id': user.remnawave_user_id
            })

        return users_to_notify

    except Exception as e:
        logger.error(f"Ошибка получения пользователей для уведомления: {e}")
        return []


async def send_renewal_notification(
    notification_service: NotificationService,
    user_data: Dict[str, Any]
) -> bool:
    """Отправка уведомления о продлении конкретному пользователю"""
    try:
        days_left = user_data['days_left']
        username = user_data.get('username', 'Пользователь')

        # Отправляем уведомление об истечении подписки
        success = await notification_service.send_subscription_expiry_warning(
            user_id=user_data['user_id'],
            days_left=days_left
        )

        logger.info(f"Уведомление отправлено пользователю {username} (дней осталось: {days_left})")
        return success

    except Exception as e:
        logger.error(f"Ошибка отправки уведомления: {e}")
        return False
