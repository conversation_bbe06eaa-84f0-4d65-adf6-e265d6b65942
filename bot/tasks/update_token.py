"""
UnveilVPN Shop - Token Update Task (Deprecated)
Задача обновления токена - больше не нужна для Remnawave API
"""

import logging

# from utils import remnawave_api  # Не требуется для Remnawave

logger = logging.getLogger(__name__)

async def update_token():
    """
    Обновление токена (deprecated для Remnawave)
    Remnawave использует API ключи, которые не требуют обновления
    """
    logger.info("Token update task called - not needed for Remnawave API")
    # Remnawave использует статические API ключи, обновление не требуется
    pass