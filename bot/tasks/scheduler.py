#!/usr/bin/env python3
"""
UnveilVPN Shop - Background Tasks Scheduler
Отдельный процесс для запуска фоновых задач Remnawave
"""

import asyncio
import logging
import sys
import os
from pathlib import Path

# Добавляем корневую директорию в путь
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

from bot.common.config import BotConfig
from bot.tasks import register

# Настройка логирования
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('logs/scheduler.log', encoding='utf-8')
    ]
)

logger = logging.getLogger(__name__)


async def main():
    """Основная функция планировщика задач"""
    try:
        logger.info("🚀 Запуск планировщика фоновых задач Remnawave")
        
        # Загружаем конфигурацию
        config = BotConfig.from_env()
        
        # Проверяем настройки Remnawave
        if not config.remnawave_panel_url or not config.remnawave_api_key:
            logger.error("❌ Не настроены параметры Remnawave API")
            return 1
        
        logger.info(f"✅ Remnawave панель: {config.remnawave_panel_url}")
        
        # Запускаем планировщик
        await register()
        
    except KeyboardInterrupt:
        logger.info("🛑 Получен сигнал остановки планировщика")
    except Exception as e:
        logger.error(f"❌ Критическая ошибка планировщика: {e}", exc_info=True)
        return 1
    
    return 0


if __name__ == "__main__":
    # Создаем директорию для логов если её нет
    os.makedirs('logs', exist_ok=True)
    
    # Запускаем планировщик
    sys.exit(asyncio.run(main()))
