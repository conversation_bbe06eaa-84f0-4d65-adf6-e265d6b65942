"""
UnveilVPN Shop - Background Tasks Scheduler
Планировщик фоновых задач для Remnawave интеграции
"""

import aioschedule
import asyncio
import logging

from .update_token import update_token
from .notify_renew_subscription import notify_users_to_renew_sub

import glv

logger = logging.getLogger(__name__)

async def register():
    """Регистрация фоновых задач для Remnawave"""
    try:
        # Задача обновления токена (deprecated для Remnawave, но оставляем для совместимости)
        aioschedule.every(5).minutes.do(update_token)

        # Задача уведомлений о продлении подписки (обновлена для Remnawave)
        notification_time = glv.config.get('RENEW_NOTIFICATION_TIME', '10:00')
        aioschedule.every().day.at(notification_time).do(notify_users_to_renew_sub)

        logger.info("Фоновые задачи зарегистрированы для Remnawave")

        # Основной цикл планировщика
        while True:
            await aioschedule.run_pending()
            await asyncio.sleep(1)

    except Exception as e:
        logger.error(f"Ошибка в планировщике задач: {e}")
        raise