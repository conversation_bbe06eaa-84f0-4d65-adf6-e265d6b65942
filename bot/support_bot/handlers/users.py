"""
UnveilVPN Shop - Support Bot Users Handler
Обработчик управления пользователями для бота поддержки
"""

from aiogram import Router, F
from aiogram.types import Message, CallbackQuery
from aiogram.filters import Command
from typing import Dict, Any

from bot.common.handlers import BaseHandler
from bot.services.user_service import UserService


class UsersHandler(BaseHandler):
    """Обработчик управления пользователями"""
    
    def __init__(self, user_service: UserService = None):
        super().__init__(name='SupportUsersHandler')
        self.user_service = user_service  # Может быть None, создается по мере необходимости
        self.router = Router()
        self._register_handlers()
    
    def _register_handlers(self):
        """Регистрация обработчиков"""
        # Команды управления пользователями
        self.router.message.register(
            self.show_users_menu,
            Command("users")
        )
        
        # Callback обработчики
        self.router.callback_query.register(
            self.handle_users_callback,
            F.data.startswith("users:")
        )

    def _get_user_service(self, session):
        """Получение UserService с актуальной сессией"""
        if self.user_service is None:
            from bot.services.user_service import UserService
            return UserService(session)
        return self.user_service
    
    async def show_users_menu(self, message: Message, **kwargs):
        """Показать меню управления пользователями"""
        try:
            # Проверка прав доступа
            if not await self._check_admin_access(message.from_user.id):
                await message.answer("❌ У вас нет прав доступа к этой функции")
                return
            
            text = "👥 Управление пользователями\n\n"
            text += "Выберите действие:"
            
            # Здесь будет клавиатура управления пользователями
            await message.answer(text)
            
        except Exception as e:
            await self._handle_error(e, message)
    
    async def handle_users_callback(self, callback: CallbackQuery, **kwargs):
        """Обработка callback'ов управления пользователями"""
        try:
            action = callback.data.split(":")[1]
            
            if action == "list":
                await self._show_users_list(callback)
            elif action == "search":
                await self._show_user_search(callback)
            elif action == "stats":
                await self._show_users_stats(callback)
            else:
                await callback.answer("❌ Неизвестное действие")
                
        except Exception as e:
            await self._handle_error(e, callback)
    
    async def _show_users_list(self, callback: CallbackQuery):
        """Показать список пользователей"""
        text = "👥 Список пользователей\n\n"
        text += "Функция в разработке..."
        
        await callback.message.edit_text(text)
        await callback.answer()
    
    async def _show_user_search(self, callback: CallbackQuery):
        """Показать поиск пользователей"""
        text = "🔍 Поиск пользователей\n\n"
        text += "Функция в разработке..."
        
        await callback.message.edit_text(text)
        await callback.answer()
    
    async def _show_users_stats(self, callback: CallbackQuery):
        """Показать статистику пользователей"""
        text = "📊 Статистика пользователей\n\n"
        text += "Функция в разработке..."
        
        await callback.message.edit_text(text)
        await callback.answer()
    
    async def _check_admin_access(self, user_id: int) -> bool:
        """Проверка прав администратора"""
        # Здесь будет проверка прав доступа
        return True  # Временно разрешаем всем
