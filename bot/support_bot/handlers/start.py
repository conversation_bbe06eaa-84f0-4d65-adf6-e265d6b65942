"""
UnveilVPN Shop - Support Start Handler
Обработчик команды /start для бота поддержки
"""

from typing import Dict, Any

from aiogram.types import Message
from aiogram.utils.i18n import gettext as _

from ...common.handlers import CommandHandler
from ...common.utils import MessageFormatter, BotUtils
from ..keyboards import SupportMenuKeyboard


class StartHandler(CommandHandler):
    """
    Обработчик команды /start для бота поддержки
    """
    
    def __init__(self):
        super().__init__(commands=['start'], name='SupportStartHandler')
        self.support_menu_keyboard = SupportMenuKeyboard()
    
    async def handle_command(self, message: Message, **kwargs):
        """Обработка команды /start"""
        try:
            user_data = await self._get_user_data(message)
            
            # Проверяем права сотрудника поддержки
            if not await self._check_support_rights(message.from_user):
                await self._send_access_denied(message)
                return
            
            # Формируем приветственное сообщение
            welcome_text = await self._get_support_welcome_text(user_data)
            
            # Получаем клавиатуру меню поддержки
            keyboard = await self.support_menu_keyboard.get_keyboard(user_data)
            
            await message.answer(welcome_text, reply_markup=keyboard)
            
            # Логируем вход сотрудника поддержки
            self.logger.info(f"Сотрудник поддержки вошел в систему: {message.from_user.id} (@{message.from_user.username})")
            
        except Exception as e:
            self.logger.error(f"Ошибка обработки команды /start в боте поддержки: {e}")
            await self._send_error(message, _("Ошибка при запуске панели поддержки"))
    
    async def _check_support_rights(self, user) -> bool:
        """Проверка прав сотрудника поддержки"""
        return self.config.is_support(user.id)
    
    async def _send_access_denied(self, message: Message):
        """Отправка сообщения об отказе в доступе"""
        text = _("❌ <b>Доступ запрещен</b>\n\nУ вас нет прав для использования панели поддержки.")
        await message.answer(text)
        
        # Логируем попытку несанкционированного доступа
        self.logger.warning(f"Попытка несанкционированного доступа к боту поддержки: {message.from_user.id} (@{message.from_user.username})")
    
    async def _get_support_welcome_text(self, user_data: Dict[str, Any]) -> str:
        """Формирование приветственного текста для сотрудника поддержки"""
        user = user_data['user']
        
        # Получаем статистику поддержки
        stats = await self._get_support_stats()
        
        name = user.first_name or user.username or _("Сотрудник поддержки")
        
        lines = [
            _("🎧 <b>Панель поддержки {shop_name}</b>").format(
                shop_name=self.config.shop_name
            ),
            "",
            _("Добро пожаловать, {name}!").format(name=name),
            "",
            _("📊 <b>Статистика тикетов:</b>"),
            _("🟢 Открытых: {open}").format(open=stats.get('tickets_open', 0)),
            _("🟡 В работе: {in_progress}").format(in_progress=stats.get('tickets_in_progress', 0)),
            _("🔴 Ожидают ответа: {waiting}").format(waiting=stats.get('tickets_waiting', 0)),
            _("✅ Закрытых сегодня: {closed_today}").format(closed_today=stats.get('tickets_closed_today', 0)),
        ]
        
        # Добавляем информацию о назначенных тикетах
        assigned_tickets = stats.get('tickets_assigned_to_me', 0)
        if assigned_tickets > 0:
            lines.extend([
                "",
                _("👤 <b>Ваши тикеты:</b>"),
                _("Назначено вам: {count}").format(count=assigned_tickets),
            ])
        
        # Добавляем срочные уведомления
        urgent_tickets = stats.get('tickets_urgent', 0)
        if urgent_tickets > 0:
            lines.extend([
                "",
                _("🚨 <b>Требует внимания:</b>"),
                _("Срочных тикетов: {count}").format(count=urgent_tickets),
            ])
        
        # Добавляем информацию о рабочем времени
        lines.extend([
            "",
            _("🕐 <b>Рабочее время:</b>"),
            _("Сегодня в сети: {time}").format(time=stats.get('online_time_today', '0:00')),
            _("Обработано тикетов: {count}").format(count=stats.get('tickets_handled_today', 0)),
        ])
        
        lines.extend([
            "",
            _("Выберите раздел для работы:")
        ])
        
        return "\n".join(lines)
    
    async def _get_support_stats(self) -> Dict[str, Any]:
        """Получение статистики поддержки"""
        try:
            from sqlalchemy import select, func
            from ...db.models import SupportTickets
            
            # Здесь будет реальная логика получения статистики из БД
            # Пока возвращаем заглушку
            
            return {
                'tickets_open': 0,
                'tickets_in_progress': 0,
                'tickets_waiting': 0,
                'tickets_closed_today': 0,
                'tickets_assigned_to_me': 0,
                'tickets_urgent': 0,
                'online_time_today': '0:00',
                'tickets_handled_today': 0
            }
            
        except Exception as e:
            self.logger.error(f"Ошибка получения статистики поддержки: {e}")
            return {
                'tickets_open': 0,
                'tickets_in_progress': 0,
                'tickets_waiting': 0,
                'tickets_closed_today': 0,
                'tickets_assigned_to_me': 0,
                'tickets_urgent': 0,
                'online_time_today': 'N/A',
                'tickets_handled_today': 0
            }
