"""
UnveilVPN Shop - Support Bot Menu Handler
Обработчик главного меню для бота поддержки
"""

from typing import Dict, Any

from aiogram.types import Message, CallbackQuery
from aiogram.utils.i18n import gettext as _

from ...common.handlers import <PERSON>u<PERSON>and<PERSON> as BaseMenuHandler
from ...common.utils import MessageFormatter, BotUtils
from ...services.support_service import SupportService
from ..keyboards.tickets import SupportMenuKeyboard, QuickActionsKeyboard


class MenuHandler(BaseMenuHandler):
    """
    Обработчик главного меню бота поддержки
    """
    
    def __init__(self):
        menu_keyboard = SupportMenuKeyboard()
        super().__init__(menu_keyboard, name='SupportMenuHandler')
        
        # Регистрируем дополнительные обработчики
        self._register_menu_callbacks()
    
    def _register_menu_callbacks(self):
        """Регистрация обработчиков callback для пунктов меню"""
        
        # Быстрые действия
        self.router.callback_query.register(
            self.show_quick_actions,
            lambda c: c.data == 'quick_actions'
        )
        
        # Статистика
        self.router.callback_query.register(
            self.show_statistics,
            lambda c: c.data == 'statistics'
        )
        
        # Настройки
        self.router.callback_query.register(
            self.show_settings,
            lambda c: c.data == 'settings'
        )
        
        # Пользователи
        self.router.callback_query.register(
            self.show_users,
            lambda c: c.data == 'users'
        )
    
    async def _get_menu_text(self, user_data: Dict[str, Any]) -> str:
        """Получение текста главного меню"""
        user = user_data['user']
        db_user = user_data['db_user']
        db_session = user_data['db_session']
        
        name = user.first_name or user.username or _("Сотрудник")
        
        lines = [
            _("🎧 <b>Панель поддержки</b>"),
            "",
            _("Добро пожаловать, {name}!").format(name=name),
        ]
        
        try:
            # Получаем статистику тикетов
            support_service = SupportService(db_session)
            
            # Новые тикеты
            new_tickets = await support_service.get_tickets(
                status='open',
                assigned_to_id=None,
                limit=100
            )
            
            # Мои тикеты
            my_tickets = await support_service.get_tickets(
                assigned_to_id=db_user.id,
                limit=100
            )
            
            # Просроченные тикеты
            overdue_tickets = await support_service.get_overdue_tickets()
            
            lines.extend([
                "",
                _("📊 <b>Текущая ситуация:</b>"),
                _("🆕 Новых тикетов: {count}").format(count=len(new_tickets)),
                _("👤 Моих тикетов: {count}").format(count=len(my_tickets)),
                _("⏰ Просроченных: {count}").format(count=len(overdue_tickets)),
            ])
            
            # Предупреждения
            if len(overdue_tickets) > 0:
                lines.extend([
                    "",
                    _("⚠️ <b>Внимание!</b>"),
                    _("Есть просроченные тикеты, требующие внимания!")
                ])
            
            if len(new_tickets) > 5:
                lines.extend([
                    "",
                    _("📈 <b>Высокая нагрузка</b>"),
                    _("Много новых тикетов ожидают назначения")
                ])
        
        except Exception as e:
            self.logger.error(f"Ошибка получения статистики для меню: {e}")
            lines.extend([
                "",
                _("📊 Статистика временно недоступна")
            ])
        
        lines.extend([
            "",
            _("Выберите действие:")
        ])
        
        return "\n".join(lines)
    
    async def show_quick_actions(self, callback: CallbackQuery, **kwargs):
        """Показ быстрых действий"""
        try:
            text = _(
                "⚡ <b>Быстрые действия</b>\n\n"
                "Выберите нужное действие для быстрого доступа:"
            )
            
            keyboard = await QuickActionsKeyboard().get_keyboard()
            
            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Ошибка показа быстрых действий: {e}")
            await self._send_error(callback, _("Ошибка загрузки быстрых действий"))
    
    async def show_statistics(self, callback: CallbackQuery, **kwargs):
        """Показ статистики поддержки"""
        try:
            user_data = await self._get_user_data(callback)
            db_session = user_data['db_session']
            
            support_service = SupportService(db_session)
            stats = await support_service.get_support_statistics()
            
            text = await self._get_statistics_text(stats)
            
            from ..keyboards.tickets import SupportStatsKeyboard
            keyboard = await SupportStatsKeyboard().get_keyboard()
            
            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Ошибка показа статистики: {e}")
            await self._send_error(callback, _("Ошибка загрузки статистики"))
    
    async def show_settings(self, callback: CallbackQuery, **kwargs):
        """Показ настроек"""
        try:
            user_data = await self._get_user_data(callback)
            
            settings_text = await self._get_settings_text(user_data)
            
            from ..keyboards.tickets import NotificationSettingsKeyboard
            keyboard = await NotificationSettingsKeyboard().get_keyboard()
            
            await callback.message.edit_text(settings_text, reply_markup=keyboard)
            await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Ошибка показа настроек: {e}")
            await self._send_error(callback, _("Ошибка загрузки настроек"))
    
    async def show_users(self, callback: CallbackQuery, **kwargs):
        """Показ пользователей"""
        try:
            user_data = await self._get_user_data(callback)
            db_session = user_data['db_session']
            
            # Здесь будет логика получения пользователей
            text = _(
                "👥 <b>Управление пользователями</b>\n\n"
                "Функция в разработке.\n"
                "Скоро здесь будет возможность:\n\n"
                "• Поиск пользователей\n"
                "• Просмотр истории тикетов\n"
                "• Управление подписками\n"
                "• Блокировка пользователей"
            )
            
            from ..keyboards.tickets import BackToMenuKeyboard
            keyboard = await BackToMenuKeyboard().get_keyboard()
            
            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Ошибка показа пользователей: {e}")
            await self._send_error(callback, _("Ошибка загрузки пользователей"))
    
    async def _get_statistics_text(self, stats: Dict[str, Any]) -> str:
        """Формирование текста статистики"""
        lines = [
            _("📊 <b>Статистика поддержки</b>"),
            ""
        ]
        
        # Общая статистика
        total_tickets = stats.get('total_tickets', 0)
        open_tickets = stats.get('open_tickets', 0)
        closed_tickets = stats.get('closed_tickets', 0)
        
        lines.extend([
            _("📈 <b>Общая статистика:</b>"),
            _("Всего тикетов: {total}").format(total=total_tickets),
            _("Открытых: {open}").format(open=open_tickets),
            _("Закрытых: {closed}").format(closed=closed_tickets),
            ""
        ])
        
        # Статистика по статусам
        if 'status_distribution' in stats:
            lines.append(_("📊 <b>По статусам:</b>"))
            for status, count in stats['status_distribution'].items():
                status_name = SupportService.STATUSES.get(status, status)
                lines.append(f"• {status_name}: {count}")
            lines.append("")
        
        # Статистика по категориям
        if 'category_distribution' in stats:
            lines.append(_("📂 <b>По категориям:</b>"))
            for category, count in stats['category_distribution'].items():
                category_name = SupportService.CATEGORIES.get(category, category)
                lines.append(f"• {category_name}: {count}")
            lines.append("")
        
        # Время ответа
        avg_response_time = stats.get('avg_response_time_hours', 0)
        if avg_response_time:
            lines.extend([
                _("⏱ <b>Время ответа:</b>"),
                _("Среднее время: {time:.1f} часов").format(time=avg_response_time),
                ""
            ])
        
        # Просроченные тикеты
        overdue_count = stats.get('overdue_tickets', 0)
        if overdue_count > 0:
            lines.extend([
                _("⚠️ <b>Просроченные тикеты:</b>"),
                _("Количество: {count}").format(count=overdue_count),
                ""
            ])
        
        return "\n".join(lines)
    
    async def _get_settings_text(self, user_data: Dict[str, Any]) -> str:
        """Формирование текста настроек"""
        db_user = user_data['db_user']
        
        lines = [
            _("⚙️ <b>Настройки поддержки</b>"),
            "",
            _("👤 <b>Профиль:</b>"),
            _("ID: {id}").format(id=db_user.id),
        ]
        
        if db_user.username:
            lines.append(_("Username: @{username}").format(username=db_user.username))
        
        lines.extend([
            "",
            _("🔔 <b>Уведомления:</b>"),
            _("Новые тикеты: ✅"),
            _("Просроченные: ✅"),
            _("Назначенные мне: ✅"),
            _("Срочные: ✅"),
            "",
            _("⏰ <b>Рабочее время:</b>"),
            _("Пн-Пт: 09:00 - 18:00"),
            _("Сб-Вс: Выходной"),
            "",
            _("📱 <b>Способ уведомлений:</b>"),
            _("Telegram: ✅"),
            _("Email: ❌"),
            "",
            _("Настройте параметры уведомлений:")
        ])
        
        return "\n".join(lines)
