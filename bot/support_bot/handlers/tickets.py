"""
UnveilVPN Shop - Support Bot Tickets Handler
Обработчик тикетов для бота поддержки
"""

from typing import Dict, Any, List
from uuid import UUID

from aiogram.types import Message, CallbackQuery
from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import State, StatesGroup
from aiogram.utils.i18n import gettext as _

from ...common.handlers import BaseHandler
from ...common.utils import MessageFormatter, BotUtils
from ...common.exceptions import NotFoundError, ValidationError, BusinessLogicError
from ...services.support_service import SupportService
from ..keyboards import (
    TicketListKeyboard, TicketDetailsKeyboard, TicketActionsKeyboard,
    TicketStatusKeyboard, TicketPriorityKeyboard, AssignTicketKeyboard
)


class TicketStates(StatesGroup):
    """Состояния для работы с тикетами"""
    waiting_reply = State()
    waiting_internal_note = State()
    waiting_status_reason = State()


class TicketsHandler(BaseHandler):
    """
    Обработчик тикетов для бота поддержки
    """
    
    def __init__(self):
        super().__init__(name='SupportTicketsHandler')
    
    def _register_handlers(self):
        """Регистрация обработчиков"""
        
        # Основные обработчики
        self.router.callback_query.register(
            self.show_tickets_list,
            lambda c: c.data == 'tickets'
        )
        
        self.router.callback_query.register(
            self.show_new_tickets,
            lambda c: c.data == 'new_tickets'
        )
        
        self.router.callback_query.register(
            self.show_my_tickets,
            lambda c: c.data == 'my_tickets'
        )
        
        self.router.callback_query.register(
            self.show_overdue_tickets,
            lambda c: c.data == 'overdue_tickets'
        )
        
        self.router.callback_query.register(
            self.view_ticket,
            lambda c: c.data.startswith('ticket_')
        )
        
        self.router.callback_query.register(
            self.assign_ticket_to_me,
            lambda c: c.data.startswith('assign_me_')
        )
        
        self.router.callback_query.register(
            self.assign_ticket_to_user,
            lambda c: c.data.startswith('assign_user_')
        )
        
        self.router.callback_query.register(
            self.change_ticket_status,
            lambda c: c.data.startswith('status_')
        )
        
        self.router.callback_query.register(
            self.change_ticket_priority,
            lambda c: c.data.startswith('priority_')
        )
        
        self.router.callback_query.register(
            self.reply_to_ticket,
            lambda c: c.data.startswith('reply_')
        )
        
        self.router.callback_query.register(
            self.add_internal_note,
            lambda c: c.data.startswith('note_')
        )
        
        # Обработчики состояний
        self.router.message.register(
            self.process_reply_input,
            TicketStates.waiting_reply
        )
        
        self.router.message.register(
            self.process_internal_note_input,
            TicketStates.waiting_internal_note
        )
        
        self.router.message.register(
            self.process_status_reason_input,
            TicketStates.waiting_status_reason
        )
    
    async def show_tickets_list(self, callback: CallbackQuery, **kwargs):
        """Показ списка всех тикетов"""
        try:
            user_data = await self._get_user_data(callback)
            db_session = user_data['db_session']
            
            support_service = SupportService(db_session)
            tickets = await support_service.get_tickets(limit=20)
            
            text = await self._get_tickets_list_text(tickets, "Все тикеты")
            keyboard = await TicketListKeyboard(tickets, "all").get_keyboard()
            
            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Ошибка показа списка тикетов: {e}")
            await self._send_error(callback, _("Ошибка загрузки тикетов"))
    
    async def show_new_tickets(self, callback: CallbackQuery, **kwargs):
        """Показ новых тикетов"""
        try:
            user_data = await self._get_user_data(callback)
            db_session = user_data['db_session']
            
            support_service = SupportService(db_session)
            tickets = await support_service.get_tickets(
                status='open',
                assigned_to_id=None,
                limit=20
            )
            
            text = await self._get_tickets_list_text(tickets, "Новые тикеты")
            keyboard = await TicketListKeyboard(tickets, "new").get_keyboard()
            
            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Ошибка показа новых тикетов: {e}")
            await self._send_error(callback, _("Ошибка загрузки новых тикетов"))
    
    async def show_my_tickets(self, callback: CallbackQuery, **kwargs):
        """Показ тикетов назначенных мне"""
        try:
            user_data = await self._get_user_data(callback)
            db_session = user_data['db_session']
            db_user = user_data['db_user']
            
            support_service = SupportService(db_session)
            tickets = await support_service.get_tickets(
                assigned_to_id=db_user.id,
                limit=20
            )
            
            text = await self._get_tickets_list_text(tickets, "Мои тикеты")
            keyboard = await TicketListKeyboard(tickets, "my").get_keyboard()
            
            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Ошибка показа моих тикетов: {e}")
            await self._send_error(callback, _("Ошибка загрузки моих тикетов"))
    
    async def show_overdue_tickets(self, callback: CallbackQuery, **kwargs):
        """Показ просроченных тикетов"""
        try:
            user_data = await self._get_user_data(callback)
            db_session = user_data['db_session']
            
            support_service = SupportService(db_session)
            tickets = await support_service.get_overdue_tickets()
            
            text = await self._get_tickets_list_text(tickets, "Просроченные тикеты")
            keyboard = await TicketListKeyboard(tickets, "overdue").get_keyboard()
            
            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Ошибка показа просроченных тикетов: {e}")
            await self._send_error(callback, _("Ошибка загрузки просроченных тикетов"))
    
    async def view_ticket(self, callback: CallbackQuery, **kwargs):
        """Просмотр конкретного тикета"""
        try:
            # Извлекаем ID тикета
            ticket_id = callback.data.split('_', 1)[1]
            
            user_data = await self._get_user_data(callback)
            db_session = user_data['db_session']
            
            support_service = SupportService(db_session)
            ticket = await support_service.get_ticket_by_id(ticket_id)
            
            if not ticket:
                await callback.answer(_("Тикет не найден"), show_alert=True)
                return
            
            text = await self._get_ticket_details_text(ticket)
            keyboard = await TicketDetailsKeyboard(str(ticket.id), ticket.status).get_keyboard()
            
            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Ошибка просмотра тикета: {e}")
            await self._send_error(callback, _("Ошибка загрузки тикета"))
    
    async def assign_ticket_to_me(self, callback: CallbackQuery, **kwargs):
        """Назначение тикета себе"""
        try:
            # Извлекаем ID тикета
            ticket_id = callback.data.split('_', 2)[2]
            
            user_data = await self._get_user_data(callback)
            db_session = user_data['db_session']
            db_user = user_data['db_user']
            
            support_service = SupportService(db_session)
            ticket = await support_service.assign_ticket(
                ticket_id=ticket_id,
                assigned_to_id=db_user.id,
                assigned_by_id=db_user.id
            )
            
            await db_session.commit()
            
            # Обновляем отображение тикета
            text = await self._get_ticket_details_text(ticket)
            keyboard = await TicketDetailsKeyboard(str(ticket.id), ticket.status).get_keyboard()
            
            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer(_("Тикет назначен вам"))
            
        except Exception as e:
            self.logger.error(f"Ошибка назначения тикета: {e}")
            await self._send_error(callback, _("Ошибка назначения тикета"))

    async def assign_ticket_to_user(self, callback: CallbackQuery, **kwargs):
        """Назначение тикета конкретному пользователю"""
        try:
            # Извлекаем ID тикета и пользователя
            parts = callback.data.split('_')
            if len(parts) >= 3:
                ticket_id = parts[2]
                user_id = parts[3] if len(parts) > 3 else None
            else:
                await self._send_error(callback, _("Неверный формат данных"))
                return

            user_data = await self._get_user_data(callback)
            db_session = user_data['db_session']

            # Здесь будет логика назначения тикета конкретному пользователю
            # Пока что просто отправляем сообщение о том, что функция в разработке

            await callback.message.edit_text(
                f"⚠️ <b>Функция в разработке</b>\n\n"
                f"Назначение тикета {ticket_id} пользователю {user_id}\n"
                f"Эта функция будет реализована в следующих версиях.",
                reply_markup=None
            )
            await callback.answer(_("Функция в разработке"))

        except Exception as e:
            self.logger.error(f"Ошибка назначения тикета пользователю: {e}")
            await self._send_error(callback, _("Ошибка назначения тикета"))

    async def change_ticket_priority(self, callback: CallbackQuery, **kwargs):
        """Изменение приоритета тикета"""
        try:
            # Извлекаем ID тикета и приоритет
            parts = callback.data.split('_')
            if len(parts) >= 3:
                ticket_id = parts[2]
                priority = parts[3] if len(parts) > 3 else None
            else:
                await self._send_error(callback, _("Неверный формат данных"))
                return

            user_data = await self._get_user_data(callback)
            db_session = user_data['db_session']

            # Здесь будет логика изменения приоритета тикета
            # Пока что просто отправляем сообщение о том, что функция в разработке

            priority_names = {
                'low': 'Низкий',
                'normal': 'Обычный',
                'high': 'Высокий',
                'urgent': 'Срочный'
            }

            priority_name = priority_names.get(priority, priority)

            await callback.message.edit_text(
                f"⚠️ <b>Функция в разработке</b>\n\n"
                f"Изменение приоритета тикета {ticket_id} на '{priority_name}'\n"
                f"Эта функция будет реализована в следующих версиях.",
                reply_markup=None
            )
            await callback.answer(_("Функция в разработке"))

        except Exception as e:
            self.logger.error(f"Ошибка изменения приоритета тикета: {e}")
            await self._send_error(callback, _("Ошибка изменения приоритета"))

    async def change_ticket_status(self, callback: CallbackQuery, state: FSMContext, **kwargs):
        """Изменение статуса тикета"""
        try:
            # Извлекаем данные
            parts = callback.data.split('_')
            if len(parts) == 3:
                # status_change_TICKET_ID
                ticket_id = parts[2]
                
                # Показываем клавиатуру выбора статуса
                text = _("Выберите новый статус тикета:")
                keyboard = await TicketStatusKeyboard(ticket_id).get_keyboard()
                
                await callback.message.edit_text(text, reply_markup=keyboard)
                await callback.answer()
                
            elif len(parts) == 4:
                # status_TICKET_ID_NEW_STATUS
                ticket_id = parts[1]
                new_status = parts[3]
                
                # Сохраняем данные для ввода причины
                await state.update_data(ticket_id=ticket_id, new_status=new_status)
                await state.set_state(TicketStates.waiting_status_reason)
                
                text = _(
                    "Введите причину изменения статуса на '{status}' (или отправьте '-' для пропуска):"
                ).format(status=SupportService.STATUSES.get(new_status, new_status))
                
                await callback.message.edit_text(text)
                await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Ошибка изменения статуса тикета: {e}")
            await self._send_error(callback, _("Ошибка изменения статуса"))
    
    async def reply_to_ticket(self, callback: CallbackQuery, state: FSMContext, **kwargs):
        """Ответ на тикет"""
        try:
            # Извлекаем ID тикета
            ticket_id = callback.data.split('_', 1)[1]
            
            # Сохраняем ID тикета и переходим к вводу ответа
            await state.update_data(ticket_id=ticket_id)
            await state.set_state(TicketStates.waiting_reply)
            
            text = _(
                "💬 <b>Ответ на тикет</b>\n\n"
                "Введите ваш ответ пользователю:"
            )
            
            await callback.message.edit_text(text)
            await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Ошибка ответа на тикет: {e}")
            await self._send_error(callback, _("Ошибка обработки ответа"))
    
    async def add_internal_note(self, callback: CallbackQuery, state: FSMContext, **kwargs):
        """Добавление внутренней заметки"""
        try:
            # Извлекаем ID тикета
            ticket_id = callback.data.split('_', 1)[1]
            
            # Сохраняем ID тикета и переходим к вводу заметки
            await state.update_data(ticket_id=ticket_id)
            await state.set_state(TicketStates.waiting_internal_note)
            
            text = _(
                "📝 <b>Внутренняя заметка</b>\n\n"
                "Введите внутреннюю заметку (не видна пользователю):"
            )
            
            await callback.message.edit_text(text)
            await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Ошибка добавления заметки: {e}")
            await self._send_error(callback, _("Ошибка обработки заметки"))
    
    async def process_reply_input(self, message: Message, state: FSMContext, **kwargs):
        """Обработка ввода ответа на тикет"""
        try:
            reply_text = message.text.strip()
            
            # Валидация ответа
            if len(reply_text) < 1:
                await message.answer(_("❌ Ответ не может быть пустым. Попробуйте еще раз:"))
                return
            
            # Получаем данные из состояния
            data = await state.get_data()
            ticket_id = data.get('ticket_id')
            
            user_data = await self._get_user_data(message)
            db_session = user_data['db_session']
            db_user = user_data['db_user']
            
            # Добавляем сообщение в тикет
            support_service = SupportService(db_session)
            ticket = await support_service.add_message_to_ticket(
                ticket_id=ticket_id,
                message=reply_text,
                author_id=db_user.id,
                is_internal=False
            )
            
            await db_session.commit()
            await state.clear()
            
            text = _(
                "✅ <b>Ответ отправлен!</b>\n\n"
                "📋 Тикет: {ticket_number}\n"
                "💬 Ваш ответ добавлен в тикет.\n\n"
                "Пользователь получит уведомление."
            ).format(ticket_number=ticket.ticket_number)
            
            keyboard = await TicketDetailsKeyboard(str(ticket.id), ticket.status).get_keyboard()
            
            await message.answer(text, reply_markup=keyboard)
            
        except Exception as e:
            self.logger.error(f"Ошибка отправки ответа: {e}")
            await message.answer(_("❌ Ошибка отправки ответа"))
    
    async def process_internal_note_input(self, message: Message, state: FSMContext, **kwargs):
        """Обработка ввода внутренней заметки"""
        try:
            note_text = message.text.strip()
            
            # Валидация заметки
            if len(note_text) < 1:
                await message.answer(_("❌ Заметка не может быть пустой. Попробуйте еще раз:"))
                return
            
            # Получаем данные из состояния
            data = await state.get_data()
            ticket_id = data.get('ticket_id')
            
            user_data = await self._get_user_data(message)
            db_session = user_data['db_session']
            db_user = user_data['db_user']
            
            # Добавляем внутреннюю заметку
            support_service = SupportService(db_session)
            ticket = await support_service.add_message_to_ticket(
                ticket_id=ticket_id,
                message=note_text,
                author_id=db_user.id,
                is_internal=True
            )
            
            await db_session.commit()
            await state.clear()
            
            text = _(
                "✅ <b>Внутренняя заметка добавлена!</b>\n\n"
                "📋 Тикет: {ticket_number}\n"
                "📝 Заметка сохранена.\n\n"
                "Заметка видна только сотрудникам поддержки."
            ).format(ticket_number=ticket.ticket_number)
            
            keyboard = await TicketDetailsKeyboard(str(ticket.id), ticket.status).get_keyboard()
            
            await message.answer(text, reply_markup=keyboard)
            
        except Exception as e:
            self.logger.error(f"Ошибка добавления заметки: {e}")
            await message.answer(_("❌ Ошибка добавления заметки"))
    
    async def process_status_reason_input(self, message: Message, state: FSMContext, **kwargs):
        """Обработка ввода причины изменения статуса"""
        try:
            reason = message.text.strip()
            if reason == '-':
                reason = None
            
            # Получаем данные из состояния
            data = await state.get_data()
            ticket_id = data.get('ticket_id')
            new_status = data.get('new_status')
            
            user_data = await self._get_user_data(message)
            db_session = user_data['db_session']
            db_user = user_data['db_user']
            
            # Обновляем статус тикета
            support_service = SupportService(db_session)
            ticket = await support_service.update_ticket_status(
                ticket_id=ticket_id,
                new_status=new_status,
                updated_by_id=db_user.id,
                reason=reason
            )
            
            await db_session.commit()
            await state.clear()
            
            status_name = SupportService.STATUSES.get(new_status, new_status)
            
            text = _(
                "✅ <b>Статус изменен!</b>\n\n"
                "📋 Тикет: {ticket_number}\n"
                "📊 Новый статус: {status}\n"
            ).format(
                ticket_number=ticket.ticket_number,
                status=status_name
            )
            
            if reason:
                text += f"\n💬 Причина: {reason}"
            
            keyboard = await TicketDetailsKeyboard(str(ticket.id), ticket.status).get_keyboard()
            
            await message.answer(text, reply_markup=keyboard)
            
        except Exception as e:
            self.logger.error(f"Ошибка изменения статуса: {e}")
            await message.answer(_("❌ Ошибка изменения статуса"))
    
    # Вспомогательные методы
    
    async def _get_tickets_list_text(self, tickets: List, title: str) -> str:
        """Формирование текста списка тикетов"""
        lines = [
            f"🎫 <b>{title}</b>",
            ""
        ]
        
        if not tickets:
            lines.extend([
                _("Тикетов не найдено."),
            ])
            return "\n".join(lines)
        
        for ticket in tickets[:10]:  # Показываем первые 10
            status_emoji = {
                'open': '🟡',
                'in_progress': '🔵',
                'waiting_user': '🟠',
                'closed': '🟢'
            }.get(ticket.status, '⚪')
            
            priority_emoji = {
                'low': '🟢',
                'medium': '🟡',
                'high': '🟠',
                'urgent': '🔴'
            }.get(ticket.priority, '⚪')
            
            lines.append(
                f"{status_emoji}{priority_emoji} <code>{ticket.ticket_number}</code>"
            )
            lines.append(f"   {ticket.subject[:40]}...")
            lines.append(f"   {ticket.created_at.strftime('%d.%m.%Y %H:%M')}")
            lines.append("")
        
        if len(tickets) > 10:
            lines.append(f"... и еще {len(tickets) - 10} тикетов")
        
        return "\n".join(lines)
    
    async def _get_ticket_details_text(self, ticket) -> str:
        """Формирование текста деталей тикета"""
        lines = [
            f"🎫 <b>Тикет {ticket.ticket_number}</b>",
            "",
            f"📝 Тема: {ticket.subject}",
            f"📂 Категория: {SupportService.CATEGORIES.get(ticket.category, ticket.category)}",
            f"📊 Статус: {SupportService.STATUSES.get(ticket.status, ticket.status)}",
            f"⚡ Приоритет: {SupportService.PRIORITIES.get(ticket.priority, ticket.priority)}",
            f"📅 Создан: {ticket.created_at.strftime('%d.%m.%Y %H:%M')}",
        ]
        
        if ticket.assigned_to_id:
            lines.append(f"👤 Назначен: ID {ticket.assigned_to_id}")
        else:
            lines.append("👤 Не назначен")
        
        # Показываем сообщения
        if ticket.messages:
            lines.append("")
            lines.append("💬 <b>Сообщения:</b>")
            lines.append("")
            
            # Показываем последние 3 сообщения
            recent_messages = ticket.messages[-3:] if len(ticket.messages) > 3 else ticket.messages
            
            for msg in recent_messages:
                is_internal = msg.get('is_internal', False)
                author_type = "Внутренняя заметка" if is_internal else (
                    "Поддержка" if str(msg.get('author_id')) != str(ticket.user_id) else "Пользователь"
                )
                
                from datetime import datetime
                created_at = datetime.fromisoformat(msg.get('created_at', ''))
                
                prefix = "🔒" if is_internal else "💬"
                lines.append(f"{prefix} <b>{author_type}</b> ({created_at.strftime('%d.%m %H:%M')}):")
                lines.append(msg.get('content', '')[:200] + ('...' if len(msg.get('content', '')) > 200 else ''))
                lines.append("")
        
        return "\n".join(lines)
