"""
UnveilVPN Shop - Support Bot Main
Точка входа для бота поддержки
"""

import asyncio
import logging
import sys
from pathlib import Path

# Добавляем путь к корню проекта
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

from bot.common.config import init_config
from bot.support_bot.app import SupportBotApplication


async def main():
    """Основная функция запуска бота поддержки"""
    try:
        # Инициализируем конфигурацию
        config = init_config('support')
        
        # Проверяем наличие сотрудников поддержки
        if not config.support_user_ids:
            logging.error("❌ Не настроены ID сотрудников поддержки! Установите SUPPORT_USER_IDS в переменных окружения.")
            return 1
        
        # Создаем и запускаем приложение
        app = SupportBotApplication(config)
        await app.run()
        
    except KeyboardInterrupt:
        logging.info("Получен сигнал остановки")
    except Exception as e:
        logging.error(f"Критическая ошибка: {e}", exc_info=True)
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
