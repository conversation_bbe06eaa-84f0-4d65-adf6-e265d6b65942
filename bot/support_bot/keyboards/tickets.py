"""
UnveilVPN Shop - Support Bot Tickets Keyboards
Клавиатуры для работы с тикетами в боте поддержки
"""

from typing import Dict, Any, List

from aiogram.types import InlineKeyboardMarkup
from aiogram.utils.i18n import gettext as _

from ...common.keyboards import InlineKeyboard
from ...services.support_service import SupportService


class SupportMenuKeyboard(InlineKeyboard):
    """
    Клавиатура главного меню бота поддержки
    """
    
    def __init__(self):
        super().__init__(max_width=2)
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры главного меню"""
        
        # Основные разделы
        self.add_button(_("🎫 Все тикеты"), "tickets")
        self.add_button(_("🆕 Новые тикеты"), "new_tickets")
        
        self.add_button(_("👤 Мои тикеты"), "my_tickets")
        self.add_button(_("⏰ Просроченные"), "overdue_tickets")
        
        # Пользователи и статистика
        self.add_button(_("👥 Пользователи"), "users")
        self.add_button(_("📊 Статистика"), "statistics")
        
        # Настройки
        self.add_button(_("⚙️ Настройки"), "settings")
        
        return self.build()


class TicketListKeyboard(InlineKeyboard):
    """Клавиатура списка тикетов"""
    
    def __init__(self, tickets: List, list_type: str = "all"):
        super().__init__(max_width=1)
        self.tickets = tickets
        self.list_type = list_type
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры списка тикетов"""
        
        # Кнопки для каждого тикета
        for ticket in self.tickets[:10]:  # Максимум 10 тикетов
            status_emoji = self._get_status_emoji(ticket.status)
            priority_emoji = self._get_priority_emoji(ticket.priority)
            
            button_text = f"{status_emoji}{priority_emoji} {ticket.ticket_number} - {ticket.subject[:25]}..."
            self.add_button(button_text, f"ticket_{ticket.id}")
        
        # Навигация
        if len(self.tickets) > 10:
            self.add_button(_("📄 Показать еще"), f"more_{self.list_type}")
        
        # Действия
        if self.list_type == "new":
            self.add_button(_("🔄 Обновить"), "new_tickets")
        elif self.list_type == "my":
            self.add_button(_("🔄 Обновить"), "my_tickets")
        elif self.list_type == "overdue":
            self.add_button(_("🔄 Обновить"), "overdue_tickets")
        else:
            self.add_button(_("🔄 Обновить"), "tickets")
        
        # Возврат в главное меню
        self.add_button(_("🏠 Главное меню"), "main_menu")
        
        return self.build()
    
    def _get_status_emoji(self, status: str) -> str:
        """Получение эмодзи для статуса"""
        emojis = {
            'open': '🟡',
            'in_progress': '🔵',
            'waiting_user': '🟠',
            'closed': '🟢'
        }
        return emojis.get(status, '⚪')
    
    def _get_priority_emoji(self, priority: str) -> str:
        """Получение эмодзи для приоритета"""
        emojis = {
            'low': '🟢',
            'medium': '🟡',
            'high': '🟠',
            'urgent': '🔴'
        }
        return emojis.get(priority, '⚪')


class TicketDetailsKeyboard(InlineKeyboard):
    """Клавиатура деталей тикета"""
    
    def __init__(self, ticket_id: str, ticket_status: str):
        super().__init__(max_width=2)
        self.ticket_id = ticket_id
        self.ticket_status = ticket_status
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры деталей тикета"""
        
        # Основные действия
        self.add_button(_("💬 Ответить"), f"reply_{self.ticket_id}")
        self.add_button(_("📝 Заметка"), f"note_{self.ticket_id}")
        
        # Управление тикетом
        if self.ticket_status != 'closed':
            self.add_button(_("📊 Статус"), f"status_change_{self.ticket_id}")
            self.add_button(_("⚡ Приоритет"), f"priority_change_{self.ticket_id}")
        
        # Назначение
        self.add_button(_("👤 Назначить мне"), f"assign_me_{self.ticket_id}")
        self.add_button(_("👥 Назначить другому"), f"assign_user_{self.ticket_id}")
        
        # Навигация
        self.add_button(_("🔄 Обновить"), f"ticket_{self.ticket_id}")
        self.add_button(_("⬅️ К списку"), "tickets")
        
        return self.build()


class TicketActionsKeyboard(InlineKeyboard):
    """Клавиатура действий с тикетом"""
    
    def __init__(self, ticket_id: str):
        super().__init__(max_width=2)
        self.ticket_id = ticket_id
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры действий"""
        
        # Быстрые действия
        self.add_button(_("✅ Решен"), f"status_{self.ticket_id}_closed")
        self.add_button(_("🔄 В работе"), f"status_{self.ticket_id}_in_progress")
        
        self.add_button(_("⏳ Ждем клиента"), f"status_{self.ticket_id}_waiting_user")
        self.add_button(_("🔴 Срочный"), f"priority_{self.ticket_id}_urgent")
        
        # Возврат
        self.add_button(_("⬅️ Назад"), f"ticket_{self.ticket_id}")
        
        return self.build()


class TicketStatusKeyboard(InlineKeyboard):
    """Клавиатура выбора статуса тикета"""
    
    def __init__(self, ticket_id: str):
        super().__init__(max_width=2)
        self.ticket_id = ticket_id
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры статусов"""
        
        # Статусы из сервиса
        statuses = SupportService.STATUSES
        
        for status_key, status_name in statuses.items():
            # Добавляем эмодзи для статусов
            emoji = self._get_status_emoji(status_key)
            button_text = f"{emoji} {status_name}"
            self.add_button(button_text, f"status_{self.ticket_id}_{status_key}")
        
        # Возврат
        self.add_button(_("⬅️ Назад"), f"ticket_{self.ticket_id}")
        
        return self.build()
    
    def _get_status_emoji(self, status: str) -> str:
        """Получение эмодзи для статуса"""
        emojis = {
            'open': '🟡',
            'in_progress': '🔵',
            'waiting_user': '🟠',
            'closed': '🟢'
        }
        return emojis.get(status, '⚪')


class TicketPriorityKeyboard(InlineKeyboard):
    """Клавиатура выбора приоритета тикета"""
    
    def __init__(self, ticket_id: str):
        super().__init__(max_width=2)
        self.ticket_id = ticket_id
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры приоритетов"""
        
        # Приоритеты из сервиса
        priorities = SupportService.PRIORITIES
        
        for priority_key, priority_name in priorities.items():
            # Добавляем эмодзи для приоритетов
            emoji = self._get_priority_emoji(priority_key)
            button_text = f"{emoji} {priority_name}"
            self.add_button(button_text, f"priority_{self.ticket_id}_{priority_key}")
        
        # Возврат
        self.add_button(_("⬅️ Назад"), f"ticket_{self.ticket_id}")
        
        return self.build()
    
    def _get_priority_emoji(self, priority: str) -> str:
        """Получение эмодзи для приоритета"""
        emojis = {
            'low': '🟢',
            'medium': '🟡',
            'high': '🟠',
            'urgent': '🔴'
        }
        return emojis.get(priority, '⚪')


class AssignTicketKeyboard(InlineKeyboard):
    """Клавиатура назначения тикета"""
    
    def __init__(self, ticket_id: str, support_users: List = None):
        super().__init__(max_width=1)
        self.ticket_id = ticket_id
        self.support_users = support_users or []
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры назначения"""
        
        # Список сотрудников поддержки
        for user in self.support_users:
            name = user.get('name', f"ID {user.get('id')}")
            self.add_button(f"👤 {name}", f"assign_{self.ticket_id}_{user.get('id')}")
        
        # Снять назначение
        self.add_button(_("❌ Снять назначение"), f"unassign_{self.ticket_id}")
        
        # Возврат
        self.add_button(_("⬅️ Назад"), f"ticket_{self.ticket_id}")
        
        return self.build()


class TicketFiltersKeyboard(InlineKeyboard):
    """Клавиатура фильтров тикетов"""
    
    def __init__(self):
        super().__init__(max_width=2)
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры фильтров"""
        
        # Фильтры по статусу
        self.add_button(_("🟡 Открытые"), "filter_status_open")
        self.add_button(_("🔵 В работе"), "filter_status_in_progress")
        
        self.add_button(_("🟠 Ждем клиента"), "filter_status_waiting_user")
        self.add_button(_("🟢 Закрытые"), "filter_status_closed")
        
        # Фильтры по приоритету
        self.add_button(_("🔴 Срочные"), "filter_priority_urgent")
        self.add_button(_("🟠 Высокий"), "filter_priority_high")
        
        # Фильтры по категории
        self.add_button(_("🔧 Технические"), "filter_category_technical")
        self.add_button(_("💳 Платежи"), "filter_category_billing")
        
        # Сброс фильтров
        self.add_button(_("🔄 Сбросить"), "filter_reset")
        
        # Возврат
        self.add_button(_("⬅️ К тикетам"), "tickets")
        
        return self.build()


class QuickActionsKeyboard(InlineKeyboard):
    """Клавиатура быстрых действий"""
    
    def __init__(self):
        super().__init__(max_width=2)
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры быстрых действий"""
        
        # Быстрые действия
        self.add_button(_("🆕 Новые тикеты"), "new_tickets")
        self.add_button(_("⏰ Просроченные"), "overdue_tickets")
        
        self.add_button(_("👤 Мои тикеты"), "my_tickets")
        self.add_button(_("🔴 Срочные"), "urgent_tickets")
        
        # Статистика
        self.add_button(_("📊 Статистика дня"), "stats_today")
        self.add_button(_("📈 Статистика недели"), "stats_week")
        
        # Возврат
        self.add_button(_("🏠 Главное меню"), "main_menu")
        
        return self.build()


class SupportStatsKeyboard(InlineKeyboard):
    """Клавиатура статистики поддержки"""
    
    def __init__(self):
        super().__init__(max_width=2)
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры статистики"""
        
        # Периоды статистики
        self.add_button(_("📅 Сегодня"), "stats_today")
        self.add_button(_("📅 Вчера"), "stats_yesterday")
        
        self.add_button(_("📅 Эта неделя"), "stats_week")
        self.add_button(_("📅 Этот месяц"), "stats_month")
        
        # Детальная статистика
        self.add_button(_("👥 По сотрудникам"), "stats_agents")
        self.add_button(_("📂 По категориям"), "stats_categories")
        
        self.add_button(_("⏱ Время ответа"), "stats_response_time")
        self.add_button(_("📊 SLA метрики"), "stats_sla")
        
        # Экспорт
        self.add_button(_("📄 Экспорт отчета"), "export_report")
        
        # Возврат
        self.add_button(_("⬅️ Назад"), "main_menu")
        
        return self.build()


class NotificationSettingsKeyboard(InlineKeyboard):
    """Клавиатура настроек уведомлений"""
    
    def __init__(self):
        super().__init__(max_width=1)
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры настроек уведомлений"""
        
        # Настройки уведомлений
        self.add_button(_("🔔 Новые тикеты: ВКЛ"), "toggle_new_tickets")
        self.add_button(_("⏰ Просроченные: ВКЛ"), "toggle_overdue")
        self.add_button(_("👤 Назначенные мне: ВКЛ"), "toggle_assigned")
        self.add_button(_("🔴 Срочные: ВКЛ"), "toggle_urgent")
        
        # Время работы
        self.add_button(_("🕐 Рабочие часы"), "working_hours")
        self.add_button(_("📱 Способ уведомлений"), "notification_method")
        
        # Возврат
        self.add_button(_("⬅️ Назад"), "settings")
        
        return self.build()


class BackToMenuKeyboard(InlineKeyboard):
    """Клавиатура возврата в меню"""
    
    def __init__(self):
        super().__init__(max_width=1)
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры возврата"""
        
        self.add_button(_("🏠 Главное меню"), "main_menu")
        
        return self.build()
