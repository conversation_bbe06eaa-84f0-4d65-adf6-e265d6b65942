"""
UnveilVPN Shop - Support Menu Keyboard
Клавиатура главного меню бота поддержки
"""

from typing import Dict, Any

from aiogram.types import InlineKeyboardMarkup
from aiogram.utils.i18n import gettext as _

from ...common.keyboards import InlineKeyboard


class SupportMenuKeyboard(InlineKeyboard):
    """
    Клавиатура главного меню бота поддержки
    """
    
    def __init__(self):
        super().__init__(max_width=2)
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры меню поддержки"""
        
        # Основные разделы работы с тикетами
        self.add_button(_("🎫 Активные тикеты"), "support_active_tickets")
        self.add_button(_("📋 Все тикеты"), "support_all_tickets")
        
        self.add_button(_("👤 Мои тикеты"), "support_my_tickets")
        self.add_button(_("🚨 Срочные"), "support_urgent_tickets")
        
        # Работа с пользователями
        self.add_button(_("👥 Поиск пользователя"), "support_find_user")
        self.add_button(_("💳 Поиск платежа"), "support_find_payment")
        
        # Статистика и отчеты
        self.add_button(_("📊 Статистика"), "support_statistics")
        self.add_button(_("📈 Мои показатели"), "support_my_stats")
        
        # Быстрые действия
        self.add_button(_("⚡ Быстрые ответы"), "support_quick_replies")
        self.add_button(_("📝 Шаблоны"), "support_templates")
        
        # Настройки и инструменты
        self.add_button(_("⚙️ Настройки"), "support_settings")
        self.add_button(_("📚 База знаний"), "support_knowledge_base")
        
        return self.build()


class TicketListKeyboard(InlineKeyboard):
    """Клавиатура списка тикетов"""
    
    def __init__(self, tickets_type: str = "all"):
        super().__init__(max_width=1)
        self.tickets_type = tickets_type
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры списка тикетов"""
        
        # Фильтры
        if self.tickets_type == "all":
            self.add_button(_("🟢 Только открытые"), "filter_open")
            self.add_button(_("🟡 В работе"), "filter_in_progress")
            self.add_button(_("🔴 Ожидают ответа"), "filter_waiting")
        
        # Сортировка
        self.add_button(_("📅 По дате"), "sort_by_date")
        self.add_button(_("⚡ По приоритету"), "sort_by_priority")
        
        # Действия
        self.add_button(_("🔄 Обновить"), "refresh_tickets")
        self.add_button(_("📤 Экспорт"), "export_tickets")
        
        # Возврат в главное меню
        self.add_main_menu_button()
        
        return self.build()


class TicketActionsKeyboard(InlineKeyboard):
    """Клавиатура действий с тикетом"""
    
    def __init__(self, ticket_id: str, ticket_status: str = "open"):
        super().__init__(max_width=2)
        self.ticket_id = ticket_id
        self.ticket_status = ticket_status
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры действий с тикетом"""
        
        # Основные действия
        if self.ticket_status == "open":
            self.add_button(_("✋ Взять в работу"), f"take_ticket_{self.ticket_id}")
        elif self.ticket_status == "in_progress":
            self.add_button(_("💬 Ответить"), f"reply_ticket_{self.ticket_id}")
            self.add_button(_("✅ Закрыть"), f"close_ticket_{self.ticket_id}")
        
        # Дополнительные действия
        self.add_button(_("👤 Профиль пользователя"), f"user_profile_{self.ticket_id}")
        self.add_button(_("📋 История"), f"ticket_history_{self.ticket_id}")
        
        # Управление тикетом
        self.add_button(_("🏷️ Изменить категорию"), f"change_category_{self.ticket_id}")
        self.add_button(_("⚡ Изменить приоритет"), f"change_priority_{self.ticket_id}")
        
        # Передача тикета
        self.add_button(_("👥 Передать коллеге"), f"transfer_ticket_{self.ticket_id}")
        self.add_button(_("🔝 Эскалация"), f"escalate_ticket_{self.ticket_id}")
        
        # Возврат к списку
        self.add_button(_("⬅️ К списку тикетов"), "back_to_tickets")
        
        return self.build()


class QuickRepliesKeyboard(InlineKeyboard):
    """Клавиатура быстрых ответов"""
    
    def __init__(self):
        super().__init__(max_width=1)
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры быстрых ответов"""
        
        # Категории быстрых ответов
        self.add_button(_("💳 Проблемы с оплатой"), "quick_payment_issues")
        self.add_button(_("🔧 Технические проблемы"), "quick_technical_issues")
        self.add_button(_("📱 Настройка приложений"), "quick_app_setup")
        self.add_button(_("🌐 Проблемы с подключением"), "quick_connection_issues")
        self.add_button(_("❓ Общие вопросы"), "quick_general_questions")
        self.add_button(_("💰 Возврат средств"), "quick_refund_requests")
        
        # Управление шаблонами
        self.add_button(_("➕ Добавить шаблон"), "add_template")
        self.add_button(_("✏️ Редактировать шаблоны"), "edit_templates")
        
        # Возврат в главное меню
        self.add_main_menu_button()
        
        return self.build()


class UserSearchKeyboard(InlineKeyboard):
    """Клавиатура поиска пользователей"""
    
    def __init__(self):
        super().__init__(max_width=2)
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры поиска пользователей"""
        
        # Типы поиска
        self.add_button(_("🆔 По Telegram ID"), "search_by_telegram_id")
        self.add_button(_("👤 По username"), "search_by_username")
        
        self.add_button(_("📧 По email"), "search_by_email")
        self.add_button(_("📱 По номеру телефона"), "search_by_phone")
        
        # Дополнительные фильтры
        self.add_button(_("💰 Платящие клиенты"), "search_paid_users")
        self.add_button(_("🆓 Бесплатные пользователи"), "search_free_users")
        
        self.add_button(_("🚫 Заблокированные"), "search_banned_users")
        self.add_button(_("📅 По дате регистрации"), "search_by_registration")
        
        # Возврат в главное меню
        self.add_main_menu_button()
        
        return self.build()


class SupportStatsKeyboard(InlineKeyboard):
    """Клавиатура статистики поддержки"""
    
    def __init__(self):
        super().__init__(max_width=2)
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры статистики поддержки"""
        
        # Периоды статистики
        self.add_button(_("📅 Сегодня"), "stats_today")
        self.add_button(_("📊 Эта неделя"), "stats_week")
        
        self.add_button(_("📈 Этот месяц"), "stats_month")
        self.add_button(_("📋 Весь период"), "stats_all_time")
        
        # Типы статистики
        self.add_button(_("🎫 По тикетам"), "stats_tickets")
        self.add_button(_("👥 По сотрудникам"), "stats_agents")
        
        self.add_button(_("⏱️ Время ответа"), "stats_response_time")
        self.add_button(_("😊 Удовлетворенность"), "stats_satisfaction")
        
        # Экспорт
        self.add_button(_("📤 Экспорт отчета"), "export_stats")
        self.add_button(_("📊 Графики"), "stats_charts")
        
        # Возврат в главное меню
        self.add_main_menu_button()
        
        return self.build()
