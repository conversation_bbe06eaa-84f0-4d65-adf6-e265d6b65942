"""
UnveilVPN Shop - Support Bot Application
Основное приложение бота поддержки
"""

import logging
from typing import List

from ..common.base import BaseBotApplication
from ..common.config import BotConfig
from ..common.middleware import AuthMiddleware
from .handlers import (
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    StatisticsHandler,
    NotificationsHandler
)


class SupportBotApplication(BaseBotApplication):
    """
    Приложение бота поддержки
    """
    
    def __init__(self, config: BotConfig):
        super().__init__(config)
        self.logger = logging.getLogger("SupportBot")
    
    def get_bot_type(self) -> str:
        """Возвращает тип бота"""
        return "support"
    
    def get_bot_specific_middlewares(self) -> List:
        """Возвращает специфичные для бота поддержки middleware"""
        return [
            AuthMiddleware('support')  # Проверка прав сотрудника поддержки
        ]
    
    def get_handlers(self) -> List:
        """Возвращает список обработчиков для бота поддержки"""
        # UserService будет создан в обработчике с актуальной сессией
        return [
            StartHandler(),
            MenuHandler(),
            TicketsHandler(),
            UsersHandler(user_service=None),  # Передаем None, сервис создается в обработчике
            ChatHandler(),
            StatisticsHandler(),
            NotificationsHandler()
        ]
    
    def get_webhook_routes(self) -> List[tuple]:
        """Возвращает webhook маршруты для бота поддержки"""
        return [
            # API endpoints для функций поддержки
            ("POST", "/support/ticket/create", self._handle_create_ticket_api),
            ("POST", "/support/ticket/update", self._handle_update_ticket_api),
            ("GET", "/support/tickets", self._handle_get_tickets_api),
            ("POST", "/support/notify", self._handle_notify_support_api),
        ]
    
    async def on_startup(self):
        """Действия при запуске бота поддержки"""
        self.logger.info("🚀 Бот поддержки запущен")
        
        # Проверяем права сотрудников поддержки
        await self._check_support_permissions()
        
        # Инициализируем компоненты поддержки
        await self._initialize_support_components()
        
        # Запускаем систему уведомлений
        await self._start_notification_system()
    
    async def on_shutdown(self):
        """Действия при остановке бота поддержки"""
        self.logger.info("🛑 Бот поддержки остановлен")
        
        # Останавливаем систему уведомлений
        await self._stop_notification_system()
    
    async def _check_support_permissions(self):
        """Проверка прав сотрудников поддержки"""
        if not self.config.support_user_ids:
            self.logger.warning("⚠️ Не настроены ID сотрудников поддержки!")
            return
        
        self.logger.info(f"✅ Настроено {len(self.config.support_user_ids)} сотрудников поддержки")
        for support_id in self.config.support_user_ids:
            self.logger.info(f"  - Сотрудник поддержки: {support_id}")
    
    async def _initialize_support_components(self):
        """Инициализация компонентов поддержки"""
        try:
            # Здесь можно добавить инициализацию специфичных для поддержки компонентов
            # Например, подключение к системам CRM, настройка интеграций и т.д.
            
            self.logger.info("✅ Компоненты поддержки инициализированы")
            
        except Exception as e:
            self.logger.error(f"❌ Ошибка инициализации компонентов поддержки: {e}")
    
    async def _start_notification_system(self):
        """Запуск системы уведомлений"""
        try:
            # Здесь будет логика запуска системы уведомлений о новых тикетах
            self.logger.info("✅ Система уведомлений запущена")
            
        except Exception as e:
            self.logger.error(f"❌ Ошибка запуска системы уведомлений: {e}")
    
    async def _stop_notification_system(self):
        """Остановка системы уведомлений"""
        try:
            # Здесь будет логика остановки системы уведомлений
            self.logger.info("✅ Система уведомлений остановлена")
            
        except Exception as e:
            self.logger.error(f"❌ Ошибка остановки системы уведомлений: {e}")
    
    async def _handle_create_ticket_api(self, request):
        """API endpoint для создания тикета"""
        try:
            from aiohttp import web
            
            data = await request.json()
            
            # Здесь будет логика создания тикета
            ticket = await self._create_support_ticket(data)
            
            # Уведомляем сотрудников поддержки
            await self._notify_support_about_new_ticket(ticket)
            
            return web.json_response({
                "success": True,
                "ticket_id": ticket.get('id'),
                "message": "Тикет создан успешно"
            })
            
        except Exception as e:
            self.logger.error(f"Ошибка API создания тикета: {e}")
            return web.json_response({
                "success": False,
                "error": str(e)
            }, status=500)
    
    async def _handle_update_ticket_api(self, request):
        """API endpoint для обновления тикета"""
        try:
            from aiohttp import web
            
            data = await request.json()
            ticket_id = data.get('ticket_id')
            
            if not ticket_id:
                return web.json_response({
                    "success": False,
                    "error": "Не указан ID тикета"
                }, status=400)
            
            # Здесь будет логика обновления тикета
            ticket = await self._update_support_ticket(ticket_id, data)
            
            return web.json_response({
                "success": True,
                "ticket": ticket,
                "message": "Тикет обновлен успешно"
            })
            
        except Exception as e:
            self.logger.error(f"Ошибка API обновления тикета: {e}")
            return web.json_response({
                "success": False,
                "error": str(e)
            }, status=500)
    
    async def _handle_get_tickets_api(self, request):
        """API endpoint для получения списка тикетов"""
        try:
            from aiohttp import web
            
            # Параметры запроса
            status = request.query.get('status')
            assigned_to = request.query.get('assigned_to')
            limit = int(request.query.get('limit', 50))
            offset = int(request.query.get('offset', 0))
            
            # Здесь будет логика получения тикетов
            tickets = await self._get_support_tickets(status, assigned_to, limit, offset)
            
            return web.json_response({
                "success": True,
                "tickets": tickets,
                "total": len(tickets)
            })
            
        except Exception as e:
            self.logger.error(f"Ошибка API получения тикетов: {e}")
            return web.json_response({
                "success": False,
                "error": str(e)
            }, status=500)
    
    async def _handle_notify_support_api(self, request):
        """API endpoint для уведомления поддержки"""
        try:
            from aiohttp import web
            
            data = await request.json()
            message = data.get('message')
            priority = data.get('priority', 'normal')
            
            if not message:
                return web.json_response({
                    "success": False,
                    "error": "Сообщение не может быть пустым"
                }, status=400)
            
            # Отправляем уведомление сотрудникам поддержки
            await self._send_support_notification(message, priority)
            
            return web.json_response({
                "success": True,
                "message": "Уведомление отправлено"
            })
            
        except Exception as e:
            self.logger.error(f"Ошибка API уведомления: {e}")
            return web.json_response({
                "success": False,
                "error": str(e)
            }, status=500)
    
    async def _create_support_ticket(self, data):
        """Создание тикета поддержки"""
        # Заглушка для создания тикета
        return {
            'id': 'TICKET_001',
            'user_id': data.get('user_id'),
            'subject': data.get('subject'),
            'message': data.get('message'),
            'status': 'open',
            'priority': data.get('priority', 'normal')
        }
    
    async def _update_support_ticket(self, ticket_id, data):
        """Обновление тикета поддержки"""
        # Заглушка для обновления тикета
        return {
            'id': ticket_id,
            'status': data.get('status'),
            'assigned_to': data.get('assigned_to'),
            'updated_at': 'now'
        }
    
    async def _get_support_tickets(self, status, assigned_to, limit, offset):
        """Получение списка тикетов"""
        # Заглушка для получения тикетов
        return []
    
    async def _notify_support_about_new_ticket(self, ticket):
        """Уведомление поддержки о новом тикете"""
        # Заглушка для уведомления
        pass
    
    async def _send_support_notification(self, message, priority):
        """Отправка уведомления сотрудникам поддержки"""
        # Заглушка для уведомления
        pass
