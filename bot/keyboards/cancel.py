from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton
from aiogram.utils.i18n import gettext as _

class CancelKeyboard:
    """
    Клавиатура с кнопкой "Отмена"
    """
    def __init__(self):
        pass

    async def get_keyboard(self) -> InlineKeyboardMarkup:
        keyboard = [
            [
                InlineKeyboardButton(text=_("Отмена"), callback_data="cancel_action")
            ]
        ]
        return InlineKeyboardMarkup(inline_keyboard=keyboard)
