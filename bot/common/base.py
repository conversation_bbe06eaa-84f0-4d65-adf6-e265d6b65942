"""
UnveilVPN Shop - Base Bot Application
Базовый класс для всех ботов с общей функциональностью
"""

import asyncio
import logging
import sys
from abc import ABC, abstractmethod
from pathlib import Path
from typing import Optional, Dict, Any, List

from aiogram import Bo<PERSON>, Dispatcher, enums
from aiogram.fsm.storage.memory import MemoryStorage
from aiogram.fsm.storage.redis import RedisStorage
from aiogram.utils.i18n import I18n
from aiohttp import web
from aiogram.webhook.aiohttp_server import SimpleRequestHandler, setup_application

from .config import BotConfig
from .middleware import (
    DatabaseMiddleware,
    LoggingMiddleware,
    I18nMiddleware,
    ErrorHandlerMiddleware
)
from .exceptions import BotException


class BaseBotApplication(ABC):
    """
    Базовый класс приложения бота с общей функциональностью
    """
    
    def __init__(self, config: BotConfig):
        self.config = config
        self.bot: Optional[Bot] = None
        self.dp: Optional[Dispatcher] = None
        self.storage = None
        self.app: Optional[web.Application] = None
        self.i18n: Optional[I18n] = None
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # Настройка логирования
        self._setup_logging()
    
    def _setup_logging(self):
        """Настройка логирования"""
        logging.basicConfig(
            level=getattr(logging, self.config.log_level.upper()),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            stream=sys.stdout
        )
    
    def _create_storage(self):
        """Создание хранилища FSM"""
        if self.config.redis_url:
            # Используем Redis для продакшена
            from redis.asyncio import Redis
            redis = Redis.from_url(self.config.redis_url)
            self.storage = RedisStorage(redis=redis)
            self.logger.info("Используется Redis storage")
        else:
            # Memory storage для разработки
            self.storage = MemoryStorage()
            self.logger.info("Используется Memory storage")
    
    def _create_bot_and_dispatcher(self):
        """Создание бота и диспетчера"""
        self.bot = Bot(
            token=self.config.bot_token,
            parse_mode=enums.ParseMode.HTML
        )
        
        self._create_storage()
        self.dp = Dispatcher(storage=self.storage)
        
        self.logger.info(f"Бот {self.get_bot_type()} инициализирован")
    
    def _setup_i18n(self):
        """Настройка интернационализации"""
        locales_path = Path(__file__).parent.parent.parent / 'locales'
        self.i18n = I18n(
            path=locales_path,
            default_locale=self.config.default_locale,
            domain='bot'
        )
        self.logger.info("I18n настроен")
    
    def _setup_middlewares(self):
        """Настройка middleware"""
        # Порядок важен!
        middlewares = [
            ErrorHandlerMiddleware(),
            LoggingMiddleware(),
            DatabaseMiddleware(),
            I18nMiddleware(self.i18n),
        ]
        
        # Добавляем специфичные для типа бота middleware
        bot_specific_middlewares = self.get_bot_specific_middlewares()
        middlewares.extend(bot_specific_middlewares)
        
        # Регистрируем middleware
        for middleware in middlewares:
            self.dp.message.middleware(middleware)
            self.dp.callback_query.middleware(middleware)
            
        self.logger.info(f"Зарегистрировано {len(middlewares)} middleware")
    
    def _setup_handlers(self):
        """Настройка обработчиков"""
        handlers = self.get_handlers()
        
        for handler in handlers:
            self.dp.include_router(handler.get_router())
            
        self.logger.info(f"Зарегистрировано {len(handlers)} обработчиков")
    
    def _setup_webhook_routes(self):
        """Настройка webhook маршрутов"""
        if not self.app:
            self.app = web.Application()
        
        # Базовые маршруты
        routes = self.get_webhook_routes()
        for route in routes:
            method, path, handler = route
            if method.upper() == 'GET':
                self.app.router.add_get(path, handler)
            elif method.upper() == 'POST':
                self.app.router.add_post(path, handler)
            elif method.upper() == 'PUT':
                self.app.router.add_put(path, handler)
            elif method.upper() == 'DELETE':
                self.app.router.add_delete(path, handler)
        
        # Webhook для Telegram
        webhook_handler = SimpleRequestHandler(
            dispatcher=self.dp,
            bot=self.bot,
        )
        webhook_handler.register(self.app, path="/webhook")
        
        self.logger.info(f"Настроено {len(routes)} webhook маршрутов")
    
    async def _on_startup(self):
        """Действия при запуске бота"""
        if self.config.webhook_mode and self.config.webhook_url:
            await self.bot.set_webhook(f"{self.config.webhook_url}/webhook")
            self.logger.info(f"Webhook установлен: {self.config.webhook_url}/webhook")
        elif not self.config.webhook_mode:
            # Удаляем webhook если режим polling
            await self.bot.delete_webhook()
            self.logger.info("Webhook удален, работаем в режиме polling")

        # Выполняем специфичные для бота действия
        await self.on_startup()
    
    async def _on_shutdown(self):
        """Действия при остановке бота"""
        await self.on_shutdown()
        
        if self.bot:
            await self.bot.session.close()
        
        self.logger.info("Бот остановлен")
    
    async def setup(self):
        """Настройка приложения"""
        self.logger.info(f"Настройка бота {self.get_bot_type()}...")
        
        # Создаем основные компоненты
        self._create_bot_and_dispatcher()
        self._setup_i18n()
        
        # Настраиваем компоненты
        self._setup_middlewares()
        self._setup_handlers()
        
        if self.config.webhook_mode:
            self._setup_webhook_routes()
        
        # Регистрируем события
        self.dp.startup.register(self._on_startup)
        self.dp.shutdown.register(self._on_shutdown)
        
        self.logger.info(f"Бот {self.get_bot_type()} настроен")
    
    async def run_polling(self):
        """Запуск в режиме polling"""
        self.logger.info("Запуск в режиме polling...")
        await self.dp.start_polling(self.bot)
    
    async def run_webhook(self):
        """Запуск в режиме webhook"""
        self.logger.info(f"Запуск в режиме webhook на порту {self.config.webhook_port}...")
        
        setup_application(self.app, self.dp, bot=self.bot)
        
        runner = web.AppRunner(self.app)
        await runner.setup()
        
        site = web.TCPSite(
            runner, 
            host=self.config.webhook_host,
            port=self.config.webhook_port
        )
        await site.start()
        
        self.logger.info(f"Webhook сервер запущен на {self.config.webhook_host}:{self.config.webhook_port}")
        
        # Ждем бесконечно
        try:
            await asyncio.Future()  # run forever
        except KeyboardInterrupt:
            self.logger.info("Получен сигнал остановки")
        finally:
            await runner.cleanup()
    
    async def run(self):
        """Запуск бота"""
        await self.setup()
        
        if self.config.webhook_mode:
            await self.run_webhook()
        else:
            await self.run_polling()
    
    # Абстрактные методы для переопределения в наследниках
    
    @abstractmethod
    def get_bot_type(self) -> str:
        """Возвращает тип бота (client, admin, support)"""
        pass
    
    @abstractmethod
    def get_handlers(self) -> List:
        """Возвращает список обработчиков для бота"""
        pass
    
    def get_bot_specific_middlewares(self) -> List:
        """Возвращает специфичные для бота middleware"""
        return []
    
    def get_webhook_routes(self) -> List[tuple]:
        """Возвращает список webhook маршрутов (method, path, handler)"""
        return []
    
    async def on_startup(self):
        """Действия при запуске (переопределяется в наследниках)"""
        pass
    
    async def on_shutdown(self):
        """Действия при остановке (переопределяется в наследниках)"""
        pass
