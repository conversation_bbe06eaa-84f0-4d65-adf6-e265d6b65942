"""
UnveilVPN Shop - Common Middleware
Общие middleware для всех ботов
"""

import logging
import time
from typing import Callable, Dict, Any, Awaitable, Optional

from aiogram import BaseMiddleware
from aiogram.types import TelegramObject, User, Message, CallbackQuery
from aiogram.utils.i18n import I18n, SimpleI18nMiddleware
from sqlalchemy.ext.asyncio import AsyncSession

from ..db import VPNUsers
from ..db.session import AsyncSessionLocal, get_async_session
from .config import get_config
from .exceptions import (
    BotException, 
    AuthenticationError, 
    DatabaseError,
    RateLimitError
)


class ErrorHandlerMiddleware(BaseMiddleware):
    """Middleware для обработки ошибок"""
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
    
    async def __call__(
        self,
        handler: Callable[[TelegramObject, Dict[str, Any]], Awaitable[Any]],
        event: TelegramObject,
        data: Dict[str, Any]
    ) -> Any:
        try:
            return await handler(event, data)
        except BotException as e:
            # Логируем бизнес-ошибки
            self.logger.warning(f"Business error: {e.to_dict()}")
            
            # Отправляем пользователю понятное сообщение
            if isinstance(event, (Message, CallbackQuery)):
                await self._send_error_message(event, e)
            
            return None
        except Exception as e:
            # Логируем системные ошибки
            self.logger.error(f"System error: {e}", exc_info=True)
            
            # Отправляем пользователю общее сообщение об ошибке
            if isinstance(event, (Message, CallbackQuery)):
                await self._send_system_error_message(event)
            
            return None
    
    async def _send_error_message(self, event: TelegramObject, error: BotException):
        """Отправка сообщения об ошибке пользователю"""
        try:
            if isinstance(event, Message):
                await event.answer(f"❌ {error.message}")
            elif isinstance(event, CallbackQuery):
                await event.answer(f"❌ {error.message}", show_alert=True)
        except Exception as e:
            self.logger.error(f"Failed to send error message: {e}")
    
    async def _send_system_error_message(self, event: TelegramObject):
        """Отправка сообщения о системной ошибке"""
        try:
            message = "⚠️ Произошла техническая ошибка. Попробуйте позже."
            
            if isinstance(event, Message):
                await event.answer(message)
            elif isinstance(event, CallbackQuery):
                await event.answer(message, show_alert=True)
        except Exception as e:
            self.logger.error(f"Failed to send system error message: {e}")


class LoggingMiddleware(BaseMiddleware):
    """Middleware для логирования запросов"""
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
    
    async def __call__(
        self,
        handler: Callable[[TelegramObject, Dict[str, Any]], Awaitable[Any]],
        event: TelegramObject,
        data: Dict[str, Any]
    ) -> Any:
        start_time = time.time()
        
        # Получаем информацию о пользователе
        user: Optional[User] = data.get("event_from_user")
        user_info = f"User {user.id} (@{user.username})" if user else "Unknown user"
        
        # Получаем тип события
        event_type = event.__class__.__name__
        
        # Логируем начало обработки
        self.logger.info(f"Processing {event_type} from {user_info}")
        
        try:
            result = await handler(event, data)
            
            # Логируем успешное завершение
            duration = time.time() - start_time
            self.logger.info(f"Completed {event_type} from {user_info} in {duration:.3f}s")
            
            return result
        except Exception as e:
            # Логируем ошибку
            duration = time.time() - start_time
            self.logger.error(f"Failed {event_type} from {user_info} in {duration:.3f}s: {e}")
            raise


class DatabaseMiddleware(BaseMiddleware):
    """Middleware для работы с базой данных"""
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
    
    async def __call__(
        self,
        handler: Callable[[TelegramObject, Dict[str, Any]], Awaitable[Any]],
        event: TelegramObject,
        data: Dict[str, Any]
    ) -> Any:
        async with get_async_session() as session:
            data["db_session"] = session

            try:
                # Получаем или создаем пользователя
                user: Optional[User] = data.get("event_from_user")
                if user:
                    db_user = await self._get_or_create_user(session, user)
                    data["db_user"] = db_user

                result = await handler(event, data)
                await session.commit()
                return result
            except Exception as e:
                await session.rollback()
                self.logger.error(f"Database transaction rolled back: {e}")
                raise
    
    async def _get_or_create_user(self, session: AsyncSession, user: User) -> VPNUsers:
        """Получение или создание пользователя в БД"""
        try:
            # Ищем существующего пользователя
            from sqlalchemy import select
            stmt = select(VPNUsers).where(VPNUsers.telegram_id == user.id)
            result = await session.execute(stmt)
            db_user = result.scalar_one_or_none()
            
            if db_user:
                # Обновляем информацию о пользователе
                db_user.username = user.username
                db_user.first_name = user.first_name
                db_user.last_name = user.last_name
                db_user.language_code = user.language_code or 'ru'
                return db_user
            
            # Создаем нового пользователя
            db_user = VPNUsers(
                telegram_id=user.id,
                username=user.username,
                first_name=user.first_name,
                last_name=user.last_name,
                language_code=user.language_code or 'ru'
            )
            
            session.add(db_user)
            await session.flush()  # Получаем ID
            
            self.logger.info(f"Created new user: {user.id} (@{user.username})")
            return db_user
            
        except Exception as e:
            self.logger.error(f"Failed to get/create user {user.id}: {e}")
            raise DatabaseError(f"Ошибка работы с пользователем: {e}")


class I18nMiddleware(SimpleI18nMiddleware):
    """Расширенный middleware для интернационализации"""
    
    def __init__(self, i18n: I18n):
        super().__init__(i18n=i18n)
        self.logger = logging.getLogger(self.__class__.__name__)
    
    async def __call__(
        self,
        handler: Callable[[TelegramObject, Dict[str, Any]], Awaitable[Any]],
        event: TelegramObject,
        data: Dict[str, Any]
    ) -> Any:
        # Получаем язык пользователя из БД или Telegram
        user: Optional[User] = data.get("event_from_user")
        db_user: Optional[VPNUsers] = data.get("db_user")
        
        locale = None
        if db_user and db_user.language_code:
            locale = db_user.language_code
        elif user and user.language_code:
            locale = user.language_code
        
        # Устанавливаем локаль
        if locale:
            data["locale"] = locale
        
        return await super().__call__(handler, event, data)


class AuthMiddleware(BaseMiddleware):
    """Middleware для проверки прав доступа"""
    
    def __init__(self, required_role: Optional[str] = None):
        """
        Args:
            required_role: Требуемая роль ('admin', 'support', 'staff')
        """
        self.required_role = required_role
        self.logger = logging.getLogger(self.__class__.__name__)
    
    async def __call__(
        self,
        handler: Callable[[TelegramObject, Dict[str, Any]], Awaitable[Any]],
        event: TelegramObject,
        data: Dict[str, Any]
    ) -> Any:
        if not self.required_role:
            return await handler(event, data)
        
        user: Optional[User] = data.get("event_from_user")
        if not user:
            raise AuthenticationError("Пользователь не определен")
        
        config = get_config()
        
        # Проверяем права доступа
        if self.required_role == 'admin' and not config.is_admin(user.id):
            raise AuthenticationError("Требуются права администратора")
        elif self.required_role == 'support' and not config.is_support(user.id):
            raise AuthenticationError("Требуются права сотрудника поддержки")
        elif self.required_role == 'staff' and not config.is_staff(user.id):
            raise AuthenticationError("Требуются права сотрудника")
        
        # Добавляем информацию о ролях в данные
        data["user_roles"] = {
            'is_admin': config.is_admin(user.id),
            'is_support': config.is_support(user.id),
            'is_staff': config.is_staff(user.id)
        }
        
        return await handler(event, data)


class RateLimitMiddleware(BaseMiddleware):
    """Middleware для ограничения частоты запросов"""
    
    def __init__(self, rate_limit: int = 30, window: int = 60):
        """
        Args:
            rate_limit: Максимальное количество запросов
            window: Временное окно в секундах
        """
        self.rate_limit = rate_limit
        self.window = window
        self.requests = {}  # user_id -> [timestamps]
        self.logger = logging.getLogger(self.__class__.__name__)
    
    async def __call__(
        self,
        handler: Callable[[TelegramObject, Dict[str, Any]], Awaitable[Any]],
        event: TelegramObject,
        data: Dict[str, Any]
    ) -> Any:
        user: Optional[User] = data.get("event_from_user")
        if not user:
            return await handler(event, data)
        
        current_time = time.time()
        user_id = user.id
        
        # Инициализируем список запросов для пользователя
        if user_id not in self.requests:
            self.requests[user_id] = []
        
        # Очищаем старые запросы
        self.requests[user_id] = [
            timestamp for timestamp in self.requests[user_id]
            if current_time - timestamp < self.window
        ]
        
        # Проверяем лимит
        if len(self.requests[user_id]) >= self.rate_limit:
            self.logger.warning(f"Rate limit exceeded for user {user_id}")
            raise RateLimitError(
                "Слишком много запросов. Попробуйте позже.",
                retry_after=self.window
            )
        
        # Добавляем текущий запрос
        self.requests[user_id].append(current_time)
        
        return await handler(event, data)
