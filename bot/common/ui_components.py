"""
UnveilVPN Shop - UI Components
Компоненты пользовательского интерфейса для красивого отображения
"""

from typing import Dict, Any, List, Optional
from datetime import datetime, timezone
from decimal import Decimal


class UIComponents:
    """
    Класс для создания красивых UI компонентов
    """
    
    @staticmethod
    def create_progress_bar(
        current: int,
        total: int,
        width: int = 10,
        filled_char: str = "█",
        empty_char: str = "░"
    ) -> str:
        """Создание прогресс-бара"""
        if total == 0:
            return empty_char * width
        
        filled_length = int(width * current / total)
        filled_length = min(filled_length, width)
        
        bar = filled_char * filled_length + empty_char * (width - filled_length)
        percentage = int(100 * current / total)
        
        return f"{bar} {percentage}%"
    
    @staticmethod
    def create_subscription_card(subscription_info: Dict[str, Any]) -> str:
        """Создание карточки подписки"""
        lines = []
        
        # Заголовок карточки
        is_active = subscription_info.get('is_active', False)
        status_emoji = "🟢" if is_active else "🔴"
        status_text = "Активна" if is_active else "Неактивна"
        
        lines.extend([
            "┌─────────────────────────────┐",
            f"│ {status_emoji} <b>VPN Подписка</b> - {status_text}     │",
            "├─────────────────────────────┤"
        ])
        
        # Информация о подписке
        if is_active:
            expires_at = subscription_info.get('expires_at')
            if expires_at:
                try:
                    expires_date = datetime.fromisoformat(expires_at.replace('Z', '+00:00'))
                    days_left = (expires_date - datetime.now(timezone.utc)).days
                    
                    if days_left > 0:
                        lines.append(f"│ ⏰ Осталось: {days_left} дней          │")
                    else:
                        lines.append("│ ⚠️ Подписка истекла           │")
                except:
                    lines.append("│ ⏰ Срок действия: неизвестно   │")
            
            # Трафик
            traffic_used = subscription_info.get('traffic_used', 0)
            traffic_limit = subscription_info.get('traffic_limit')
            
            if traffic_limit:
                progress = UIComponents.create_progress_bar(
                    int(traffic_used), int(traffic_limit), width=8
                )
                lines.append(f"│ 📊 Трафик: {traffic_used:.1f}/{traffic_limit} ГБ │")
                lines.append(f"│ {progress}           │")
            else:
                lines.append(f"│ 📊 Использовано: {traffic_used:.1f} ГБ    │")
                lines.append("│ 🚀 Безлимитный трафик         │")
        else:
            lines.extend([
                "│ 💡 Купите подписку для        │",
                "│    доступа к VPN серверам     │"
            ])
        
        lines.append("└─────────────────────────────┘")
        
        return "\n".join(lines)
    
    @staticmethod
    def create_tariff_card(tariff: Dict[str, Any], is_selected: bool = False) -> str:
        """Создание карточки тарифа"""
        lines = []
        
        # Заголовок
        selection_emoji = "✅" if is_selected else "📦"
        popular_badge = " 🔥 ХИТ" if tariff.get('is_popular') else ""
        
        lines.extend([
            "┌─────────────────────────────┐",
            f"│ {selection_emoji} <b>{tariff.get('name', 'Тариф')}</b>{popular_badge}        │",
            "├─────────────────────────────┤"
        ])
        
        # Цена
        prices = tariff.get('prices', {})
        rub_price = prices.get('RUB', 0)
        usd_price = prices.get('USD', 0)
        
        lines.append(f"│ 💰 {rub_price} ₽ / {usd_price} $           │")
        
        # Длительность
        duration = tariff.get('duration_days', 0)
        if duration == 30:
            duration_text = "1 месяц"
        elif duration == 90:
            duration_text = "3 месяца"
        elif duration == 180:
            duration_text = "6 месяцев"
        elif duration == 365:
            duration_text = "1 год"
        else:
            duration_text = f"{duration} дней"
        
        lines.append(f"│ ⏰ Срок: {duration_text}              │")
        
        # Характеристики
        features = tariff.get('features', {})
        
        # Устройства
        devices = features.get('max_devices', 'Безлимит')
        lines.append(f"│ 📱 Устройств: {devices}               │")
        
        # Скорость
        speed = features.get('speed_limit', 'Без ограничений')
        lines.append(f"│ 🚀 Скорость: {speed}              │")
        
        # Серверы
        servers = features.get('server_locations', 'Все')
        lines.append(f"│ 🌍 Серверы: {servers}                │")
        
        lines.append("└─────────────────────────────┘")
        
        return "\n".join(lines)
    
    @staticmethod
    def create_payment_card(payment_info: Dict[str, Any]) -> str:
        """Создание карточки платежа"""
        lines = []
        
        # Статус платежа
        status = payment_info.get('status', 'pending')
        status_emojis = {
            'pending': '🟡',
            'completed': '🟢',
            'failed': '🔴',
            'cancelled': '⚫'
        }
        status_texts = {
            'pending': 'Ожидает оплаты',
            'completed': 'Оплачен',
            'failed': 'Ошибка',
            'cancelled': 'Отменен'
        }
        
        status_emoji = status_emojis.get(status, '⚪')
        status_text = status_texts.get(status, status)
        
        lines.extend([
            "┌─────────────────────────────┐",
            f"│ {status_emoji} <b>Платеж</b> - {status_text}        │",
            "├─────────────────────────────┤"
        ])
        
        # Сумма
        amount = payment_info.get('amount', 0)
        currency = payment_info.get('currency', 'RUB')
        currency_symbol = {'RUB': '₽', 'USD': '$', 'EUR': '€'}.get(currency, currency)
        
        lines.append(f"│ 💰 Сумма: {amount} {currency_symbol}              │")
        
        # Способ оплаты
        payment_method = payment_info.get('payment_method', 'unknown')
        method_emojis = {
            'yookassa': '💳',
            'cryptomus': '₿',
            'telegram_stars': '⭐'
        }
        method_names = {
            'yookassa': 'Банковская карта',
            'cryptomus': 'Криптовалюта',
            'telegram_stars': 'Telegram Stars'
        }
        
        method_emoji = method_emojis.get(payment_method, '💳')
        method_name = method_names.get(payment_method, payment_method)
        
        lines.append(f"│ {method_emoji} {method_name}           │")
        
        # Дата создания
        created_at = payment_info.get('created_at')
        if created_at:
            try:
                date = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                date_str = date.strftime('%d.%m.%Y %H:%M')
                lines.append(f"│ 📅 {date_str}              │")
            except:
                pass
        
        lines.append("└─────────────────────────────┘")
        
        return "\n".join(lines)
    
    @staticmethod
    def create_referral_card(referral_info: Dict[str, Any]) -> str:
        """Создание карточки реферальной программы"""
        lines = []
        
        lines.extend([
            "┌─────────────────────────────┐",
            "│ 👥 <b>Реферальная программа</b>      │",
            "├─────────────────────────────┤"
        ])
        
        # Реферальный код
        referral_code = referral_info.get('referral_code', 'Не создан')
        lines.append(f"│ 🔗 Код: <code>{referral_code}</code>              │")
        
        # Статистика
        total_referrals = referral_info.get('total_referrals', 0)
        total_earnings = referral_info.get('total_earnings', 0)
        
        lines.extend([
            f"│ 👤 Приглашено: {total_referrals}              │",
            f"│ 💰 Заработано: {total_earnings:.2f} ₽        │"
        ])
        
        # Уровни
        levels = referral_info.get('levels', {})
        if levels:
            lines.append("│                               │")
            lines.append("│ 📊 По уровням:               │")
            for level, data in levels.items():
                count = data.get('count', 0)
                earnings = data.get('earnings', 0)
                lines.append(f"│ Ур.{level}: {count} чел, {earnings:.2f} ₽      │")
        
        lines.append("└─────────────────────────────┘")
        
        return "\n".join(lines)
    
    @staticmethod
    def create_support_ticket_card(ticket_info: Dict[str, Any]) -> str:
        """Создание карточки тикета поддержки"""
        lines = []
        
        # Статус тикета
        status = ticket_info.get('status', 'open')
        status_emojis = {
            'open': '🟡',
            'in_progress': '🔵',
            'waiting_user': '🟠',
            'closed': '🟢'
        }
        status_texts = {
            'open': 'Открыт',
            'in_progress': 'В работе',
            'waiting_user': 'Ждет ответа',
            'closed': 'Закрыт'
        }
        
        status_emoji = status_emojis.get(status, '⚪')
        status_text = status_texts.get(status, status)
        
        lines.extend([
            "┌─────────────────────────────┐",
            f"│ {status_emoji} <b>Тикет #{ticket_info.get('ticket_number', 'N/A')}</b>     │",
            "├─────────────────────────────┤"
        ])
        
        # Тема
        subject = ticket_info.get('subject', 'Без темы')
        if len(subject) > 25:
            subject = subject[:22] + "..."
        lines.append(f"│ 📝 {subject}                │")
        
        # Статус и приоритет
        priority = ticket_info.get('priority', 'medium')
        priority_emojis = {
            'low': '🟢',
            'medium': '🟡',
            'high': '🟠',
            'urgent': '🔴'
        }
        priority_emoji = priority_emojis.get(priority, '⚪')
        
        lines.append(f"│ {status_emoji} {status_text} {priority_emoji}              │")
        
        # Дата создания
        created_at = ticket_info.get('created_at')
        if created_at:
            try:
                date = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                date_str = date.strftime('%d.%m.%Y')
                lines.append(f"│ 📅 {date_str}                  │")
            except:
                pass
        
        # Количество сообщений
        messages_count = len(ticket_info.get('messages', []))
        lines.append(f"│ 💬 Сообщений: {messages_count}               │")
        
        lines.append("└─────────────────────────────┘")
        
        return "\n".join(lines)
    
    @staticmethod
    def create_notification_banner(
        message: str,
        banner_type: str = "info",
        width: int = 35
    ) -> str:
        """Создание баннера уведомления"""
        
        type_configs = {
            'info': {'emoji': 'ℹ️', 'border': '─'},
            'success': {'emoji': '✅', 'border': '═'},
            'warning': {'emoji': '⚠️', 'border': '─'},
            'error': {'emoji': '❌', 'border': '─'},
            'promo': {'emoji': '🎉', 'border': '═'}
        }
        
        config = type_configs.get(banner_type, type_configs['info'])
        emoji = config['emoji']
        border_char = config['border']
        
        # Разбиваем сообщение на строки
        words = message.split()
        lines = []
        current_line = ""
        
        for word in words:
            if len(current_line + " " + word) <= width - 6:  # 6 символов для рамки и отступов
                current_line += (" " if current_line else "") + word
            else:
                if current_line:
                    lines.append(current_line)
                current_line = word
        
        if current_line:
            lines.append(current_line)
        
        # Создаем баннер
        result = []
        result.append("┌" + border_char * (width - 2) + "┐")
        
        for i, line in enumerate(lines):
            prefix = f"{emoji} " if i == 0 else "  "
            padding = width - len(prefix) - len(line) - 3
            result.append(f"│ {prefix}{line}" + " " * padding + "│")
        
        result.append("└" + border_char * (width - 2) + "┘")
        
        return "\n".join(result)
    
    @staticmethod
    def create_step_indicator(current_step: int, total_steps: int, step_names: List[str] = None) -> str:
        """Создание индикатора шагов"""
        lines = []
        
        for i in range(1, total_steps + 1):
            if i < current_step:
                emoji = "✅"
                status = "завершен"
            elif i == current_step:
                emoji = "🔄"
                status = "текущий"
            else:
                emoji = "⚪"
                status = "ожидает"
            
            step_name = step_names[i-1] if step_names and len(step_names) >= i else f"Шаг {i}"
            lines.append(f"{emoji} {step_name}")
        
        return "\n".join(lines)
    
    @staticmethod
    def format_price_comparison(
        original_price: Decimal,
        discounted_price: Decimal,
        currency: str = "RUB"
    ) -> str:
        """Форматирование сравнения цен"""
        currency_symbol = {'RUB': '₽', 'USD': '$', 'EUR': '€'}.get(currency, currency)
        
        if original_price == discounted_price:
            return f"💰 {discounted_price} {currency_symbol}"
        
        discount_percent = int((1 - discounted_price / original_price) * 100)
        
        return (
            f"💰 <s>{original_price} {currency_symbol}</s> ➜ <b>{discounted_price} {currency_symbol}</b>\n"
            f"🎉 Скидка {discount_percent}%!"
        )
