"""
UnveilVPN Shop - Common Exceptions
Общие исключения для всех ботов
"""

from typing import Optional, Dict, Any


class BotException(Exception):
    """Базовое исключение для всех ботов"""
    
    def __init__(
        self, 
        message: str, 
        error_code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        status_code: int = 500
    ):
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        self.status_code = status_code
    
    def to_dict(self) -> Dict[str, Any]:
        """Преобразование в словарь для логирования"""
        return {
            'error_type': self.__class__.__name__,
            'message': self.message,
            'error_code': self.error_code,
            'details': self.details
        }


class ValidationError(BotException):
    """Ошибка валидации данных"""
    
    def __init__(
        self, 
        message: str, 
        field: Optional[str] = None,
        value: Optional[Any] = None
    ):
        super().__init__(message, error_code='VALIDATION_ERROR')
        self.field = field
        self.value = value
        
        if field:
            self.details['field'] = field
        if value is not None:
            self.details['value'] = str(value)


class AuthenticationError(BotException):
    """Ошибка аутентификации"""
    
    def __init__(self, message: str = "Недостаточно прав доступа"):
        super().__init__(message, error_code='AUTH_ERROR')


class AuthorizationError(BotException):
    """Ошибка авторизации"""
    
    def __init__(self, message: str = "Доступ запрещен"):
        super().__init__(message, error_code='AUTHORIZATION_ERROR')


class DatabaseError(BotException):
    """Ошибка базы данных"""
    
    def __init__(
        self, 
        message: str, 
        operation: Optional[str] = None,
        table: Optional[str] = None
    ):
        super().__init__(message, error_code='DATABASE_ERROR')
        if operation:
            self.details['operation'] = operation
        if table:
            self.details['table'] = table


class ExternalServiceError(BotException):
    """Ошибка внешнего сервиса"""
    
    def __init__(
        self, 
        message: str, 
        service: Optional[str] = None,
        status_code: Optional[int] = None
    ):
        super().__init__(message, error_code='EXTERNAL_SERVICE_ERROR')
        if service:
            self.details['service'] = service
        if status_code:
            self.details['status_code'] = status_code


class PaymentError(BotException):
    """Ошибка платежной системы"""
    
    def __init__(
        self, 
        message: str, 
        payment_method: Optional[str] = None,
        payment_id: Optional[str] = None
    ):
        super().__init__(message, error_code='PAYMENT_ERROR')
        if payment_method:
            self.details['payment_method'] = payment_method
        if payment_id:
            self.details['payment_id'] = payment_id


class VPNServiceError(BotException):
    """Ошибка VPN сервиса"""

    def __init__(
        self,
        message: str,
        vpn_id: Optional[str] = None,
        operation: Optional[str] = None
    ):
        super().__init__(message, error_code='VPN_SERVICE_ERROR')
        if vpn_id:
            self.details['vpn_id'] = vpn_id
        if operation:
            self.details['operation'] = operation


class VPNPanelError(BotException):
    """Ошибка VPN панели управления"""

    def __init__(
        self,
        message: str,
        panel_type: Optional[str] = None,
        operation: Optional[str] = None,
        status_code: Optional[int] = None
    ):
        super().__init__(message, error_code='VPN_PANEL_ERROR')
        if panel_type:
            self.details['panel_type'] = panel_type
        if operation:
            self.details['operation'] = operation
        if status_code:
            self.details['status_code'] = status_code


class RateLimitError(BotException):
    """Ошибка превышения лимита запросов"""
    
    def __init__(
        self, 
        message: str = "Превышен лимит запросов",
        retry_after: Optional[int] = None
    ):
        super().__init__(message, error_code='RATE_LIMIT_ERROR')
        if retry_after:
            self.details['retry_after'] = retry_after


class ConfigurationError(BotException):
    """Ошибка конфигурации"""
    
    def __init__(
        self, 
        message: str, 
        config_key: Optional[str] = None
    ):
        super().__init__(message, error_code='CONFIGURATION_ERROR')
        if config_key:
            self.details['config_key'] = config_key


class NotFoundError(BotException):
    """Ошибка "не найдено" """

    def __init__(
        self,
        message: str,
        resource: Optional[str] = None,
        resource_id: Optional[str] = None
    ):
        super().__init__(message, error_code='NOT_FOUND_ERROR')
        if resource:
            self.details['resource'] = resource
        if resource_id:
            self.details['resource_id'] = resource_id


class BusinessLogicError(BotException):
    """Ошибка бизнес-логики"""

    def __init__(
        self,
        message: str,
        operation: Optional[str] = None
    ):
        super().__init__(message, error_code='BUSINESS_LOGIC_ERROR')
        if operation:
            self.details['operation'] = operation


class UserNotFoundError(BotException):
    """Пользователь не найден"""
    
    def __init__(
        self, 
        user_id: Optional[int] = None,
        telegram_id: Optional[int] = None
    ):
        message = "Пользователь не найден"
        if telegram_id:
            message += f" (Telegram ID: {telegram_id})"
        
        super().__init__(message, error_code='USER_NOT_FOUND')
        
        if user_id:
            self.details['user_id'] = str(user_id)
        if telegram_id:
            self.details['telegram_id'] = telegram_id


class TariffNotFoundError(BotException):
    """Тариф не найден"""
    
    def __init__(self, tariff_id: Optional[str] = None):
        message = "Тариф не найден"
        if tariff_id:
            message += f" (ID: {tariff_id})"
        
        super().__init__(message, error_code='TARIFF_NOT_FOUND')
        
        if tariff_id:
            self.details['tariff_id'] = tariff_id


class PromocodeError(BotException):
    """Ошибка промокода"""
    
    def __init__(
        self, 
        message: str, 
        promocode: Optional[str] = None,
        reason: Optional[str] = None
    ):
        super().__init__(message, error_code='PROMOCODE_ERROR')
        if promocode:
            self.details['promocode'] = promocode
        if reason:
            self.details['reason'] = reason


class SubscriptionError(BotException):
    """Ошибка подписки"""
    
    def __init__(
        self, 
        message: str, 
        subscription_id: Optional[str] = None,
        operation: Optional[str] = None
    ):
        super().__init__(message, error_code='SUBSCRIPTION_ERROR')
        if subscription_id:
            self.details['subscription_id'] = subscription_id
        if operation:
            self.details['operation'] = operation


# Функции для быстрого создания исключений

def validation_error(message: str, field: str = None, value: Any = None) -> ValidationError:
    """Создание ошибки валидации"""
    return ValidationError(message, field, value)


def auth_error(message: str = "Недостаточно прав доступа") -> AuthenticationError:
    """Создание ошибки аутентификации"""
    return AuthenticationError(message)


def db_error(message: str, operation: str = None, table: str = None) -> DatabaseError:
    """Создание ошибки базы данных"""
    return DatabaseError(message, operation, table)


def external_service_error(message: str, service: str = None, status_code: int = None) -> ExternalServiceError:
    """Создание ошибки внешнего сервиса"""
    return ExternalServiceError(message, service, status_code)


def payment_error(message: str, payment_method: str = None, payment_id: str = None) -> PaymentError:
    """Создание ошибки платежа"""
    return PaymentError(message, payment_method, payment_id)


def user_not_found(user_id: int = None, telegram_id: int = None) -> UserNotFoundError:
    """Создание ошибки "пользователь не найден" """
    return UserNotFoundError(user_id, telegram_id)


def tariff_not_found(tariff_id: str = None) -> TariffNotFoundError:
    """Создание ошибки "тариф не найден" """
    return TariffNotFoundError(tariff_id)
