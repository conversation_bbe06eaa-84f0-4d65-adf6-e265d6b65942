"""
UnveilVPN Shop - Common Components Package
Общие компоненты для всех ботов (клиентский, админ, поддержка)
"""

from .base import BaseBotApplication
from .middleware import (
    DatabaseMiddleware,
    LoggingMiddleware, 
    AuthMiddleware,
    I18nMiddleware,
    ErrorHandlerMiddleware
)
from .handlers import BaseHandler
from .keyboards import BaseKeyboard
from .utils import (
    BotUtils,
    MessageFormatter,
    ValidationUtils,
    SecurityUtils
)
from .config import BotConfig
from .exceptions import (
    BotException,
    ValidationError,
    AuthenticationError,
    DatabaseError
)

__all__ = [
    # Base classes
    'BaseBotApplication',
    'BaseHandler', 
    'BaseKeyboard',
    
    # Middleware
    'DatabaseMiddleware',
    'LoggingMiddleware',
    'AuthMiddleware', 
    'I18nMiddleware',
    'ErrorHandlerMiddleware',
    
    # Utils
    'BotUtils',
    'MessageFormatter',
    'ValidationUtils',
    'SecurityUtils',
    
    # Config
    'BotConfig',
    
    # Exceptions
    'BotException',
    'ValidationError',
    'AuthenticationError', 
    'DatabaseError'
]
