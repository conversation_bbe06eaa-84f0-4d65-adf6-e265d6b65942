"""
UnveilVPN Shop - Base Keyboards
Базовые классы клавиатур для всех ботов
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional, Union

from aiogram.types import (
    InlineKeyboardMarkup, 
    InlineKeyboardButton,
    ReplyKeyboardMarkup,
    KeyboardButton,
    WebAppInfo
)
from aiogram.utils.keyboard import InlineKeyboardBuilder, ReplyKeyboardBuilder
from aiogram.utils.i18n import gettext as _, lazy_gettext as __


class BaseKeyboard(ABC):
    """
    Базовый класс для всех клавиатур
    """
    
    def __init__(self, max_width: int = 2):
        self.max_width = max_width
    
    @abstractmethod
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> Union[InlineKeyboardMarkup, ReplyKeyboardMarkup]:
        """Получение клавиатуры (переопределяется в наследниках)"""
        pass

class InlineKeyboard(BaseKeyboard):
    """
    Базовый класс для inline клавиатур
    """
    
    def __init__(self, max_width: int = 2):
        super().__init__(max_width)
        self.builder = InlineKeyboardBuilder()
    
    def add_button(
        self, 
        text: str, 
        callback_data: str = None,
        url: str = None,
        web_app: WebAppInfo = None,
        width: int = 1
    ):
        """Добавление кнопки"""
        if callback_data:
            button = InlineKeyboardButton(text=text, callback_data=callback_data)
        elif url:
            button = InlineKeyboardButton(text=text, url=url)
        elif web_app:
            button = InlineKeyboardButton(text=text, web_app=web_app)
        else:
            raise ValueError("Необходимо указать callback_data, url или web_app")
        
        self.builder.add(button)
        if width > 1:
            self.builder.adjust(width)
        
        return self
    
    def add_row(self, buttons: List[InlineKeyboardButton]):
        """Добавление ряда кнопок"""
        self.builder.row(*buttons)
        return self
    
    def add_back_button(self, callback_data: str = "back", text: str = None):
        """Добавление кнопки "Назад" """
        text = text or _("⬅️ Назад")
        return self.add_button(text, callback_data)
    
    def add_main_menu_button(self, text: str = None):
        """Добавление кнопки "Главное меню" """
        text = text or _("🏠 Главное меню")
        return self.add_button(text, "main_menu")
    
    def add_cancel_button(self, text: str = None):
        """Добавление кнопки "Отмена" """
        text = text or _("❌ Отмена")
        return self.add_button(text, "cancel")
    
    def build(self) -> InlineKeyboardMarkup:
        """Построение клавиатуры"""
        self.builder.adjust(self.max_width)
        return self.builder.as_markup()
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры"""
        self.builder = InlineKeyboardBuilder() # Сброс билдера перед добавлением кнопок
        return self.build()

class CancelKeyboard(InlineKeyboard):
    """Клавиатура отмены"""
    
    def __init__(self, back_callback: str = "main_menu"):
        super().__init__(max_width=1)
        self.back_callback = back_callback
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры отмены"""
        
        self.add_button(_("⬅️ Назад"), self.back_callback)
        
        return self.build()


class ReplyKeyboard(BaseKeyboard):
    """
    Базовый класс для reply клавиатур
    """
    
    def __init__(
        self, 
        max_width: int = 2,
        resize_keyboard: bool = True,
        one_time_keyboard: bool = False
    ):
        super().__init__(max_width)
        self.builder = ReplyKeyboardBuilder()
        self.resize_keyboard = resize_keyboard
        self.one_time_keyboard = one_time_keyboard
    
    def add_button(
        self, 
        text: str,
        request_contact: bool = False,
        request_location: bool = False,
        web_app: WebAppInfo = None
    ):
        """Добавление кнопки"""
        button = KeyboardButton(
            text=text,
            request_contact=request_contact,
            request_location=request_location,
            web_app=web_app
        )
        self.builder.add(button)
        return self
    
    def add_row(self, buttons: List[KeyboardButton]):
        """Добавление ряда кнопок"""
        self.builder.row(*buttons)
        return self
    
    def build(self) -> ReplyKeyboardMarkup:
        """Построение клавиатуры"""
        self.builder.adjust(self.max_width)
        return self.builder.as_markup(
            resize_keyboard=self.resize_keyboard,
            one_time_keyboard=self.one_time_keyboard
        )
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> ReplyKeyboardMarkup:
        """Получение клавиатуры"""
        return self.build()


class MenuKeyboard(InlineKeyboard):
    """
    Клавиатура главного меню
    """
    
    def __init__(self, menu_items: List[Dict[str, str]], max_width: int = 2):
        """
        Args:
            menu_items: Список элементов меню [{"text": "Текст", "callback": "callback_data"}]
        """
        super().__init__(max_width)
        self.menu_items = menu_items
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры меню"""
        for item in self.menu_items:
            self.add_button(item["text"], item["callback"])
        
        return self.build()


class PaginationKeyboard(InlineKeyboard):
    """
    Клавиатура с пагинацией
    """
    
    def __init__(
        self, 
        current_page: int,
        total_pages: int,
        callback_prefix: str = "page",
        max_width: int = 3
    ):
        super().__init__(max_width)
        self.current_page = current_page
        self.total_pages = total_pages
        self.callback_prefix = callback_prefix
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры с пагинацией"""
        buttons = []
        
        # Кнопка "Предыдущая"
        if self.current_page > 0:
            buttons.append(
                InlineKeyboardButton(
                    text="⬅️",
                    callback_data=f"{self.callback_prefix}_{self.current_page - 1}"
                )
            )
        
        # Информация о текущей странице
        buttons.append(
            InlineKeyboardButton(
                text=f"{self.current_page + 1}/{self.total_pages}",
                callback_data="current_page"
            )
        )
        
        # Кнопка "Следующая"
        if self.current_page < self.total_pages - 1:
            buttons.append(
                InlineKeyboardButton(
                    text="➡️",
                    callback_data=f"{self.callback_prefix}_{self.current_page + 1}"
                )
            )
        
        if buttons:
            self.add_row(buttons)
        
        return self.build()


class ConfirmationKeyboard(InlineKeyboard):
    """
    Клавиатура подтверждения действия
    """
    
    def __init__(
        self,
        confirm_callback: str,
        cancel_callback: str = "cancel",
        confirm_text: str = None,
        cancel_text: str = None
    ):
        super().__init__(max_width=2)
        self.confirm_callback = confirm_callback
        self.cancel_callback = cancel_callback
        self.confirm_text = confirm_text or _("✅ Подтвердить")
        self.cancel_text = cancel_text or _("❌ Отмена")
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры подтверждения"""
        self.add_button(self.confirm_text, self.confirm_callback)
        self.add_button(self.cancel_text, self.cancel_callback)
        return self.build()


class TariffKeyboard(InlineKeyboard):
    """
    Клавиатура для выбора тарифов
    """
    
    def __init__(self, tariffs: List[Dict[str, Any]], max_width: int = 1):
        super().__init__(max_width)
        self.tariffs = tariffs
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры тарифов"""
        for tariff in self.tariffs:
            # Формируем текст кнопки с ценой
            price_text = self._format_price(tariff)
            button_text = f"{tariff['name']} - {price_text}"
            
            if tariff.get('is_popular'):
                button_text = f"⭐ {button_text}"
            
            self.add_button(button_text, f"tariff_{tariff['id']}")
        
        self.add_main_menu_button()
        return self.build()
    
    def _format_price(self, tariff: Dict[str, Any]) -> str:
        """Форматирование цены тарифа"""
        prices = tariff.get('prices', {})
        
        # Приоритет валют для отображения
        if 'rub' in prices and prices['rub'] > 0:
            return f"{prices['rub']} ₽"
        elif 'usd' in prices and prices['usd'] > 0:
            return f"${prices['usd']}"
        elif 'stars' in prices and prices['stars'] > 0:
            return f"{prices['stars']} ⭐"
        
        return _("Цена не указана")


class PaymentMethodKeyboard(InlineKeyboard):
    """
    Клавиатура для выбора способа оплаты
    """
    
    def __init__(self, available_methods: List[str], max_width: int = 1):
        super().__init__(max_width)
        self.available_methods = available_methods
        
        # Маппинг методов оплаты
        self.method_mapping = {
            'yookassa': {'text': '💳 Банковская карта', 'callback': 'pay_yookassa'},
            'cryptomus': {'text': '₿ Криптовалюта', 'callback': 'pay_cryptomus'},
            'telegram_stars': {'text': '⭐ Telegram Stars', 'callback': 'pay_stars'}
        }
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры способов оплаты"""
        for method in self.available_methods:
            if method in self.method_mapping:
                method_info = self.method_mapping[method]
                self.add_button(method_info['text'], method_info['callback'])
        
        self.add_back_button()
        return self.build()


class AdminKeyboard(InlineKeyboard):
    """
    Клавиатура для административных функций
    """
    
    def __init__(self, max_width: int = 2):
        super().__init__(max_width)
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение административной клавиатуры"""
        # Основные административные функции
        self.add_button("👥 Пользователи", "admin_users")
        self.add_button("💰 Тарифы", "admin_tariffs")
        self.add_button("🎫 Промокоды", "admin_promocodes")
        self.add_button("🔗 Рефералы", "admin_referrals")
        self.add_button("💳 Платежи", "admin_payments")
        self.add_button("📊 Статистика", "admin_stats")
        self.add_button("⚙️ Настройки", "admin_settings")
        self.add_button("📝 Логи", "admin_logs")
        
        return self.build()


class SupportKeyboard(InlineKeyboard):
    """
    Клавиатура для функций поддержки
    """
    
    def __init__(self, max_width: int = 2):
        super().__init__(max_width)
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры поддержки"""
        # Функции поддержки
        self.add_button("🎫 Активные тикеты", "support_active_tickets")
        self.add_button("📋 Все тикеты", "support_all_tickets")
        self.add_button("👤 Поиск пользователя", "support_find_user")
        self.add_button("💳 Поиск платежа", "support_find_payment")
        self.add_button("📊 Статистика", "support_stats")
        self.add_button("⚙️ Настройки", "support_settings")
        
        return self.build()


# Функции-хелперы для быстрого создания клавиатур

def create_menu_keyboard(items: List[Dict[str, str]], max_width: int = 2) -> MenuKeyboard:
    """Создание клавиатуры меню"""
    return MenuKeyboard(items, max_width)


def create_confirmation_keyboard(
    confirm_callback: str,
    cancel_callback: str = "cancel"
) -> ConfirmationKeyboard:
    """Создание клавиатуры подтверждения"""
    return ConfirmationKeyboard(confirm_callback, cancel_callback)


def create_pagination_keyboard(
    current_page: int,
    total_pages: int,
    callback_prefix: str = "page"
) -> PaginationKeyboard:
    """Создание клавиатуры с пагинацией"""
    return PaginationKeyboard(current_page, total_pages, callback_prefix)
