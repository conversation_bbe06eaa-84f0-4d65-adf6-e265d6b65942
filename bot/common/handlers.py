"""
UnveilVPN Shop - Base Handlers
Базовые классы обработчиков для всех ботов
"""

import logging
from abc import ABC, abstractmethod
from typing import Optional, Dict, Any, List, Union

from aiogram import Router, F
from aiogram.types import Message, CallbackQuery, User
from aiogram.filters import Command, StateFilter
from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import State, StatesGroup
from aiogram.utils.i18n import gettext as _, lazy_gettext as __
from sqlalchemy.ext.asyncio import AsyncSession

from ..db.models import VPNUsers
from .config import get_config
from .exceptions import BotException, ValidationError, AuthenticationError
from .keyboards import BaseKeyboard


class BaseHandler(ABC):
    """
    Базовый класс для всех обработчиков
    """
    
    def __init__(self, name: str = None):
        self.name = name or self.__class__.__name__
        self.router = Router(name=self.name)
        self.logger = logging.getLogger(self.name)
        self.config = get_config()
        
        # Регистрируем обработчики
        self._register_handlers()
    
    def get_router(self) -> Router:
        """Получение роутера"""
        return self.router
    
    @abstractmethod
    def _register_handlers(self):
        """Регистрация обработчиков (переопределяется в наследниках)"""
        pass
    
    async def _get_user_data(self, event: Union[Message, CallbackQuery], db_session: AsyncSession = None, db_user: VPNUsers = None, user_roles: dict = None, locale: str = 'ru', **kwargs) -> Dict[str, Any]:
        """Получение данных пользователя из события и middleware"""
        user: User = event.from_user

        # Если данных нет, создаем временную сессию
        if not db_session:
            from ..db.session import get_async_session
            async with get_async_session() as session:
                db_session = session
                if not db_user and db_session:
                    db_user = await self._get_or_create_user(db_session, user)

                return {
                    'user': user,
                    'db_session': db_session,
                    'db_user': db_user,
                    'user_roles': user_roles or {},
                    'locale': locale
                }
        else:
            if not db_user and db_session:
                db_user = await self._get_or_create_user(db_session, user)

            return {
                'user': user,
                'db_session': db_session,
                'db_user': db_user,
                'user_roles': user_roles or {},
                'locale': locale
            }

    async def _get_or_create_user(self, session: AsyncSession, user: User) -> VPNUsers:
        """Получение или создание пользователя в БД"""
        try:
            # Ищем существующего пользователя
            from sqlalchemy import select
            stmt = select(VPNUsers).where(VPNUsers.telegram_id == user.id)
            result = await session.execute(stmt)
            db_user = result.scalar_one_or_none()

            if db_user:
                # Обновляем информацию о пользователе
                db_user.username = user.username
                db_user.first_name = user.first_name
                db_user.last_name = user.last_name
                db_user.language_code = user.language_code or 'ru'
                return db_user

            # Создаем нового пользователя
            db_user = VPNUsers(
                telegram_id=user.id,
                username=user.username,
                first_name=user.first_name,
                last_name=user.last_name,
                language_code=user.language_code or 'ru'
            )

            session.add(db_user)
            await session.flush()  # Получаем ID

            self.logger.info(f"Created new user: {user.id} (@{user.username})")
            return db_user

        except Exception as e:
            self.logger.error(f"Failed to get/create user {user.id}: {e}")
            # Возвращаем временного пользователя для избежания критических ошибок
            return VPNUsers(
                tg_id=user.id,
                username=user.username,
                first_name=user.first_name,
                last_name=user.last_name,
                language_code=user.language_code or 'ru'
            )
    
    async def _send_error(self, event: Union[Message, CallbackQuery], error: str):
        """Отправка сообщения об ошибке"""
        try:
            if isinstance(event, Message):
                await event.answer(f"❌ {error}")
            elif isinstance(event, CallbackQuery):
                await event.answer(f"❌ {error}", show_alert=True)
        except Exception as e:
            self.logger.error(f"Failed to send error message: {e}")
    
    async def _send_success(self, event: Union[Message, CallbackQuery], message: str):
        """Отправка сообщения об успехе"""
        try:
            if isinstance(event, Message):
                await event.answer(f"✅ {message}")
            elif isinstance(event, CallbackQuery):
                await event.answer(f"✅ {message}")
        except Exception as e:
            self.logger.error(f"Failed to send success message: {e}")


class CommandHandler(BaseHandler):
    """
    Базовый класс для обработчиков команд
    """
    
    def __init__(self, commands: List[str], name: str = None):
        self.commands = commands
        super().__init__(name)
    
    def _register_handlers(self):
        """Регистрация обработчиков команд"""
        for command in self.commands:
            self.router.message.register(
                self.handle_command,
                Command(command)
            )
    
    @abstractmethod
    async def handle_command(self, message: Message, db_session: AsyncSession = None, db_user: VPNUsers = None, user_roles: dict = None, locale: str = 'ru', **kwargs):
        """Обработка команды (переопределяется в наследниках)"""
        pass


class CallbackHandler(BaseHandler):
    """
    Базовый класс для обработчиков callback запросов
    """
    
    def __init__(self, callback_patterns: List[str], name: str = None):
        self.callback_patterns = callback_patterns
        super().__init__(name)
    
    def _register_handlers(self):
        """Регистрация обработчиков callback"""
        for pattern in self.callback_patterns:
            self.router.callback_query.register(
                self.handle_callback,
                F.data.startswith(pattern)
            )
    
    @abstractmethod
    async def handle_callback(self, callback: CallbackQuery, db_session: AsyncSession = None, db_user: VPNUsers = None, user_roles: dict = None, locale: str = 'ru', **kwargs):
        """Обработка callback (переопределяется в наследниках)"""
        pass


class StateHandler(BaseHandler):
    """
    Базовый класс для обработчиков состояний FSM
    """
    
    def __init__(self, states: StatesGroup, name: str = None):
        self.states = states
        super().__init__(name)
    
    def _register_handlers(self):
        """Регистрация обработчиков состояний"""
        # Переопределяется в наследниках для конкретных состояний
        pass
    
    async def set_state(self, state: FSMContext, new_state: State, data: Dict[str, Any] = None):
        """Установка состояния с данными"""
        await state.set_state(new_state)
        if data:
            await state.update_data(**data)
    
    async def clear_state(self, state: FSMContext):
        """Очистка состояния"""
        await state.clear()


class AdminHandler:
    """
    Базовый класс для административных обработчиков
    """
    
    
    
    async def _check_admin_rights(self, user: User) -> bool:
        """Проверка прав администратора"""
        return self.config.is_admin(user.id)


class SupportHandler(BaseHandler):
    """
    Базовый класс для обработчиков поддержки
    """
    
    def __init__(self, name: str = None):
        super().__init__(name)
        # Добавляем middleware для проверки прав поддержки
        from .middleware import AuthMiddleware
        self.router.message.middleware(AuthMiddleware('support'))
        self.router.callback_query.middleware(AuthMiddleware('support'))
    
    async def _check_support_rights(self, user: User) -> bool:
        """Проверка прав поддержки"""
        return self.config.is_support(user.id)


class MenuHandler(BaseHandler):
    """
    Базовый класс для обработчиков меню
    """
    
    def __init__(self, menu_keyboard: BaseKeyboard, name: str = None):
        self.menu_keyboard = menu_keyboard
        super().__init__(name)
    
    def _register_handlers(self):
        """Регистрация обработчиков меню"""
        # Команда для показа главного меню
        self.router.message.register(
            self.show_main_menu,
            Command('menu', 'start')
        )
        
        # Callback для возврата в главное меню
        self.router.callback_query.register(
            self.show_main_menu_callback,
            F.data == 'main_menu'
        )
    
    async def show_main_menu(self, message: Message, **kwargs):
        """Показ главного меню"""
        self.logger.debug(f"Вызвана show_main_menu для пользователя {message.from_user.id}")
        try:
            user_data = await self._get_user_data(message, **kwargs)
            keyboard = await self.menu_keyboard.get_keyboard(user_data)

            text = await self._get_menu_text(user_data)

            await message.answer(text, reply_markup=keyboard)
            self.logger.debug(f"Отправлено новое сообщение с главным меню для пользователя {message.from_user.id}")

        except Exception as e:
            self.logger.error(f"Error showing main menu: {e}", exc_info=True)
            await self._send_error(message, "Ошибка отображения меню")
    
    async def show_main_menu_callback(self, callback: CallbackQuery, **kwargs):
        """Показ главного меню через callback"""
        self.logger.debug(f"Вызвана show_main_menu_callback для пользователя {callback.from_user.id}")
        try:
            user_data = await self._get_user_data(callback, **kwargs)
            keyboard = await self.menu_keyboard.get_keyboard(user_data)

            text = await self._get_menu_text(user_data)

            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()
            self.logger.debug(f"Отредактировано сообщение с главным меню для пользователя {callback.from_user.id}")

        except Exception as e:
            self.logger.error(f"Error showing main menu callback: {e}", exc_info=True)
            await self._send_error(callback, "Ошибка отображения меню")
    
    @abstractmethod
    async def _get_menu_text(self, user_data: Dict[str, Any]) -> str:
        """Получение текста меню (переопределяется в наследниках)"""
        pass


class PaginationHandler(BaseHandler):
    """
    Базовый класс для обработчиков с пагинацией
    """
    
    def __init__(self, items_per_page: int = 5, name: str = None):
        self.items_per_page = items_per_page
        super().__init__(name)
    
    def _register_handlers(self):
        """Регистрация обработчиков пагинации"""
        # Callback для навигации по страницам
        self.router.callback_query.register(
            self.handle_pagination,
            F.data.startswith('page_')
        )
    
    async def handle_pagination(self, callback: CallbackQuery, **kwargs):
        """Обработка пагинации"""
        try:
            # Извлекаем номер страницы из callback_data
            page_data = callback.data.split('_')
            if len(page_data) >= 2:
                page = int(page_data[1])
                await self._show_page(callback, page)
            
            await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Error handling pagination: {e}")
            await self._send_error(callback, "Ошибка навигации")
    
    @abstractmethod
    async def _show_page(self, callback: CallbackQuery, page: int):
        """Показ страницы (переопределяется в наследниках)"""
        pass
    
    def _calculate_pagination(self, total_items: int, current_page: int) -> Dict[str, Any]:
        """Расчет параметров пагинации"""
        total_pages = (total_items + self.items_per_page - 1) // self.items_per_page
        
        start_index = current_page * self.items_per_page
        end_index = min(start_index + self.items_per_page, total_items)
        
        return {
            'total_pages': total_pages,
            'current_page': current_page,
            'start_index': start_index,
            'end_index': end_index,
            'has_prev': current_page > 0,
            'has_next': current_page < total_pages - 1
        }


class FormHandler(StateHandler):
    """
    Базовый класс для обработчиков форм
    """
    
    def __init__(self, form_states: StatesGroup, name: str = None):
        super().__init__(form_states, name)
        self.form_fields = {}  # field_name -> validation_function
    
    def add_field(self, field_name: str, state: State, validation_func=None):
        """Добавление поля формы"""
        self.form_fields[field_name] = {
            'state': state,
            'validation': validation_func
        }
    
    async def start_form(self, event: Union[Message, CallbackQuery], state: FSMContext):
        """Начало заполнения формы"""
        first_field = list(self.form_fields.keys())[0]
        first_state = self.form_fields[first_field]['state']
        
        await self.set_state(state, first_state)
        await self._ask_field(event, first_field)
    
    async def process_field(self, message: Message, state: FSMContext, field_name: str):
        """Обработка поля формы"""
        try:
            # Валидация поля
            field_config = self.form_fields[field_name]
            if field_config['validation']:
                value = await field_config['validation'](message.text)
            else:
                value = message.text
            
            # Сохраняем значение
            await state.update_data(**{field_name: value})
            
            # Переходим к следующему полю или завершаем форму
            await self._next_field_or_finish(message, state, field_name)
            
        except ValidationError as e:
            await self._send_error(message, str(e))
            await self._ask_field(message, field_name)  # Повторяем вопрос
    
    @abstractmethod
    async def _ask_field(self, event: Union[Message, CallbackQuery], field_name: str):
        """Запрос поля (переопределяется в наследниках)"""
        pass
    
    @abstractmethod
    async def _next_field_or_finish(self, message: Message, state: FSMContext, current_field: str):
        """Переход к следующему полю или завершение (переопределяется в наследниках)"""
        pass
    
    @abstractmethod
    async def _finish_form(self, message: Message, state: FSMContext, form_data: Dict[str, Any]):
        """Завершение формы (переопределяется в наследниках)"""
        pass
