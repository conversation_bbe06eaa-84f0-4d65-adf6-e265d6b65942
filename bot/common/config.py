"""
UnveilVPN Shop - Bot Configuration
Конфигурация для всех ботов
"""

import os
from dataclasses import dataclass
from typing import Optional, Dict, Any
from pathlib import Path


@dataclass
class BotConfig:
    """
    Конфигурация бота
    """
    
    # Основные настройки
    bot_token: str
    bot_type: str  # client, admin, support
    
    # База данных
    db_host: str = "localhost"
    db_port: int = 5432
    db_name: str = "unveilvpn"
    db_user: str = "unveilvpn_user"
    db_pass: str = ""
    
    # Redis (опционально)
    redis_url: Optional[str] = None
    
    # Webhook настройки
    webhook_mode: bool = False
    webhook_url: Optional[str] = None
    webhook_host: str = "0.0.0.0"
    webhook_port: int = 8000
    
    # Локализация
    default_locale: str = "ru"
    
    # Логирование
    log_level: str = "INFO"
    
    # Безопасность
    admin_user_ids: list = None
    support_user_ids: list = None
    
    # Внешние сервисы - Remnawave VPN Panel
    remnawave_panel_url: Optional[str] = None
    remnawave_api_key: Optional[str] = None
    remnawave_subscription_url: Optional[str] = None
    remnawave_protocols: list = None


    
    # Платежные системы
    yookassa_shop_id: Optional[str] = None
    yookassa_secret_key: Optional[str] = None
    cryptomus_merchant_id: Optional[str] = None
    cryptomus_api_key: Optional[str] = None
    
    # Дополнительные настройки
    test_period_enabled: bool = True
    shop_name: str = "UnveilVPN Shop"
    support_url: Optional[str] = None
    about_url: Optional[str] = None
    version: str = "1.0.0"
    
    def __post_init__(self):
        """Инициализация после создания объекта"""
        if self.admin_user_ids is None:
            self.admin_user_ids = []
        if self.support_user_ids is None:
            self.support_user_ids = []
        if self.remnawave_protocols is None:
            self.remnawave_protocols = ['vless', 'vmess', 'trojan', 'shadowsocks']
    
    @property
    def database_url(self) -> str:
        """URL для подключения к базе данных"""
        return f"postgresql+asyncpg://{self.db_user}:{self.db_pass}@{self.db_host}:{self.db_port}/{self.db_name}"
    
    @property
    def database_url_sync(self) -> str:
        """Синхронный URL для подключения к базе данных"""
        return f"postgresql+psycopg2://{self.db_user}:{self.db_pass}@{self.db_host}:{self.db_port}/{self.db_name}"
    
    def is_admin(self, user_id: int) -> bool:
        """Проверка, является ли пользователь администратором"""
        return user_id in self.admin_user_ids
    
    def is_support(self, user_id: int) -> bool:
        """Проверка, является ли пользователь сотрудником поддержки"""
        return user_id in self.support_user_ids
    
    def is_staff(self, user_id: int) -> bool:
        """Проверка, является ли пользователь сотрудником (админ или поддержка)"""
        return self.is_admin(user_id) or self.is_support(user_id)
    
    @classmethod
    def from_env(cls, bot_type: str) -> 'BotConfig':
        """Создание конфигурации из переменных окружения"""
        
        # Определяем токен бота в зависимости от типа
        token_env_map = {
            'client': 'CLIENT_BOT_TOKEN',
            'admin': 'ADMIN_BOT_TOKEN', 
            'support': 'SUPPORT_BOT_TOKEN'
        }
        
        bot_token = os.getenv(token_env_map.get(bot_type, 'BOT_TOKEN'))
        if not bot_token:
            raise ValueError(f"Не найден токен для бота типа {bot_type}")
        
        # Парсим списки пользователей
        admin_ids = []
        admin_ids_str = os.getenv('ADMIN_USER_IDS', '')
        if admin_ids_str:
            admin_ids = [int(uid.strip()) for uid in admin_ids_str.split(',') if uid.strip()]
        
        support_ids = []
        support_ids_str = os.getenv('SUPPORT_USER_IDS', '')
        if support_ids_str:
            support_ids = [int(uid.strip()) for uid in support_ids_str.split(',') if uid.strip()]
        
        return cls(
            # Основные настройки
            bot_token=bot_token,
            bot_type=bot_type,
            
            # База данных
            db_host=os.getenv('DB_HOST', 'localhost'),
            db_port=int(os.getenv('DB_PORT', 5432)),
            db_name=os.getenv('DB_NAME', 'unveilvpn'),
            db_user=os.getenv('DB_USER', 'unveilvpn_user'),
            db_pass=os.getenv('DB_PASS', ''),
            
            # Redis
            redis_url=os.getenv('REDIS_URL'),
            
            # Webhook
            webhook_mode=os.getenv('WEBHOOK_MODE', 'false').lower() == 'true',
            webhook_url=os.getenv('WEBHOOK_URL'),
            webhook_host=os.getenv('WEBHOOK_HOST', '0.0.0.0'),
            webhook_port=int(os.getenv('WEBHOOK_PORT', 8000)),
            
            # Локализация
            default_locale=os.getenv('DEFAULT_LOCALE', 'ru'),
            
            # Логирование
            
            
            # Безопасность
            admin_user_ids=admin_ids,
            support_user_ids=support_ids,
            
            # Внешние сервисы - Remnawave VPN Panel
            remnawave_panel_url=os.getenv('REMNAWAVE_PANEL_URL'),
            remnawave_api_key=os.getenv('REMNAWAVE_API_KEY'),
            remnawave_subscription_url=os.getenv('REMNAWAVE_SUBSCRIPTION_URL'),
            remnawave_protocols=os.getenv('REMNAWAVE_PROTOCOLS', 'vless vmess trojan shadowsocks').split(),


            
            # Платежные системы
            yookassa_shop_id=os.getenv('YOOKASSA_SHOP_ID'),
            yookassa_secret_key=os.getenv('YOOKASSA_SECRET_KEY'),
            cryptomus_merchant_id=os.getenv('CRYPTOMUS_MERCHANT_ID'),
            cryptomus_api_key=os.getenv('CRYPTOMUS_API_KEY'),
            
            # Дополнительные настройки
            test_period_enabled=os.getenv('TEST_PERIOD_ENABLED', 'true').lower() == 'true',
            shop_name=os.getenv('SHOP_NAME', 'UnveilVPN Shop'),
            support_url=os.getenv('SUPPORT_URL'),
            about_url=os.getenv('ABOUT_URL'),
            version=os.getenv('BOT_VERSION', '1.0.0')
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """Преобразование в словарь"""
        return {
            'bot_token': self.bot_token[:10] + '...' if self.bot_token else None,  # Скрываем токен
            'bot_type': self.bot_type,
            'db_host': self.db_host,
            'db_port': self.db_port,
            'db_name': self.db_name,
            'db_user': self.db_user,
            'webhook_mode': self.webhook_mode,
            'webhook_host': self.webhook_host,
            'webhook_port': self.webhook_port,
            'default_locale': self.default_locale,
            'log_level': self.log_level,
            'admin_user_ids_count': len(self.admin_user_ids),
            'support_user_ids_count': len(self.support_user_ids),
            'test_period_enabled': self.test_period_enabled,
            'shop_name': self.shop_name,
            'version': self.version
        }
    
    def validate(self) -> bool:
        """Валидация конфигурации"""
        errors = []
        
        if not self.bot_token:
            errors.append("Отсутствует токен бота")
        
        if self.bot_type not in ['client', 'admin', 'support']:
            errors.append(f"Неверный тип бота: {self.bot_type}")
        
        if not self.db_host:
            errors.append("Отсутствует хост базы данных")
        
        if not self.db_name:
            errors.append("Отсутствует имя базы данных")
        
        if not self.db_user:
            errors.append("Отсутствует пользователь базы данных")
        
        if self.webhook_mode and not self.webhook_url:
            errors.append("Webhook режим включен, но не указан webhook URL")
        
        if errors:
            raise ValueError(f"Ошибки конфигурации: {'; '.join(errors)}")
        
        return True


# Глобальная конфигурация (будет инициализирована в каждом боте)
config: Optional[BotConfig] = None


def get_config() -> BotConfig:
    """Получение глобальной конфигурации"""
    if config is None:
        raise RuntimeError("Конфигурация не инициализирована")
    return config


def init_config(bot_type: str) -> BotConfig:
    """Инициализация глобальной конфигурации"""
    global config
    config = BotConfig.from_env(bot_type)
    config.validate()
    return config


# Алиас для обратной совместимости
Config = BotConfig
