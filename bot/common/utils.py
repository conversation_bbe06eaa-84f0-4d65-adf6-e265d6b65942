"""
UnveilVPN Shop - Common Utils
Общие утилиты для всех ботов
"""

import re
import hashlib
import secrets
import string
from datetime import datetime, timezone, timedelta
from typing import Optional, Dict, Any, List, Union
from decimal import Decimal

from aiogram.utils.i18n import gettext as _


class BotUtils:
    """Общие утилиты для ботов"""
    
    @staticmethod
    def generate_random_string(length: int = 8, use_digits: bool = True, use_letters: bool = True) -> str:
        """Генерация случайной строки"""
        chars = ""
        if use_letters:
            chars += string.ascii_uppercase
        if use_digits:
            chars += string.digits
        
        if not chars:
            raise ValueError("Необходимо включить буквы или цифры")
        
        return ''.join(secrets.choice(chars) for _ in range(length))
    
    @staticmethod
    def generate_referral_code(length: int = 8) -> str:
        """Генерация реферального кода"""
        return BotUtils.generate_random_string(length, use_digits=True, use_letters=True)
    
    @staticmethod
    def generate_promocode(prefix: str = "", length: int = 8) -> str:
        """Генерация промокода"""
        code = BotUtils.generate_random_string(length)
        return f"{prefix}{code}" if prefix else code
    
    @staticmethod
    def hash_string(text: str, salt: str = "") -> str:
        """Хеширование строки"""
        return hashlib.sha256(f"{text}{salt}".encode()).hexdigest()
    
    @staticmethod
    def format_datetime(dt: datetime, format_type: str = "full") -> str:
        """Форматирование даты и времени"""
        if not dt:
            return _("Не указано")
        
        # Конвертируем в UTC если нет timezone
        if dt.tzinfo is None:
            dt = dt.replace(tzinfo=timezone.utc)
        
        # Конвертируем в московское время
        moscow_tz = timezone(timedelta(hours=3))
        dt_moscow = dt.astimezone(moscow_tz)
        
        if format_type == "date":
            return dt_moscow.strftime("%d.%m.%Y")
        elif format_type == "time":
            return dt_moscow.strftime("%H:%M")
        elif format_type == "short":
            return dt_moscow.strftime("%d.%m.%Y %H:%M")
        else:  # full
            return dt_moscow.strftime("%d.%m.%Y %H:%M:%S")
    
    @staticmethod
    def format_duration(days: int) -> str:
        """Форматирование продолжительности"""
        if days == 1:
            return _("1 день")
        elif days < 5:
            return _(f"{days} дня")
        elif days < 21:
            return _(f"{days} дней")
        elif days == 30:
            return _("1 месяц")
        elif days == 90:
            return _("3 месяца")
        elif days == 180:
            return _("6 месяцев")
        elif days == 365:
            return _("1 год")
        else:
            return _(f"{days} дней")
    
    @staticmethod
    def truncate_text(text: str, max_length: int = 100, suffix: str = "...") -> str:
        """Обрезка текста с добавлением суффикса"""
        if len(text) <= max_length:
            return text
        return text[:max_length - len(suffix)] + suffix
    
    @staticmethod
    def escape_markdown(text: str) -> str:
        """Экранирование символов для Markdown"""
        escape_chars = ['_', '*', '[', ']', '(', ')', '~', '`', '>', '#', '+', '-', '=', '|', '{', '}', '.', '!']
        for char in escape_chars:
            text = text.replace(char, f'\\{char}')
        return text
    
    @staticmethod
    def format_file_size(size_bytes: int) -> str:
        """Форматирование размера файла"""
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB", "TB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        
        return f"{size_bytes:.1f} {size_names[i]}"


class MessageFormatter:
    """Форматирование сообщений"""
    
    @staticmethod
    def format_user_info(user_data: Dict[str, Any]) -> str:
        """Форматирование информации о пользователе"""
        user = user_data.get('user')
        db_user = user_data.get('db_user')
        
        if not user:
            return _("Пользователь не найден")
        
        lines = [
            f"👤 <b>Пользователь</b>",
            f"ID: <code>{user.id}</code>",
        ]
        
        if user.username:
            lines.append(f"Username: @{user.username}")
        
        if user.first_name:
            lines.append(f"Имя: {user.first_name}")
        
        if user.last_name:
            lines.append(f"Фамилия: {user.last_name}")
        
        if db_user:
            lines.extend([
                f"Статус: {'🟢 Активен' if db_user.is_active else '🔴 Неактивен'}",
                f"Тест использован: {'✅' if db_user.is_test_used else '❌'}",
                f"Дата регистрации: {BotUtils.format_datetime(db_user.created_at, 'short')}"
            ])
            
            if db_user.referral_code:
                lines.append(f"Реферальный код: <code>{db_user.referral_code}</code>")
        
        return "\n".join(lines)
    
    @staticmethod
    def format_tariff_info(tariff: Dict[str, Any]) -> str:
        """Форматирование информации о тарифе"""
        lines = [
            f"💰 <b>{tariff['name']}</b>",
        ]
        
        if tariff.get('description'):
            lines.append(f"{tariff['description']}")
        
        lines.append(f"Продолжительность: {BotUtils.format_duration(tariff['duration_days'])}")
        
        # Цены
        prices = tariff.get('prices', {})
        price_lines = []
        
        if prices.get('rub'):
            price_lines.append(f"💳 {prices['rub']} ₽")
        if prices.get('usd'):
            price_lines.append(f"💵 ${prices['usd']}")
        if prices.get('stars'):
            price_lines.append(f"⭐ {prices['stars']} Stars")
        
        if price_lines:
            lines.append("Цены: " + " | ".join(price_lines))
        
        # Характеристики
        features = tariff.get('features', {})
        if features:
            lines.append("\n📋 <b>Характеристики:</b>")
            
            if features.get('devices_count'):
                lines.append(f"📱 Устройств: {features['devices_count']}")
            
            if features.get('traffic_limit'):
                lines.append(f"📊 Трафик: {features['traffic_limit']} GB")
            else:
                lines.append("📊 Трафик: Безлимит")
            
            if features.get('speed_limit'):
                lines.append(f"⚡ Скорость: {features['speed_limit']} Mbps")
            else:
                lines.append("⚡ Скорость: Безлимит")
            
            if features.get('locations'):
                locations = ", ".join(features['locations'])
                lines.append(f"🌍 Локации: {locations}")
        
        if tariff.get('is_popular'):
            lines.insert(1, "⭐ <i>Популярный тариф</i>")
        
        return "\n".join(lines)
    
    @staticmethod
    def format_payment_info(payment: Dict[str, Any]) -> str:
        """Форматирование информации о платеже"""
        lines = [
            f"💳 <b>Платеж #{payment.get('id', 'N/A')[:8]}</b>",
            f"Сумма: {payment.get('final_amount', 0)} {payment.get('currency', 'RUB')}",
            f"Метод: {MessageFormatter._format_payment_method(payment.get('payment_method'))}",
            f"Статус: {MessageFormatter._format_payment_status(payment.get('status'))}",
        ]
        
        if payment.get('created_at'):
            lines.append(f"Дата: {BotUtils.format_datetime(payment['created_at'], 'short')}")
        
        if payment.get('external_payment_id'):
            lines.append(f"ID платежа: <code>{payment['external_payment_id']}</code>")
        
        return "\n".join(lines)
    
    @staticmethod
    def _format_payment_method(method: str) -> str:
        """Форматирование метода платежа"""
        methods = {
            'yookassa': '💳 Банковская карта',
            'cryptomus': '₿ Криптовалюта',
            'telegram_stars': '⭐ Telegram Stars'
        }
        return methods.get(method, method)
    
    @staticmethod
    def _format_payment_status(status: str) -> str:
        """Форматирование статуса платежа"""
        statuses = {
            'pending': '🟡 Ожидает оплаты',
            'completed': '🟢 Оплачен',
            'failed': '🔴 Ошибка',
            'refunded': '🔄 Возврат'
        }
        return statuses.get(status, status)


class ValidationUtils:
    """Утилиты для валидации"""
    
    @staticmethod
    def validate_email(email: str) -> bool:
        """Валидация email"""
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email) is not None
    
    @staticmethod
    def validate_phone(phone: str) -> bool:
        """Валидация номера телефона"""
        # Убираем все символы кроме цифр и +
        clean_phone = re.sub(r'[^\d+]', '', phone)
        
        # Проверяем формат
        patterns = [
            r'^\+7\d{10}$',  # +7XXXXXXXXXX
            r'^8\d{10}$',    # 8XXXXXXXXXX
            r'^7\d{10}$',    # 7XXXXXXXXXX
        ]
        
        return any(re.match(pattern, clean_phone) for pattern in patterns)
    
    @staticmethod
    def validate_promocode(code: str) -> bool:
        """Валидация промокода"""
        # Промокод должен содержать только буквы и цифры, длина 4-20 символов
        pattern = r'^[A-Z0-9]{4,20}$'
        return re.match(pattern, code.upper()) is not None
    
    @staticmethod
    def validate_amount(amount: Union[str, int, float, Decimal]) -> bool:
        """Валидация суммы"""
        try:
            decimal_amount = Decimal(str(amount))
            return decimal_amount > 0
        except (ValueError, TypeError):
            return False
    
    @staticmethod
    def validate_telegram_id(telegram_id: Union[str, int]) -> bool:
        """Валидация Telegram ID"""
        try:
            tid = int(telegram_id)
            return 1 <= tid <= 2147483647  # Максимальное значение для 32-bit integer
        except (ValueError, TypeError):
            return False


class SecurityUtils:
    """Утилиты для безопасности"""
    
    @staticmethod
    def sanitize_input(text: str, max_length: int = 1000) -> str:
        """Очистка пользовательского ввода"""
        if not text:
            return ""
        
        # Убираем опасные символы
        text = re.sub(r'[<>"\']', '', text)
        
        # Ограничиваем длину
        text = text[:max_length]
        
        # Убираем лишние пробелы
        text = re.sub(r'\s+', ' ', text).strip()
        
        return text
    
    @staticmethod
    def mask_sensitive_data(data: str, mask_char: str = "*", visible_chars: int = 4) -> str:
        """Маскировка чувствительных данных"""
        if not data or len(data) <= visible_chars:
            return data
        
        visible_part = data[:visible_chars]
        masked_part = mask_char * (len(data) - visible_chars)
        
        return visible_part + masked_part
    
    @staticmethod
    def generate_secure_token(length: int = 32) -> str:
        """Генерация безопасного токена"""
        return secrets.token_urlsafe(length)
    
    @staticmethod
    def check_rate_limit(user_id: int, action: str, limit: int = 10, window: int = 60) -> bool:
        """Проверка лимита запросов (упрощенная версия)"""
        # В реальном приложении здесь должна быть проверка через Redis или БД
        # Пока возвращаем True (нет ограничений)
        return True


# Функции-хелперы

def format_price(amount: Union[int, float, Decimal], currency: str = "RUB") -> str:
    """Форматирование цены"""
    currency_symbols = {
        'RUB': '₽',
        'USD': '$',
        'EUR': '€',
        'STARS': '⭐'
    }
    
    symbol = currency_symbols.get(currency.upper(), currency)
    
    if currency.upper() == 'STARS':
        return f"{int(amount)} {symbol}"
    else:
        return f"{amount} {symbol}"


def format_percentage(value: Union[int, float], decimals: int = 1) -> str:
    """Форматирование процентов"""
    return f"{value:.{decimals}f}%"


def format_number(number: Union[int, float], thousands_sep: str = " ") -> str:
    """Форматирование числа с разделителями тысяч"""
    return f"{number:,}".replace(",", thousands_sep)
