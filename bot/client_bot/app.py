"""
UnveilVPN Shop - Client Bot Application
Основное приложение клиентского бота
"""

import logging
from typing import List

from ..common.base import BaseBotApplication
from ..common.config import BotConfig
from .handlers import (
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    Subscription<PERSON>andler,
    PaymentsHandler,
    SupportHandler,
    TestPeriodHandler,
    ReferralsHandler,
    PromocodesHandler
)


class ClientBotApplication(BaseBotApplication):
    """
    Приложение клиентского бота
    """
    
    def __init__(self, config: BotConfig):
        super().__init__(config)
        self.logger = logging.getLogger("ClientBot")
    
    def get_bot_type(self) -> str:
        """Возвращает тип бота"""
        return "client"
    
    def get_handlers(self) -> List:
        """Возвращает список обработчиков для клиентского бота"""
        return [
            Start<PERSON><PERSON><PERSON>(),
            <PERSON><PERSON><PERSON><PERSON><PERSON>(),
            SubscriptionHandler(),
            PaymentsHandler(),
            SupportHandler(),
            TestPeriodHandler(),
            ReferralsHandler(),
            PromocodesHandler()
        ]
    
    def get_webhook_routes(self) -> List[tuple]:
        """Возвращает webhook маршруты для клиентского бота"""
        return [
            # Webhook для платежных систем
            ("POST", "/webhooks/yookassa", self._handle_yookassa_webhook),
            ("POST", "/webhooks/cryptomus", self._handle_cryptomus_webhook),
            ("POST", "/webhooks/telegram_stars", self._handle_stars_webhook),
        ]
    
    async def on_startup(self):
        """Действия при запуске клиентского бота"""
        self.logger.info("🚀 Клиентский бот запущен")
        
        # Здесь можно добавить инициализацию специфичных для клиентского бота ресурсов
        # Например, подключение к VPN панели, проверка платежных систем и т.д.
        
        await self._check_external_services()
    
    async def on_shutdown(self):
        """Действия при остановке клиентского бота"""
        self.logger.info("🛑 Клиентский бот остановлен")
        
        # Здесь можно добавить очистку ресурсов
    
    async def _check_external_services(self):
        """Проверка доступности внешних сервисов"""
        try:
            # Проверяем VPN панель (Remnawave)
            if self.config.remnawave_panel_url:
                self.logger.info(f"Проверка Remnawave панели: {self.config.remnawave_panel_url}")
                # Здесь будет проверка доступности панели
            
            # Проверяем платежные системы
            if self.config.yookassa_shop_id:
                self.logger.info("YooKassa настроена")
            
            if self.config.cryptomus_merchant_id:
                self.logger.info("Cryptomus настроен")
            
            self.logger.info("✅ Проверка внешних сервисов завершена")
            
        except Exception as e:
            self.logger.error(f"❌ Ошибка проверки внешних сервисов: {e}")
    
    async def _handle_yookassa_webhook(self, request):
        """Обработка webhook от YooKassa"""
        try:
            from aiohttp import web
            
            # Получаем данные webhook
            data = await request.json()
            
            # Здесь будет обработка webhook от YooKassa
            self.logger.info(f"Получен webhook от YooKassa: {data.get('event', {}).get('type')}")
            
            # Обрабатываем платеж
            await self._process_yookassa_payment(data)
            
            return web.Response(status=200)
            
        except Exception as e:
            self.logger.error(f"Ошибка обработки YooKassa webhook: {e}")
            return web.Response(status=500)
    
    async def _handle_cryptomus_webhook(self, request):
        """Обработка webhook от Cryptomus"""
        try:
            from aiohttp import web
            
            # Получаем данные webhook
            data = await request.json()
            
            # Здесь будет обработка webhook от Cryptomus
            self.logger.info(f"Получен webhook от Cryptomus: {data.get('type')}")
            
            # Обрабатываем платеж
            await self._process_cryptomus_payment(data)
            
            return web.Response(status=200)
            
        except Exception as e:
            self.logger.error(f"Ошибка обработки Cryptomus webhook: {e}")
            return web.Response(status=500)
    
    async def _handle_stars_webhook(self, request):
        """Обработка webhook от Telegram Stars"""
        try:
            from aiohttp import web
            
            # Получаем данные webhook
            data = await request.json()
            
            # Здесь будет обработка webhook от Telegram Stars
            self.logger.info(f"Получен webhook от Telegram Stars")
            
            # Обрабатываем платеж
            await self._process_stars_payment(data)
            
            return web.Response(status=200)
            
        except Exception as e:
            self.logger.error(f"Ошибка обработки Telegram Stars webhook: {e}")
            return web.Response(status=500)
    
    async def _process_yookassa_payment(self, data):
        """Обработка платежа YooKassa"""
        # Здесь будет логика обработки платежа YooKassa
        # Пока заглушка
        pass
    
    async def _process_cryptomus_payment(self, data):
        """Обработка платежа Cryptomus"""
        # Здесь будет логика обработки платежа Cryptomus
        # Пока заглушка
        pass
    
    async def _process_stars_payment(self, data):
        """Обработка платежа Telegram Stars"""
        # Здесь будет логика обработки платежа Telegram Stars
        # Пока заглушка
        pass
