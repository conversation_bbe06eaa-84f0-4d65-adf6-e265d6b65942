"""
UnveilVPN Shop - Subscription Keyboards
Клавиатуры для управления подписками с улучшенным UX
"""

from typing import Dict, Any, List

from aiogram.types import InlineKeyboardMarkup
from aiogram.utils.i18n import gettext as _

from ...common.keyboards import InlineKeyboard


class SubscriptionKeyboard(InlineKeyboard):
    """
    Клавиатура управления подписками
    """
    
    def __init__(self, has_subscription: bool = False):
        super().__init__(max_width=2)
        self.has_subscription = has_subscription
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры подписок"""
        
        if self.has_subscription:
            # Пользователь с активной подпиской
            self.add_button(_("🔄 Обновить инфо"), "subscription_info")
            self.add_button(_("📱 Конфигурации"), "manage_configs")
            
            self.add_button(_("⏰ Продлить"), "extend_subscription")
            self.add_button(_("📊 Статистика"), "subscription_stats")
            
            self.add_button(_("🎧 Поддержка"), "support")
        else:
            # Пользователь без подписки
            self.add_button(_("💰 Купить VPN"), "buy_vpn")
            self.add_button(_("🎁 Тест 7 дней"), "test_period")
            
            self.add_button(_("💡 О тарифах"), "tariff_info")
            self.add_button(_("❓ Как это работает"), "how_it_works")
        
        # Возврат в главное меню
        self.add_main_menu_button()
        
        return self.build()


class ConfigKeyboard(InlineKeyboard):
    """Клавиатура управления конфигурациями"""
    
    def __init__(self, configs: List = None, show_download: bool = False):
        super().__init__(max_width=2)
        self.configs = configs or []
        self.show_download = show_download
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры конфигураций"""
        
        # Создание новых конфигураций (Remnawave протоколы)
        self.add_button(_("🔐 VLESS"), "generate_config_vless")
        self.add_button(_("⚡ VMess"), "generate_config_vmess")
        self.add_button(_("🛡️ Trojan"), "generate_config_trojan")
        self.add_button(_("🔒 Shadowsocks"), "generate_config_shadowsocks")
        
        # Существующие конфигурации Remnawave
        if self.configs:
            for config in self.configs[:5]:  # Максимум 5 конфигураций
                config_id = config.get('id', 'unknown')
                device_name = config.get('device_name', 'Устройство')
                protocol = config.get('protocol', 'vless').upper()

                # Эмодзи для протоколов Remnawave
                protocol_emoji = {
                    'VLESS': '🔐',
                    'VMESS': '⚡',
                    'TROJAN': '🛡️',
                    'SHADOWSOCKS': '🔒'
                }.get(protocol, '📱')

                # Кнопки для каждой конфигурации
                self.add_button(
                    f"{protocol_emoji} {device_name} ({protocol})",
                    f"download_config_{config_id}"
                )
                self.add_button(
                    f"🗑 Удалить",
                    f"revoke_config_{config_id}"
                )
        
        # Дополнительные действия
        if self.show_download and self.configs:
            latest_config = self.configs[-1]
            config_id = latest_config.get('id', 'unknown')
            self.add_button(_("📥 Скачать"), f"download_config_{config_id}")
        
        # Навигация
        self.add_button(_("🔄 Обновить"), "manage_configs")
        self.add_button(_("⬅️ К подписке"), "subscription_info")
        
        return self.build()


class TariffComparisonKeyboard(InlineKeyboard):
    """Клавиатура сравнения тарифов"""
    
    def __init__(self, tariffs: List):
        super().__init__(max_width=1)
        self.tariffs = tariffs
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры сравнения тарифов"""
        
        # Кнопки для каждого тарифа
        for tariff in self.tariffs:
            # Определяем эмодзи для тарифа
            if tariff.is_popular:
                emoji = "🔥"
            elif tariff.duration_days >= 365:
                emoji = "💎"
            elif tariff.duration_days >= 90:
                emoji = "⭐"
            else:
                emoji = "📦"
            
            # Цена в рублях
            rub_price = tariff.prices.get('RUB', 0)
            
            button_text = f"{emoji} {tariff.name} - {rub_price} ₽"
            self.add_button(button_text, f"tariff_{tariff.id}")
        
        # Дополнительные действия
        self.add_button(_("📊 Сравнить все"), "compare_tariffs")
        self.add_button(_("💡 Помощь в выборе"), "tariff_help")
        
        # Возврат
        self.add_button(_("⬅️ Назад"), "subscription_info")
        
        return self.build()


class PaymentProgressKeyboard(InlineKeyboard):
    """Клавиатура прогресса оплаты"""
    
    def __init__(self, current_step: int = 1):
        super().__init__(max_width=2)
        self.current_step = current_step
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры прогресса оплаты"""
        
        if self.current_step == 1:
            # Шаг 1: Выбор тарифа
            self.add_button(_("⬅️ Изменить тариф"), "buy_vpn")
            self.add_button(_("➡️ Продолжить"), "select_payment_method")
        
        elif self.current_step == 2:
            # Шаг 2: Способ оплаты
            self.add_button(_("💳 Банковская карта"), "pay_yookassa")
            self.add_button(_("₿ Криптовалюта"), "pay_cryptomus")
            
            self.add_button(_("⭐ Telegram Stars"), "pay_stars")
            self.add_button(_("🎫 Промокод"), "enter_promocode")
            
            self.add_button(_("⬅️ Назад"), "buy_vpn")
        
        elif self.current_step == 3:
            # Шаг 3: Оплата
            self.add_button(_("💳 Оплатить"), "process_payment")
            self.add_button(_("🔄 Проверить статус"), "check_payment")
            
            self.add_button(_("⬅️ Изменить способ"), "select_payment_method")
            self.add_button(_("❌ Отменить"), "cancel_payment")
        
        return self.build()


class SubscriptionStatsKeyboard(InlineKeyboard):
    """Клавиатура статистики подписки"""
    
    def __init__(self):
        super().__init__(max_width=2)
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры статистики"""
        
        # Статистика
        self.add_button(_("📊 Трафик"), "traffic_stats")
        self.add_button(_("⏱ Время онлайн"), "online_stats")
        
        self.add_button(_("🌍 Серверы"), "server_stats")
        self.add_button(_("📱 Устройства"), "device_stats")
        
        # Экспорт
        self.add_button(_("📄 Отчет"), "export_stats")
        
        # Возврат
        self.add_button(_("⬅️ К подписке"), "subscription_info")
        
        return self.build()


class QuickActionsKeyboard(InlineKeyboard):
    """Клавиатура быстрых действий"""
    
    def __init__(self, has_subscription: bool = False):
        super().__init__(max_width=2)
        self.has_subscription = has_subscription
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры быстрых действий"""
        
        if self.has_subscription:
            # Быстрые действия для подписчиков
            self.add_button(_("📱 Новая конфигурация"), "quick_generate_config")
            self.add_button(_("🔄 Продлить на месяц"), "quick_extend_month")
            
            self.add_button(_("📊 Мой трафик"), "quick_traffic")
            self.add_button(_("🎧 Поддержка"), "support")
        else:
            # Быстрые действия для новых пользователей
            self.add_button(_("🚀 Быстрая покупка"), "quick_buy")
            self.add_button(_("🎁 Тест 7 дней"), "test_period")
            
            self.add_button(_("💡 Как выбрать тариф"), "tariff_guide")
            self.add_button(_("❓ Частые вопросы"), "faq")
        
        # Возврат
        self.add_main_menu_button()
        
        return self.build()


class TestPeriodKeyboard(InlineKeyboard):
    """Клавиатура тестового периода"""
    
    def __init__(self):
        super().__init__(max_width=1)
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры тестового периода"""
        
        # Активация тестового периода
        self.add_button(_("🎁 Активировать тест"), "activate_test_period")
        
        # Информация
        self.add_button(_("ℹ️ Условия тестового периода"), "test_period_info")
        self.add_button(_("📖 Как пользоваться"), "test_period_guide")
        
        # Альтернативы
        self.add_button(_("💰 Лучше купить тариф"), "buy_vpn")
        
        # Возврат
        self.add_button(_("⬅️ Назад"), "subscription_info")
        
        return self.build()


class SubscriptionHelpKeyboard(InlineKeyboard):
    """Клавиатура помощи по подпискам"""
    
    def __init__(self):
        super().__init__(max_width=2)
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры помощи"""
        
        # Разделы помощи
        self.add_button(_("🔧 Настройка"), "help_setup")
        self.add_button(_("📱 Приложения"), "help_apps")
        
        self.add_button(_("🌍 Серверы"), "help_servers")
        self.add_button(_("⚡ Скорость"), "help_speed")
        
        self.add_button(_("🔒 Безопасность"), "help_security")
        self.add_button(_("❓ Проблемы"), "help_troubleshooting")
        
        # Контакты
        self.add_button(_("🎧 Связаться с поддержкой"), "support")
        
        # Возврат
        self.add_button(_("⬅️ Назад"), "subscription_info")
        
        return self.build()
