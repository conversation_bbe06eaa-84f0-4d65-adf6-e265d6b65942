"""
UnveilVPN Shop - Cancel Keyboard
Клавиатура отмены для клиентского бота
"""

from typing import Dict, Any
from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton

from bot.common.keyboards import BaseKeyboard


class CancelKeyboard(BaseKeyboard):
    """Клавиатура отмены"""
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры отмены"""
        buttons = [
            [InlineKeyboardButton(
                text="❌ Отмена",
                callback_data="cancel"
            )]
        ]
        
        return InlineKeyboardMarkup(inline_keyboard=buttons)
    
    async def get_back_keyboard(self, back_callback: str = "main_menu") -> InlineKeyboardMarkup:
        """Получение клавиатуры с кнопкой назад"""
        buttons = [
            [InlineKeyboardButton(
                text="⬅️ Назад",
                callback_data=back_callback
            )],
            [InlineKeyboardButton(
                text="❌ Отмена",
                callback_data="cancel"
            )]
        ]
        
        return InlineKeyboardMarkup(inline_keyboard=buttons)
    
    async def get_confirm_cancel_keyboard(self, confirm_callback: str, cancel_callback: str = "cancel") -> InlineKeyboardMarkup:
        """Получение клавиатуры подтверждения с отменой"""
        buttons = [
            [
                InlineKeyboardButton(
                    text="✅ Подтвердить",
                    callback_data=confirm_callback
                ),
                InlineKeyboardButton(
                    text="❌ Отмена",
                    callback_data=cancel_callback
                )
            ]
        ]
        
        return InlineKeyboardMarkup(inline_keyboard=buttons)
