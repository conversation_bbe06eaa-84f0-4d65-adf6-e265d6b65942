"""
UnveilVPN Shop - Tariffs Keyboards
Клавиатуры для работы с тарифами в клиентском боте
"""

from typing import Dict, Any, List, Optional

from aiogram.types import InlineKeyboardMarkup
from aiogram.utils.i18n import gettext as _

from ...common.keyboards import InlineKeyboard
from ...common.utils import BotUtils


class TariffSelectionKeyboard(InlineKeyboard):
    """
    Клавиатура выбора тарифов
    """
    
    def __init__(
        self, 
        tariffs: List,
        is_popular: bool = False,
        filter_duration: Optional[int] = None,
        sort_by: Optional[str] = None
    ):
        super().__init__(max_width=1)
        self.tariffs = tariffs
        self.is_popular = is_popular
        self.filter_duration = filter_duration
        self.sort_by = sort_by
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры выбора тарифов"""
        
        # Добавляем кнопки тарифов
        for tariff in self.tariffs[:8]:  # Показываем максимум 8 тарифов
            button_text = await self._format_tariff_button(tariff)
            self.add_button(button_text, f"tariff_{tariff.id}")
        
        # Фильтры и сортировка (если не популярные тарифы)
        if not self.is_popular:
            await self._add_filter_buttons()
            await self._add_sort_buttons()
        
        # Дополнительные действия
        if not self.is_popular:
            self.add_button(_("⭐ Популярные тарифы"), "popular_tariffs")
        
        # Тестовый период (если доступен)
        db_user = user_data.get('db_user') if user_data else None
        if (db_user and not db_user.is_test_used and 
            hasattr(self, 'config') and getattr(self.config, 'test_period_enabled', True)):
            self.add_button(_("🎁 Бесплатный тест 7 дней"), "test_period")
        
        # Возврат в главное меню
        self.add_main_menu_button()
        
        return self.build()
    
    async def _format_tariff_button(self, tariff) -> str:
        """Форматирование кнопки тарифа"""
        # Иконки статуса
        popular_icon = "⭐ " if tariff.is_popular else ""
        discount_icon = "🏷️ " if tariff.discount_percent > 0 else ""
        
        # Основная цена (приоритет: рубли -> доллары -> stars)
        price_text = ""
        if tariff.prices.get('rub', 0) > 0:
            price = tariff.prices['rub']
            if tariff.discount_percent > 0:
                original_price = price / (1 - tariff.discount_percent / 100)
                price_text = f"~~{original_price:.0f}~~ {price:.0f} ₽"
            else:
                price_text = f"{price:.0f} ₽"
        elif tariff.prices.get('usd', 0) > 0:
            price_text = f"${tariff.prices['usd']}"
        elif tariff.prices.get('stars', 0) > 0:
            price_text = f"{tariff.prices['stars']} ⭐"
        
        # Продолжительность
        duration_text = BotUtils.format_duration(tariff.duration_days)
        
        return f"{popular_icon}{discount_icon}{tariff.name} - {price_text} ({duration_text})"
    
    async def _add_filter_buttons(self):
        """Добавление кнопок фильтрации"""
        # Фильтры по продолжительности
        durations = [7, 30, 90, 365]  # Неделя, месяц, 3 месяца, год
        
        filter_buttons = []
        for duration in durations:
            duration_text = BotUtils.format_duration(duration)
            is_active = self.filter_duration == duration
            button_text = f"{'✅' if is_active else '⚪'} {duration_text}"
            filter_buttons.append({
                'text': button_text,
                'callback_data': f"filter_duration_{duration}"
            })
        
        # Добавляем кнопки фильтров по 2 в ряд
        for i in range(0, len(filter_buttons), 2):
            row_buttons = filter_buttons[i:i+2]
            for btn in row_buttons:
                self.add_button(btn['text'], btn['callback_data'])
    
    async def _add_sort_buttons(self):
        """Добавление кнопок сортировки"""
        sort_options = [
            ('popular', _('По популярности')),
            ('price_asc', _('Дешевле')),
            ('price_desc', _('Дороже')),
            ('duration', _('По сроку'))
        ]
        
        for sort_key, sort_name in sort_options:
            is_active = self.sort_by == sort_key
            button_text = f"{'🔸' if is_active else '🔹'} {sort_name}"
            self.add_button(button_text, f"sort_{sort_key}")


class TariffDetailsKeyboard(InlineKeyboard):
    """Клавиатура деталей тарифа"""
    
    def __init__(self, tariff_id: str):
        super().__init__(max_width=2)
        self.tariff_id = tariff_id
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры деталей тарифа"""
        
        # Основные действия
        self.add_button(_("💳 Купить"), f"buy_tariff_{self.tariff_id}")
        self.add_button(_("🔄 Сравнить"), f"compare_tariff_{self.tariff_id}")
        
        # Дополнительная информация
        self.add_button(_("📋 Характеристики"), f"features_{self.tariff_id}")
        self.add_button(_("❓ Вопросы"), f"faq_{self.tariff_id}")
        
        # Навигация
        self.add_button(_("⬅️ К списку тарифов"), "buy_vpn")
        self.add_main_menu_button()
        
        return self.build()


class PaymentMethodKeyboard(InlineKeyboard):
    """Клавиатура выбора способа оплаты"""
    
    def __init__(self, available_methods: List[str], tariff_id: str):
        super().__init__(max_width=1)
        self.available_methods = available_methods
        self.tariff_id = tariff_id
        
        # Маппинг методов оплаты
        self.method_mapping = {
            'yookassa': {
                'text': _('💳 Банковская карта (Россия)'),
                'callback': f'pay_yookassa_{tariff_id}'
            },
            'cryptomus': {
                'text': _('₿ Криптовалюта'),
                'callback': f'pay_cryptomus_{tariff_id}'
            },
            'telegram_stars': {
                'text': _('⭐ Telegram Stars'),
                'callback': f'pay_stars_{tariff_id}'
            }
        }
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры способов оплаты"""
        
        # Добавляем доступные способы оплаты
        for method in self.available_methods:
            if method in self.method_mapping:
                method_info = self.method_mapping[method]
                self.add_button(method_info['text'], method_info['callback'])
        
        # Дополнительные опции
        self.add_button(_("🎫 Ввести промокод"), f"promocode_{self.tariff_id}")
        
        # Навигация
        self.add_button(_("⬅️ К тарифу"), f"tariff_{self.tariff_id}")
        self.add_button(_("🏠 Главное меню"), "main_menu")
        
        return self.build()


class TariffComparisonKeyboard(InlineKeyboard):
    """Клавиатура сравнения тарифов"""
    
    def __init__(self, tariff_ids: List[str]):
        super().__init__(max_width=2)
        self.tariff_ids = tariff_ids
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры сравнения тарифов"""
        
        # Действия с выбранными тарифами
        if len(self.tariff_ids) >= 2:
            self.add_button(_("📊 Сравнить"), "compare_selected")
            self.add_button(_("🗑️ Очистить"), "clear_comparison")
        
        # Добавить еще тарифы для сравнения
        if len(self.tariff_ids) < 3:
            self.add_button(_("➕ Добавить тариф"), "add_to_comparison")
        
        # Навигация
        self.add_button(_("⬅️ К списку тарифов"), "buy_vpn")
        
        return self.build()


class TariffFeaturesKeyboard(InlineKeyboard):
    """Клавиатура характеристик тарифа"""
    
    def __init__(self, tariff_id: str):
        super().__init__(max_width=2)
        self.tariff_id = tariff_id
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры характеристик тарифа"""
        
        # Детальная информация
        self.add_button(_("🌍 Серверы"), f"servers_{self.tariff_id}")
        self.add_button(_("📱 Устройства"), f"devices_{self.tariff_id}")
        
        self.add_button(_("⚡ Скорость"), f"speed_{self.tariff_id}")
        self.add_button(_("📊 Трафик"), f"traffic_{self.tariff_id}")
        
        # Техническая информация
        self.add_button(_("🔧 Протоколы"), f"protocols_{self.tariff_id}")
        self.add_button(_("🛡️ Безопасность"), f"security_{self.tariff_id}")
        
        # Навигация
        self.add_button(_("⬅️ К тарифу"), f"tariff_{self.tariff_id}")
        
        return self.build()


class PromocodeKeyboard(InlineKeyboard):
    """Клавиатура ввода промокода"""
    
    def __init__(self, tariff_id: str):
        super().__init__(max_width=2)
        self.tariff_id = tariff_id
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры промокода"""
        
        # Популярные промокоды (если есть)
        self.add_button(_("🎁 WELCOME10"), f"apply_promo_WELCOME10_{self.tariff_id}")
        self.add_button(_("🎉 FIRST20"), f"apply_promo_FIRST20_{self.tariff_id}")
        
        # Действия
        self.add_button(_("✏️ Ввести свой"), f"enter_promo_{self.tariff_id}")
        self.add_button(_("❌ Без промокода"), f"no_promo_{self.tariff_id}")
        
        # Навигация
        self.add_button(_("⬅️ К способам оплаты"), f"buy_tariff_{self.tariff_id}")
        
        return self.build()


class PaymentUrlKeyboard(InlineKeyboard):
    """Клавиатура с URL для оплаты"""

    def __init__(self, payment_url: str, payment_id: str):
        super().__init__(max_width=1)
        self.payment_url = payment_url
        self.payment_id = payment_id

    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры с URL оплаты"""

        # Кнопка оплаты
        self.add_button(_("💳 Перейти к оплате"), url=self.payment_url)

        # Проверка статуса
        self.add_button(_("🔄 Проверить статус"), f"check_payment_{self.payment_id}")

        # Отмена
        self.add_button(_("❌ Отменить"), "cancel_payment")

        return self.build()


class PaymentStatusKeyboard(InlineKeyboard):
    """Клавиатура проверки статуса платежа"""

    def __init__(self, payment_id: str):
        super().__init__(max_width=1)
        self.payment_id = payment_id

    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры статуса платежа"""

        # Проверка статуса
        self.add_button(_("🔄 Обновить статус"), f"check_payment_{self.payment_id}")

        # Поддержка
        self.add_button(_("💬 Связаться с поддержкой"), "contact_support")

        # Главное меню
        self.add_main_menu_button()

        return self.build()
