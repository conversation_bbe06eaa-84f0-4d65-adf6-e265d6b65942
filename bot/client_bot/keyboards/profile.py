"""
UnveilVPN Shop - Profile Keyboards
Клавиатуры для профиля пользователя
"""

from typing import Dict, Any, Optional
from datetime import datetime

from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton
from aiogram.utils.keyboard import InlineKeyboardBuilder
from aiogram.utils.i18n import gettext as _

from bot.common.keyboards import InlineKeyboard
from bot.db.models import VPNUsers


class ProfileKeyboard(InlineKeyboard):
    """Клавиатура профиля пользователя"""
    
    def __init__(self, user: Optional[VPNUsers] = None):
        super().__init__(max_width=2)
        self.user = user
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры профиля"""
        user = user_data.get('db_user') if user_data else self.user
        
        # Основная информация
        self.add_button(_("📊 Моя подписка"), "profile_subscription")
        self.add_button(_("💳 История платежей"), "profile_payments")
        
        # Реферальная система
        self.add_button(_("👥 Рефералы"), "profile_referrals")
        self.add_button(_("🎫 Промокоды"), "profile_promocodes")
        
        # Настройки
        self.add_button(_("⚙️ Настройки"), "profile_settings")
        self.add_button(_("📞 Поддержка"), "profile_support")
        
        # Возврат в главное меню
        self.add_button(_("🔙 Главное меню"), "main_menu")
        
        return self.build()


def get_subscription_info_keyboard(has_active: bool = False) -> InlineKeyboardMarkup:
    """Клавиатура информации о подписке"""
    builder = InlineKeyboardBuilder()
    
    if has_active:
        builder.row(
            InlineKeyboardButton(
                text="🔄 Продлить подписку",
                callback_data="extend_subscription"
            )
        )
        
        builder.row(
            InlineKeyboardButton(
                text="📱 Получить конфиг",
                callback_data="get_vpn_config"
            )
        )
        
        builder.row(
            InlineKeyboardButton(
                text="❌ Отменить подписку",
                callback_data="cancel_subscription"
            )
        )
    else:
        builder.row(
            InlineKeyboardButton(
                text="💰 Купить подписку",
                callback_data="buy_subscription"
            )
        )
        
        builder.row(
            InlineKeyboardButton(
                text="🎁 Активировать тест",
                callback_data="activate_trial"
            )
        )
    
    builder.row(
        InlineKeyboardButton(
            text="🔙 Профиль",
            callback_data="profile_menu"
        )
    )
    
    return builder.as_markup()


def get_payment_history_keyboard(page: int = 0, has_next: bool = False, has_prev: bool = False) -> InlineKeyboardMarkup:
    """Клавиатура истории платежей"""
    builder = InlineKeyboardBuilder()
    
    # Навигация
    nav_buttons = []
    
    if has_prev:
        nav_buttons.append(
            InlineKeyboardButton(
                text="⬅️ Предыдущая",
                callback_data=f"payment_history:{page-1}"
            )
        )
    
    if has_next:
        nav_buttons.append(
            InlineKeyboardButton(
                text="➡️ Следующая",
                callback_data=f"payment_history:{page+1}"
            )
        )
    
    if nav_buttons:
        builder.row(*nav_buttons)
    
    # Дополнительные действия
    builder.row(
        InlineKeyboardButton(
            text="📊 Статистика",
            callback_data="payment_stats"
        )
    )
    
    builder.row(
        InlineKeyboardButton(
            text="🔙 Профиль",
            callback_data="profile_menu"
        )
    )
    
    return builder.as_markup()


def get_referral_keyboard(referral_code: str, referral_count: int = 0) -> InlineKeyboardMarkup:
    """Клавиатура реферальной системы"""
    builder = InlineKeyboardBuilder()
    
    builder.row(
        InlineKeyboardButton(
            text="📤 Поделиться ссылкой",
            callback_data="share_referral_link"
        )
    )
    
    if referral_count > 0:
        builder.row(
            InlineKeyboardButton(
                text=f"👥 Мои рефералы ({referral_count})",
                callback_data="view_referrals"
            )
        )
        
        builder.row(
            InlineKeyboardButton(
                text="💰 История бонусов",
                callback_data="referral_bonuses"
            )
        )
    
    builder.row(
        InlineKeyboardButton(
            text="ℹ️ Как это работает",
            callback_data="referral_info"
        )
    )
    
    builder.row(
        InlineKeyboardButton(
            text="🔙 Профиль",
            callback_data="profile_menu"
        )
    )
    
    return builder.as_markup()


def get_settings_keyboard() -> InlineKeyboardMarkup:
    """Клавиатура настроек профиля"""
    builder = InlineKeyboardBuilder()
    
    builder.row(
        InlineKeyboardButton(
            text="🌐 Язык",
            callback_data="settings_language"
        )
    )
    
    builder.row(
        InlineKeyboardButton(
            text="🔔 Уведомления",
            callback_data="settings_notifications"
        )
    )
    
    builder.row(
        InlineKeyboardButton(
            text="🔐 Безопасность",
            callback_data="settings_security"
        )
    )
    
    builder.row(
        InlineKeyboardButton(
            text="📱 Устройства",
            callback_data="settings_devices"
        )
    )
    
    builder.row(
        InlineKeyboardButton(
            text="🗑️ Удалить аккаунт",
            callback_data="delete_account"
        )
    )
    
    builder.row(
        InlineKeyboardButton(
            text="🔙 Профиль",
            callback_data="profile_menu"
        )
    )
    
    return builder.as_markup()


def get_language_keyboard(current_lang: str = "ru") -> InlineKeyboardMarkup:
    """Клавиатура выбора языка"""
    builder = InlineKeyboardBuilder()
    
    languages = [
        ("🇷🇺 Русский", "ru"),
        ("🇺🇸 English", "en"),
        ("🇪🇸 Español", "es"),
        ("🇩🇪 Deutsch", "de"),
        ("🇫🇷 Français", "fr")
    ]
    
    for lang_name, lang_code in languages:
        text = f"✅ {lang_name}" if lang_code == current_lang else lang_name
        builder.row(
            InlineKeyboardButton(
                text=text,
                callback_data=f"set_language:{lang_code}"
            )
        )
    
    builder.row(
        InlineKeyboardButton(
            text="🔙 Настройки",
            callback_data="profile_settings"
        )
    )
    
    return builder.as_markup()


def get_notifications_keyboard(notifications_enabled: bool = True) -> InlineKeyboardMarkup:
    """Клавиатура настроек уведомлений"""
    builder = InlineKeyboardBuilder()
    
    # Общие уведомления
    status_text = "✅ Включены" if notifications_enabled else "❌ Отключены"
    action = "disable" if notifications_enabled else "enable"
    
    builder.row(
        InlineKeyboardButton(
            text=f"🔔 Уведомления: {status_text}",
            callback_data=f"notifications_{action}"
        )
    )
    
    if notifications_enabled:
        # Типы уведомлений
        builder.row(
            InlineKeyboardButton(
                text="💳 О платежах",
                callback_data="notifications_payments"
            )
        )
        
        builder.row(
            InlineKeyboardButton(
                text="⏰ О подписке",
                callback_data="notifications_subscription"
            )
        )
        
        builder.row(
            InlineKeyboardButton(
                text="🎁 О бонусах",
                callback_data="notifications_bonuses"
            )
        )
        
        builder.row(
            InlineKeyboardButton(
                text="📢 Новости",
                callback_data="notifications_news"
            )
        )
    
    builder.row(
        InlineKeyboardButton(
            text="🔙 Настройки",
            callback_data="profile_settings"
        )
    )
    
    return builder.as_markup()


def get_delete_account_keyboard() -> InlineKeyboardMarkup:
    """Клавиатура подтверждения удаления аккаунта"""
    builder = InlineKeyboardBuilder()
    
    builder.row(
        InlineKeyboardButton(
            text="⚠️ Да, удалить аккаунт",
            callback_data="confirm_delete_account"
        )
    )
    
    builder.row(
        InlineKeyboardButton(
            text="❌ Отменить",
            callback_data="profile_settings"
        )
    )
    
    return builder.as_markup()
