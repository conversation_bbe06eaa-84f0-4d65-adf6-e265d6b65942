"""
UnveilVPN Shop - Support Keyboards
Клавиатуры для системы поддержки в клиентском боте
"""

from typing import Dict, Any, List

from aiogram.types import InlineKeyboardMarkup
from aiogram.utils.i18n import gettext as _

from ...common.keyboards import InlineKeyboard
from ...services.support_service import SupportService


class SupportMenuKeyboard(InlineKeyboard):
    """
    Клавиатура главного меню поддержки
    """
    
    def __init__(self, open_tickets_count: int = 0):
        super().__init__(max_width=2)
        self.open_tickets_count = open_tickets_count
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры меню поддержки"""
        
        # Основные действия
        self.add_button(_("📝 Создать обращение"), "create_ticket")
        
        if self.open_tickets_count > 0:
            self.add_button(
                _("📋 Мои обращения ({count})").format(count=self.open_tickets_count),
                "my_tickets"
            )
        else:
            self.add_button(_("📋 Мои обращения"), "my_tickets")
        
        # Информация
        self.add_button(_("❓ Частые вопросы"), "support_faq")
        self.add_button(_("📞 Контакты"), "support_contacts")
        
        # Возврат в главное меню
        self.add_main_menu_button()
        
        return self.build()


class SupportCategoryKeyboard(InlineKeyboard):
    """Клавиатура выбора категории обращения"""
    
    def __init__(self):
        super().__init__(max_width=2)
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры категорий"""
        
        # Категории из сервиса
        categories = SupportService.CATEGORIES
        
        for category_key, category_name in categories.items():
            # Добавляем эмодзи для категорий
            emoji = self._get_category_emoji(category_key)
            button_text = f"{emoji} {category_name}"
            self.add_button(button_text, f"category_{category_key}")
        
        # Возврат
        self.add_button(_("⬅️ Назад"), "support")
        
        return self.build()
    
    def _get_category_emoji(self, category: str) -> str:
        """Получение эмодзи для категории"""
        emojis = {
            'technical': '🔧',
            'billing': '💳',
            'account': '👤',
            'connection': '🌐',
            'feature': '💡',
            'other': '❓'
        }
        return emojis.get(category, '📝')


class MyTicketsKeyboard(InlineKeyboard):
    """Клавиатура списка тикетов пользователя"""
    
    def __init__(self, tickets: List):
        super().__init__(max_width=1)
        self.tickets = tickets
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры тикетов"""
        
        # Кнопки для каждого тикета
        for ticket in self.tickets[:10]:  # Максимум 10 тикетов
            status_emoji = self._get_status_emoji(ticket.status)
            button_text = f"{status_emoji} {ticket.ticket_number} - {ticket.subject[:25]}..."
            self.add_button(button_text, f"ticket_{ticket.id}")
        
        # Создать новое обращение
        self.add_button(_("📝 Создать новое"), "create_ticket")
        
        # Возврат
        self.add_button(_("⬅️ К поддержке"), "support")
        
        return self.build()
    
    def _get_status_emoji(self, status: str) -> str:
        """Получение эмодзи для статуса"""
        emojis = {
            'open': '🟡',
            'in_progress': '🔵',
            'waiting_user': '🟠',
            'closed': '🟢'
        }
        return emojis.get(status, '⚪')


class TicketDetailsKeyboard(InlineKeyboard):
    """Клавиатура деталей тикета"""
    
    def __init__(self, ticket_id: str, ticket_status: str):
        super().__init__(max_width=2)
        self.ticket_id = ticket_id
        self.ticket_status = ticket_status
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры деталей тикета"""
        
        # Действия с тикетом
        if self.ticket_status != 'closed':
            self.add_button(_("💬 Ответить"), f"reply_{self.ticket_id}")
        
        self.add_button(_("🔄 Обновить"), f"ticket_{self.ticket_id}")
        
        # Возврат
        self.add_button(_("⬅️ К обращениям"), "my_tickets")
        self.add_button(_("🎧 К поддержке"), "support")
        
        return self.build()


class SupportTicketKeyboard(InlineKeyboard):
    """Клавиатура после создания/обновления тикета"""
    
    def __init__(self, ticket_id: str):
        super().__init__(max_width=2)
        self.ticket_id = ticket_id
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры тикета"""
        
        # Действия
        self.add_button(_("👁 Просмотреть"), f"ticket_{self.ticket_id}")
        self.add_button(_("📋 Мои обращения"), "my_tickets")
        
        # Возврат
        self.add_button(_("🎧 К поддержке"), "support")
        
        return self.build()


class CancelKeyboard(InlineKeyboard):
    """Клавиатура отмены"""
    
    def __init__(self):
        super().__init__(max_width=1)
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры отмены"""
        
        self.add_button(_("❌ Отмена"), "support")
        
        return self.build()


class SupportFAQKeyboard(InlineKeyboard):
    """Клавиатура FAQ поддержки"""
    
    def __init__(self):
        super().__init__(max_width=1)
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры FAQ"""
        
        # Частые вопросы
        self.add_button(_("🔧 Проблемы с подключением"), "faq_connection")
        self.add_button(_("💳 Вопросы по оплате"), "faq_billing")
        self.add_button(_("📱 Настройка приложений"), "faq_setup")
        self.add_button(_("🔒 Безопасность"), "faq_security")
        self.add_button(_("⚡ Скорость соединения"), "faq_speed")
        
        # Создать обращение
        self.add_button(_("📝 Задать свой вопрос"), "create_ticket")
        
        # Возврат
        self.add_button(_("⬅️ К поддержке"), "support")
        
        return self.build()


class SupportContactsKeyboard(InlineKeyboard):
    """Клавиатура контактов поддержки"""
    
    def __init__(self):
        super().__init__(max_width=2)
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры контактов"""
        
        # Контакты
        self.add_button(_("💬 Telegram"), url="https://t.me/unveilvpn_support")
        self.add_button(_("📧 Email"), url="mailto:<EMAIL>")
        
        # Социальные сети
        self.add_button(_("📢 Канал новостей"), url="https://t.me/unveilvpn_news")
        self.add_button(_("💬 Чат пользователей"), url="https://t.me/unveilvpn_chat")
        
        # Создать обращение
        self.add_button(_("📝 Создать обращение"), "create_ticket")
        
        # Возврат
        self.add_button(_("⬅️ К поддержке"), "support")
        
        return self.build()


class SupportPriorityKeyboard(InlineKeyboard):
    """Клавиатура выбора приоритета (для админов)"""
    
    def __init__(self):
        super().__init__(max_width=2)
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры приоритетов"""
        
        # Приоритеты из сервиса
        priorities = SupportService.PRIORITIES
        
        for priority_key, priority_name in priorities.items():
            # Добавляем эмодзи для приоритетов
            emoji = self._get_priority_emoji(priority_key)
            button_text = f"{emoji} {priority_name}"
            self.add_button(button_text, f"priority_{priority_key}")
        
        return self.build()
    
    def _get_priority_emoji(self, priority: str) -> str:
        """Получение эмодзи для приоритета"""
        emojis = {
            'low': '🟢',
            'medium': '🟡',
            'high': '🟠',
            'urgent': '🔴'
        }
        return emojis.get(priority, '⚪')


class SupportStatusKeyboard(InlineKeyboard):
    """Клавиатура выбора статуса (для админов)"""
    
    def __init__(self):
        super().__init__(max_width=2)
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры статусов"""
        
        # Статусы из сервиса
        statuses = SupportService.STATUSES
        
        for status_key, status_name in statuses.items():
            # Добавляем эмодзи для статусов
            emoji = self._get_status_emoji(status_key)
            button_text = f"{emoji} {status_name}"
            self.add_button(button_text, f"status_{status_key}")
        
        return self.build()
    
    def _get_status_emoji(self, status: str) -> str:
        """Получение эмодзи для статуса"""
        emojis = {
            'open': '🟡',
            'in_progress': '🔵',
            'waiting_user': '🟠',
            'closed': '🟢'
        }
        return emojis.get(status, '⚪')


class BackToSupportKeyboard(InlineKeyboard):
    """Клавиатура возврата к поддержке"""
    
    def __init__(self):
        super().__init__(max_width=1)
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры возврата"""
        
        self.add_button(_("⬅️ К поддержке"), "support")
        self.add_main_menu_button()
        
        return self.build()
