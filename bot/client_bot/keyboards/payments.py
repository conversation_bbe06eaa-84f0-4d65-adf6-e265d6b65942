"""
UnveilVPN Shop - Клавиатуры для платежей
"""

from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton
from aiogram.utils.keyboard import InlineKeyboardBuilder
from typing import List, Optional, Dict, Any

from bot.db.models import Tariffs
from bot.common.keyboards import InlineKeyboard, BaseKeyboard


def get_payment_methods_keyboard() -> InlineKeyboardMarkup:
    """Клавиатура выбора способа оплаты"""
    builder = InlineKeyboardBuilder()
    
    builder.row(
        InlineKeyboardButton(
            text="💳 Банковская карта",
            callback_data="payment_method:yookassa"
        )
    )
    
    builder.row(
        InlineKeyboardButton(
            text="₿ Криптовалюта",
            callback_data="payment_method:cryptomus"
        )
    )
    
    builder.row(
        InlineKeyboardButton(
            text="⭐ Telegram Stars",
            callback_data="payment_method:telegram_stars"
        )
    )
    
    builder.row(
        InlineKeyboardButton(
            text="🔙 Назад",
            callback_data="back_to_tariffs"
        )
    )
    
    return builder.as_markup()


def get_tariffs_keyboard(tariffs: List[Tariffs]) -> InlineKeyboardMarkup:
    """Клавиатура выбора тарифа"""
    builder = InlineKeyboardBuilder()
    
    for tariff in tariffs:
        # Формируем текст кнопки
        button_text = f"{tariff.name} - {tariff.price_rub}₽"
        if tariff.discount_percent > 0:
            button_text += f" (-{tariff.discount_percent}%)"
        
        builder.row(
            InlineKeyboardButton(
                text=button_text,
                callback_data=f"select_tariff:{tariff.id}"
            )
        )
    
    builder.row(
        InlineKeyboardButton(
            text="🔙 Главное меню",
            callback_data="main_menu"
        )
    )
    
    return builder.as_markup()


def get_payment_confirmation_keyboard(payment_id: str) -> InlineKeyboardMarkup:
    """Клавиатура подтверждения платежа"""
    builder = InlineKeyboardBuilder()
    
    builder.row(
        InlineKeyboardButton(
            text="✅ Оплатить",
            callback_data=f"confirm_payment:{payment_id}"
        )
    )
    
    builder.row(
        InlineKeyboardButton(
            text="❌ Отменить",
            callback_data="cancel_payment"
        )
    )
    
    return builder.as_markup()


def get_payment_status_keyboard(payment_id: str, status: str) -> InlineKeyboardMarkup:
    """Клавиатура статуса платежа"""
    builder = InlineKeyboardBuilder()
    
    if status == "pending":
        builder.row(
            InlineKeyboardButton(
                text="🔄 Проверить статус",
                callback_data=f"check_payment:{payment_id}"
            )
        )
    
    builder.row(
        InlineKeyboardButton(
            text="🔙 Главное меню",
            callback_data="main_menu"
        )
    )
    
    return builder.as_markup()


def get_promocode_keyboard() -> InlineKeyboardMarkup:
    """Клавиатура для ввода промокода"""
    builder = InlineKeyboardBuilder()
    
    builder.row(
        InlineKeyboardButton(
            text="🎫 Ввести промокод",
            callback_data="enter_promocode"
        )
    )
    
    builder.row(
        InlineKeyboardButton(
            text="➡️ Продолжить без промокода",
            callback_data="skip_promocode"
        )
    )
    
    builder.row(
        InlineKeyboardButton(
            text="🔙 Назад",
            callback_data="back_to_tariffs"
        )
    )
    
    return builder.as_markup()


def get_subscription_management_keyboard() -> InlineKeyboardMarkup:
    """Клавиатура управления подпиской"""
    builder = InlineKeyboardBuilder()
    
    builder.row(
        InlineKeyboardButton(
            text="📊 Статус подписки",
            callback_data="subscription_status"
        )
    )
    
    builder.row(
        InlineKeyboardButton(
            text="🔄 Продлить подписку",
            callback_data="extend_subscription"
        )
    )
    
    builder.row(
        InlineKeyboardButton(
            text="❌ Отменить подписку",
            callback_data="cancel_subscription"
        )
    )
    
    builder.row(
        InlineKeyboardButton(
            text="🔙 Главное меню",
            callback_data="main_menu"
        )
    )
    
    return builder.as_markup()


def get_refund_keyboard(payment_id: str) -> InlineKeyboardMarkup:
    """Клавиатура для возврата средств"""
    builder = InlineKeyboardBuilder()
    
    builder.row(
        InlineKeyboardButton(
            text="💰 Запросить возврат",
            callback_data=f"request_refund:{payment_id}"
        )
    )
    
    builder.row(
        InlineKeyboardButton(
            text="❌ Отменить",
            callback_data="cancel_refund"
        )
    )
    
    return builder.as_markup()


def get_payment_history_keyboard(page: int = 0, has_next: bool = False) -> InlineKeyboardMarkup:
    """Клавиатура истории платежей"""
    builder = InlineKeyboardBuilder()
    
    # Навигация по страницам
    nav_buttons = []
    
    if page > 0:
        nav_buttons.append(
            InlineKeyboardButton(
                text="⬅️ Предыдущая",
                callback_data=f"payment_history:{page-1}"
            )
        )
    
    if has_next:
        nav_buttons.append(
            InlineKeyboardButton(
                text="➡️ Следующая",
                callback_data=f"payment_history:{page+1}"
            )
        )
    
    if nav_buttons:
        builder.row(*nav_buttons)
    
    builder.row(
        InlineKeyboardButton(
            text="🔙 Главное меню",
            callback_data="main_menu"
        )
    )
    
    return builder.as_markup()


class PaymentMethodKeyboard(InlineKeyboard):
    """Класс клавиатуры выбора способа оплаты"""

    def __init__(self, available_methods: List[str] = None):
        super().__init__(max_width=1)
        self.available_methods = available_methods or ['yookassa', 'cryptomus', 'telegram_stars']

    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры способов оплаты"""
        return get_payment_methods_keyboard()


class PaymentStatusKeyboard(BaseKeyboard):
    """Клавиатура для статуса платежа"""

    def __init__(self):
        super().__init__(max_width=2)

    @staticmethod
    def payment_pending(payment_id: str) -> InlineKeyboardMarkup:
        """Клавиатура для ожидающего платежа"""
        builder = InlineKeyboardBuilder()

        builder.row(
            InlineKeyboardButton(
                text="🔄 Проверить статус",
                callback_data=f"check_payment_{payment_id}"
            )
        )

        builder.row(
            InlineKeyboardButton(
                text="❌ Отменить платеж",
                callback_data=f"cancel_payment_{payment_id}"
            )
        )

        return builder.as_markup()

    @staticmethod
    def payment_success() -> InlineKeyboardMarkup:
        """Клавиатура для успешного платежа"""
        builder = InlineKeyboardBuilder()

        builder.row(
            InlineKeyboardButton(
                text="📱 Мои подписки",
                callback_data="subscription_menu"
            )
        )

        builder.row(
            InlineKeyboardButton(
                text="🏠 Главное меню",
                callback_data="main_menu"
            )
        )

        return builder.as_markup()

    @staticmethod
    def payment_failed(payment_id: str) -> InlineKeyboardMarkup:
        """Клавиатура для неудачного платежа"""
        builder = InlineKeyboardBuilder()

        builder.row(
            InlineKeyboardButton(
                text="🔄 Попробовать снова",
                callback_data=f"retry_payment_{payment_id}"
            )
        )

        builder.row(
            InlineKeyboardButton(
                text="💬 Поддержка",
                callback_data="support_menu"
            )
        )

        builder.row(
            InlineKeyboardButton(
                text="🏠 Главное меню",
                callback_data="main_menu"
            )
        )

        return builder.as_markup()


class PromocodeSelectionKeyboard(BaseKeyboard):
    """Клавиатура для работы с промокодами"""

    def __init__(self):
        super().__init__(max_width=1)

    @staticmethod
    def promocode_input() -> InlineKeyboardMarkup:
        """Клавиатура для ввода промокода"""
        builder = InlineKeyboardBuilder()

        builder.row(
            InlineKeyboardButton(
                text="🎫 Ввести промокод",
                callback_data="enter_promocode"
            )
        )

        builder.row(
            InlineKeyboardButton(
                text="⏭️ Пропустить",
                callback_data="skip_promocode"
            )
        )

        builder.row(
            InlineKeyboardButton(
                text="🔙 Назад",
                callback_data="back_to_payment_methods"
            )
        )

        return builder.as_markup()

    @staticmethod
    def promocode_applied(discount_amount: float) -> InlineKeyboardMarkup:
        """Клавиатура после применения промокода"""
        builder = InlineKeyboardBuilder()

        builder.row(
            InlineKeyboardButton(
                text="✅ Продолжить с промокодом",
                callback_data="continue_with_promocode"
            )
        )

        builder.row(
            InlineKeyboardButton(
                text="🗑️ Удалить промокод",
                callback_data="remove_promocode"
            )
        )

        return builder.as_markup()

    @staticmethod
    def promocode_invalid() -> InlineKeyboardMarkup:
        """Клавиатура для неверного промокода"""
        builder = InlineKeyboardBuilder()

        builder.row(
            InlineKeyboardButton(
                text="🔄 Попробовать другой",
                callback_data="enter_promocode"
            )
        )

        builder.row(
            InlineKeyboardButton(
                text="⏭️ Продолжить без промокода",
                callback_data="skip_promocode"
            )
        )

        return builder.as_markup()
