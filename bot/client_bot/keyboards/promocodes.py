"""
UnveilVPN Shop - Promocodes Keyboards
Клавиатуры для промокодов в клиентском боте
"""

from typing import Dict, Any, List

from aiogram.types import InlineKeyboardMarkup
from aiogram.utils.i18n import gettext as _

from ...common.keyboards import InlineKeyboard


class PromocodesMenuKeyboard(InlineKeyboard):
    """
    Клавиатура главного меню промокодов
    """
    
    def __init__(self, promocodes: List = None):
        super().__init__(max_width=2)
        self.promocodes = promocodes or []
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры меню промокодов"""
        
        # Основные действия
        if self.promocodes:
            self.add_button(_("🎉 Доступные промокоды"), "public_promocodes")
        
        self.add_button(_("🎫 Ввести промокод"), "enter_promocode")
        
        # Информация
        self.add_button(_("ℹ️ Как использовать"), "promocodes_info")
        
        # Возврат в главное меню
        self.add_main_menu_button()
        
        return self.build()


class PublicPromocodesKeyboard(InlineKeyboard):
    """Клавиатура публичных промокодов"""
    
    def __init__(self, promocodes: List):
        super().__init__(max_width=1)
        self.promocodes = promocodes
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры публичных промокодов"""
        
        # Кнопки для каждого промокода
        for promo in self.promocodes[:10]:  # Максимум 10 промокодов
            discount_text = self._format_discount(promo)
            button_text = f"🎫 {promo.code} ({discount_text})"
            self.add_button(button_text, f"validate_promo_{promo.code}")
        
        # Ввод своего промокода
        self.add_button(_("✏️ Ввести свой код"), "enter_promocode")
        
        # Возврат
        self.add_button(_("⬅️ К промокодам"), "promocodes")
        
        return self.build()
    
    def _format_discount(self, promocode) -> str:
        """Форматирование скидки"""
        if promocode.discount_type == 'percent':
            return f"{int(promocode.discount_value)}%"
        elif promocode.discount_type == 'fixed':
            return f"{int(promocode.discount_value)} ₽"
        elif promocode.discount_type == 'days':
            return f"+{int(promocode.discount_value)} дней"
        else:
            return "Скидка"


class PromocodeValidationKeyboard(InlineKeyboard):
    """Клавиатура валидации промокода"""
    
    def __init__(self, promocode):
        super().__init__(max_width=2)
        self.promocode = promocode
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры валидации"""
        
        # Действия с промокодом
        self.add_button(_("🛒 Купить с промокодом"), f"buy_with_promo_{self.promocode.code}")
        self.add_button(_("📋 Скопировать код"), f"copy_promo_{self.promocode.code}")
        
        # Поделиться
        share_text = f"🎁 Промокод {self.promocode.code} для UnveilVPN!"
        share_url = f"https://t.me/share/url?url={share_text}"
        self.add_button(_("📤 Поделиться"), url=share_url)
        
        # Возврат
        self.add_button(_("⬅️ К промокодам"), "promocodes")
        
        return self.build()


class PromocodeInfoKeyboard(InlineKeyboard):
    """Клавиатура информации о промокодах"""
    
    def __init__(self):
        super().__init__(max_width=2)
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры информации"""
        
        # Действия
        self.add_button(_("🎉 Доступные промокоды"), "public_promocodes")
        self.add_button(_("🎫 Ввести промокод"), "enter_promocode")
        
        # FAQ
        self.add_button(_("❓ Где взять промокоды"), "promo_faq_where")
        self.add_button(_("❓ Как применить"), "promo_faq_how")
        self.add_button(_("❓ Типы скидок"), "promo_faq_types")
        
        # Возврат
        self.add_button(_("⬅️ К промокодам"), "promocodes")
        
        return self.build()


class BackToPromocodesKeyboard(InlineKeyboard):
    """Клавиатура возврата к промокодам"""
    
    def __init__(self):
        super().__init__(max_width=1)
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры возврата"""
        
        self.add_button(_("⬅️ К промокодам"), "promocodes")
        self.add_main_menu_button()
        
        return self.build()


class CancelKeyboard(InlineKeyboard):
    """Клавиатура отмены"""
    
    def __init__(self):
        super().__init__(max_width=1)
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры отмены"""
        
        self.add_button(_("❌ Отмена"), "promocodes")
        
        return self.build()


class PromocodeSelectionKeyboard(InlineKeyboard):
    """Клавиатура выбора промокода при покупке"""
    
    def __init__(self, promocodes: List, tariff_id: str):
        super().__init__(max_width=1)
        self.promocodes = promocodes
        self.tariff_id = tariff_id
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры выбора промокода"""
        
        # Доступные промокоды
        for promo in self.promocodes[:5]:  # Максимум 5 промокодов
            discount_text = self._format_discount(promo)
            button_text = f"🎫 {promo.code} ({discount_text})"
            self.add_button(button_text, f"select_promo_{self.tariff_id}_{promo.code}")
        
        # Ввод своего промокода
        self.add_button(_("✏️ Ввести другой код"), f"enter_promo_for_{self.tariff_id}")
        
        # Покупка без промокода
        self.add_button(_("🚫 Без промокода"), f"buy_tariff_{self.tariff_id}")
        
        # Возврат
        self.add_button(_("⬅️ Назад"), f"tariff_details_{self.tariff_id}")
        
        return self.build()
    
    def _format_discount(self, promocode) -> str:
        """Форматирование скидки"""
        if promocode.discount_type == 'percent':
            return f"{int(promocode.discount_value)}%"
        elif promocode.discount_type == 'fixed':
            return f"{int(promocode.discount_value)} ₽"
        elif promocode.discount_type == 'days':
            return f"+{int(promocode.discount_value)} дней"
        else:
            return "Скидка"


class PromocodeAppliedKeyboard(InlineKeyboard):
    """Клавиатура после применения промокода"""
    
    def __init__(self, tariff_id: str, promocode_code: str, discount_amount: float):
        super().__init__(max_width=2)
        self.tariff_id = tariff_id
        self.promocode_code = promocode_code
        self.discount_amount = discount_amount
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры после применения промокода"""
        
        # Продолжить покупку
        self.add_button(
            _("✅ Продолжить покупку"),
            f"proceed_payment_{self.tariff_id}_{self.promocode_code}"
        )
        
        # Изменить промокод
        self.add_button(
            _("🔄 Другой промокод"),
            f"select_promocode_{self.tariff_id}"
        )
        
        # Убрать промокод
        self.add_button(_("🚫 Без промокода"), f"buy_tariff_{self.tariff_id}")
        
        # Возврат
        self.add_button(_("⬅️ К тарифу"), f"tariff_details_{self.tariff_id}")
        
        return self.build()


class PromocodeManagementKeyboard(InlineKeyboard):
    """Клавиатура управления промокодами (для админов)"""
    
    def __init__(self):
        super().__init__(max_width=2)
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры управления"""
        
        # CRUD операции
        self.add_button(_("➕ Создать промокод"), "create_promocode")
        self.add_button(_("📋 Список промокодов"), "list_promocodes")
        
        # Генерация
        self.add_button(_("🎲 Сгенерировать"), "generate_promocode")
        self.add_button(_("📊 Статистика"), "promocode_stats")
        
        # Массовые операции
        self.add_button(_("🔄 Обновить все"), "refresh_promocodes")
        self.add_button(_("🗑 Очистить истекшие"), "cleanup_expired")
        
        # Возврат
        self.add_button(_("⬅️ Админ панель"), "admin_menu")
        
        return self.build()
