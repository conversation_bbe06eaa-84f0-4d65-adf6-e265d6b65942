"""
UnveilVPN Shop - Help Keyboard
Клавиатура помощи для клиентского бота
"""

from aiogram.types import InlineKeyboardButton, InlineKeyboardMarkup
from typing import Optional, Dict, Any

from bot.common.keyboards import BaseKeyboard


class HelpKeyboard(BaseKeyboard):
    """Клавиатура помощи"""

    def __init__(self, max_width: int = 2):
        super().__init__(max_width)

    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение основной клавиатуры помощи"""
        return self.get_help_menu()

    @staticmethod
    def get_help_menu() -> InlineKeyboardMarkup:
        """Получить главное меню помощи"""
        keyboard = [
            [
                InlineKeyboardButton("📖 Инструкции", callback_data="help_instructions"),
                InlineKeyboardButton("❓ FAQ", callback_data="help_faq")
            ],
            [
                InlineKeyboardButton("🔧 Настройка VPN", callback_data="help_setup"),
                InlineKeyboardButton("📱 Приложения", callback_data="help_apps")
            ],
            [
                InlineKeyboardButton("🆘 Техподдержка", callback_data="help_support"),
                InlineKeyboardButton("📞 Контакты", callback_data="help_contacts")
            ],
            [
                InlineKeyboardButton("🔙 Назад", callback_data="back_to_main")
            ]
        ]
        return InlineKeyboardMarkup(keyboard)
    
    @staticmethod
    def get_instructions_menu() -> InlineKeyboardMarkup:
        """Получить меню инструкций"""
        keyboard = [
            [
                InlineKeyboardButton("📱 iOS", callback_data="help_ios"),
                InlineKeyboardButton("🤖 Android", callback_data="help_android")
            ],
            [
                InlineKeyboardButton("💻 Windows", callback_data="help_windows"),
                InlineKeyboardButton("🍎 macOS", callback_data="help_macos")
            ],
            [
                InlineKeyboardButton("🐧 Linux", callback_data="help_linux"),
                InlineKeyboardButton("🌐 Router", callback_data="help_router")
            ],
            [
                InlineKeyboardButton("🔙 Назад", callback_data="help_menu")
            ]
        ]
        return InlineKeyboardMarkup(keyboard)
    
    @staticmethod
    def get_back_to_help() -> InlineKeyboardMarkup:
        """Получить кнопку возврата к помощи"""
        keyboard = [
            [InlineKeyboardButton("🔙 Назад к помощи", callback_data="help_menu")]
        ]
        return InlineKeyboardMarkup(keyboard)
    
    @staticmethod
    def get_faq_menu() -> InlineKeyboardMarkup:
        """Получить меню FAQ"""
        keyboard = [
            [
                InlineKeyboardButton("🚀 Скорость", callback_data="faq_speed"),
                InlineKeyboardButton("🔒 Безопасность", callback_data="faq_security")
            ],
            [
                InlineKeyboardButton("💰 Оплата", callback_data="faq_payment"),
                InlineKeyboardButton("📋 Подписка", callback_data="faq_subscription")
            ],
            [
                InlineKeyboardButton("🔧 Проблемы", callback_data="faq_issues"),
                InlineKeyboardButton("📍 Серверы", callback_data="faq_servers")
            ],
            [
                InlineKeyboardButton("🔙 Назад", callback_data="help_menu")
            ]
        ]
        return InlineKeyboardMarkup(keyboard)
