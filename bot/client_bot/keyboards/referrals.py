"""
UnveilVPN Shop - Referrals Keyboards
Клавиатуры для реферальной системы в клиентском боте
"""

from typing import Dict, Any

from aiogram.types import InlineKeyboardMarkup
from aiogram.utils.i18n import gettext as _

from ...common.keyboards import InlineKeyboard


class ReferralMenuKeyboard(InlineKeyboard):
    """
    Клавиатура главного меню реферальной системы
    """
    
    def __init__(self, user):
        super().__init__(max_width=2)
        self.user = user
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры меню рефералов"""
        
        # Основные действия
        if self.user.referral_code:
            self.add_button(_("📊 Моя статистика"), "referral_stats")
            self.add_button(_("🌳 Дерево рефералов"), "referral_tree")
            self.add_button(_("📋 Поделиться кодом"), f"share_referral_{self.user.referral_code}")
        else:
            self.add_button(_("🔗 Создать код"), "generate_referral_code")
        
        # Ввод чужого кода (если пользователь еще не реферал)
        if not self.user.referred_by_id:
            self.add_button(_("🎫 Ввести код"), "enter_referral_code")
        
        # Информация
        self.add_button(_("ℹ️ Как это работает"), "referral_info")
        self.add_button(_("💰 Процентные ставки"), "referral_rates")
        
        # Возврат в главное меню
        self.add_main_menu_button()
        
        return self.build()


class ReferralStatsKeyboard(InlineKeyboard):
    """Клавиатура статистики рефералов"""
    
    def __init__(self):
        super().__init__(max_width=2)
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры статистики"""
        
        # Действия со статистикой
        self.add_button(_("🔄 Обновить"), "referral_stats")
        self.add_button(_("🌳 Дерево рефералов"), "referral_tree")
        
        # Управление
        self.add_button(_("📋 Поделиться кодом"), "share_referral_code")
        self.add_button(_("💸 Вывести бонусы"), "withdraw_bonuses")
        
        # Возврат
        self.add_button(_("⬅️ К рефералам"), "referrals")
        
        return self.build()


class ReferralCodeKeyboard(InlineKeyboard):
    """Клавиатура с реферальным кодом"""
    
    def __init__(self, referral_code: str):
        super().__init__(max_width=1)
        self.referral_code = referral_code
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры с кодом"""
        
        # Поделиться кодом
        share_text = f"🎁 Присоединяйся к UnveilVPN по моему коду {self.referral_code} и получи бонусы!"
        share_url = f"https://t.me/share/url?url={share_text}"
        
        self.add_button(_("📤 Поделиться"), url=share_url)
        
        # Скопировать код
        self.add_button(_("📋 Скопировать код"), f"copy_code_{self.referral_code}")
        
        # Статистика
        self.add_button(_("📊 Моя статистика"), "referral_stats")
        
        # Возврат
        self.add_button(_("⬅️ К рефералам"), "referrals")
        
        return self.build()


class ReferralInfoKeyboard(InlineKeyboard):
    """Клавиатура информации о реферальной системе"""
    
    def __init__(self):
        super().__init__(max_width=2)
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры информации"""
        
        # Действия
        self.add_button(_("🔗 Создать код"), "generate_referral_code")
        self.add_button(_("🎫 Ввести код"), "enter_referral_code")
        
        # Дополнительная информация
        self.add_button(_("💰 Процентные ставки"), "referral_rates")
        self.add_button(_("❓ FAQ"), "referral_faq")
        
        # Возврат
        self.add_button(_("⬅️ К рефералам"), "referrals")
        
        return self.build()


class ReferralRatesKeyboard(InlineKeyboard):
    """Клавиатура процентных ставок"""
    
    def __init__(self):
        super().__init__(max_width=1)
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры ставок"""
        
        # Примеры расчетов
        self.add_button(_("🧮 Калькулятор доходов"), "referral_calculator")
        
        # Информация
        self.add_button(_("ℹ️ Как это работает"), "referral_info")
        
        # Возврат
        self.add_button(_("⬅️ К рефералам"), "referrals")
        
        return self.build()


class BackToReferralsKeyboard(InlineKeyboard):
    """Клавиатура возврата к рефералам"""
    
    def __init__(self):
        super().__init__(max_width=1)
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры возврата"""
        
        self.add_button(_("⬅️ К рефералам"), "referrals")
        self.add_main_menu_button()
        
        return self.build()


class CancelKeyboard(InlineKeyboard):
    """Клавиатура отмены"""
    
    def __init__(self):
        super().__init__(max_width=1)
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры отмены"""
        
        self.add_button(_("❌ Отмена"), "referrals")
        
        return self.build()


class ReferralWithdrawKeyboard(InlineKeyboard):
    """Клавиатура вывода бонусов"""
    
    def __init__(self, available_amount: float):
        super().__init__(max_width=2)
        self.available_amount = available_amount
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры вывода"""
        
        if self.available_amount >= 100:  # Минимум для вывода
            # Варианты вывода
            self.add_button(_("💳 На карту"), "withdraw_card")
            self.add_button(_("📱 На кошелек"), "withdraw_wallet")
            
            # Использовать для покупки
            self.add_button(_("🛒 Для покупки VPN"), "use_bonuses_purchase")
        else:
            # Недостаточно средств
            min_amount = 100
            needed = min_amount - self.available_amount
            self.add_button(
                _("💰 Нужно еще {amount:.2f} ₽").format(amount=needed),
                "referral_stats"
            )
        
        # Возврат
        self.add_button(_("⬅️ К статистике"), "referral_stats")
        
        return self.build()


class ReferralCalculatorKeyboard(InlineKeyboard):
    """Клавиатура калькулятора доходов"""
    
    def __init__(self):
        super().__init__(max_width=3)
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры калькулятора"""
        
        # Примеры расчетов
        examples = [
            ("1 реферал", "calc_1"),
            ("5 рефералов", "calc_5"),
            ("10 рефералов", "calc_10"),
            ("25 рефералов", "calc_25"),
            ("50 рефералов", "calc_50"),
            ("100 рефералов", "calc_100")
        ]
        
        for text, callback in examples:
            self.add_button(text, callback)
        
        # Возврат
        self.add_button(_("⬅️ К ставкам"), "referral_rates")
        
        return self.build()


class ReferralFAQKeyboard(InlineKeyboard):
    """Клавиатура FAQ по рефералам"""
    
    def __init__(self):
        super().__init__(max_width=1)
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры FAQ"""
        
        # Частые вопросы
        self.add_button(_("❓ Как получить код?"), "faq_get_code")
        self.add_button(_("❓ Как пригласить друга?"), "faq_invite")
        self.add_button(_("❓ Когда начисляются бонусы?"), "faq_bonuses")
        self.add_button(_("❓ Как вывести деньги?"), "faq_withdraw")
        self.add_button(_("❓ Сколько уровней?"), "faq_levels")
        
        # Поддержка
        self.add_button(_("💬 Связаться с поддержкой"), "contact_support")
        
        # Возврат
        self.add_button(_("⬅️ К рефералам"), "referrals")
        
        return self.build()
