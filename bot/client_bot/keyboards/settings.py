"""
UnveilVPN Shop - Settings Keyboard
Клавиатура настроек для клиентского бота
"""

from aiogram.types import InlineKeyboardButton, InlineKeyboardMarkup
from typing import Optional, Dict, Any

from bot.common.keyboards import BaseKeyboard


class SettingsKeyboard(BaseKeyboard):
    """Клавиатура настроек"""

    def __init__(self, max_width: int = 2):
        super().__init__(max_width)

    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение основной клавиатуры настроек"""
        return self.get_settings_menu()

    @staticmethod
    def get_settings_menu() -> InlineKeyboardMarkup:
        """Получить главное меню настроек"""
        keyboard = [
            [
                InlineKeyboardButton("🌍 Язык", callback_data="settings_language"),
                InlineKeyboardButton("🔔 Уведомления", callback_data="settings_notifications")
            ],
            [
                InlineKeyboardButton("🎨 Тема", callback_data="settings_theme"),
                InlineKeyboardButton("⏰ Часовой пояс", callback_data="settings_timezone")
            ],
            [
                InlineKeyboardButton("🔐 Безопасность", callback_data="settings_security"),
                InlineKeyboardButton("📊 Статистика", callback_data="settings_stats")
            ],
            [
                InlineKeyboardButton("🗑 Удалить аккаунт", callback_data="settings_delete_account")
            ],
            [
                InlineKeyboardButton("🔙 Назад", callback_data="back_to_main")
            ]
        ]
        return InlineKeyboardMarkup(keyboard)
    
    @staticmethod
    def get_language_menu() -> InlineKeyboardMarkup:
        """Получить меню выбора языка"""
        keyboard = [
            [
                InlineKeyboardButton("🇷🇺 Русский", callback_data="lang_ru"),
                InlineKeyboardButton("🇺🇸 English", callback_data="lang_en")
            ],
            [
                InlineKeyboardButton("🇨🇳 中文", callback_data="lang_zh"),
                InlineKeyboardButton("🇪🇸 Español", callback_data="lang_es")
            ],
            [
                InlineKeyboardButton("🔙 Назад", callback_data="settings_menu")
            ]
        ]
        return InlineKeyboardMarkup(keyboard)
    
    @staticmethod
    def get_notifications_menu() -> InlineKeyboardMarkup:
        """Получить меню настроек уведомлений"""
        keyboard = [
            [
                InlineKeyboardButton("🔔 Все уведомления", callback_data="notif_all"),
                InlineKeyboardButton("🔕 Отключить все", callback_data="notif_none")
            ],
            [
                InlineKeyboardButton("💰 Только платежи", callback_data="notif_payments"),
                InlineKeyboardButton("📋 Только подписка", callback_data="notif_subscription")
            ],
            [
                InlineKeyboardButton("🔙 Назад", callback_data="settings_menu")
            ]
        ]
        return InlineKeyboardMarkup(keyboard)
    
    @staticmethod
    def get_security_menu() -> InlineKeyboardMarkup:
        """Получить меню настроек безопасности"""
        keyboard = [
            [
                InlineKeyboardButton("🔑 Сменить пароль", callback_data="security_password"),
                InlineKeyboardButton("📱 2FA", callback_data="security_2fa")
            ],
            [
                InlineKeyboardButton("📋 Активные сессии", callback_data="security_sessions"),
                InlineKeyboardButton("🔒 Блокировка", callback_data="security_lock")
            ],
            [
                InlineKeyboardButton("🔙 Назад", callback_data="settings_menu")
            ]
        ]
        return InlineKeyboardMarkup(keyboard)
    
    @staticmethod
    def get_back_to_settings() -> InlineKeyboardMarkup:
        """Получить кнопку возврата к настройкам"""
        keyboard = [
            [InlineKeyboardButton("🔙 Назад к настройкам", callback_data="settings_menu")]
        ]
        return InlineKeyboardMarkup(keyboard)
