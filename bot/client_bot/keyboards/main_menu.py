"""
UnveilVPN Shop - Main Menu Keyboard
Клавиатура главного меню клиентского бота
"""

from typing import Dict, Any

from aiogram.types import InlineKeyboardMarkup
from aiogram.utils.i18n import gettext as _

from ...common.keyboards import InlineKeyboard


class MainMenuKeyboard(InlineKeyboard):
    """
    Клавиатура главного меню клиентского бота
    """
    
    def __init__(self):
        super().__init__(max_width=2)
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры главного меню"""
        db_user = user_data.get('db_user') if user_data else None

        # Основные кнопки меню
        if db_user and db_user.remnawave_user_id:
            # Пользователь с активной Remnawave подпиской
            self.add_button(_("📊 Моя подписка"), "subscription_info")
            self.add_button(_("🔄 Продлить"), "extend_subscription")
        else:
            # Пользователь без подписки
            self.add_button(_("💰 Купить VPN"), "buy_vpn")

            # Тестовый период (если доступен)
            if (db_user and not db_user.is_test_used and
                hasattr(self, 'config') and getattr(self.config, 'test_period_enabled', True)):
                self.add_button(_("🎁 Тест 7 дней"), "test_period")

        # Новые системы
        self.add_button(_("👥 Рефералы"), "referrals")
        self.add_button(_("🎫 Промокоды"), "promocodes")

        # Общие кнопки
        self.add_button(_("👤 Профиль"), "profile")
        self.add_button(_("ℹ️ О сервисе"), "about")

        # Вторая строка
        self.add_button(_("🎧 Поддержка"), "support")
        self.add_button(_("❓ Помощь"), "help")

        # Третья строка
        self.add_button(_("⚙️ Настройки"), "settings")
        
        return self.build()


class ProfileKeyboard(InlineKeyboard):
    """Клавиатура профиля пользователя"""
    
    def __init__(self):
        super().__init__(max_width=2)
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры профиля"""
        
        # Кнопки профиля
        self.add_button(_("📝 Редактировать"), "edit_profile")
        self.add_button(_("🔗 Реферальная ссылка"), "referral_link")
        
        # История
        self.add_button(_("📋 История покупок"), "purchase_history")
        self.add_button(_("💳 Способы оплаты"), "payment_methods")
        
        # Возврат в главное меню
        self.add_main_menu_button()
        
        return self.build()


class AboutKeyboard(InlineKeyboard):
    """Клавиатура информации о сервисе"""
    
    def __init__(self):
        super().__init__(max_width=2)
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры информации"""
        
        # Ссылки на ресурсы
        self.add_button(_("🌐 Наш сайт"), url="https://unveilvpn.com")
        self.add_button(_("📱 Приложения"), "download_apps")
        
        # Социальные сети
        self.add_button(_("📢 Telegram канал"), url="https://t.me/unveilvpn")
        self.add_button(_("💬 Чат пользователей"), url="https://t.me/unveilvpn_chat")
        
        # Документы
        self.add_button(_("📄 Пользовательское соглашение"), "terms_of_service")
        self.add_button(_("🔒 Политика конфиденциальности"), "privacy_policy")
        
        # Возврат в главное меню
        self.add_main_menu_button()
        
        return self.build()


class HelpKeyboard(InlineKeyboard):
    """Клавиатура справки"""
    
    def __init__(self):
        super().__init__(max_width=2)
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры справки"""
        
        # Разделы справки
        self.add_button(_("🚀 Быстрый старт"), "help_quick_start")
        self.add_button(_("📱 Настройка устройств"), "help_setup")
        
        self.add_button(_("💳 Оплата и тарифы"), "help_payment")
        self.add_button(_("🔧 Решение проблем"), "help_troubleshooting")
        
        self.add_button(_("❓ Частые вопросы"), "help_faq")
        self.add_button(_("📞 Связаться с поддержкой"), "contact_support")
        
        # Возврат в главное меню
        self.add_main_menu_button()
        
        return self.build()


class SettingsKeyboard(InlineKeyboard):
    """Клавиатура настроек"""
    
    def __init__(self):
        super().__init__(max_width=2)
    
    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение клавиатуры настроек"""
        
        # Настройки
        self.add_button(_("🌐 Язык"), "settings_language")
        self.add_button(_("🔔 Уведомления"), "settings_notifications")
        
        self.add_button(_("🔐 Безопасность"), "settings_security")
        self.add_button(_("📊 Статистика"), "settings_stats")
        
        # Управление аккаунтом
        self.add_button(_("🗑️ Удалить аккаунт"), "delete_account")
        self.add_button(_("📤 Экспорт данных"), "export_data")
        
        # Возврат в главное меню
        self.add_main_menu_button()
        
        return self.build()
