"""
UnveilVPN Shop - About Keyboard
Клавиатура для раздела "О сервисе"
"""

from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton
from aiogram.utils.keyboard import InlineKeyboardBuilder
from typing import Dict, Any

from bot.common.keyboards import BaseKeyboard


class AboutKeyboard(BaseKeyboard):
    """Клавиатура раздела "О сервисе" """

    def __init__(self, max_width: int = 2):
        super().__init__(max_width)

    async def get_keyboard(self, user_data: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Получение основной клавиатуры раздела 'О сервисе'"""
        return self.main_about()

    @staticmethod
    def main_about() -> InlineKeyboardMarkup:
        """Основная клавиатура раздела "О сервисе" """
        builder = InlineKeyboardBuilder()
        
        # Информация о сервисе
        builder.row(
            InlineKeyboardButton(
                text="📋 Что такое VPN?",
                callback_data="about:what_is_vpn"
            )
        )
        
        builder.row(
            InlineKeyboardButton(
                text="🔒 Безопасность",
                callback_data="about:security"
            ),
            InlineKeyboardButton(
                text="🌍 Серверы",
                callback_data="about:servers"
            )
        )
        
        builder.row(
            InlineKeyboardButton(
                text="📱 Приложения",
                callback_data="about:apps"
            ),
            InlineKeyboardButton(
                text="❓ FAQ",
                callback_data="about:faq"
            )
        )
        
        # Кнопка назад
        builder.row(
            InlineKeyboardButton(
                text="◀️ Назад",
                callback_data="main_menu"
            )
        )
        
        return builder.as_markup()
    
    @staticmethod
    def what_is_vpn() -> InlineKeyboardMarkup:
        """Клавиатура для раздела "Что такое VPN?" """
        builder = InlineKeyboardBuilder()
        
        builder.row(
            InlineKeyboardButton(
                text="🔗 Подробнее в интернете",
                url="https://ru.wikipedia.org/wiki/VPN"
            )
        )
        
        builder.row(
            InlineKeyboardButton(
                text="◀️ Назад к разделу",
                callback_data="about:main"
            )
        )
        
        return builder.as_markup()
    
    @staticmethod
    def security() -> InlineKeyboardMarkup:
        """Клавиатура для раздела "Безопасность" """
        builder = InlineKeyboardBuilder()
        
        builder.row(
            InlineKeyboardButton(
                text="🔐 Протоколы шифрования",
                callback_data="about:protocols"
            )
        )
        
        builder.row(
            InlineKeyboardButton(
                text="🛡️ Политика логов",
                callback_data="about:logs_policy"
            )
        )
        
        builder.row(
            InlineKeyboardButton(
                text="◀️ Назад к разделу",
                callback_data="about:main"
            )
        )
        
        return builder.as_markup()
    
    @staticmethod
    def servers() -> InlineKeyboardMarkup:
        """Клавиатура для раздела "Серверы" """
        builder = InlineKeyboardBuilder()
        
        # Популярные страны
        countries = [
            ("🇺🇸 США", "about:server:usa"),
            ("🇩🇪 Германия", "about:server:germany"),
            ("🇳🇱 Нидерланды", "about:server:netherlands"),
            ("🇸🇬 Сингапур", "about:server:singapore"),
            ("🇯🇵 Япония", "about:server:japan"),
        ]
        
        for country_name, callback_data in countries:
            builder.row(
                InlineKeyboardButton(
                    text=country_name,
                    callback_data=callback_data
                )
            )
        
        builder.row(
            InlineKeyboardButton(
                text="🌍 Все серверы",
                callback_data="about:all_servers"
            )
        )
        
        builder.row(
            InlineKeyboardButton(
                text="◀️ Назад к разделу",
                callback_data="about:main"
            )
        )
        
        return builder.as_markup()
    
    @staticmethod
    def apps() -> InlineKeyboardMarkup:
        """Клавиатура для раздела "Приложения" """
        builder = InlineKeyboardBuilder()
        
        # Платформы
        platforms = [
            ("📱 iOS", "about:app:ios"),
            ("🤖 Android", "about:app:android"),
            ("💻 Windows", "about:app:windows"),
            ("🍎 macOS", "about:app:macos"),
            ("🐧 Linux", "about:app:linux"),
        ]
        
        for platform_name, callback_data in platforms:
            builder.row(
                InlineKeyboardButton(
                    text=platform_name,
                    callback_data=callback_data
                )
            )
        
        builder.row(
            InlineKeyboardButton(
                text="📖 Инструкции по настройке",
                callback_data="about:setup_guides"
            )
        )
        
        builder.row(
            InlineKeyboardButton(
                text="◀️ Назад к разделу",
                callback_data="about:main"
            )
        )
        
        return builder.as_markup()
    
    @staticmethod
    def faq() -> InlineKeyboardMarkup:
        """Клавиатура для раздела FAQ"""
        builder = InlineKeyboardBuilder()
        
        # Популярные вопросы
        questions = [
            ("❓ Как подключиться?", "about:faq:how_to_connect"),
            ("💰 Как оплатить?", "about:faq:how_to_pay"),
            ("🔄 Как продлить?", "about:faq:how_to_renew"),
            ("📱 Проблемы с подключением", "about:faq:connection_issues"),
            ("🌐 Медленная скорость", "about:faq:slow_speed"),
        ]
        
        for question, callback_data in questions:
            builder.row(
                InlineKeyboardButton(
                    text=question,
                    callback_data=callback_data
                )
            )
        
        builder.row(
            InlineKeyboardButton(
                text="💬 Связаться с поддержкой",
                callback_data="support:create_ticket"
            )
        )
        
        builder.row(
            InlineKeyboardButton(
                text="◀️ Назад к разделу",
                callback_data="about:main"
            )
        )
        
        return builder.as_markup()
    
    @staticmethod
    def back_to_about() -> InlineKeyboardMarkup:
        """Универсальная кнопка возврата к разделу "О сервисе" """
        builder = InlineKeyboardBuilder()
        
        builder.row(
            InlineKeyboardButton(
                text="◀️ Назад к разделу",
                callback_data="about:main"
            )
        )
        
        return builder.as_markup()
    
    @staticmethod
    def external_link(url: str, text: str = "🔗 Открыть ссылку") -> InlineKeyboardMarkup:
        """Клавиатура с внешней ссылкой"""
        builder = InlineKeyboardBuilder()
        
        builder.row(
            InlineKeyboardButton(
                text=text,
                url=url
            )
        )
        
        builder.row(
            InlineKeyboardButton(
                text="◀️ Назад к разделу",
                callback_data="about:main"
            )
        )
        
        return builder.as_markup()
