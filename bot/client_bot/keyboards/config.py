"""
UnveilVPN Shop - Config Keyboards
Клавиатуры для управления конфигурациями VPN
"""

from typing import List, Dict, Any, Optional
from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton
from aiogram.utils.keyboard import InlineKeyboardBuilder

from ...common.keyboards import BaseKeyboard


class ConfigKeyboard(BaseKeyboard):
    """Клавиатура для управления VPN конфигурациями"""
    
    @staticmethod
    def config_list(configs: List[Dict[str, Any]], user_id: int) -> InlineKeyboardMarkup:
        """
        Создает клавиатуру со списком конфигураций пользователя
        
        Args:
            configs: Список конфигураций
            user_id: ID пользователя
            
        Returns:
            InlineKeyboardMarkup: Клавиатура со списком конфигураций
        """
        builder = InlineKeyboardBuilder()
        
        if not configs:
            builder.row(
                InlineKeyboardButton(
                    text="📱 Создать первую конфигурацию",
                    callback_data=f"config_create_{user_id}"
                )
            )
        else:
            # Добавляем кнопки для каждой конфигурации
            for config in configs:
                config_id = config.get('id', '')
                device_name = config.get('device_name', 'Неизвестное устройство')
                protocol = config.get('protocol', 'unknown').upper()
                is_active = config.get('is_active', False)
                
                status_emoji = "✅" if is_active else "❌"
                
                builder.row(
                    InlineKeyboardButton(
                        text=f"{status_emoji} {device_name} ({protocol})",
                        callback_data=f"config_view_{config_id}"
                    )
                )
            
            # Кнопка создания новой конфигурации
            builder.row(
                InlineKeyboardButton(
                    text="➕ Добавить конфигурацию",
                    callback_data=f"config_create_{user_id}"
                )
            )
        
        # Кнопка назад
        builder.row(
            InlineKeyboardButton(
                text="🔙 Назад",
                callback_data="subscription_menu"
            )
        )
        
        return builder.as_markup()
    
    @staticmethod
    def config_details(config: Dict[str, Any]) -> InlineKeyboardMarkup:
        """
        Создает клавиатуру для просмотра деталей конфигурации
        
        Args:
            config: Данные конфигурации
            
        Returns:
            InlineKeyboardMarkup: Клавиатура с действиями для конфигурации
        """
        builder = InlineKeyboardBuilder()
        
        config_id = config.get('id', '')
        is_active = config.get('is_active', False)
        
        # Кнопка скачивания конфигурации
        builder.row(
            InlineKeyboardButton(
                text="📥 Скачать конфигурацию",
                callback_data=f"config_download_{config_id}"
            )
        )
        
        # Кнопка получения QR-кода
        builder.row(
            InlineKeyboardButton(
                text="📱 QR-код",
                callback_data=f"config_qr_{config_id}"
            )
        )
        
        # Кнопка активации/деактивации
        if is_active:
            builder.row(
                InlineKeyboardButton(
                    text="⏸️ Деактивировать",
                    callback_data=f"config_deactivate_{config_id}"
                )
            )
        else:
            builder.row(
                InlineKeyboardButton(
                    text="▶️ Активировать",
                    callback_data=f"config_activate_{config_id}"
                )
            )
        
        # Кнопка удаления
        builder.row(
            InlineKeyboardButton(
                text="🗑️ Удалить",
                callback_data=f"config_delete_confirm_{config_id}"
            )
        )
        
        # Кнопка назад
        builder.row(
            InlineKeyboardButton(
                text="🔙 К списку конфигураций",
                callback_data="config_list"
            )
        )
        
        return builder.as_markup()
    
    @staticmethod
    def config_create_protocol() -> InlineKeyboardMarkup:
        """
        Создает клавиатуру для выбора протокола при создании конфигурации
        
        Returns:
            InlineKeyboardMarkup: Клавиатура с выбором протокола
        """
        builder = InlineKeyboardBuilder()
        
        protocols = [
            ("VLESS", "vless", "🔒"),
            ("VMess", "vmess", "🛡️"),
            ("Trojan", "trojan", "⚔️"),
            ("Shadowsocks", "shadowsocks", "👤")
        ]
        
        for name, protocol, emoji in protocols:
            builder.row(
                InlineKeyboardButton(
                    text=f"{emoji} {name}",
                    callback_data=f"config_create_protocol_{protocol}"
                )
            )
        
        # Кнопка назад
        builder.row(
            InlineKeyboardButton(
                text="🔙 Назад",
                callback_data="config_list"
            )
        )
        
        return builder.as_markup()
    
    @staticmethod
    def config_delete_confirm(config_id: str) -> InlineKeyboardMarkup:
        """
        Создает клавиатуру подтверждения удаления конфигурации
        
        Args:
            config_id: ID конфигурации
            
        Returns:
            InlineKeyboardMarkup: Клавиатура подтверждения
        """
        builder = InlineKeyboardBuilder()
        
        builder.row(
            InlineKeyboardButton(
                text="✅ Да, удалить",
                callback_data=f"config_delete_{config_id}"
            ),
            InlineKeyboardButton(
                text="❌ Отмена",
                callback_data=f"config_view_{config_id}"
            )
        )
        
        return builder.as_markup()
    
    @staticmethod
    def config_instructions(protocol: str) -> InlineKeyboardMarkup:
        """
        Создает клавиатуру с инструкциями по настройке
        
        Args:
            protocol: Протокол конфигурации
            
        Returns:
            InlineKeyboardMarkup: Клавиатура с инструкциями
        """
        builder = InlineKeyboardBuilder()
        
        # Кнопки для разных платформ
        platforms = [
            ("📱 Android", f"instructions_{protocol}_android"),
            ("🍎 iOS", f"instructions_{protocol}_ios"),
            ("💻 Windows", f"instructions_{protocol}_windows"),
            ("🐧 Linux", f"instructions_{protocol}_linux"),
            ("🍎 macOS", f"instructions_{protocol}_macos")
        ]
        
        for platform_name, callback_data in platforms:
            builder.row(
                InlineKeyboardButton(
                    text=platform_name,
                    callback_data=callback_data
                )
            )
        
        # Кнопка назад
        builder.row(
            InlineKeyboardButton(
                text="🔙 Назад",
                callback_data="config_list"
            )
        )
        
        return builder.as_markup()
