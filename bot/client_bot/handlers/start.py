"""
UnveilVPN Shop - Start Handler
Обработчик команды /start для клиентского бота
"""

import re
from typing import Dict, Any

from aiogram.types import Message
from aiogram.utils.i18n import gettext as _

from ...common.handlers import CommandHandler
from ...common.utils import MessageFormatter
from ...common.exceptions import ValidationError
from ..keyboards import MainMenuKeyboard


class StartHandler(CommandHandler):
    """
    Обработчик команды /start
    """
    
    def __init__(self):
        super().__init__(commands=['start'], name='StartHandler')
        self.main_menu_keyboard = MainMenuKeyboard()
    
    async def handle_command(self, message: Message, **kwargs):
        """Обработка команды /start"""
        try:
            user_data = await self._get_user_data(message, **kwargs)
            
            # Проверяем наличие реферального кода в команде
            referral_code = self._extract_referral_code(message.text)
            
            if referral_code:
                await self._process_referral(user_data, referral_code)
            
            # Формируем приветственное сообщение
            welcome_text = await self._get_welcome_text(user_data)
            
            # Получаем клавиатуру главного меню
            keyboard = await self.main_menu_keyboard.get_keyboard(user_data)
            
            await message.answer(welcome_text, reply_markup=keyboard)
            
            # Логируем новых пользователей
            if not user_data['db_user'].last_activity:
                self.logger.info(f"Новый пользователь: {message.from_user.id} (@{message.from_user.username})")
            
        except Exception as e:
            self.logger.error(f"Ошибка обработки команды /start: {e}")
            await self._send_error(message, _("Ошибка при запуске бота"))
    
    def _extract_referral_code(self, text: str) -> str:
        """Извлечение реферального кода из команды /start"""
        if not text or ' ' not in text:
            return None
        
        # Формат: /start REF_CODE
        parts = text.split(' ', 1)
        if len(parts) != 2:
            return None
        
        referral_code = parts[1].strip().upper()
        
        # Валидация реферального кода
        if not re.match(r'^[A-Z0-9]{6,12}$', referral_code):
            return None
        
        return referral_code
    
    async def _process_referral(self, user_data: Dict[str, Any], referral_code: str):
        """Обработка реферального кода"""
        try:
            from sqlalchemy import select
            from ...db.models import VPNUsers, Referrals
            
            db_session = user_data['db_session']
            db_user = user_data['db_user']
            
            # Проверяем, что пользователь еще не привязан к рефереру
            if db_user.referred_by_id:
                self.logger.info(f"Пользователь {db_user.telegram_id} уже имеет реферера")
                return
            
            # Ищем пользователя с таким реферальным кодом
            stmt = select(VPNUsers).where(VPNUsers.referral_code == referral_code)
            result = await db_session.execute(stmt)
            referrer = result.scalar_one_or_none()
            
            if not referrer:
                self.logger.warning(f"Реферальный код {referral_code} не найден")
                return
            
            # Проверяем, что пользователь не пытается использовать свой код
            if referrer.id == db_user.id:
                self.logger.warning(f"Пользователь {db_user.telegram_id} пытается использовать свой реферальный код")
                return
            
            # Привязываем пользователя к рефереру
            db_user.referred_by_id = referrer.id
            
            # Создаем запись в таблице рефералов
            referral = Referrals(
                referrer_id=referrer.id,
                referred_id=db_user.id,
                level=1,
                is_active=True,
                is_verified=False  # Будет подтверждено после первой покупки
            )
            
            db_session.add(referral)
            await db_session.flush()
            
            self.logger.info(f"Пользователь {db_user.telegram_id} привязан к рефереру {referrer.telegram_id}")
            
        except Exception as e:
            self.logger.error(f"Ошибка обработки реферального кода {referral_code}: {e}")
    
    async def _get_welcome_text(self, user_data: Dict[str, Any]) -> str:
        """Формирование приветственного текста"""
        user = user_data['user']
        db_user = user_data['db_user']
        
        # Базовое приветствие
        name = user.first_name or user.username or _("Пользователь")
        
        lines = [
            _("👋 Привет, {name}!").format(name=name),
            "",
            _("Добро пожаловать в {shop_name} — надежный VPN сервис для безопасного интернета.").format(
                shop_name=self.config.shop_name
            ),
            "",
            _("🔒 Что мы предлагаем:"),
            _("• Высокая скорость подключения"),
            _("• Серверы в разных странах"),
            _("• Защита ваших данных"),
            _("• Круглосуточная поддержка"),
        ]
        
        # Добавляем информацию о тестовом периоде
        if self.config.test_period_enabled and not db_user.is_test_used:
            lines.extend([
                "",
                _("🎁 Специально для вас — бесплатный тестовый период!"),
                _("Попробуйте наш сервис без ограничений.")
            ])
        
        # Добавляем информацию о реферальной программе
        if db_user.referral_code:
            lines.extend([
                "",
                _("💰 Ваш реферальный код: <code>{code}</code>").format(code=db_user.referral_code),
                _("Приглашайте друзей и получайте бонусы!")
            ])
        
        lines.extend([
            "",
            _("Выберите действие в меню ниже ⬇️")
        ])
        
        return "\n".join(lines)
