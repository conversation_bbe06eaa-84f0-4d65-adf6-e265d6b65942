"""
UnveilVPN Shop - Client Promocodes Handler
Обработчик промокодов для клиентского бота
"""

from typing import Dict, Any

from aiogram.types import Message, CallbackQuery
from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import State, StatesGroup
from aiogram.utils.i18n import gettext as _

from ...common.handlers import BaseHandler
from ...common.utils import MessageFormatter, BotUtils
from ...common.exceptions import ValidationError, BusinessLogicError
from ...services.promocode_service import PromocodeService
from ..keyboards import PromocodesMenuKeyboard, PromocodeValidationKeyboard


class PromocodeStates(StatesGroup):
    """Состояния для работы с промокодами"""
    waiting_promocode_input = State()


class PromocodesHandler(BaseHandler):
    """
    Обработчик промокодов для клиентского бота
    """
    
    def __init__(self):
        super().__init__(name='ClientPromocodesHandler')
    
    def _register_handlers(self):
        """Регистрация обработчиков"""
        
        # Основные обработчики
        self.router.callback_query.register(
            self.show_promocodes_menu,
            lambda c: c.data == 'promocodes'
        )
        
        self.router.callback_query.register(
            self.show_public_promocodes,
            lambda c: c.data == 'public_promocodes'
        )
        
        self.router.callback_query.register(
            self.enter_promocode,
            lambda c: c.data == 'enter_promocode'
        )
        
        self.router.callback_query.register(
            self.validate_promocode_callback,
            lambda c: c.data.startswith('validate_promo_')
        )
        
        # Ввод промокода
        self.router.message.register(
            self.process_promocode_input,
            PromocodeStates.waiting_promocode_input
        )
    
    async def show_promocodes_menu(self, callback: CallbackQuery, **kwargs):
        """Показ главного меню промокодов"""
        try:
            user_data = await self._get_user_data(callback, **kwargs)
            db_session = user_data['db_session']
            
            # Получаем публичные промокоды
            promocode_service = PromocodeService(db_session)
            public_promocodes = await promocode_service.get_public_promocodes()
            
            text = await self._get_promocodes_menu_text(public_promocodes)
            keyboard = await PromocodesMenuKeyboard(public_promocodes).get_keyboard(user_data)
            
            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Ошибка показа меню промокодов: {e}")
            await self._send_error(callback, _("Ошибка загрузки промокодов"))
    
    async def show_public_promocodes(self, callback: CallbackQuery, **kwargs):
        """Показ публичных промокодов"""
        try:
            user_data = await self._get_user_data(callback, **kwargs)
            db_session = user_data['db_session']
            
            promocode_service = PromocodeService(db_session)
            promocodes = await promocode_service.get_public_promocodes()
            
            text = await self._get_public_promocodes_text(promocodes)
            
            from ..keyboards import PublicPromocodesKeyboard
            keyboard = await PublicPromocodesKeyboard(promocodes).get_keyboard()
            
            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Ошибка показа публичных промокодов: {e}")
            await self._send_error(callback, _("Ошибка загрузки промокодов"))
    
    async def enter_promocode(self, callback: CallbackQuery, state: FSMContext, **kwargs):
        """Запрос на ввод промокода"""
        try:
            await state.set_state(PromocodeStates.waiting_promocode_input)
            
            text = _(
                "🎫 <b>Введите промокод</b>\n\n"
                "Отправьте код промокода текстом, чтобы проверить его действительность "
                "и узнать размер скидки.\n\n"
                "Промокод можно будет применить при покупке подписки."
            )
            
            from ..keyboards import CancelKeyboard
            keyboard = await CancelKeyboard().get_keyboard()
            
            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Ошибка запроса промокода: {e}")
            await self._send_error(callback, _("Ошибка обработки запроса"))
    
    async def process_promocode_input(self, message: Message, state: FSMContext, **kwargs):
        """Обработка ввода промокода"""
        try:
            code = message.text.strip().upper()
            
            # Валидация формата
            if len(code) < 3 or len(code) > 50:
                await message.answer(_("❌ Неверный формат промокода. Попробуйте еще раз:"))
                return
            
            user_data = await self._get_user_data(message, **kwargs)
            db_session = user_data['db_session']
            db_user = user_data['db_user']
            
            promocode_service = PromocodeService(db_session)
            
            # Валидируем промокод
            is_valid, message_text, promocode = await promocode_service.validate_promocode(
                code=code,
                user_id=db_user.id
            )
            
            await state.clear()
            
            if is_valid and promocode:
                text = _(
                    "✅ <b>Промокод действителен!</b>\n\n"
                    "🎫 Код: <code>{code}</code>\n"
                    "📝 Название: {name}\n"
                    "💰 Скидка: {discount}\n"
                    "📅 Действует до: {expires}\n\n"
                    "Промокод можно применить при покупке подписки."
                ).format(
                    code=promocode.code,
                    name=promocode.name or "Без названия",
                    discount=self._format_discount(promocode),
                    expires=promocode.expires_at.strftime("%d.%m.%Y") if promocode.expires_at else "Без ограничений"
                )
                
                keyboard = await PromocodeValidationKeyboard(promocode).get_keyboard()
            else:
                text = f"❌ {message_text}"
                
                from ..keyboards import BackToPromocodesKeyboard
                keyboard = await BackToPromocodesKeyboard().get_keyboard()
            
            await message.answer(text, reply_markup=keyboard)
            
        except Exception as e:
            self.logger.error(f"Ошибка обработки промокода: {e}")
            await message.answer(_("❌ Ошибка проверки промокода"))
    
    async def validate_promocode_callback(self, callback: CallbackQuery, **kwargs):
        """Валидация промокода по callback"""
        try:
            # Извлекаем код промокода
            code = callback.data.split('_', 2)[-1]
            
            user_data = await self._get_user_data(callback, **kwargs)
            db_session = user_data['db_session']
            db_user = user_data['db_user']
            
            promocode_service = PromocodeService(db_session)
            
            # Валидируем промокод
            is_valid, message_text, promocode = await promocode_service.validate_promocode(
                code=code,
                user_id=db_user.id
            )
            
            if is_valid and promocode:
                text = _(
                    "✅ <b>Промокод {code} действителен!</b>\n\n"
                    "📝 {name}\n"
                    "💰 Скидка: {discount}\n"
                    "📊 Использований: {used}/{limit}\n"
                    "📅 Действует до: {expires}\n\n"
                    "{description}"
                ).format(
                    code=promocode.code,
                    name=promocode.name or "Промокод",
                    discount=self._format_discount(promocode),
                    used=promocode.usage_count,
                    limit=promocode.usage_limit or "∞",
                    expires=promocode.expires_at.strftime("%d.%m.%Y") if promocode.expires_at else "Без ограничений",
                    description=promocode.description or ""
                )
                
                keyboard = await PromocodeValidationKeyboard(promocode).get_keyboard()
            else:
                text = f"❌ {message_text}"
                
                from ..keyboards import BackToPromocodesKeyboard
                keyboard = await BackToPromocodesKeyboard().get_keyboard()
            
            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Ошибка валидации промокода: {e}")
            await self._send_error(callback, _("Ошибка проверки промокода"))
    
    # Вспомогательные методы
    
    async def _get_promocodes_menu_text(self, promocodes) -> str:
        """Формирование текста главного меню промокодов"""
        lines = [
            _("🎫 <b>Промокоды</b>"),
            ""
        ]
        
        if promocodes:
            lines.extend([
                _("🎉 <b>Доступные промокоды:</b>"),
                ""
            ])
            
            for promo in promocodes[:3]:  # Показываем первые 3
                discount = self._format_discount(promo)
                lines.append(f"• <code>{promo.code}</code> - {discount}")
            
            if len(promocodes) > 3:
                lines.append(f"... и еще {len(promocodes) - 3} промокодов")
            
            lines.append("")
        
        lines.extend([
            _("💡 <b>Как использовать:</b>"),
            _("• Выберите промокод из списка"),
            _("• Или введите свой код"),
            _("• Применяйте при покупке подписки"),
            ""
        ])
        
        return "\n".join(lines)
    
    async def _get_public_promocodes_text(self, promocodes) -> str:
        """Формирование текста публичных промокодов"""
        lines = [
            _("🎉 <b>Доступные промокоды</b>"),
            ""
        ]
        
        if not promocodes:
            lines.extend([
                _("В данный момент нет доступных промокодов."),
                _("Следите за новостями в нашем канале!")
            ])
            return "\n".join(lines)
        
        for promo in promocodes:
            lines.extend([
                f"🎫 <b>{promo.name or promo.code}</b>",
                f"📝 Код: <code>{promo.code}</code>",
                f"💰 Скидка: {self._format_discount(promo)}",
                f"📊 Осталось: {(promo.usage_limit - promo.usage_count) if promo.usage_limit else '∞'}"
            ])
            
            if promo.expires_at:
                lines.append(f"📅 До: {promo.expires_at.strftime('%d.%m.%Y')}")
            
            if promo.description:
                lines.append(f"ℹ️ {promo.description}")
            
            lines.append("")
        
        return "\n".join(lines)
    
    def _format_discount(self, promocode) -> str:
        """Форматирование скидки для отображения"""
        if promocode.discount_type == 'percent':
            return f"{int(promocode.discount_value)}%"
        elif promocode.discount_type == 'fixed':
            return f"{int(promocode.discount_value)} ₽"
        elif promocode.discount_type == 'days':
            return f"+{int(promocode.discount_value)} дней"
        else:
            return "Скидка"
