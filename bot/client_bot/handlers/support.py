"""
UnveilVPN Shop - Client Support Handler
Обработчик системы поддержки для клиентского бота
"""

from typing import Dict, Any

from aiogram.types import Message, CallbackQuery
from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import State, StatesGroup
from aiogram.utils.i18n import gettext as _

from ...common.handlers import BaseHandler
from ...common.utils import MessageFormatter, BotUtils
from ...common.exceptions import ValidationError, BusinessLogicError
from ...services.support_service import SupportService
from ..keyboards import SupportMenuKeyboard, SupportCategoryKeyboard


class SupportStates(StatesGroup):
    """Состояния для работы с поддержкой"""
    waiting_subject = State()
    waiting_message = State()
    waiting_reply = State()


class SupportHandler(BaseHandler):
    """
    Обработчик системы поддержки для клиентского бота
    """
    
    def __init__(self):
        super().__init__(name='ClientSupportHandler')
    
    def _register_handlers(self):
        """Регистрация обработчиков"""
        
        # Основные обработчики
        self.router.callback_query.register(
            self.show_support_menu,
            lambda c: c.data == 'support'
        )
        
        self.router.callback_query.register(
            self.create_ticket,
            lambda c: c.data == 'create_ticket'
        )
        
        self.router.callback_query.register(
            self.my_tickets,
            lambda c: c.data == 'my_tickets'
        )
        
        self.router.callback_query.register(
            self.select_category,
            lambda c: c.data.startswith('category_')
        )
        
        self.router.callback_query.register(
            self.view_ticket,
            lambda c: c.data.startswith('ticket_')
        )
        
        self.router.callback_query.register(
            self.reply_to_ticket,
            lambda c: c.data.startswith('reply_')
        )
        
        # Обработчики состояний
        self.router.message.register(
            self.process_subject_input,
            SupportStates.waiting_subject
        )
        
        self.router.message.register(
            self.process_message_input,
            SupportStates.waiting_message
        )
        
        self.router.message.register(
            self.process_reply_input,
            SupportStates.waiting_reply
        )
    
    async def show_support_menu(self, callback: CallbackQuery, **kwargs):
        """Показ главного меню поддержки"""
        try:
            user_data = await self._get_user_data(callback, **kwargs)
            db_session = user_data['db_session']
            db_user = user_data['db_user']
            
            # Получаем количество открытых тикетов пользователя
            support_service = SupportService(db_session)
            user_tickets = await support_service.get_tickets(
                user_id=db_user.id,
                status='open',
                limit=100
            )
            
            text = await self._get_support_menu_text(len(user_tickets))
            keyboard = await SupportMenuKeyboard(len(user_tickets)).get_keyboard(user_data)
            
            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Ошибка показа меню поддержки: {e}")
            await self._send_error(callback, _("Ошибка загрузки поддержки"))
    
    async def create_ticket(self, callback: CallbackQuery, state: FSMContext, **kwargs):
        """Начало создания тикета"""
        try:
            text = _(
                "📝 <b>Создание обращения в поддержку</b>\n\n"
                "Выберите категорию вашего вопроса:"
            )
            
            keyboard = await SupportCategoryKeyboard().get_keyboard()
            
            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Ошибка создания тикета: {e}")
            await self._send_error(callback, _("Ошибка создания обращения"))
    
    async def select_category(self, callback: CallbackQuery, state: FSMContext, **kwargs):
        """Выбор категории тикета"""
        try:
            category = callback.data.split('_', 1)[1]
            
            # Сохраняем категорию в состоянии
            await state.update_data(category=category)
            await state.set_state(SupportStates.waiting_subject)
            
            category_name = SupportService.CATEGORIES.get(category, category)
            
            text = _(
                "📝 <b>Создание обращения</b>\n\n"
                "Категория: <b>{category}</b>\n\n"
                "Введите краткую тему вашего обращения:"
            ).format(category=category_name)
            
            from ..keyboards import CancelKeyboard
            keyboard = await CancelKeyboard().get_keyboard()
            
            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Ошибка выбора категории: {e}")
            await self._send_error(callback, _("Ошибка обработки категории"))
    
    async def process_subject_input(self, message: Message, state: FSMContext, **kwargs):
        """Обработка ввода темы тикета"""
        try:
            subject = message.text.strip()
            
            # Валидация темы
            if len(subject) < 5:
                await message.answer(_("❌ Тема должна содержать минимум 5 символов. Попробуйте еще раз:"))
                return
            
            if len(subject) > 500:
                await message.answer(_("❌ Тема слишком длинная (максимум 500 символов). Попробуйте еще раз:"))
                return
            
            # Сохраняем тему и переходим к сообщению
            await state.update_data(subject=subject)
            await state.set_state(SupportStates.waiting_message)
            
            text = _(
                "📝 <b>Создание обращения</b>\n\n"
                "Тема: <b>{subject}</b>\n\n"
                "Теперь опишите вашу проблему подробно:"
            ).format(subject=subject)
            
            await message.answer(text)
            
        except Exception as e:
            self.logger.error(f"Ошибка обработки темы: {e}")
            await message.answer(_("❌ Ошибка обработки темы"))
    
    async def process_message_input(self, message: Message, state: FSMContext, **kwargs):
        """Обработка ввода сообщения тикета"""
        try:
            message_text = message.text.strip()
            
            # Валидация сообщения
            if len(message_text) < 10:
                await message.answer(_("❌ Сообщение должно содержать минимум 10 символов. Попробуйте еще раз:"))
                return
            
            # Получаем данные из состояния
            data = await state.get_data()
            category = data.get('category', 'other')
            subject = data.get('subject')
            
            user_data = await self._get_user_data(message, **kwargs)
            db_session = user_data['db_session']
            db_user = user_data['db_user']
            
            # Создаем тикет
            support_service = SupportService(db_session)
            ticket = await support_service.create_ticket(
                user_id=db_user.id,
                subject=subject,
                message=message_text,
                category=category,
                priority='medium'
            )
            
            await db_session.commit()
            await state.clear()
            
            text = _(
                "✅ <b>Обращение создано!</b>\n\n"
                "📋 Номер: <code>{ticket_number}</code>\n"
                "📝 Тема: {subject}\n"
                "📂 Категория: {category}\n\n"
                "Мы ответим вам в ближайшее время. "
                "Вы можете отслеживать статус в разделе 'Мои обращения'."
            ).format(
                ticket_number=ticket.ticket_number,
                subject=ticket.subject,
                category=SupportService.CATEGORIES.get(ticket.category, ticket.category)
            )
            
            from ..keyboards import SupportTicketKeyboard
            keyboard = await SupportTicketKeyboard(str(ticket.id)).get_keyboard()
            
            await message.answer(text, reply_markup=keyboard)
            
        except Exception as e:
            self.logger.error(f"Ошибка создания тикета: {e}")
            await message.answer(_("❌ Ошибка создания обращения"))
    
    async def my_tickets(self, callback: CallbackQuery, **kwargs):
        """Показ тикетов пользователя"""
        try:
            user_data = await self._get_user_data(callback, **kwargs)
            db_session = user_data['db_session']
            db_user = user_data['db_user']
            
            support_service = SupportService(db_session)
            tickets = await support_service.get_tickets(
                user_id=db_user.id,
                limit=20
            )
            
            text = await self._get_my_tickets_text(tickets)
            
            from ..keyboards import MyTicketsKeyboard
            keyboard = await MyTicketsKeyboard(tickets).get_keyboard()
            
            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Ошибка показа тикетов: {e}")
            await self._send_error(callback, _("Ошибка загрузки обращений"))
    
    async def view_ticket(self, callback: CallbackQuery, **kwargs):
        """Просмотр конкретного тикета"""
        try:
            # Извлекаем ID тикета
            ticket_id = callback.data.split('_', 1)[1]
            
            user_data = await self._get_user_data(callback, **kwargs)
            db_session = user_data['db_session']
            db_user = user_data['db_user']
            
            support_service = SupportService(db_session)
            ticket = await support_service.get_ticket_by_id(ticket_id)
            
            if not ticket or ticket.user_id != db_user.id:
                await callback.answer(_("Обращение не найдено"), show_alert=True)
                return
            
            text = await self._get_ticket_details_text(ticket)
            
            from ..keyboards import TicketDetailsKeyboard
            keyboard = await TicketDetailsKeyboard(str(ticket.id), ticket.status).get_keyboard()
            
            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Ошибка просмотра тикета: {e}")
            await self._send_error(callback, _("Ошибка загрузки обращения"))
    
    async def reply_to_ticket(self, callback: CallbackQuery, state: FSMContext, **kwargs):
        """Ответ на тикет"""
        try:
            # Извлекаем ID тикета
            ticket_id = callback.data.split('_', 1)[1]
            
            user_data = await self._get_user_data(callback, **kwargs)
            db_user = user_data['db_user']
            
            # Проверяем доступ к тикету
            db_session = user_data['db_session']
            support_service = SupportService(db_session)
            ticket = await support_service.get_ticket_by_id(ticket_id)
            
            if not ticket or ticket.user_id != db_user.id:
                await callback.answer(_("Обращение не найдено"), show_alert=True)
                return
            
            if ticket.status == 'closed':
                await callback.answer(_("Нельзя отвечать на закрытое обращение"), show_alert=True)
                return
            
            # Сохраняем ID тикета и переходим к вводу ответа
            await state.update_data(ticket_id=ticket_id)
            await state.set_state(SupportStates.waiting_reply)
            
            text = _(
                "💬 <b>Ответ на обращение</b>\n\n"
                "📋 Номер: {ticket_number}\n"
                "📝 Тема: {subject}\n\n"
                "Введите ваш ответ:"
            ).format(
                ticket_number=ticket.ticket_number,
                subject=ticket.subject
            )
            
            from ..keyboards import CancelKeyboard
            keyboard = await CancelKeyboard().get_keyboard()
            
            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Ошибка ответа на тикет: {e}")
            await self._send_error(callback, _("Ошибка обработки ответа"))
    
    async def process_reply_input(self, message: Message, state: FSMContext, **kwargs):
        """Обработка ввода ответа на тикет"""
        try:
            reply_text = message.text.strip()
            
            # Валидация ответа
            if len(reply_text) < 1:
                await message.answer(_("❌ Ответ не может быть пустым. Попробуйте еще раз:"))
                return
            
            # Получаем данные из состояния
            data = await state.get_data()
            ticket_id = data.get('ticket_id')
            
            user_data = await self._get_user_data(message, **kwargs)
            db_session = user_data['db_session']
            db_user = user_data['db_user']
            
            # Добавляем сообщение в тикет
            support_service = SupportService(db_session)
            ticket = await support_service.add_message_to_ticket(
                ticket_id=ticket_id,
                message=reply_text,
                author_id=db_user.id,
                is_internal=False
            )
            
            await db_session.commit()
            await state.clear()
            
            text = _(
                "✅ <b>Ответ отправлен!</b>\n\n"
                "📋 Обращение: {ticket_number}\n"
                "💬 Ваш ответ добавлен в обращение.\n\n"
                "Мы ответим вам в ближайшее время."
            ).format(ticket_number=ticket.ticket_number)
            
            from ..keyboards import SupportTicketKeyboard
            keyboard = await SupportTicketKeyboard(str(ticket.id)).get_keyboard()
            
            await message.answer(text, reply_markup=keyboard)
            
        except Exception as e:
            self.logger.error(f"Ошибка отправки ответа: {e}")
            await message.answer(_("❌ Ошибка отправки ответа"))
    
    # Вспомогательные методы
    
    async def _get_support_menu_text(self, open_tickets_count: int) -> str:
        """Формирование текста главного меню поддержки"""
        lines = [
            _("🎧 <b>Служба поддержки</b>"),
            ""
        ]
        
        if open_tickets_count > 0:
            lines.extend([
                _("📋 У вас есть {count} открытых обращений").format(count=open_tickets_count),
                ""
            ])
        
        lines.extend([
            _("💡 <b>Мы поможем вам с:</b>"),
            _("• Техническими проблемами"),
            _("• Вопросами по оплате"),
            _("• Настройкой подключения"),
            _("• Проблемами с аккаунтом"),
            _("• Любыми другими вопросами"),
            "",
            _("⏱ <b>Время ответа:</b>"),
            _("• Критические вопросы: до 2 часов"),
            _("• Обычные вопросы: до 24 часов"),
            ""
        ])
        
        return "\n".join(lines)
    
    async def _get_my_tickets_text(self, tickets) -> str:
        """Формирование текста списка тикетов"""
        lines = [
            _("📋 <b>Мои обращения</b>"),
            ""
        ]
        
        if not tickets:
            lines.extend([
                _("У вас пока нет обращений в поддержку."),
                _("Создайте новое обращение, если у вас есть вопросы.")
            ])
            return "\n".join(lines)
        
        for ticket in tickets[:10]:  # Показываем первые 10
            status_emoji = {
                'open': '🟡',
                'in_progress': '🔵',
                'waiting_user': '🟠',
                'closed': '🟢'
            }.get(ticket.status, '⚪')
            
            status_name = SupportService.STATUSES.get(ticket.status, ticket.status)
            
            lines.append(
                f"{status_emoji} <code>{ticket.ticket_number}</code> - {ticket.subject[:30]}..."
            )
            lines.append(f"   {status_name} • {ticket.created_at.strftime('%d.%m.%Y')}")
            lines.append("")
        
        if len(tickets) > 10:
            lines.append(f"... и еще {len(tickets) - 10} обращений")
        
        return "\n".join(lines)
    
    async def _get_ticket_details_text(self, ticket) -> str:
        """Формирование текста деталей тикета"""
        lines = [
            _("📋 <b>Обращение {ticket_number}</b>").format(ticket_number=ticket.ticket_number),
            "",
            _("📝 Тема: {subject}").format(subject=ticket.subject),
            _("📂 Категория: {category}").format(
                category=SupportService.CATEGORIES.get(ticket.category, ticket.category)
            ),
            _("📊 Статус: {status}").format(
                status=SupportService.STATUSES.get(ticket.status, ticket.status)
            ),
            _("📅 Создано: {date}").format(
                date=ticket.created_at.strftime("%d.%m.%Y %H:%M")
            ),
            ""
        ]
        
        # Показываем последние сообщения
        if ticket.messages:
            lines.append(_("💬 <b>Сообщения:</b>"))
            lines.append("")
            
            # Показываем последние 3 сообщения
            recent_messages = ticket.messages[-3:] if len(ticket.messages) > 3 else ticket.messages
            
            for msg in recent_messages:
                if msg.get('is_internal'):
                    continue  # Скрываем внутренние сообщения
                
                author = "Вы" if str(msg.get('author_id')) == str(ticket.user_id) else "Поддержка"
                from datetime import datetime
                created_at = datetime.fromisoformat(msg.get('created_at', ''))
                
                lines.append(f"👤 <b>{author}</b> ({created_at.strftime('%d.%m %H:%M')}):")
                lines.append(msg.get('content', '')[:200] + ('...' if len(msg.get('content', '')) > 200 else ''))
                lines.append("")
        
        return "\n".join(lines)
