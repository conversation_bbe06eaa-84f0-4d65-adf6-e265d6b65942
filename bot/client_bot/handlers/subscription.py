"""
UnveilVPN Shop - Client Subscription Handler
Обработчик подписок для клиентского бота с улучшенным UX
"""

from typing import Dict, Any, List
from uuid import UUID

from aiogram.types import Message, CallbackQuery
from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import State, StatesGroup
from aiogram.utils.i18n import gettext as _

from ...common.handlers import BaseHandler
from ...common.utils import MessageFormatter, BotUtils
from ...common.exceptions import NotFoundError, ValidationError, BusinessLogicError
from ...common.ui_components import UIComponents
from ...services.subscription_service import SubscriptionService
from ...services.tariff_service import TariffService
from ...services.vpn_panel_service import VPNPanelService
from ..keyboards import SubscriptionKeyboard, TariffSelectionKeyboard, ConfigKeyboard


class SubscriptionStates(StatesGroup):
    """Состояния для работы с подписками"""
    selecting_tariff = State()
    configuring_device = State()


class SubscriptionHandler(BaseHandler):
    """
    Обработчик подписок для клиентского бота
    """
    
    def __init__(self):
        super().__init__(name='ClientSubscriptionHandler')
    
    def _register_handlers(self):
        """Регистрация обработчиков"""
        
        # Основные обработчики
        self.router.callback_query.register(
            self.show_subscription_info,
            lambda c: c.data == 'subscription_info'
        )
        
        self.router.callback_query.register(
            self.buy_vpn,
            lambda c: c.data == 'buy_vpn'
        )
        
        self.router.callback_query.register(
            self.extend_subscription,
            lambda c: c.data == 'extend_subscription'
        )
        
        self.router.callback_query.register(
            self.select_tariff,
            lambda c: c.data.startswith('tariff_')
        )
        
        self.router.callback_query.register(
            self.manage_configs,
            lambda c: c.data == 'manage_configs'
        )
        
        self.router.callback_query.register(
            self.generate_config,
            lambda c: c.data.startswith('generate_config_')
        )
        
        self.router.callback_query.register(
            self.revoke_config,
            lambda c: c.data.startswith('revoke_config_')
        )
        
        self.router.callback_query.register(
            self.download_config,
            lambda c: c.data.startswith('download_config_')
        )
    
    async def show_subscription_info(self, callback: CallbackQuery, **kwargs):
        """Показ информации о подписке"""
        try:
            user_data = await self._get_user_data(callback, **kwargs)
            db_session = user_data['db_session']
            db_user = user_data['db_user']
            
            # Получаем информацию о подписке через Remnawave
            vpn_panel_service = VPNPanelService(
                panel_url=self.config.remnawave_panel_url,
                api_key=self.config.remnawave_api_key
            )
            subscription_service = SubscriptionService(db_session, vpn_panel_service)
            
            subscription_info = await subscription_service.get_subscription_info(db_user.id)
            
            if not subscription_info:
                # Пользователь без подписки
                text = _(
                    "📊 <b>Информация о подписке</b>\n\n"
                    "У вас пока нет активной VPN подписки.\n\n"
                    "💡 Купите подписку, чтобы получить:\n"
                    "• Безопасное соединение\n"
                    "• Обход блокировок\n"
                    "• Высокую скорость\n"
                    "• Серверы по всему миру"
                )
                
                keyboard = await SubscriptionKeyboard(has_subscription=False).get_keyboard()
            else:
                # Создаем красивую карточку подписки
                subscription_card = UIComponents.create_subscription_card(subscription_info)
                
                text = _(
                    "📊 <b>Информация о подписке</b>\n\n"
                    "{card}\n\n"
                    "Выберите действие:"
                ).format(card=subscription_card)
                
                keyboard = await SubscriptionKeyboard(has_subscription=True).get_keyboard()
            
            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Ошибка показа информации о подписке: {e}")
            await self._send_error(callback, _("Ошибка загрузки информации о подписке"))
    
    async def buy_vpn(self, callback: CallbackQuery, state: FSMContext, **kwargs):
        """Покупка VPN подписки"""
        try:
            user_data = await self._get_user_data(callback, **kwargs)
            db_session = user_data['db_session']
            
            # Получаем доступные тарифы
            tariff_service = TariffService(db_session)
            tariffs = await tariff_service.get_active_tariffs()
            
            if not tariffs:
                await callback.answer(_("Тарифы временно недоступны"), show_alert=True)
                return
            
            # Создаем красивые карточки тарифов
            text_lines = [
                _("💰 <b>Выберите тариф</b>"),
                "",
                _("Доступные тарифные планы:")
            ]
            
            for tariff in tariffs[:3]:  # Показываем первые 3 тарифа
                tariff_dict = {
                    'name': tariff.name,
                    'prices': tariff.prices,
                    'duration_days': tariff.duration_days,
                    'features': tariff.features,
                    'is_popular': tariff.is_popular
                }
                
                card = UIComponents.create_tariff_card(tariff_dict)
                text_lines.extend(["", card])
            
            text = "\n".join(text_lines)
            
            # Устанавливаем состояние выбора тарифа
            await state.set_state(SubscriptionStates.selecting_tariff)
            
            keyboard = await TariffSelectionKeyboard(tariffs).get_keyboard()
            
            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Ошибка покупки VPN: {e}")
            await self._send_error(callback, _("Ошибка загрузки тарифов"))
    
    async def extend_subscription(self, callback: CallbackQuery, **kwargs):
        """Продление подписки"""
        try:
            # Показываем тарифы для продления
            await self.buy_vpn(callback, **kwargs)
            
            # Добавляем уведомление о продлении
            notification = UIComponents.create_notification_banner(
                "При покупке нового тарифа время добавится к текущей подписке",
                banner_type="info"
            )
            
            current_text = callback.message.text
            new_text = f"{current_text}\n\n{notification}"
            
            await callback.message.edit_text(new_text, reply_markup=callback.message.reply_markup)
            
        except Exception as e:
            self.logger.error(f"Ошибка продления подписки: {e}")
            await self._send_error(callback, _("Ошибка продления подписки"))
    
    async def select_tariff(self, callback: CallbackQuery, state: FSMContext, **kwargs):
        """Выбор тарифа"""
        try:
            # Извлекаем ID тарифа
            tariff_id = callback.data.split('_', 1)[1]
            
            user_data = await self._get_user_data(callback, **kwargs)
            db_session = user_data['db_session']
            
            # Получаем информацию о тарифе
            tariff_service = TariffService(db_session)
            tariff = await tariff_service.get_tariff_by_id(UUID(tariff_id))
            
            if not tariff:
                await callback.answer(_("Тариф не найден"), show_alert=True)
                return
            
            # Создаем карточку выбранного тарифа
            tariff_dict = {
                'name': tariff.name,
                'prices': tariff.prices,
                'duration_days': tariff.duration_days,
                'features': tariff.features,
                'is_popular': tariff.is_popular
            }
            
            selected_card = UIComponents.create_tariff_card(tariff_dict, is_selected=True)
            
            # Создаем индикатор шагов
            steps = UIComponents.create_step_indicator(
                current_step=2,
                total_steps=3,
                step_names=["Выбор тарифа", "Способ оплаты", "Оплата"]
            )
            
            text = _(
                "✅ <b>Тариф выбран</b>\n\n"
                "{card}\n\n"
                "📋 <b>Прогресс покупки:</b>\n"
                "{steps}\n\n"
                "Выберите способ оплаты:"
            ).format(card=selected_card, steps=steps)
            
            # Сохраняем выбранный тариф
            await state.update_data(selected_tariff_id=tariff_id)
            
            from ..keyboards import PaymentMethodKeyboard
            keyboard = await PaymentMethodKeyboard(tariff).get_keyboard()
            
            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Ошибка выбора тарифа: {e}")
            await self._send_error(callback, _("Ошибка выбора тарифа"))
    
    async def manage_configs(self, callback: CallbackQuery, **kwargs):
        """Управление конфигурациями"""
        try:
            user_data = await self._get_user_data(callback, **kwargs)
            db_session = user_data['db_session']
            db_user = user_data['db_user']
            
            # Получаем конфигурации пользователя через Remnawave
            vpn_panel_service = VPNPanelService(
                panel_url=self.config.remnawave_panel_url,
                api_key=self.config.remnawave_api_key
            )
            subscription_service = SubscriptionService(db_session, vpn_panel_service)
            
            subscription_info = await subscription_service.get_subscription_info(db_user.id)
            
            if not subscription_info:
                await callback.answer(_("У вас нет активной подписки"), show_alert=True)
                return
            
            configs = subscription_info.get('configs', [])
            
            text_lines = [
                _("📱 <b>Управление конфигурациями</b>"),
                "",
                _("Ваши VPN конфигурации для подключения устройств:")
            ]
            
            if configs:
                for i, config in enumerate(configs, 1):
                    device_name = config.get('device_name', f'Устройство {i}')
                    protocol = config.get('protocol', 'WireGuard')
                    created_at = config.get('created_at', '')
                    
                    try:
                        from datetime import datetime
                        date = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                        date_str = date.strftime('%d.%m.%Y')
                    except:
                        date_str = 'Неизвестно'
                    
                    text_lines.extend([
                        "",
                        f"📱 <b>{device_name}</b>",
                        f"🔧 Протокол: {protocol}",
                        f"📅 Создан: {date_str}"
                    ])
            else:
                text_lines.extend([
                    "",
                    _("У вас пока нет созданных конфигураций."),
                    _("Создайте конфигурацию для подключения устройства.")
                ])
            
            text_lines.extend([
                "",
                _("💡 <b>Что можно делать:</b>"),
                _("• Создать новую конфигурацию"),
                _("• Скачать существующую"),
                _("• Удалить ненужную"),
                "",
                _("Выберите действие:")
            ])
            
            text = "\n".join(text_lines)
            
            keyboard = await ConfigKeyboard(configs).get_keyboard()
            
            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Ошибка управления конфигурациями: {e}")
            await self._send_error(callback, _("Ошибка загрузки конфигураций"))
    
    async def generate_config(self, callback: CallbackQuery, state: FSMContext, **kwargs):
        """Генерация новой конфигурации"""
        try:
            # Извлекаем протокол
            protocol = callback.data.split('_', 2)[2]
            
            user_data = await self._get_user_data(callback, **kwargs)
            db_session = user_data['db_session']
            db_user = user_data['db_user']
            
            # Генерируем конфигурацию через Remnawave
            vpn_panel_service = VPNPanelService(
                panel_url=self.config.remnawave_panel_url,
                api_key=self.config.remnawave_api_key
            )
            subscription_service = SubscriptionService(db_session, vpn_panel_service)
            
            # Генерируем имя устройства
            from datetime import datetime
            device_name = f"Device_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            config = await subscription_service.generate_config(
                user_id=db_user.id,
                device_name=device_name,
                protocol=protocol
            )
            
            # Создаем уведомление об успехе
            success_banner = UIComponents.create_notification_banner(
                f"Конфигурация {protocol.upper()} успешно создана!",
                banner_type="success"
            )
            
            text = _(
                "✅ <b>Конфигурация создана</b>\n\n"
                "{banner}\n\n"
                "📱 <b>Устройство:</b> {device_name}\n"
                "🔧 <b>Протокол:</b> {protocol}\n\n"
                "💾 Конфигурация готова к использованию.\n"
                "Нажмите 'Скачать', чтобы получить файл конфигурации."
            ).format(
                banner=success_banner,
                device_name=device_name,
                protocol=protocol.upper()
            )
            
            keyboard = await ConfigKeyboard([config], show_download=True).get_keyboard()
            
            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Ошибка генерации конфигурации: {e}")
            await self._send_error(callback, _("Ошибка создания конфигурации"))
    
    async def revoke_config(self, callback: CallbackQuery, **kwargs):
        """Отзыв конфигурации"""
        try:
            # Извлекаем ID конфигурации
            config_id = callback.data.split('_', 2)[2]

            # Отзываем конфигурацию через VPN API
            success = await self.vpn_service.revoke_config(config_id)

            if success:
                await callback.answer(_("Конфигурация отозвана"))
                # Обновляем сообщение
                await self.show_subscription_details(callback.message, callback.from_user.id)
            else:
                await callback.answer(_("Ошибка отзыва конфигурации"), show_alert=True)

        except Exception as e:
            self.logger.error(f"Ошибка отзыва конфигурации: {e}")
            await callback.answer(_("Ошибка отзыва конфигурации"), show_alert=True)

    async def download_config(self, callback: CallbackQuery, **kwargs):
        """Скачивание конфигурации"""
        try:
            # Извлекаем ID конфигурации
            config_id = callback.data.split('_', 2)[2]
            
            # Здесь будет логика отправки файла конфигурации
            # Пока отправляем уведомление
            
            notification = UIComponents.create_notification_banner(
                "Конфигурация отправлена в личные сообщения",
                banner_type="info"
            )
            
            text = _(
                "📥 <b>Скачивание конфигурации</b>\n\n"
                "{notification}\n\n"
                "📱 Установите конфигурацию на ваше устройство:\n\n"
                "• <b>Android:</b> WireGuard app\n"
                "• <b>iOS:</b> WireGuard app\n"
                "• <b>Windows:</b> WireGuard client\n"
                "• <b>macOS:</b> WireGuard app\n\n"
                "📖 Подробные инструкции по настройке доступны в разделе 'Помощь'."
            ).format(notification=notification)
            
            await callback.message.edit_text(text, reply_markup=callback.message.reply_markup)
            await callback.answer(_("Конфигурация отправлена!"))
            
        except Exception as e:
            self.logger.error(f"Ошибка скачивания конфигурации: {e}")
            await self._send_error(callback, _("Ошибка скачивания конфигурации"))
