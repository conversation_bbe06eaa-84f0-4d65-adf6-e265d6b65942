"""
UnveilVPN Shop - Client Payments Handler
Обработчик платежей для клиентского бота
"""

from typing import Dict, Any, Optional
from uuid import UUID

from aiogram.types import Message, CallbackQuery, PreCheckoutQuery, SuccessfulPayment
from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import State, StatesGroup
from aiogram.utils.i18n import gettext as _

from ...common.handlers import BaseHandler
from ...common.utils import MessageFormatter, BotUtils
from ...common.exceptions import PaymentError, ValidationError
from ...services.payment_manager import PaymentManager
from ...services.promocode_service import PromocodeService
from ..keyboards import PaymentMethodKeyboard, PaymentStatusKeyboard, PromocodeSelectionKeyboard


class PaymentStates(StatesGroup):
    """Состояния для процесса оплаты"""
    waiting_promocode = State()
    processing_payment = State()


class PaymentsHandler(BaseHandler):
    """
    Обработчик платежей для клиентского бота
    """
    
    def __init__(self):
        super().__init__(name='ClientPaymentsHandler')
        self.payment_manager = None
    
    def _register_handlers(self):
        """Регистрация обработчиков"""
        
        # Основные обработчики платежей
        self.router.callback_query.register(
            self.select_payment_method,
            lambda c: c.data.startswith('pay_')
        )
        
        self.router.callback_query.register(
            self.process_promocode_request,
            lambda c: c.data.startswith('promocode_')
        )
        
        # Telegram Stars
        self.router.pre_checkout_query.register(
            self.process_pre_checkout_query
        )
        
        self.router.message.register(
            self.process_successful_payment,
            lambda m: m.successful_payment is not None
        )
        
        # Ввод промокода
        self.router.message.register(
            self.process_promocode_input,
            PaymentStates.waiting_promocode
        )
        
        # Проверка статуса платежа
        self.router.callback_query.register(
            self.check_payment_status,
            lambda c: c.data.startswith('check_payment_')
        )
    
    async def select_payment_method(self, callback: CallbackQuery, state: FSMContext, **kwargs):
        """Обработка выбора метода оплаты"""
        try:
            # Парсим callback_data
            parts = callback.data.split('_')
            if len(parts) < 3:
                await callback.answer(_("Ошибка обработки запроса"), show_alert=True)
                return
            
            payment_method = parts[1]  # yookassa, cryptomus, stars
            tariff_id = parts[2]
            
            user_data = await self._get_user_data(callback, **kwargs)
            db_session = user_data['db_session']
            db_user = user_data['db_user']
            
            # Получаем менеджер платежей
            payment_manager = await self._get_payment_manager(db_session)
            
            # Проверяем доступность метода оплаты
            if not payment_manager.is_method_available(payment_method):
                await callback.answer(_("Метод оплаты временно недоступен"), show_alert=True)
                return
            
            # Сохраняем данные для создания платежа
            await state.update_data(
                tariff_id=tariff_id,
                payment_method=payment_method,
                user_id=str(db_user.id)
            )
            
            # Создаем платеж
            await self._create_payment(callback, state, payment_manager)
            
        except Exception as e:
            self.logger.error(f"Ошибка выбора метода оплаты: {e}")
            await self._send_error(callback, _("Ошибка обработки платежа"))
    
    async def _create_payment(
        self,
        callback: CallbackQuery,
        state: FSMContext,
        payment_manager: PaymentManager,
        promocode_id: Optional[UUID] = None
    ):
        """Создание платежа"""
        try:
            payment_data = await state.get_data()
            
            # Создаем платеж
            result = await payment_manager.create_payment(
                user_id=UUID(payment_data['user_id']),
                tariff_id=UUID(payment_data['tariff_id']),
                payment_method=payment_data['payment_method'],
                promocode_id=promocode_id,
                metadata={
                    'telegram_user_id': callback.from_user.id,
                    'chat_id': callback.message.chat.id,
                    'created_via': 'telegram_bot'
                }
            )
            
            if result['success']:
                await self._handle_successful_payment_creation(callback, state, result)
            else:
                await self._handle_failed_payment_creation(callback, result)
                
        except (PaymentError, ValidationError) as e:
            await self._send_error(callback, str(e))
        except Exception as e:
            self.logger.error(f"Ошибка создания платежа: {e}")
            await self._send_error(callback, _("Не удалось создать платеж"))
    
    async def _handle_successful_payment_creation(
        self,
        callback: CallbackQuery,
        state: FSMContext,
        result: Dict[str, Any]
    ):
        """Обработка успешного создания платежа"""
        try:
            payment_method = result['method']
            
            if payment_method == 'telegram_stars':
                # Для Telegram Stars платеж уже отправлен пользователю
                text = _(
                    "💳 <b>Платеж создан!</b>\n\n"
                    "Сумма: {amount} ⭐\n"
                    "Метод: Telegram Stars\n\n"
                    "Нажмите кнопку 'Оплатить' в сообщении выше."
                ).format(amount=int(result['amount']))
                
                keyboard = await PaymentStatusKeyboard(result['payment_id']).get_keyboard()
                
            else:
                # Для внешних платежных систем
                text = _(
                    "💳 <b>Платеж создан!</b>\n\n"
                    "Сумма: {amount} {currency}\n"
                    "Метод: {method}\n\n"
                    "Нажмите кнопку ниже для перехода к оплате:"
                ).format(
                    amount=result['amount'],
                    currency=result['currency'],
                    method=self._get_method_name(payment_method)
                )
                
                from ..keyboards import PaymentUrlKeyboard
                keyboard = await PaymentUrlKeyboard(
                    result['payment_url'],
                    result['payment_id']
                ).get_keyboard()
            
            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()
            
            # Сохраняем ID платежа в состоянии
            await state.update_data(payment_id=result['payment_id'])
            await state.set_state(PaymentStates.processing_payment)
            
        except Exception as e:
            self.logger.error(f"Ошибка обработки успешного создания платежа: {e}")
            await self._send_error(callback, _("Ошибка обработки платежа"))
    
    async def _handle_failed_payment_creation(
        self,
        callback: CallbackQuery,
        result: Dict[str, Any]
    ):
        """Обработка неудачного создания платежа"""
        error_message = result.get('message', _("Неизвестная ошибка"))
        
        text = _(
            "❌ <b>Ошибка создания платежа</b>\n\n"
            "{error}\n\n"
            "Попробуйте еще раз или выберите другой способ оплаты."
        ).format(error=error_message)
        
        from ..keyboards import TariffDetailsKeyboard
        tariff_id = callback.data.split('_')[2]
        keyboard = await TariffDetailsKeyboard(tariff_id).get_keyboard()
        
        await callback.message.edit_text(text, reply_markup=keyboard)
        await callback.answer()
    
    async def process_promocode_request(self, callback: CallbackQuery, state: FSMContext, **kwargs):
        """Обработка запроса на ввод промокода"""
        try:
            await state.set_state(PaymentStates.waiting_promocode)
            
            text = _(
                "🎫 <b>Введите промокод</b>\n\n"
                "Отправьте промокод текстом или нажмите 'Отмена' для продолжения без промокода."
            )
            
            from ..keyboards import CancelKeyboard
            keyboard = await CancelKeyboard().get_keyboard()
            
            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Ошибка запроса промокода: {e}")
            await self._send_error(callback, _("Ошибка обработки промокода"))
    
    async def process_promocode_input(self, message: Message, state: FSMContext, **kwargs):
        """Обработка ввода промокода"""
        try:
            promocode = message.text.strip().upper()
            
            # Валидация промокода
            if len(promocode) < 3 or len(promocode) > 50:
                await message.answer(_("❌ Неверный формат промокода. Попробуйте еще раз:"))
                return
            
            user_data = await self._get_user_data(message, **kwargs)
            db_session = user_data['db_session']
            
            # Проверяем промокод
            from ...services.promocode_service import PromocodeService
            promocode_service = PromocodeService(db_session)
            
            promocode_obj = await promocode_service.get_promocode_by_code(promocode)
            
            if not promocode_obj:
                await message.answer(_("❌ Промокод не найден. Попробуйте еще раз:"))
                return
            
            # Проверяем валидность
            if not await promocode_service.is_promocode_valid(promocode_obj.id, user_data['db_user'].id):
                await message.answer(_("❌ Промокод недействителен или уже использован. Попробуйте еще раз:"))
                return
            
            # Применяем промокод
            payment_manager = await self._get_payment_manager(db_session)
            await self._create_payment(message, state, payment_manager, promocode_obj.id)
            
            await state.clear()
            
        except Exception as e:
            self.logger.error(f"Ошибка обработки промокода: {e}")
            await message.answer(_("❌ Ошибка обработки промокода"))
    
    async def process_pre_checkout_query(self, pre_checkout_query: PreCheckoutQuery, **kwargs):
        """Обработка pre_checkout_query для Telegram Stars"""
        try:
            # Автоматически подтверждаем pre_checkout
            await pre_checkout_query.answer(ok=True)
            
        except Exception as e:
            self.logger.error(f"Ошибка pre_checkout_query: {e}")
            await pre_checkout_query.answer(
                ok=False,
                error_message=_("Ошибка обработки платежа")
            )
    
    async def process_successful_payment(self, message: Message, **kwargs):
        """Обработка успешного платежа Telegram Stars"""
        try:
            payment_info = message.successful_payment
            
            text = _(
                "✅ <b>Платеж успешно завершен!</b>\n\n"
                "Сумма: {amount} ⭐\n"
                "Ваша подписка активирована!\n\n"
                "Спасибо за покупку!"
            ).format(amount=payment_info.total_amount)
            
            from ..keyboards import MainMenuKeyboard
            keyboard = await MainMenuKeyboard().get_keyboard()
            
            await message.answer(text, reply_markup=keyboard)
            
        except Exception as e:
            self.logger.error(f"Ошибка обработки успешного платежа: {e}")
            await message.answer(_("Платеж завершен, но произошла ошибка обработки"))
    
    async def check_payment_status(self, callback: CallbackQuery, **kwargs):
        """Проверка статуса платежа"""
        try:
            payment_id = callback.data.replace('check_payment_', '')
            
            user_data = await self._get_user_data(callback, **kwargs)
            db_session = user_data['db_session']
            
            payment_manager = await self._get_payment_manager(db_session)
            result = await payment_manager.check_payment_status(UUID(payment_id))
            
            status = result['status']
            
            if status == 'completed':
                text = _(
                    "✅ <b>Платеж завершен!</b>\n\n"
                    "Ваша подписка активирована.\n"
                    "Спасибо за покупку!"
                )
                
                from ..keyboards import MainMenuKeyboard
                keyboard = await MainMenuKeyboard().get_keyboard()
                
            elif status == 'failed':
                text = _(
                    "❌ <b>Платеж не удался</b>\n\n"
                    "Попробуйте еще раз или выберите другой способ оплаты."
                )
                
                from ..keyboards import TariffSelectionKeyboard
                keyboard = await TariffSelectionKeyboard([]).get_keyboard()
                
            else:
                text = _(
                    "⏳ <b>Платеж обрабатывается</b>\n\n"
                    "Статус: {status}\n"
                    "Проверьте статус через несколько минут."
                ).format(status=self._get_status_name(status))
                
                keyboard = await PaymentStatusKeyboard(payment_id).get_keyboard()
            
            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Ошибка проверки статуса платежа: {e}")
            await self._send_error(callback, _("Ошибка проверки статуса"))
    
    # Вспомогательные методы
    
    async def _get_payment_manager(self, db_session) -> PaymentManager:
        """Получение менеджера платежей"""
        if not self.payment_manager:
            config = self.config
            payment_config = {
                'yookassa': {
                    'shop_id': config.yookassa_shop_id,
                    'secret_key': config.yookassa_secret_key,
                    'receipt_email': config.receipt_email
                },
                'cryptomus': {
                    'merchant_id': config.cryptomus_merchant_id,
                    'api_key': config.cryptomus_api_key
                },
                'telegram_stars': {
                    'bot_token': config.bot_token,
                    'provider_token': config.telegram_stars_provider_token
                }
            }
            self.payment_manager = PaymentManager(payment_config, db_session)
        
        return self.payment_manager
    
    def _get_method_name(self, method: str) -> str:
        """Получение читаемого названия метода оплаты"""
        method_names = {
            'yookassa': _('Банковская карта'),
            'cryptomus': _('Криптовалюта'),
            'telegram_stars': _('Telegram Stars')
        }
        return method_names.get(method, method)
    
    def _get_status_name(self, status: str) -> str:
        """Получение читаемого названия статуса"""
        status_names = {
            'pending': _('Ожидает оплаты'),
            'completed': _('Завершен'),
            'failed': _('Не удался'),
            'refunded': _('Возвращен')
        }
        return status_names.get(status, status)
