"""
UnveilVPN Shop - Client Referrals Handler
Обработчик реферальной системы для клиентского бота
"""

from typing import Dict, Any

from aiogram.types import Message, CallbackQuery
from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import State, StatesGroup
from aiogram.utils.i18n import gettext as _

from ...common.handlers import BaseHandler
from ...common.utils import MessageFormatter, BotUtils
from ...common.exceptions import ValidationError, BusinessLogicError
from ...services.referral_service import ReferralService
from ..keyboards import ReferralMenuKeyboard, ReferralStatsKeyboard


class ReferralStates(StatesGroup):
    """Состояния для работы с рефералами"""
    waiting_referral_code = State()


class ReferralsHandler(BaseHandler):
    """
    Обработчик реферальной системы для клиентского бота
    """
    
    def __init__(self):
        super().__init__(name='ClientReferralsHandler')
    
    def _register_handlers(self):
        """Регистрация обработчиков"""
        
        # Основные обработчики
        self.router.callback_query.register(
            self.show_referral_menu,
            lambda c: c.data == 'referrals'
        )
        
        self.router.callback_query.register(
            self.show_referral_stats,
            lambda c: c.data == 'referral_stats'
        )
        
        self.router.callback_query.register(
            self.generate_referral_code,
            lambda c: c.data == 'generate_referral_code'
        )
        
        self.router.callback_query.register(
            self.enter_referral_code,
            lambda c: c.data == 'enter_referral_code'
        )
        
        self.router.callback_query.register(
            self.show_referral_tree,
            lambda c: c.data == 'referral_tree'
        )
        
        # Ввод реферального кода
        self.router.message.register(
            self.process_referral_code_input,
            ReferralStates.waiting_referral_code
        )
    
    async def show_referral_menu(self, callback: CallbackQuery, **kwargs):
        """Показ главного меню реферальной системы"""
        try:
            user_data = await self._get_user_data(callback, **kwargs)
            db_session = user_data['db_session']
            db_user = user_data['db_user']
            
            # Получаем базовую статистику
            referral_service = ReferralService(db_session)
            stats = await referral_service.get_user_referral_stats(db_user.id)
            
            text = await self._get_referral_menu_text(stats, db_user)
            keyboard = await ReferralMenuKeyboard(db_user).get_keyboard(user_data)
            
            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Ошибка показа меню рефералов: {e}")
            await self._send_error(callback, _("Ошибка загрузки реферальной системы"))
    
    async def show_referral_stats(self, callback: CallbackQuery, **kwargs):
        """Показ детальной статистики рефералов"""
        try:
            user_data = await self._get_user_data(callback, **kwargs)
            db_session = user_data['db_session']
            db_user = user_data['db_user']
            
            referral_service = ReferralService(db_session)
            stats = await referral_service.get_user_referral_stats(db_user.id)
            
            text = await self._get_referral_stats_text(stats)
            keyboard = await ReferralStatsKeyboard().get_keyboard(user_data)
            
            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Ошибка показа статистики рефералов: {e}")
            await self._send_error(callback, _("Ошибка загрузки статистики"))
    
    async def generate_referral_code(self, callback: CallbackQuery, **kwargs):
        """Генерация реферального кода"""
        try:
            user_data = await self._get_user_data(callback, **kwargs)
            db_session = user_data['db_session']
            db_user = user_data['db_user']
            
            referral_service = ReferralService(db_session)
            code = await referral_service.generate_referral_code(db_user.id)
            
            await db_session.commit()
            
            text = _(
                "🎉 <b>Ваш реферальный код готов!</b>\n\n"
                "🔗 Код: <code>{code}</code>\n\n"
                "📋 <b>Как это работает:</b>\n"
                "• Поделитесь кодом с друзьями\n"
                "• Они регистрируются и покупают подписку\n"
                "• Вы получаете бонусы:\n"
                "  - 10% с покупок прямых рефералов\n"
                "  - 5% с покупок рефералов 2-го уровня\n"
                "  - 2% с покупок рефералов 3-го уровня\n\n"
                "💰 Бонусы можно использовать для покупки подписок!"
            ).format(code=code)
            
            from ..keyboards import ReferralCodeKeyboard
            keyboard = await ReferralCodeKeyboard(code).get_keyboard()
            
            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Ошибка генерации реферального кода: {e}")
            await self._send_error(callback, _("Ошибка генерации кода"))
    
    async def enter_referral_code(self, callback: CallbackQuery, state: FSMContext, **kwargs):
        """Запрос на ввод реферального кода"""
        try:
            user_data = await self._get_user_data(callback, **kwargs)
            db_user = user_data['db_user']
            
            # Проверяем, не является ли пользователь уже рефералом
            if db_user.referred_by_id:
                await callback.answer(_("Вы уже являетесь рефералом"), show_alert=True)
                return
            
            await state.set_state(ReferralStates.waiting_referral_code)
            
            text = _(
                "🔗 <b>Введите реферальный код</b>\n\n"
                "Если у вас есть реферальный код от друга, "
                "введите его, чтобы получить бонусы при покупке подписки.\n\n"
                "Отправьте код текстом или нажмите 'Отмена'."
            )
            
            from ..keyboards import CancelKeyboard
            keyboard = await CancelKeyboard().get_keyboard()
            
            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Ошибка запроса реферального кода: {e}")
            await self._send_error(callback, _("Ошибка обработки запроса"))
    
    async def process_referral_code_input(self, message: Message, state: FSMContext, **kwargs):
        """Обработка ввода реферального кода"""
        try:
            code = message.text.strip().upper()
            
            # Валидация кода
            if len(code) < 6 or len(code) > 12:
                await message.answer(_("❌ Неверный формат кода. Попробуйте еще раз:"))
                return
            
            user_data = await self._get_user_data(message, **kwargs)
            db_session = user_data['db_session']
            db_user = user_data['db_user']
            
            referral_service = ReferralService(db_session)
            
            try:
                success = await referral_service.register_referral(code, db_user.id)
                
                if success:
                    await db_session.commit()
                    await state.clear()
                    
                    text = _(
                        "✅ <b>Реферальная связь установлена!</b>\n\n"
                        "Теперь вы получите бонусы при покупке подписки.\n"
                        "Также вы можете создать свой реферальный код "
                        "и приглашать друзей!"
                    )
                    
                    from ..keyboards import ReferralMenuKeyboard
                    keyboard = await ReferralMenuKeyboard(db_user).get_keyboard()
                    
                    await message.answer(text, reply_markup=keyboard)
                else:
                    await message.answer(_("❌ Не удалось установить реферальную связь"))
                    
            except ValidationError as e:
                await message.answer(f"❌ {str(e)}")
            
        except Exception as e:
            self.logger.error(f"Ошибка обработки реферального кода: {e}")
            await message.answer(_("❌ Ошибка обработки кода"))
    
    async def show_referral_tree(self, callback: CallbackQuery, **kwargs):
        """Показ дерева рефералов"""
        try:
            user_data = await self._get_user_data(callback, **kwargs)
            db_session = user_data['db_session']
            db_user = user_data['db_user']
            
            referral_service = ReferralService(db_session)
            tree = await referral_service.get_referral_tree(db_user.id, max_depth=3)
            
            text = await self._get_referral_tree_text(tree)
            
            from ..keyboards import BackToReferralsKeyboard
            keyboard = await BackToReferralsKeyboard().get_keyboard()
            
            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Ошибка показа дерева рефералов: {e}")
            await self._send_error(callback, _("Ошибка загрузки дерева рефералов"))
    
    # Вспомогательные методы
    
    async def _get_referral_menu_text(self, stats: Dict[str, Any], user) -> str:
        """Формирование текста главного меню рефералов"""
        lines = [
            _("👥 <b>Реферальная система</b>"),
            ""
        ]
        
        # Информация о коде
        if stats['referral_code']:
            lines.extend([
                _("🔗 Ваш код: <code>{code}</code>").format(code=stats['referral_code']),
                ""
            ])
        
        # Краткая статистика
        lines.extend([
            _("📊 <b>Ваша статистика:</b>"),
            _("• Приглашено: {count} человек").format(count=stats['total_referrals']),
            _("• Заработано: {earnings:.2f} ₽").format(earnings=stats['total_earnings']),
            ""
        ])
        
        # Информация о том, кто пригласил
        if stats['invited_by']:
            invited_by = stats['invited_by']
            lines.extend([
                _("👤 Вас пригласил: @{username}").format(
                    username=invited_by.get('username', 'Unknown')
                ),
                ""
            ])
        
        # Процентные ставки
        lines.extend([
            _("💰 <b>Процентные ставки:</b>"),
            _("• 1-й уровень: 10%"),
            _("• 2-й уровень: 5%"),
            _("• 3-й уровень: 2%"),
            ""
        ])
        
        return "\n".join(lines)
    
    async def _get_referral_stats_text(self, stats: Dict[str, Any]) -> str:
        """Формирование текста детальной статистики"""
        lines = [
            _("📊 <b>Детальная статистика рефералов</b>"),
            ""
        ]
        
        if stats['referral_code']:
            lines.extend([
                _("🔗 Ваш код: <code>{code}</code>").format(code=stats['referral_code']),
                ""
            ])
        
        # Общая статистика
        lines.extend([
            _("📈 <b>Общие показатели:</b>"),
            _("• Всего рефералов: {count}").format(count=stats['total_referrals']),
            _("• Общий заработок: {earnings:.2f} ₽").format(earnings=stats['total_earnings']),
            ""
        ])
        
        # Статистика по уровням
        if stats['stats_by_level']:
            lines.append(_("📋 <b>По уровням:</b>"))
            
            for level, level_stats in stats['stats_by_level'].items():
                if level_stats['count'] > 0:
                    lines.append(
                        _("• {level}-й уровень: {count} чел., {earnings:.2f} ₽").format(
                            level=level,
                            count=level_stats['count'],
                            earnings=level_stats['earnings']
                        )
                    )
            
            lines.append("")
        
        # Информация о приглашении
        if stats['invited_by']:
            invited_by = stats['invited_by']
            lines.extend([
                _("👤 <b>Вас пригласил:</b>"),
                _("• @{username}").format(username=invited_by.get('username', 'Unknown')),
                _("• Код: {code}").format(code=invited_by.get('referral_code', 'N/A')),
                ""
            ])
        
        return "\n".join(lines)
    
    async def _get_referral_tree_text(self, tree: Dict[str, Any]) -> str:
        """Формирование текста дерева рефералов"""
        lines = [
            _("🌳 <b>Дерево ваших рефералов</b>"),
            ""
        ]
        
        if not tree['tree']:
            lines.append(_("У вас пока нет рефералов."))
            return "\n".join(lines)
        
        # Отображаем дерево по уровням
        for depth, users in tree['tree'].items():
            level_name = {
                '1': _("1-й уровень (прямые рефералы)"),
                '2': _("2-й уровень"),
                '3': _("3-й уровень")
            }.get(depth, f"{depth}-й уровень")
            
            lines.append(f"📁 <b>{level_name}:</b>")
            
            for user in users[:10]:  # Показываем максимум 10 на уровень
                username = user.get('username', 'Unknown')
                purchases = user.get('total_purchases', 0)
                earnings = user.get('bonus_earned', 0)
                
                lines.append(
                    f"  • @{username} ({purchases} покупок, {earnings:.2f} ₽)"
                )
            
            if len(users) > 10:
                lines.append(f"  ... и еще {len(users) - 10} рефералов")
            
            lines.append("")
        
        return "\n".join(lines)
