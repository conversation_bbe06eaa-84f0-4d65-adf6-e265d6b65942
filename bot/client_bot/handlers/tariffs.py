"""
UnveilVPN Shop - Client Tariffs Handler
Обработчик тарифов для клиентского бота
"""

from typing import Dict, Any, List
from uuid import UUID

from aiogram.types import Message, CallbackQuery
from aiogram.utils.i18n import gettext as _

from ...common.handlers import BaseHandler
from ...common.utils import MessageFormatter, BotUtils
from ...common.exceptions import BusinessLogicError
from ...services.tariff_service import TariffService
from ..keyboards import TariffSelectionKeyboard, PaymentMethodKeyboard


class TariffsHandler(BaseHandler):
    """
    Обработчик тарифов для клиентского бота
    """
    
    def __init__(self):
        super().__init__(name='ClientTariffsHandler')
    
    def _register_handlers(self):
        """Регистрация обработчиков"""
        
        # Основные обработчики
        self.router.callback_query.register(
            self.show_tariffs,
            lambda c: c.data == 'buy_vpn'
        )
        
        self.router.callback_query.register(
            self.show_tariff_details,
            lambda c: c.data.startswith('tariff_')
        )
        
        self.router.callback_query.register(
            self.select_payment_method,
            lambda c: c.data.startswith('buy_tariff_')
        )
        
        # Фильтры и сортировка
        self.router.callback_query.register(
            self.filter_by_duration,
            lambda c: c.data.startswith('filter_duration_')
        )
        
        self.router.callback_query.register(
            self.sort_tariffs,
            lambda c: c.data.startswith('sort_')
        )
        
        # Популярные тарифы
        self.router.callback_query.register(
            self.show_popular_tariffs,
            lambda c: c.data == 'popular_tariffs'
        )
    
    async def show_tariffs(self, callback: CallbackQuery, **kwargs):
        """Показ списка тарифов"""
        try:
            user_data = await self._get_user_data(callback, **kwargs)
            db_session = user_data['db_session']
            
            # Получаем активные тарифы
            tariff_service = TariffService(db_session)
            tariffs = await tariff_service.get_all_tariffs(
                active_only=True,
                include_test=True,
                sort_by="popular"
            )
            
            if not tariffs:
                text = _("😔 <b>Тарифы временно недоступны</b>\n\n")
                text += _("Попробуйте позже или обратитесь в поддержку.")
                
                from ..keyboards import MainMenuKeyboard
                keyboard = await MainMenuKeyboard().get_keyboard(user_data)
                
                await callback.message.edit_text(text, reply_markup=keyboard)
                await callback.answer()
                return
            
            text = await self._get_tariffs_list_text(tariffs, user_data)
            keyboard = await TariffSelectionKeyboard(tariffs).get_keyboard(user_data)
            
            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Ошибка показа тарифов: {e}")
            await self._send_error(callback, _("Ошибка загрузки тарифов"))
    
    async def show_popular_tariffs(self, callback: CallbackQuery, **kwargs):
        """Показ популярных тарифов"""
        try:
            user_data = await self._get_user_data(callback, **kwargs)
            db_session = user_data['db_session']
            
            # Получаем популярные тарифы
            tariff_service = TariffService(db_session)
            tariffs = await tariff_service.get_popular_tariffs(limit=5)
            
            if not tariffs:
                await callback.answer(_("Популярные тарифы не найдены"), show_alert=True)
                return
            
            text = await self._get_popular_tariffs_text(tariffs, user_data)
            keyboard = await TariffSelectionKeyboard(tariffs, is_popular=True).get_keyboard(user_data)
            
            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Ошибка показа популярных тарифов: {e}")
            await self._send_error(callback, _("Ошибка загрузки популярных тарифов"))
    
    async def show_tariff_details(self, callback: CallbackQuery, **kwargs):
        """Показ детальной информации о тарифе"""
        try:
            # Извлекаем ID тарифа из callback_data
            tariff_id = callback.data.split('_')[1]
            
            user_data = await self._get_user_data(callback, **kwargs)
            db_session = user_data['db_session']
            
            # Получаем тариф
            tariff_service = TariffService(db_session)
            tariff = await tariff_service.get_tariff_by_id(UUID(tariff_id))
            
            if not tariff or not tariff.is_active:
                await callback.answer(_("Тариф не найден или недоступен"), show_alert=True)
                return
            
            text = await self._get_tariff_details_text(tariff, user_data)
            
            from ..keyboards import TariffDetailsKeyboard
            keyboard = await TariffDetailsKeyboard(tariff_id).get_keyboard(user_data)
            
            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Ошибка показа деталей тарифа: {e}")
            await self._send_error(callback, _("Ошибка загрузки тарифа"))
    
    async def select_payment_method(self, callback: CallbackQuery, **kwargs):
        """Выбор способа оплаты для тарифа"""
        try:
            # Извлекаем ID тарифа из callback_data
            tariff_id = callback.data.split('_')[2]
            
            user_data = await self._get_user_data(callback, **kwargs)
            db_session = user_data['db_session']
            
            # Получаем тариф
            tariff_service = TariffService(db_session)
            tariff = await tariff_service.get_tariff_by_id(UUID(tariff_id))
            
            if not tariff or not tariff.is_active:
                await callback.answer(_("Тариф не найден или недоступен"), show_alert=True)
                return
            
            # Определяем доступные способы оплаты
            available_methods = await self._get_available_payment_methods(tariff)
            
            text = await self._get_payment_selection_text(tariff, user_data)
            keyboard = await PaymentMethodKeyboard(available_methods, tariff_id).get_keyboard(user_data)
            
            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Ошибка выбора способа оплаты: {e}")
            await self._send_error(callback, _("Ошибка выбора способа оплаты"))
    
    async def filter_by_duration(self, callback: CallbackQuery, **kwargs):
        """Фильтрация тарифов по продолжительности"""
        try:
            # Извлекаем продолжительность из callback_data
            duration = int(callback.data.split('_')[2])
            
            user_data = await self._get_user_data(callback, **kwargs)
            db_session = user_data['db_session']
            
            # Получаем тарифы по продолжительности
            tariff_service = TariffService(db_session)
            tariffs = await tariff_service.get_tariffs_by_duration(duration)
            
            if not tariffs:
                await callback.answer(_("Тарифы с такой продолжительностью не найдены"), show_alert=True)
                return
            
            text = await self._get_filtered_tariffs_text(tariffs, duration, user_data)
            keyboard = await TariffSelectionKeyboard(tariffs, filter_duration=duration).get_keyboard(user_data)
            
            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Ошибка фильтрации тарифов: {e}")
            await self._send_error(callback, _("Ошибка фильтрации"))
    
    async def sort_tariffs(self, callback: CallbackQuery, **kwargs):
        """Сортировка тарифов"""
        try:
            # Извлекаем тип сортировки из callback_data
            sort_type = callback.data.split('_')[1]
            
            user_data = await self._get_user_data(callback, **kwargs)
            db_session = user_data['db_session']
            
            # Получаем отсортированные тарифы
            tariff_service = TariffService(db_session)
            tariffs = await tariff_service.get_all_tariffs(
                active_only=True,
                include_test=True,
                sort_by=sort_type
            )
            
            text = await self._get_sorted_tariffs_text(tariffs, sort_type, user_data)
            keyboard = await TariffSelectionKeyboard(tariffs, sort_by=sort_type).get_keyboard(user_data)
            
            await callback.message.edit_text(text, reply_markup=keyboard)
            await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Ошибка сортировки тарифов: {e}")
            await self._send_error(callback, _("Ошибка сортировки"))
    
    # Вспомогательные методы
    
    async def _get_tariffs_list_text(self, tariffs: List, user_data: Dict[str, Any]) -> str:
        """Формирование текста списка тарифов"""
        db_user = user_data['db_user']
        
        lines = [
            _("💰 <b>Выберите тариф VPN</b>"),
            ""
        ]
        
        # Информация о тестовом периоде
        if self.config.test_period_enabled and not db_user.is_test_used:
            lines.extend([
                _("🎁 <b>Доступен бесплатный тест на 7 дней!</b>"),
                _("Попробуйте наш сервис без ограничений."),
                ""
            ])
        
        lines.extend([
            _("🔒 <b>Что вы получаете:</b>"),
            _("• Высокая скорость подключения"),
            _("• Серверы в разных странах"),
            _("• Защита ваших данных"),
            _("• Круглосуточная поддержка"),
            "",
            _("Выберите подходящий тариф:")
        ])
        
        return "\n".join(lines)
    
    async def _get_popular_tariffs_text(self, tariffs: List, user_data: Dict[str, Any]) -> str:
        """Формирование текста популярных тарифов"""
        lines = [
            _("⭐ <b>Популярные тарифы</b>"),
            "",
            _("Самые востребованные тарифы среди наших пользователей:"),
            ""
        ]
        
        return "\n".join(lines)
    
    async def _get_tariff_details_text(self, tariff, user_data: Dict[str, Any]) -> str:
        """Формирование детального описания тарифа"""
        tariff_info = {
            'id': str(tariff.id),
            'name': tariff.name,
            'description': tariff.description,
            'duration_days': tariff.duration_days,
            'prices': tariff.prices,
            'features': tariff.features,
            'is_popular': tariff.is_popular,
            'discount_percent': tariff.discount_percent
        }
        
        text = MessageFormatter.format_tariff_info(tariff_info)
        
        # Добавляем информацию о скидке
        if tariff.discount_percent > 0:
            text += f"\n\n🏷️ <b>Скидка {tariff.discount_percent}%!</b>"
        
        return text
    
    async def _get_payment_selection_text(self, tariff, user_data: Dict[str, Any]) -> str:
        """Формирование текста выбора способа оплаты"""
        lines = [
            _("💳 <b>Способ оплаты</b>"),
            "",
            _("Тариф: <b>{name}</b>").format(name=tariff.name),
            _("Продолжительность: {duration}").format(
                duration=BotUtils.format_duration(tariff.duration_days)
            ),
            "",
            _("Выберите удобный способ оплаты:")
        ]
        
        return "\n".join(lines)
    
    async def _get_filtered_tariffs_text(self, tariffs: List, duration: int, user_data: Dict[str, Any]) -> str:
        """Формирование текста отфильтрованных тарифов"""
        lines = [
            _("🔍 <b>Тарифы на {duration}</b>").format(
                duration=BotUtils.format_duration(duration)
            ),
            "",
            _("Найдено тарифов: {count}").format(count=len(tariffs)),
            ""
        ]
        
        return "\n".join(lines)
    
    async def _get_sorted_tariffs_text(self, tariffs: List, sort_type: str, user_data: Dict[str, Any]) -> str:
        """Формирование текста отсортированных тарифов"""
        sort_names = {
            'price_asc': _("по возрастанию цены"),
            'price_desc': _("по убыванию цены"),
            'duration': _("по продолжительности"),
            'popular': _("по популярности")
        }
        
        sort_name = sort_names.get(sort_type, _("по умолчанию"))
        
        lines = [
            _("📊 <b>Тарифы {sort_name}</b>").format(sort_name=sort_name),
            "",
            _("Всего тарифов: {count}").format(count=len(tariffs)),
            ""
        ]
        
        return "\n".join(lines)
    
    async def _get_available_payment_methods(self, tariff) -> List[str]:
        """Определение доступных способов оплаты для тарифа"""
        methods = []
        
        # Проверяем наличие цен в разных валютах
        if tariff.prices.get('rub', 0) > 0:
            methods.append('yookassa')  # Банковские карты
        
        if tariff.prices.get('usd', 0) > 0:
            methods.append('cryptomus')  # Криптовалюта
        
        if tariff.prices.get('stars', 0) > 0:
            methods.append('telegram_stars')  # Telegram Stars
        
        return methods
