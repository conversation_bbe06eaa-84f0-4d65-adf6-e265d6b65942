"""
UnveilVPN Shop - Обработчик тестового периода
Обработка тестового периода для клиентского бота
"""

from aiogram import Router, F
from aiogram.types import Message, CallbackQuery
from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import State, StatesGroup
from aiogram.filters import StateFilter
from typing import Dict, Any

from ...common.handlers import BaseHandler
from ...common.utils import MessageFormatter, BotUtils
from ...common.exceptions import ValidationError, BusinessLogicError
from ...services.subscription_service import SubscriptionService
from ...services.vpn_panel_service import VPNPanelService
from ..keyboards import MainMenuKeyboard


class TestPeriodStates(StatesGroup):
    """Состояния для тестового периода"""
    confirming_activation = State()


class TestPeriodHandler(BaseHandler):
    """
    Обработчик тестового периода для клиентского бота
    """
    
    def __init__(self):
        super().__init__()
        self.main_menu_keyboard = MainMenuKeyboard()
    
    def _register_handlers(self) -> None:
        """Регистрация обработчиков"""
        # Активация тестового периода
        self.router.callback_query.register(
            self.handle_test_period_activation,
            F.data == "test_period_activate"
        )

        # Подтверждение активации
        self.router.callback_query.register(
            self.handle_test_period_confirm,
            F.data == "test_period_confirm"
        )

        # Отмена активации
        self.router.callback_query.register(
            self.handle_test_period_cancel,
            F.data == "test_period_cancel"
        )

        # Информация о тестовом периоде
        self.router.callback_query.register(
            self.handle_test_period_info,
            F.data == "test_period_info"
        )
    
    async def handle_test_period_activation(
        self,
        callback: CallbackQuery,
        state: FSMContext
    ) -> None:
        """Обработка запроса на активацию тестового периода"""
        try:
            user_data = await self._get_user_data(callback, **kwargs)
            db_session = user_data['db_session']
            db_user = user_data['db_user']

            # Создаем сервисы
            vpn_panel_service = VPNPanelService(
                panel_url=self.config.remnawave_panel_url,
                api_key=self.config.remnawave_api_key
            )
            subscription_service = SubscriptionService(db_session, vpn_panel_service)

            # Проверяем, доступен ли тестовый период
            is_available = await subscription_service.is_test_period_available(db_user.id)
            
            if not is_available:
                await callback.answer(
                    "❌ Тестовый период уже был использован",
                    show_alert=True
                )
                return
            
            # Показываем информацию о тестовом периоде
            text = MessageFormatter.format_test_period_info()
            
            keyboard = self._get_test_period_confirmation_keyboard()
            
            await callback.message.edit_text(
                text=text,
                reply_markup=keyboard,
                parse_mode="HTML"
            )
            
            await state.set_state(TestPeriodStates.confirming_activation)
            
        except Exception as e:
            self.logger.error(f"Ошибка при запросе активации тестового периода: {e}")
            await callback.answer(
                "❌ Произошла ошибка. Попробуйте позже",
                show_alert=True
            )
    
    async def handle_test_period_confirm(
        self, 
        callback: CallbackQuery, 
        state: FSMContext
    ) -> None:
        """Подтверждение активации тестового периода"""
        try:
            user_data = await self._get_user_data(callback, **kwargs)
            db_session = user_data['db_session']
            db_user = user_data['db_user']

            # Создаем сервисы
            vpn_panel_service = VPNPanelService(
                panel_url=self.config.remnawave_panel_url,
                api_key=self.config.remnawave_api_key
            )
            subscription_service = SubscriptionService(db_session, vpn_panel_service)

            # Активируем тестовый период
            result = await subscription_service.activate_test_period(db_user.id)

            if result.get('success'):
                # Создаем VPN конфигурацию
                vpn_config = await vpn_panel_service.create_test_config(db_user.id)
                
                text = MessageFormatter.format_test_period_activated(
                    expires_at=result.get('expires_at'),
                    config_data=vpn_config
                )
                
                keyboard = await self.main_menu_keyboard.get_keyboard()
                
                await callback.message.edit_text(
                    text=text,
                    reply_markup=keyboard,
                    parse_mode="HTML"
                )
                
                await callback.answer(
                    "✅ Тестовый период активирован!",
                    show_alert=True
                )
                
            else:
                await callback.answer(
                    f"❌ {result.get('error', 'Не удалось активировать тестовый период')}",
                    show_alert=True
                )
            
            await state.clear()
            
        except Exception as e:
            self.logger.error(f"Ошибка при активации тестового периода: {e}")
            await callback.answer(
                "❌ Произошла ошибка при активации",
                show_alert=True
            )
            await state.clear()
    
    async def handle_test_period_cancel(
        self, 
        callback: CallbackQuery, 
        state: FSMContext
    ) -> None:
        """Отмена активации тестового периода"""
        try:
            keyboard = await self.main_menu_keyboard.get_keyboard()
            
            await callback.message.edit_text(
                text="🏠 Главное меню",
                reply_markup=keyboard
            )
            
            await state.clear()
            
        except Exception as e:
            self.logger.error(f"Ошибка при отмене активации тестового периода: {e}")
            await callback.answer(
                "❌ Произошла ошибка",
                show_alert=True
            )
    
    async def handle_test_period_info(
        self, 
        callback: CallbackQuery
    ) -> None:
        """Показ информации о тестовом периоде"""
        try:
            user_data = await self._get_user_data(callback, **kwargs)
            db_session = user_data['db_session']
            db_user = user_data['db_user']

            # Создаем сервисы
            vpn_panel_service = VPNPanelService(
                panel_url=self.config.remnawave_panel_url,
                api_key=self.config.remnawave_api_key
            )
            subscription_service = SubscriptionService(db_session, vpn_panel_service)

            # Получаем информацию о тестовом периоде пользователя
            test_info = await subscription_service.get_test_period_info(db_user.id)
            
            text = MessageFormatter.format_test_period_status(test_info)
            
            keyboard = await self.main_menu_keyboard.get_keyboard()
            
            await callback.message.edit_text(
                text=text,
                reply_markup=keyboard,
                parse_mode="HTML"
            )
            
        except Exception as e:
            self.logger.error(f"Ошибка при получении информации о тестовом периоде: {e}")
            await callback.answer(
                "❌ Произошла ошибка",
                show_alert=True
            )
    
    def _get_test_period_confirmation_keyboard(self):
        """Получение клавиатуры подтверждения тестового периода"""
        from aiogram.utils.keyboard import InlineKeyboardBuilder
        from aiogram.types import InlineKeyboardButton
        
        builder = InlineKeyboardBuilder()
        
        builder.row(
            InlineKeyboardButton(
                text="✅ Активировать",
                callback_data="test_period_confirm"
            )
        )
        
        builder.row(
            InlineKeyboardButton(
                text="❌ Отмена",
                callback_data="test_period_cancel"
            )
        )
        
        return builder.as_markup()
