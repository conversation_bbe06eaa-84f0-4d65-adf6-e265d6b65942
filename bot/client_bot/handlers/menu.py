"""
UnveilVPN Shop - Menu Handler
Обработчик главного меню для клиентского бота
"""

from typing import Dict, Any

from aiogram.types import Message, CallbackQuery
from aiogram.utils.i18n import gettext as _

from ...common.handlers import MenuHandler as BaseMenuHandler
from ...common.utils import MessageFormatter, BotUtils
from ..keyboards import MainMenuKeyboard


class MenuHandler(BaseMenuHandler):
    """
    Обработчик главного меню клиентского бота
    """
    
    def __init__(self):
        menu_keyboard = MainMenuKeyboard()
        super().__init__(menu_keyboard, name='MenuHandler')
        
        # Регистрируем дополнительные обработчики
        self._register_menu_callbacks()
    
    def _register_menu_callbacks(self):
        """Регистрация обработчиков callback для пунктов меню"""
        
        # Профиль пользователя
        self.router.callback_query.register(
            self.show_profile,
            lambda c: c.data == 'profile'
        )
        
        # Информация о сервисе
        self.router.callback_query.register(
            self.show_about,
            lambda c: c.data == 'about'
        )
        
        # Помощь
        self.router.callback_query.register(
            self.show_help,
            lambda c: c.data == 'help'
        )
        
        # Настройки
        self.router.callback_query.register(
            self.show_settings,
            lambda c: c.data == 'settings'
        )
    
    async def _get_menu_text(self, user_data: Dict[str, Any]) -> str:
        """Получение текста главного меню"""
        user = user_data['user']
        db_user = user_data['db_user']
        
        name = user.first_name or user.username or _("Пользователь")
        
        lines = [
            _("🏠 <b>Главное меню</b>"),
            "",
            _("Привет, {name}!").format(name=name),
        ]
        
        # Статус подписки
        if db_user.vpn_id:
            lines.extend([
                "",
                _("📊 <b>Ваша подписка:</b>"),
                _("🟢 Активна"),
                # Здесь будет информация о подписке из VPN панели
            ])
        else:
            lines.extend([
                "",
                _("📊 <b>Статус:</b>"),
                _("🔴 Подписка не активна"),
            ])
        
        # Тестовый период
        if self.config.test_period_enabled and not db_user.is_test_used:
            lines.extend([
                "",
                _("🎁 Доступен бесплатный тестовый период!")
            ])
        
        # Реферальная информация
        if db_user.referral_code:
            lines.extend([
                "",
                _("💰 <b>Реферальная программа:</b>"),
                _("Ваш код: <code>{code}</code>").format(code=db_user.referral_code),
                _("Заработано: {earnings:.2f} ₽").format(earnings=float(db_user.referral_earnings or 0)),
            ])

        # Информация о промокодах
        lines.extend([
            "",
            _("🎫 Используйте промокоды для скидок!")
        ])
        
        lines.extend([
            "",
            _("Выберите действие:")
        ])
        
        return "\n".join(lines)
    
    async def show_profile(self, callback: CallbackQuery, **kwargs):
        """Показ профиля пользователя"""
        try:
            user_data = await self._get_user_data(callback, **kwargs)
            
            profile_text = await self._get_profile_text(user_data)
            
            from ..keyboards import ProfileKeyboard
            keyboard = await ProfileKeyboard().get_keyboard(user_data)
            
            await callback.message.edit_text(profile_text, reply_markup=keyboard)
            await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Ошибка показа профиля: {e}")
            await self._send_error(callback, _("Ошибка загрузки профиля"))
    
    async def show_about(self, callback: CallbackQuery, **kwargs):
        """Показ информации о сервисе"""
        try:
            about_text = await self._get_about_text()
            
            from ..keyboards import AboutKeyboard
            keyboard = await AboutKeyboard().get_keyboard()
            
            await callback.message.edit_text(about_text, reply_markup=keyboard)
            await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Ошибка показа информации: {e}")
            await self._send_error(callback, _("Ошибка загрузки информации"))
    
    async def show_help(self, callback: CallbackQuery, **kwargs):
        """Показ справки"""
        try:
            help_text = await self._get_help_text()
            
            from ..keyboards import HelpKeyboard
            keyboard = await HelpKeyboard().get_keyboard()
            
            await callback.message.edit_text(help_text, reply_markup=keyboard)
            await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Ошибка показа справки: {e}")
            await self._send_error(callback, _("Ошибка загрузки справки"))
    
    async def show_settings(self, callback: CallbackQuery, **kwargs):
        """Показ настроек"""
        try:
            user_data = await self._get_user_data(callback, **kwargs)
            
            settings_text = await self._get_settings_text(user_data)
            
            from ..keyboards import SettingsKeyboard
            keyboard = await SettingsKeyboard().get_keyboard(user_data)
            
            await callback.message.edit_text(settings_text, reply_markup=keyboard)
            await callback.answer()
            
        except Exception as e:
            self.logger.error(f"Ошибка показа настроек: {e}")
            await self._send_error(callback, _("Ошибка загрузки настроек"))
    
    async def _get_profile_text(self, user_data: Dict[str, Any]) -> str:
        """Формирование текста профиля"""
        user = user_data['user']
        db_user = user_data['db_user']
        
        lines = [
            _("👤 <b>Ваш профиль</b>"),
            "",
            _("ID: <code>{id}</code>").format(id=user.id),
        ]
        
        if user.username:
            lines.append(_("Username: @{username}").format(username=user.username))
        
        if user.first_name:
            lines.append(_("Имя: {name}").format(name=user.first_name))
        
        if user.last_name:
            lines.append(_("Фамилия: {name}").format(name=user.last_name))
        
        lines.extend([
            "",
            _("📅 Дата регистрации: {date}").format(
                date=BotUtils.format_datetime(db_user.created_at, 'date')
            ),
            _("🕐 Последняя активность: {date}").format(
                date=BotUtils.format_datetime(db_user.last_activity, 'short')
            ),
        ])
        
        if db_user.referral_code:
            lines.extend([
                "",
                _("💰 <b>Реферальная программа:</b>"),
                _("Ваш код: <code>{code}</code>").format(code=db_user.referral_code),
                _("Заработано: {earnings} ₽").format(earnings=db_user.referral_earnings),
            ])
        
        return "\n".join(lines)
    
    async def _get_about_text(self) -> str:
        """Формирование текста о сервисе"""
        lines = [
            _("ℹ️ <b>О сервисе {shop_name}</b>").format(shop_name=self.config.shop_name),
            "",
            _("🔒 <b>Безопасность:</b>"),
            _("• Шифрование военного уровня"),
            _("• Политика отсутствия логов"),
            _("• Защита от утечек DNS"),
            "",
            _("🌍 <b>География:</b>"),
            _("• Серверы в 50+ странах"),
            _("• Высокая скорость подключения"),
            _("• Стабильное соединение"),
            "",
            _("📱 <b>Совместимость:</b>"),
            _("• Windows, macOS, Linux"),
            _("• iOS, Android"),
            _("• Роутеры и Smart TV"),
            "",
            _("🎯 <b>Особенности:</b>"),
            _("• Обход блокировок"),
            _("• Защита в публичных Wi-Fi"),
            _("• Анонимный серфинг"),
        ]
        
        if self.config.about_url:
            lines.extend([
                "",
                _("🔗 Подробнее на нашем сайте")
            ])
        
        return "\n".join(lines)
    
    async def _get_help_text(self) -> str:
        """Формирование текста справки"""
        lines = [
            _("❓ <b>Справка</b>"),
            "",
            _("🚀 <b>Быстрый старт:</b>"),
            _("1. Выберите тариф"),
            _("2. Оплатите подписку"),
            _("3. Получите конфигурацию"),
            _("4. Подключитесь к VPN"),
            "",
            _("💳 <b>Способы оплаты:</b>"),
            _("• Банковские карты"),
            _("• Криптовалюта"),
            _("• Telegram Stars"),
            "",
            _("🔧 <b>Настройка:</b>"),
            _("• Инструкции для всех устройств"),
            _("• Автоматическая конфигурация"),
            _("• Техническая поддержка"),
            "",
            _("📞 <b>Поддержка:</b>"),
            _("• Круглосуточно 24/7"),
            _("• Быстрые ответы"),
            _("• Решение любых вопросов"),
        ]
        
        return "\n".join(lines)
    
    async def _get_settings_text(self, user_data: Dict[str, Any]) -> str:
        """Формирование текста настроек"""
        db_user = user_data['db_user']
        
        lines = [
            _("⚙️ <b>Настройки</b>"),
            "",
            _("🌐 <b>Язык интерфейса:</b>"),
            _("Текущий: {lang}").format(
                lang=_("Русский") if db_user.language_code == 'ru' else db_user.language_code
            ),
            "",
            _("🔔 <b>Уведомления:</b>"),
            _("О платежах: ✅"),
            _("О подписке: ✅"),
            _("Новости сервиса: ✅"),
            "",
            _("🔐 <b>Безопасность:</b>"),
            _("Двухфакторная аутентификация: ❌"),
            _("Автоматическое подключение: ✅"),
        ]
        
        return "\n".join(lines)
