"""
UnveilVPN Shop - Client Bot Main
Точка входа для клиентского бота
"""

import asyncio
import logging
import sys
from pathlib import Path

# Добавляем путь к корню проекта
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

from bot.common.config import init_config
from bot.client_bot.app import ClientBotApplication


async def main():
    """Основная функция запуска клиентского бота"""
    try:
        # Инициализируем конфигурацию
        config = init_config('client')
        
        # Создаем и запускаем приложение
        app = ClientBotApplication(config)
        await app.run()
        
    except KeyboardInterrupt:
        logging.info("Получен сигнал остановки")
    except Exception as e:
        logging.error(f"Критическая ошибка: {e}", exc_info=True)
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
