"""
UnveilVPN Shop - Database Package
Инициализация моделей базы данных и настройка связей
"""

from .base import Base
from .models import (
    VPNUsers,
    Tariffs,
    Referrals,
    Promocodes,
    PromocodeUsage,
    SupportTickets,
    Payments,
    Subscriptions,
    VPNConfigs
)

# Импорт и настройка связей
from .relationships import configure_relationships, get_optimized_query_options

# Настройка связей между моделями
configure_relationships()

# Экспорт всех моделей
__all__ = [
    'Base',
    'VPNUsers',
    'Tariffs',
    'Referrals',
    'Promocodes',
    'PromocodeUsage',
    'SupportTickets',
    'Payments',
    'Subscriptions',
    'VPNConfigs',
    'configure_relationships',
    'get_optimized_query_options'
]