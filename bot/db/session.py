"""
UnveilVPN Shop - Управление сессиями базы данных
"""

import os
from typing import AsyncGenerator
from contextlib import asynccontextmanager
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy.orm import sessionmaker
from sqlalchemy import create_engine

from .base import Base

# Получаем настройки БД из переменных окружения
DB_HOST = os.getenv("DB_HOST", "localhost")
DB_PORT = os.getenv("DB_PORT", "5432")
DB_NAME = os.getenv("DB_NAME", "unveilvpn")
DB_USER = os.getenv("DB_USER", "unveilvpn_user")
DB_PASS = os.getenv("DB_PASS", "secure_password")

# URL для подключения к БД
DATABASE_URL = f"postgresql://{DB_USER}:{DB_PASS}@{DB_HOST}:{DB_PORT}/{DB_NAME}"
ASYNC_DATABASE_URL = f"postgresql+asyncpg://{DB_USER}:{DB_PASS}@{DB_HOST}:{DB_PORT}/{DB_NAME}"

# Создание движков
engine = create_engine(DATABASE_URL, echo=False)
async_engine = create_async_engine(ASYNC_DATABASE_URL, echo=False)

# Создание фабрик сессий
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
AsyncSessionLocal = async_sessionmaker(
    bind=async_engine,
    class_=AsyncSession,
    expire_on_commit=False
)


def get_session():
    """Получение синхронной сессии БД"""
    session = SessionLocal()
    try:
        yield session
    finally:
        session.close()


@asynccontextmanager
async def get_async_session() -> AsyncGenerator[AsyncSession, None]:
    """Получение асинхронной сессии БД"""
    async with AsyncSessionLocal() as session:
        yield session


async def init_db():
    """Инициализация базы данных"""
    from .models import (
        VPNUsers, Tariffs, Referrals, Promocodes, PromocodeUsage, SupportTickets, Payments,
        ticket_priority_enum, ticket_status_enum, discount_type_enum, payment_status_enum, payment_method_enum
    )

    async with async_engine.begin() as conn:
        # Создаем enum типы сначала
        await conn.run_sync(ticket_priority_enum.create, checkfirst=True)
        await conn.run_sync(ticket_status_enum.create, checkfirst=True)
        await conn.run_sync(discount_type_enum.create, checkfirst=True)
        await conn.run_sync(payment_status_enum.create, checkfirst=True)
        await conn.run_sync(payment_method_enum.create, checkfirst=True)

        # Создаем таблицы в правильном порядке (сначала независимые, потом зависимые)
        # 1. Независимые таблицы
        await conn.run_sync(VPNUsers.__table__.create, checkfirst=True)
        await conn.run_sync(Tariffs.__table__.create, checkfirst=True)

        # 2. Таблицы, зависящие от VPNUsers
        await conn.run_sync(Promocodes.__table__.create, checkfirst=True)
        await conn.run_sync(Referrals.__table__.create, checkfirst=True)
        await conn.run_sync(SupportTickets.__table__.create, checkfirst=True)

        # 3. Таблицы, зависящие от нескольких других таблиц
        await conn.run_sync(Payments.__table__.create, checkfirst=True)
        await conn.run_sync(PromocodeUsage.__table__.create, checkfirst=True)


async def close_db():
    """Закрытие соединений с БД"""
    await async_engine.dispose()
