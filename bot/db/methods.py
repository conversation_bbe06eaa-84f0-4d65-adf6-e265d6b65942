import hashlib

from sqlalchemy import insert, select, update, delete

from .models import Payments, VPNUsers, VPNConfigs, Subscriptions
from .session import async_engine

async def create_vpn_profile(tg_id: int):
    """Создание VPN профиля (обратная совместимость)"""
    async with engine.connect() as conn:
        sql_query = select(VPNUsers).where(VPNUsers.telegram_id == tg_id)
        result: VPNUsers = (await conn.execute(sql_query)).fetchone()
        if result != None:
            return
        hash = hashlib.md5(str(tg_id).encode()).hexdigest()
        sql_query = insert(VPNUsers).values(telegram_id=tg_id, vpn_id=hash)
        await conn.execute(sql_query)
        await conn.commit()


async def update_user_remnawave_id(telegram_id: int, remnawave_user_id: str):
    """Обновление Remnawave ID пользователя"""
    async with engine.connect() as conn:
        sql_query = update(VPNUsers).where(
            VPNUsers.telegram_id == telegram_id
        ).values(remnawave_user_id=remnawave_user_id)
        await conn.execute(sql_query)
        await conn.commit()


async def get_user_by_remnawave_id(remnawave_user_id: str):
    """Получение пользователя по Remnawave ID"""
    async with engine.connect() as conn:
        sql_query = select(VPNUsers).where(VPNUsers.remnawave_user_id == remnawave_user_id)
        result = await conn.execute(sql_query)
        return result.fetchone()


async def create_vpn_config(user_id: str, remnawave_config_id: str, device_name: str, protocol: str, config_url: str = None):
    """Создание записи о VPN конфигурации"""
    async with engine.connect() as conn:
        sql_query = insert(VPNConfigs).values(
            user_id=user_id,
            remnawave_config_id=remnawave_config_id,
            device_name=device_name,
            protocol=protocol,
            config_url=config_url
        )
        await conn.execute(sql_query)
        await conn.commit()


async def update_subscription_remnawave_id(subscription_id: str, remnawave_subscription_id: str):
    """Обновление Remnawave ID подписки"""
    async with engine.connect() as conn:
        sql_query = update(Subscriptions).where(
            Subscriptions.id == subscription_id
        ).values(remnawave_subscription_id=remnawave_subscription_id)
        await conn.execute(sql_query)
        await conn.commit()

async def get_remnawave_profile_db(tg_id: int) -> VPNUsers:
    """Получение профиля пользователя (Remnawave)"""
    async with engine.connect() as conn:
        sql_query = select(VPNUsers).where(VPNUsers.telegram_id == tg_id)
        result: VPNUsers = (await conn.execute(sql_query)).fetchone()
    return result


async def get_user_by_telegram_id(telegram_id: int) -> VPNUsers:
    """Получение пользователя по Telegram ID"""
    async with engine.connect() as conn:
        sql_query = select(VPNUsers).where(VPNUsers.telegram_id == telegram_id)
        result: VPNUsers = (await conn.execute(sql_query)).fetchone()
    return result

async def get_remnawave_profile_by_vpn_id(vpn_id: str):
    """Получение профиля пользователя по VPN ID (Remnawave)"""
    async with engine.connect() as conn:
        sql_query = select(VPNUsers).where(VPNUsers.remnawave_user_id == vpn_id)
        result: VPNUsers = (await conn.execute(sql_query)).fetchone()
    return result

async def can_get_test_sub(tg_id: int) -> bool:
    async with engine.connect() as conn:
        sql_query = select(VPNUsers).where(VPNUsers.tg_id == tg_id)
        result: VPNUsers = (await conn.execute(sql_query)).fetchone()
    return result.test

async def update_test_subscription_state(tg_id):
    async with engine.connect() as conn:
        sql_q = update(VPNUsers).where(VPNUsers.tg_id == tg_id).values(test=True)
        await conn.execute(sql_q)
        await conn.commit()

async def add_yookassa_payment(tg_id: int, callback: str, chat_id: int, lang_code: str, payment_id) -> dict:
    # TODO: Адаптировать под новую модель Payments
    return {"status": "success", "payment_id": payment_id}

async def add_cryptomus_payment(tg_id: int, callback: str, chat_id: int, lang_code: str, data) -> dict:
    # TODO: Адаптировать под новую модель Payments
    return {"status": "success", "order_id": data.get('order_id')}

async def get_yookassa_payment(payment_id):
    # TODO: Адаптировать под новую модель Payments
    return None

async def get_cryptomus_payment(order_id):
    # TODO: Адаптировать под новую модель Payments
    return None

async def delete_payment(payment_id):
    # TODO: Адаптировать под новую модель Payments
    pass