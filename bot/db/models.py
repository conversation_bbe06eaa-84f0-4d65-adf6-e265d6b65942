"""
UnveilVPN Shop - Database Models
Современные SQLAlchemy модели с поддержкой PostgreSQL UUID и JSONB
"""

import uuid
from datetime import datetime, timezone
from typing import Any
from decimal import Decimal

from sqlalchemy import (
    Column, String, Boolean, Integer, BigInteger, DateTime,
    Text, Numeric, ForeignKey, Index, CheckConstraint, UniqueConstraint, func, JSON
)
from sqlalchemy.dialects.postgresql import UUID, JSONB, ENUM
from sqlalchemy.orm import validates, relationship
from sqlalchemy.ext.hybrid import hybrid_property

from .base import Base


# Создание ENUM типов
payment_status_enum = ENUM(
    'pending', 'completed', 'failed', 'refunded',
    name='payment_status',
    create_type=False
)

payment_method_enum = ENUM(
    'yookassa', 'cryptomus', 'telegram_stars',
    name='payment_method',
    create_type=False
)

ticket_status_enum = ENUM(
    'open', 'in_progress', 'waiting_user', 'closed',
    name='ticket_status',
    create_type=False
)

ticket_priority_enum = ENUM(
    'low', 'medium', 'high', 'urgent',
    name='ticket_priority',
    create_type=False
)

discount_type_enum = ENUM(
    'percent', 'fixed', 'days',
    name='discount_type',
    create_type=False
)


class VPNUsers(Base):
    """
    Модель пользователей VPN с расширенными возможностями
    """
    __tablename__ = "vpnusers"

    # Основные поля
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    telegram_id = Column(BigInteger, unique=True, nullable=False, index=True)
    username = Column(String(255), nullable=True)
    first_name = Column(String(255), nullable=True)
    last_name = Column(String(255), nullable=True)
    language_code = Column(String(10), default='ru')

    # VPN данные
    vpn_id = Column(String(64), nullable=True)  # Deprecated: старый ID для совместимости
    remnawave_user_id = Column(String(64), nullable=True, index=True)  # UUID пользователя в Remnawave
    is_test_used = Column(Boolean, default=False)
    is_active = Column(Boolean, default=True)
    is_banned = Column(Boolean, default=False)
    ban_reason = Column(Text, nullable=True)

    # Remnawave конфигурации (JSONB)
    remnawave_configs = Column(JSONB, nullable=False, default=list)

    # Реферальная система
    referral_code = Column(String(20), unique=True, nullable=True, index=True)
    referred_by_id = Column(UUID(as_uuid=True), ForeignKey('vpnusers.id'), nullable=True)
    referral_earnings = Column(Numeric(10, 2), default=0)

    # Метаданные пользователя (JSONB)
    user_metadata = Column(JSONB, nullable=False, default=lambda: {
        'created_at': datetime.now(timezone.utc).isoformat(),
        'last_login': None,
        'preferences': {
            'notifications': True,
            'language': 'ru'
        },
        'statistics': {
            'total_purchases': 0,
            'total_spent': 0,
            'referrals_count': 0
        }
    })

    # Временные метки
    created_at = Column(DateTime(timezone=True), default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), default=func.now(), onupdate=func.now())
    last_activity = Column(DateTime(timezone=True), default=func.now())

    # Связи (будут настроены в relationships.py для избежания циклических импортов)
    # referred_by, payments, support_tickets, created_promocodes, promocode_usage

    # Индексы
    __table_args__ = (
        Index('idx_vpnusers_telegram_id', 'telegram_id'),
        Index('idx_vpnusers_referral_code', 'referral_code'),
        Index('idx_vpnusers_remnawave_user_id', 'remnawave_user_id'),
        Index('idx_vpnusers_metadata_gin', 'user_metadata', postgresql_using='gin'),
        Index('idx_vpnusers_configs_gin', 'remnawave_configs', postgresql_using='gin'),
        Index('idx_vpnusers_created_at', 'created_at'),
        CheckConstraint('telegram_id > 0', name='check_telegram_id_positive'),
    )

    @validates('telegram_id')
    def validate_telegram_id(self, key, telegram_id):
        """Валидация Telegram ID"""
        if telegram_id <= 0:
            raise ValueError("Telegram ID должен быть положительным числом")
        return telegram_id

    @hybrid_property
    def full_name(self) -> str:
        """Полное имя пользователя"""
        parts = [self.first_name, self.last_name]
        return ' '.join(filter(None, parts)) or self.username or f"User_{self.telegram_id}"

    def update_metadata(self, key: str, value: Any) -> None:
        """Обновление метаданных пользователя"""
        if self.user_metadata is None:
            self.user_metadata = {}

        # Создаем копию для триггера обновления
        metadata = dict(self.user_metadata)

        # Обновляем значение
        if '.' in key:
            # Поддержка вложенных ключей (например, 'preferences.language')
            keys = key.split('.')
            current = metadata
            for k in keys[:-1]:
                if k not in current:
                    current[k] = {}
                current = current[k]
            current[keys[-1]] = value
        else:
            metadata[key] = value

        # Обновляем время последнего изменения
        metadata['updated_at'] = datetime.now(timezone.utc).isoformat()

        self.user_metadata = metadata

    def get_metadata(self, key: str, default: Any = None) -> Any:
        """Получение значения из метаданных"""
        if self.user_metadata is None:
            return default

        if '.' in key:
            # Поддержка вложенных ключей
            keys = key.split('.')
            current = self.user_metadata
            for k in keys:
                if not isinstance(current, dict) or k not in current:
                    return default
                current = current[k]
            return current

        return self.user_metadata.get(key, default)

    # Связи
    system_logs = relationship("SystemLogs", back_populates="user")
    notification_logs = relationship("NotificationLogs", back_populates="user")

    def __repr__(self) -> str:
        return f"<VPNUser(id={self.id}, tg_id={self.tg_id}, username={self.username})>"


class Tariffs(Base):
    """
    Модель тарифов с динамическими ценами и характеристиками
    """
    __tablename__ = "tariffs"

    # Основные поля
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    duration_days = Column(Integer, nullable=False)

    # Цены в разных валютах (JSONB)
    prices = Column(JSONB, nullable=False, default=lambda: {
        'rub': 0,      # Рубли (YooKassa)
        'usd': 0,      # Доллары (Cryptomus)
        'stars': 0     # Telegram Stars
    })

    # Характеристики тарифа (JSONB)
    features = Column(JSONB, nullable=False, default=lambda: {
        'traffic_limit': None,     # Лимит трафика в GB (None = безлимит)
        'speed_limit': None,       # Лимит скорости в Mbps (None = безлимит)
        'devices_count': 5,        # Количество устройств
        'protocols': ['vless'],    # Поддерживаемые протоколы
        'locations': ['ru'],       # Доступные локации
        'support_level': 'basic'   # Уровень поддержки
    })

    # Настройки тарифа
    is_active = Column(Boolean, default=True)
    is_popular = Column(Boolean, default=False)
    is_test_available = Column(Boolean, default=True)
    sort_order = Column(Integer, default=0)

    # Скидки и акции
    discount_percent = Column(Integer, default=0)  # Скидка в процентах
    is_promo = Column(Boolean, default=False)      # Акционный тариф
    promo_text = Column(String(500), nullable=True) # Текст акции

    # Версионирование
    version = Column(Integer, default=1)

    # Временные метки
    created_at = Column(DateTime(timezone=True), default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), default=func.now(), onupdate=func.now())

    # Связи (настраиваются в relationships.py)
    # payments

    # Индексы
    __table_args__ = (
        Index('idx_tariffs_active', 'is_active'),
        Index('idx_tariffs_popular', 'is_popular'),
        Index('idx_tariffs_sort_order', 'sort_order'),
        Index('idx_tariffs_prices_gin', 'prices', postgresql_using='gin'),
        Index('idx_tariffs_features_gin', 'features', postgresql_using='gin'),
        CheckConstraint('duration_days > 0', name='check_duration_positive'),
        CheckConstraint('discount_percent >= 0 AND discount_percent <= 100', name='check_discount_range'),
    )

    @validates('prices')
    def validate_prices(self, key, prices):
        """Валидация цен"""
        if not isinstance(prices, dict):
            raise ValueError("Цены должны быть в формате словаря")

        required_currencies = ['rub', 'usd', 'stars']
        for currency in required_currencies:
            if currency not in prices:
                prices[currency] = 0
            elif not isinstance(prices[currency], (int, float)) or prices[currency] < 0:
                raise ValueError(f"Цена в {currency} должна быть неотрицательным числом")

        return prices

    @validates('features')
    def validate_features(self, key, features):
        """Валидация характеристик"""
        if not isinstance(features, dict):
            raise ValueError("Характеристики должны быть в формате словаря")

        # Проверка обязательных полей
        required_fields = ['devices_count', 'protocols']
        for field in required_fields:
            if field not in features:
                if field == 'devices_count':
                    features[field] = 1
                elif field == 'protocols':
                    features[field] = ['vless']

        # Валидация значений
        if 'devices_count' in features and features['devices_count'] < 1:
            raise ValueError("Количество устройств должно быть больше 0")

        return features

    def get_price(self, currency: str) -> float:
        """Получение цены в указанной валюте"""
        if self.prices is None:
            return 0.0

        price = self.prices.get(currency, 0)

        # Применение скидки
        if self.discount_percent > 0:
            price = price * (1 - self.discount_percent / 100)

        return round(price, 2)

    def get_feature(self, feature_name: str, default: Any = None) -> Any:
        """Получение характеристики тарифа"""
        if self.features is None:
            return default
        return self.features.get(feature_name, default)

    def update_price(self, currency: str, price: float) -> None:
        """Обновление цены в указанной валюте"""
        if self.prices is None:
            self.prices = {}

        # Создаем копию для триггера обновления
        prices = dict(self.prices)
        prices[currency] = price
        self.prices = prices

        # Увеличиваем версию
        self.version += 1

    @hybrid_property
    def is_unlimited_traffic(self) -> bool:
        """Проверка безлимитного трафика"""
        return self.get_feature('traffic_limit') is None

    @hybrid_property
    def is_unlimited_speed(self) -> bool:
        """Проверка безлимитной скорости"""
        return self.get_feature('speed_limit') is None

    def __repr__(self) -> str:
        return f"<Tariff(id={self.id}, name={self.name}, duration={self.duration_days} days)>"

    def to_dict(self) -> dict:
        """Сериализация объекта в словарь для API"""
        # Формируем список "features" для фронтенда
        features_list = []
        if self.features:
            if self.features.get('devices_count'):
                features_list.append(f"{self.features.get('devices_count')} устройств одновременно")
            if self.features.get('speed_limit'):
                features_list.append(f"Скорость до {self.features.get('speed_limit')} Мбит/с")
            else:
                features_list.append("Безлимитная скорость")
            if self.features.get('traffic_limit'):
                features_list.append(f"Трафик {self.features.get('traffic_limit')} ГБ/мес")
            else:
                features_list.append("Безлимитный трафик")
            if self.features.get('support_level') == 'premium':
                features_list.append("Приоритетная поддержка")
            else:
                features_list.append("Стандартная поддержка")

        # Формируем "characteristics" для фронтенда
        speed_limit_value = self.features.get('speed_limit')
        speed_display = f"{speed_limit_value} Мбит/с" if speed_limit_value is not None else "Безлимитная"

        characteristics = {
            "speed": speed_display,
            "devices": f"до {self.features.get('devices_count', 10)}",
            "locations": f"{len(self.features.get('locations', ['ru']))}+",
            "support": "24/7"
        }

        return {
            "id": str(self.id),
            "name": self.name,
            "description": self.description,
            "duration_days": self.duration_days,
            "prices": {
                'rub': self.prices.get('rub', 0),
                'usd': self.prices.get('usd', 0),
                'stars': self.prices.get('stars', 0),
            },
            "features": features_list,
            "popular": self.is_popular,
            "characteristics": characteristics,
        }


class Referrals(Base):
    """
    Модель многоуровневой реферальной системы
    """
    __tablename__ = "referrals"

    # Основные поля
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    referrer_id = Column(UUID(as_uuid=True), ForeignKey('vpnusers.id'), nullable=False)
    referred_id = Column(UUID(as_uuid=True), ForeignKey('vpnusers.id'), nullable=False)

    # Уровень реферальной системы (1, 2, 3)
    level = Column(Integer, nullable=False)

    # Финансовые данные
    bonus_earned = Column(Numeric(10, 2), default=0)
    bonus_paid = Column(Numeric(10, 2), default=0)
    bonus_pending = Column(Numeric(10, 2), default=0)

    # Статистика
    total_purchases = Column(Integer, default=0)
    total_amount = Column(Numeric(10, 2), default=0)

    # Статус
    is_active = Column(Boolean, default=True)
    is_verified = Column(Boolean, default=False)  # Проверка на мошенничество

    # Временные метки
    created_at = Column(DateTime(timezone=True), default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), default=func.now(), onupdate=func.now())
    last_earning_at = Column(DateTime(timezone=True), nullable=True)

    # Связи (настраиваются в relationships.py)
    # referrer, referred

    # Индексы
    __table_args__ = (
        Index('idx_referrals_referrer', 'referrer_id'),
        Index('idx_referrals_referred', 'referred_id'),
        Index('idx_referrals_level', 'level'),
        Index('idx_referrals_active', 'is_active'),
        Index('idx_referrals_created_at', 'created_at'),
        CheckConstraint('level >= 1 AND level <= 3', name='check_referral_level'),
        CheckConstraint('bonus_earned >= 0', name='check_bonus_earned_positive'),
        CheckConstraint('referrer_id != referred_id', name='check_no_self_referral'),
    )

    @validates('level')
    def validate_level(self, key, level):
        """Валидация уровня реферальной системы"""
        if level not in [1, 2, 3]:
            raise ValueError("Уровень реферальной системы должен быть 1, 2 или 3")
        return level

    def calculate_bonus_percent(self) -> float:
        """Расчет процента бонуса в зависимости от уровня"""
        bonus_rates = {
            1: 10.0,  # 10% для первого уровня
            2: 5.0,   # 5% для второго уровня
            3: 2.0    # 2% для третьего уровня
        }
        return bonus_rates.get(self.level, 0.0)

    def add_purchase(self, amount: Decimal) -> Decimal:
        """Добавление покупки и расчет бонуса"""
        if not self.is_active:
            return Decimal('0')

        bonus_percent = self.calculate_bonus_percent()
        bonus_amount = amount * Decimal(str(bonus_percent / 100))

        # Обновляем статистику
        self.total_purchases += 1
        self.total_amount += amount
        self.bonus_earned += bonus_amount
        self.bonus_pending += bonus_amount
        self.last_earning_at = func.now()

        return bonus_amount

    def pay_bonus(self, amount: Decimal) -> bool:
        """Выплата бонуса"""
        if amount > self.bonus_pending:
            return False

        self.bonus_paid += amount
        self.bonus_pending -= amount
        return True

    @hybrid_property
    def conversion_rate(self) -> float:
        """Коэффициент конверсии реферала"""
        if self.total_purchases == 0:
            return 0.0
        return float(self.total_amount / self.total_purchases)

    def __repr__(self) -> str:
        return f"<Referral(referrer={self.referrer_id}, referred={self.referred_id}, level={self.level})>"


class Promocodes(Base):
    """
    Модель промокодов с гибкими типами скидок
    """
    __tablename__ = "promocodes"

    # Основные поля
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    code = Column(String(50), unique=True, nullable=False, index=True)
    name = Column(String(255), nullable=True)
    description = Column(Text, nullable=True)

    # Тип скидки
    discount_type = Column(discount_type_enum, nullable=False)
    discount_value = Column(Numeric(10, 2), nullable=False)

    # Ограничения использования
    usage_limit = Column(Integer, nullable=True)  # Общий лимит использований
    usage_limit_per_user = Column(Integer, default=1)  # Лимит на пользователя
    usage_count = Column(Integer, default=0)  # Текущее количество использований

    # Ограничения по времени
    starts_at = Column(DateTime(timezone=True), nullable=True)
    expires_at = Column(DateTime(timezone=True), nullable=True)

    # Ограничения по тарифам
    applicable_tariffs = Column(JSONB, nullable=True)  # Список ID тарифов
    min_purchase_amount = Column(Numeric(10, 2), nullable=True)

    # Статус
    is_active = Column(Boolean, default=True)
    is_public = Column(Boolean, default=False)  # Публичный промокод

    # Создатель промокода
    created_by_id = Column(UUID(as_uuid=True), ForeignKey('vpnusers.id'), nullable=True)

    # Временные метки
    created_at = Column(DateTime(timezone=True), default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), default=func.now(), onupdate=func.now())

    # Связи (настраиваются в relationships.py)
    # created_by, usage_history

    # Индексы
    __table_args__ = (
        Index('idx_promocodes_code', 'code'),
        Index('idx_promocodes_active', 'is_active'),
        Index('idx_promocodes_expires_at', 'expires_at'),
        Index('idx_promocodes_type', 'discount_type'),
        CheckConstraint('discount_value > 0', name='check_discount_value_positive'),
        CheckConstraint('usage_limit IS NULL OR usage_limit > 0', name='check_usage_limit_positive'),
        CheckConstraint('usage_limit_per_user > 0', name='check_usage_limit_per_user_positive'),
    )

    @validates('code')
    def validate_code(self, key, code):
        """Валидация промокода"""
        if not code or len(code.strip()) < 3:
            raise ValueError("Промокод должен содержать минимум 3 символа")

        # Приводим к верхнему регистру и убираем пробелы
        return code.strip().upper()

    @validates('discount_value')
    def validate_discount_value(self, key, discount_value):
        """Валидация значения скидки"""
        if discount_value <= 0:
            raise ValueError("Значение скидки должно быть положительным")

        # Для процентной скидки проверяем максимум 100%
        if hasattr(self, 'discount_type') and self.discount_type == 'percent' and discount_value > 100:
            raise ValueError("Процентная скидка не может быть больше 100%")

        return discount_value

    def is_valid(self, user_id: uuid.UUID = None, tariff_id: uuid.UUID = None,
                 purchase_amount: Decimal = None) -> tuple[bool, str]:
        """Проверка валидности промокода"""

        # Проверка активности
        if not self.is_active:
            return False, "Промокод неактивен"

        # Проверка времени действия
        now = datetime.now(timezone.utc)
        if self.starts_at and now < self.starts_at:
            return False, "Промокод еще не активен"

        if self.expires_at and now > self.expires_at:
            return False, "Промокод истек"

        # Проверка лимита использований
        if self.usage_limit and self.usage_count >= self.usage_limit:
            return False, "Промокод исчерпан"

        # Проверка минимальной суммы покупки
        if self.min_purchase_amount and purchase_amount and purchase_amount < self.min_purchase_amount:
            return False, f"Минимальная сумма покупки: {self.min_purchase_amount}"

        # Проверка применимости к тарифу
        if self.applicable_tariffs and tariff_id:
            if str(tariff_id) not in self.applicable_tariffs:
                return False, "Промокод не применим к данному тарифу"

        return True, "Промокод валиден"

    def calculate_discount(self, original_amount: Decimal) -> tuple[Decimal, Decimal]:
        """Расчет скидки"""
        if self.discount_type == 'percent':
            discount_amount = original_amount * (Decimal(str(self.discount_value)) / Decimal('100'))
        elif self.discount_type == 'fixed':
            discount_amount = min(Decimal(str(self.discount_value)), original_amount)
        elif self.discount_type == 'days':
            # Для скидки в днях возвращаем количество дней
            return Decimal('0'), Decimal(str(self.discount_value))
        else:
            discount_amount = Decimal('0')

        final_amount = max(Decimal('0'), original_amount - discount_amount)
        return discount_amount, final_amount

    def use_promocode(self, user_id: uuid.UUID) -> bool:
        """Использование промокода"""
        is_valid, message = self.is_valid(user_id)
        if not is_valid:
            return False

        self.usage_count += 1
        return True

    @hybrid_property
    def is_expired(self) -> bool:
        """Проверка истечения промокода"""
        if not self.expires_at:
            return False
        return datetime.now(timezone.utc) > self.expires_at

    @hybrid_property
    def usage_percentage(self) -> float:
        """Процент использования промокода"""
        if not self.usage_limit:
            return 0.0
        return (self.usage_count / self.usage_limit) * 100

    def __repr__(self) -> str:
        return f"<Promocode(code={self.code}, type={self.discount_type}, value={self.discount_value})>"


class PromocodeUsage(Base):
    """
    Модель истории использования промокодов
    """
    __tablename__ = "promocode_usage"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    promocode_id = Column(UUID(as_uuid=True), ForeignKey('promocodes.id'), nullable=False)
    user_id = Column(UUID(as_uuid=True), ForeignKey('vpnusers.id'), nullable=False)
    payment_id = Column(UUID(as_uuid=True), ForeignKey('payments.id'), nullable=True)

    discount_amount = Column(Numeric(10, 2), nullable=False)
    original_amount = Column(Numeric(10, 2), nullable=False)
    final_amount = Column(Numeric(10, 2), nullable=False)

    used_at = Column(DateTime(timezone=True), default=func.now(), nullable=False)

    # Связи (настраиваются в relationships.py)
    # promocode, user, payment

    __table_args__ = (
        Index('idx_promocode_usage_promocode', 'promocode_id'),
        Index('idx_promocode_usage_user', 'user_id'),
        Index('idx_promocode_usage_used_at', 'used_at'),
    )


class Subscriptions(Base):
    """
    Модель подписок пользователей
    """
    __tablename__ = "subscriptions"

    # Основные поля
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey('vpnusers.id'), nullable=False)
    tariff_id = Column(UUID(as_uuid=True), ForeignKey('tariffs.id'), nullable=False)

    # Даты подписки
    start_date = Column(DateTime(timezone=True), default=func.now(), nullable=False)
    end_date = Column(DateTime(timezone=True), nullable=False)

    # Статус
    is_active = Column(Boolean, default=True, nullable=False)
    auto_renewal = Column(Boolean, default=False, nullable=False)

    # Remnawave интеграция
    remnawave_subscription_id = Column(String(64), nullable=True, index=True)  # ID подписки в Remnawave
    traffic_limit_gb = Column(Integer, nullable=True)  # Лимит трафика в ГБ
    traffic_used_gb = Column(Numeric(10, 2), default=0)  # Использованный трафик в ГБ

    # Метаданные
    created_at = Column(DateTime(timezone=True), default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), default=func.now(), onupdate=func.now(), nullable=False)

    # Связи (настраиваются в relationships.py)
    # user, tariff

    __table_args__ = (
        Index('idx_subscriptions_user', 'user_id'),
        Index('idx_subscriptions_tariff', 'tariff_id'),
        Index('idx_subscriptions_active', 'is_active'),
        Index('idx_subscriptions_end_date', 'end_date'),
        Index('idx_subscriptions_remnawave_id', 'remnawave_subscription_id'),
    )

    @property
    def days_left(self) -> int:
        """Количество дней до окончания подписки"""
        if not self.is_active:
            return 0

        days_left = (self.end_date - datetime.utcnow()).days
        return max(0, days_left)

    @property
    def is_expired(self) -> bool:
        """Проверка истечения подписки"""
        return datetime.utcnow() > self.end_date

    @property
    def traffic_usage_percent(self) -> float:
        """Процент использования трафика"""
        if not self.traffic_limit_gb:
            return 0.0
        return min(100.0, (float(self.traffic_used_gb) / self.traffic_limit_gb) * 100)

    @property
    def traffic_remaining_gb(self) -> float:
        """Оставшийся трафик в ГБ"""
        if not self.traffic_limit_gb:
            return float('inf')
        return max(0.0, self.traffic_limit_gb - float(self.traffic_used_gb))

    def is_traffic_exceeded(self) -> bool:
        """Проверка превышения лимита трафика"""
        if not self.traffic_limit_gb:
            return False
        return float(self.traffic_used_gb) >= self.traffic_limit_gb


class VPNConfigs(Base):
    """
    Модель конфигураций VPN для Remnawave
    """
    __tablename__ = "vpn_configs"

    # Основные поля
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey('vpnusers.id'), nullable=False)
    remnawave_config_id = Column(String(64), nullable=False, index=True)

    # Конфигурация
    device_name = Column(String(255), nullable=False)
    protocol = Column(String(50), nullable=False)  # vless, vmess, trojan, shadowsocks
    config_data = Column(Text, nullable=True)  # Данные конфигурации
    config_url = Column(String(500), nullable=True)  # URL конфигурации

    # Статус
    is_active = Column(Boolean, default=True, nullable=False)

    # Временные метки
    created_at = Column(DateTime(timezone=True), default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), default=func.now(), onupdate=func.now(), nullable=False)

    # Связи (настраиваются в relationships.py)
    # user

    # Индексы
    __table_args__ = (
        Index('idx_vpn_configs_user', 'user_id'),
        Index('idx_vpn_configs_remnawave_id', 'remnawave_config_id'),
        Index('idx_vpn_configs_protocol', 'protocol'),
        Index('idx_vpn_configs_active', 'is_active'),
        UniqueConstraint('user_id', 'device_name', name='uq_user_device_name'),
    )


class SupportTickets(Base):
    """
    Модель тикетов системы поддержки
    """
    __tablename__ = "support_tickets"

    # Основные поля
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    ticket_number = Column(String(20), unique=True, nullable=False, index=True)
    user_id = Column(UUID(as_uuid=True), ForeignKey('vpnusers.id'), nullable=False)

    # Содержание тикета
    subject = Column(String(500), nullable=False)
    category = Column(String(100), nullable=False)
    priority = Column(ticket_priority_enum, default='medium')
    status = Column(ticket_status_enum, default='open')

    # Сообщения в формате JSONB
    messages = Column(JSONB, nullable=False, default=list)

    # Метаданные тикета
    ticket_metadata = Column(JSONB, nullable=False, default=lambda: {
        'user_agent': None,
        'ip_address': None,
        'attachments': [],
        'tags': [],
        'internal_notes': []
    })

    # Назначение
    assigned_to_id = Column(UUID(as_uuid=True), ForeignKey('vpnusers.id'), nullable=True)

    # Временные метки
    created_at = Column(DateTime(timezone=True), default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), default=func.now(), onupdate=func.now())
    closed_at = Column(DateTime(timezone=True), nullable=True)

    # Связи (настраиваются в relationships.py)
    # user, assigned_to

    # Индексы
    __table_args__ = (
        Index('idx_support_tickets_user', 'user_id'),
        Index('idx_support_tickets_status', 'status'),
        Index('idx_support_tickets_priority', 'priority'),
        Index('idx_support_tickets_category', 'category'),
        Index('idx_support_tickets_created_at', 'created_at'),
        Index('idx_support_tickets_messages_gin', 'messages', postgresql_using='gin'),
    )

    def add_message(self, content: str, author_id: uuid.UUID, is_internal: bool = False) -> None:
        """Добавление сообщения в тикет"""
        message = {
            'id': str(uuid.uuid4()),
            'content': content,
            'author_id': str(author_id),
            'is_internal': is_internal,
            'created_at': datetime.now(timezone.utc).isoformat(),
            'attachments': []
        }

        # Создаем копию для триггера обновления
        messages = list(self.messages) if self.messages else []
        messages.append(message)
        self.messages = messages

    def close_ticket(self, closed_by_id: uuid.UUID, reason: str = None) -> None:
        """Закрытие тикета"""
        self.status = 'closed'
        self.closed_at = func.now()

        if reason:
            self.add_message(f"Тикет закрыт. Причина: {reason}", closed_by_id, is_internal=True)

    @hybrid_property
    def is_overdue(self) -> bool:
        """Проверка просроченности тикета"""
        if self.status == 'closed':
            return False

        # SLA в часах в зависимости от приоритета
        sla_hours = {
            'urgent': 2,
            'high': 8,
            'medium': 24,
            'low': 72
        }

        hours_passed = (datetime.now(timezone.utc) - self.created_at).total_seconds() / 3600
        return hours_passed > sla_hours.get(self.priority, 24)

    def __repr__(self) -> str:
        return f"<SupportTicket(number={self.ticket_number}, status={self.status})>"


class Payments(Base):
    """
    Единая модель платежей для всех платежных систем
    """
    __tablename__ = "payments"

    # Основные поля
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey('vpnusers.id'), nullable=False)
    tariff_id = Column(UUID(as_uuid=True), ForeignKey('tariffs.id'), nullable=False)

    # Платежная информация
    payment_method = Column(payment_method_enum, nullable=False)
    status = Column(payment_status_enum, default='pending')

    # Суммы
    original_amount = Column(Numeric(10, 2), nullable=False)
    discount_amount = Column(Numeric(10, 2), default=0)
    final_amount = Column(Numeric(10, 2), nullable=False)
    currency = Column(String(10), nullable=False)

    # Внешние идентификаторы платежных систем
    external_payment_id = Column(String(255), nullable=True)  # ID в платежной системе
    order_id = Column(String(255), nullable=True)  # Внутренний ID заказа

    # Метаданные платежа (JSONB)
    payment_metadata = Column(JSONB, nullable=False, default=lambda: {
        'ip_address': None,
        'user_agent': None,
        'payment_details': {},
        'webhook_data': {},
        'refund_info': {}
    })

    # Промокод
    promocode_id = Column(UUID(as_uuid=True), ForeignKey('promocodes.id'), nullable=True)

    # Временные метки
    created_at = Column(DateTime(timezone=True), default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), default=func.now(), onupdate=func.now())
    paid_at = Column(DateTime(timezone=True), nullable=True)
    expires_at = Column(DateTime(timezone=True), nullable=True)

    # Связи (настраиваются в relationships.py)
    # user, tariff, promocode, promocode_usage

    # Индексы
    __table_args__ = (
        Index('idx_payments_user', 'user_id'),
        Index('idx_payments_status', 'status'),
        Index('idx_payments_method', 'payment_method'),
        Index('idx_payments_external_id', 'external_payment_id'),
        Index('idx_payments_created_at', 'created_at'),
        Index('idx_payments_metadata_gin', 'payment_metadata', postgresql_using='gin'),
    )

    def update_status(self, new_status: str, metadata: dict = None) -> None:
        """Обновление статуса платежа"""
        old_status = self.status
        self.status = new_status

        if new_status == 'completed' and not self.paid_at:
            self.paid_at = func.now()

        # Обновляем метаданные
        if metadata:
            current_metadata = dict(self.payment_metadata) if self.payment_metadata else {}
            current_metadata.update(metadata)
            current_metadata['status_history'] = current_metadata.get('status_history', [])
            current_metadata['status_history'].append({
                'from_status': old_status,
                'to_status': new_status,
                'changed_at': datetime.now(timezone.utc).isoformat(),
                'metadata': metadata
            })
            self.payment_metadata = current_metadata

    def add_webhook_data(self, webhook_data: dict) -> None:
        """Добавление данных webhook"""
        current_metadata = dict(self.payment_metadata) if self.payment_metadata else {}
        current_metadata['webhook_data'] = webhook_data
        current_metadata['last_webhook_at'] = datetime.now(timezone.utc).isoformat()
        self.payment_metadata = current_metadata

    @hybrid_property
    def is_expired(self) -> bool:
        """Проверка истечения платежа"""
        if not self.expires_at or self.status == 'completed':
            return False
        return datetime.now(timezone.utc) > self.expires_at

    @hybrid_property
    def discount_percentage(self) -> float:
        """Процент скидки"""
        if self.original_amount == 0:
            return 0.0
        return float((self.discount_amount / self.original_amount) * 100)

    def __repr__(self) -> str:
        return f"<Payment(id={self.id}, method={self.payment_method}, status={self.status}, amount={self.final_amount})>"


class SystemLogs(Base):
    """
    Модель системных логов
    """
    __tablename__ = "system_logs"

    id = Column(Integer, primary_key=True, autoincrement=True)
    level = Column(String(20), nullable=False, index=True)  # INFO, WARNING, ERROR, CRITICAL
    message = Column(Text, nullable=False)
    source = Column(String(100), nullable=True, index=True)  # Источник лога
    created_at = Column(DateTime(timezone=True), nullable=False, default=func.now(), index=True)

    # Дополнительные поля для контекста
    user_id = Column(UUID(as_uuid=True), ForeignKey('vpnusers.id'), nullable=True, index=True)
    request_id = Column(String(50), nullable=True, index=True)  # Для трассировки запросов
    ip_address = Column(String(45), nullable=True)  # IPv4/IPv6
    user_agent = Column(Text, nullable=True)

    # Связи
    user = relationship("VPNUsers", back_populates="system_logs")

    __table_args__ = (
        Index('idx_system_logs_level_created', 'level', 'created_at'),
        Index('idx_system_logs_source_created', 'source', 'created_at'),
    )

    def __repr__(self) -> str:
        return f"<SystemLog(id={self.id}, level={self.level}, source={self.source})>"


class SystemBackups(Base):
    """
    Модель системных резервных копий
    """
    __tablename__ = "system_backups"

    id = Column(Integer, primary_key=True, autoincrement=True)
    backup_type = Column(String(50), nullable=False, index=True)  # database, files, full
    file_path = Column(String(500), nullable=False)  # Путь к файлу резервной копии
    file_size = Column(BigInteger, nullable=True)  # Размер файла в байтах
    checksum = Column(String(64), nullable=True)  # SHA-256 хеш для проверки целостности

    # Статус и метаданные
    status = Column(String(20), nullable=False, default='created', index=True)  # created, verified, corrupted, deleted
    compression = Column(String(20), nullable=True)  # gzip, bzip2, xz
    encryption = Column(Boolean, nullable=False, default=False)  # Зашифрован ли бэкап

    # Временные метки
    created_at = Column(DateTime(timezone=True), nullable=False, default=func.now(), index=True)
    verified_at = Column(DateTime(timezone=True), nullable=True)  # Время последней проверки
    expires_at = Column(DateTime(timezone=True), nullable=True, index=True)  # Время истечения

    # Дополнительная информация
    description = Column(Text, nullable=True)  # Описание резервной копии
    backup_metadata = Column(JSON, nullable=True)  # Дополнительные метаданные
    created_by = Column(UUID(as_uuid=True), ForeignKey('vpnusers.id'), nullable=True, index=True)  # Кто создал

    # Связи
    creator = relationship("VPNUsers", foreign_keys=[created_by])

    __table_args__ = (
        Index('idx_system_backups_type_created', 'backup_type', 'created_at'),
        Index('idx_system_backups_status_expires', 'status', 'expires_at'),
    )

    @property
    def is_expired(self) -> bool:
        """Проверка истечения срока хранения"""
        if not self.expires_at:
            return False
        return datetime.utcnow() > self.expires_at

    @property
    def file_size_human(self) -> str:
        """Размер файла в человекочитаемом формате"""
        if not self.file_size:
            return "Неизвестно"

        for unit in ['Б', 'КБ', 'МБ', 'ГБ', 'ТБ']:
            if self.file_size < 1024.0:
                return f"{self.file_size:.1f} {unit}"
            self.file_size /= 1024.0
        return f"{self.file_size:.1f} ПБ"

    def __repr__(self) -> str:
        return f"<SystemBackup(id={self.id}, type={self.backup_type}, status={self.status})>"


class NotificationLogs(Base):
    """
    Модель логов уведомлений
    """
    __tablename__ = "notification_logs"

    id = Column(Integer, primary_key=True, autoincrement=True)
    notification_type = Column(String(50), nullable=False, index=True)  # email, telegram, sms, push
    recipient = Column(String(255), nullable=False, index=True)  # email, telegram_id, phone, etc.
    subject = Column(String(500), nullable=True)  # Тема уведомления
    message = Column(Text, nullable=False)  # Текст сообщения

    # Статус доставки
    status = Column(String(20), nullable=False, default='pending', index=True)  # pending, sent, delivered, failed
    error_message = Column(Text, nullable=True)  # Сообщение об ошибке
    attempts = Column(Integer, nullable=False, default=0)  # Количество попыток отправки
    max_attempts = Column(Integer, nullable=False, default=3)  # Максимальное количество попыток

    # Временные метки
    created_at = Column(DateTime(timezone=True), nullable=False, default=func.now(), index=True)
    sent_at = Column(DateTime(timezone=True), nullable=True)  # Время отправки
    delivered_at = Column(DateTime(timezone=True), nullable=True)  # Время доставки
    next_attempt_at = Column(DateTime(timezone=True), nullable=True, index=True)  # Время следующей попытки

    # Связи и метаданные
    user_id = Column(UUID(as_uuid=True), ForeignKey('vpnusers.id'), nullable=True, index=True)
    template_id = Column(String(100), nullable=True, index=True)  # ID шаблона уведомления
    notification_metadata = Column(JSON, nullable=True)  # Дополнительные данные

    # Связи
    user = relationship("VPNUsers", back_populates="notification_logs")

    __table_args__ = (
        Index('idx_notification_logs_type_status', 'notification_type', 'status'),
        Index('idx_notification_logs_recipient_created', 'recipient', 'created_at'),
        Index('idx_notification_logs_next_attempt', 'next_attempt_at'),
    )

    @property
    def is_failed(self) -> bool:
        """Проверка неудачной доставки"""
        return self.status == 'failed' or self.attempts >= self.max_attempts

    @property
    def can_retry(self) -> bool:
        """Можно ли повторить отправку"""
        return self.status in ['pending', 'failed'] and self.attempts < self.max_attempts

    def __repr__(self) -> str:
        return f"<NotificationLog(id={self.id}, type={self.notification_type}, status={self.status})>"


class Refunds(Base):
    """
    Модель возвратов платежей
    """
    __tablename__ = "refunds"

    # Основные поля
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    payment_id = Column(UUID(as_uuid=True), ForeignKey('payments.id'), nullable=False)

    # Информация о возврате
    amount = Column(Numeric(10, 2), nullable=False)
    currency = Column(String(10), nullable=False)
    reason = Column(Text, nullable=True)

    # Администратор, выполнивший возврат
    admin_id = Column(UUID(as_uuid=True), ForeignKey('vpnusers.id'), nullable=True)

    # Флаг автоматического возврата
    auto_refund = Column(Boolean, default=False)

    # Метаданные возврата (JSONB)
    refund_metadata = Column(JSONB, nullable=False, default=lambda: {
        'original_amount': 0,
        'payment_method': None,
        'processed_at': None,
        'external_refund_id': None
    })

    # Временные метки
    created_at = Column(DateTime(timezone=True), default=func.now(), nullable=False)
    processed_at = Column(DateTime(timezone=True), default=func.now(), nullable=False)

    # Связи (настраиваются в relationships.py)
    # payment, admin

    # Индексы
    __table_args__ = (
        Index('idx_refunds_payment_id', 'payment_id'),
        Index('idx_refunds_admin_id', 'admin_id'),
        Index('idx_refunds_created_at', 'created_at'),
        Index('idx_refunds_auto_refund', 'auto_refund'),
        CheckConstraint('amount > 0', name='check_refund_amount_positive'),
    )

    @validates('amount')
    def validate_amount(self, key, amount):
        """Валидация суммы возврата"""
        if amount is not None and amount <= 0:
            raise ValueError("Сумма возврата должна быть положительной")
        return amount

    @validates('currency')
    def validate_currency(self, key, currency):
        """Валидация валюты"""
        if currency and currency not in ['RUB', 'USD', 'STARS']:
            raise ValueError(f"Неподдерживаемая валюта: {currency}")
        return currency

    @hybrid_property
    def is_processed(self) -> bool:
        """Проверка, обработан ли возврат"""
        return self.processed_at is not None

    def __repr__(self) -> str:
        return f"<Refund(id={self.id}, payment_id={self.payment_id}, amount={self.amount})>"