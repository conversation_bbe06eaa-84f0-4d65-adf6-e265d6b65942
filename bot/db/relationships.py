"""
UnveilVPN Shop - Database Relationships Configuration
Оптимизированные связи между моделями с настройкой lazy loading
"""

from sqlalchemy.orm import relationship, backref
from sqlalchemy import and_, or_


def configure_relationships():
    """
    Конфигурация оптимизированных связей между моделями
    """
    
    # Импорт моделей (избегаем циклических импортов)
    from .models import VPNUsers, Tariffs, Referrals, Promocodes, SupportTickets, Payments, PromocodeUsage
    
    # =============================================================================
    # VPNUsers relationships
    # =============================================================================
    
    # Реферальные связи с оптимизацией
    VPNUsers.referred_by = relationship(
        "VPNUsers",
        remote_side=[VPNUsers.id],
        backref=backref(
            "direct_referrals",
            lazy="dynamic",  # Для больших списков рефералов
            cascade="all, delete-orphan"
        ),
        lazy="select"  # Загружаем только при обращении
    )
    
    # Платежи пользователя
    VPNUsers.payments = relationship(
        "Payments",
        back_populates="user",
        lazy="dynamic",  # Для пагинации платежей
        order_by="Payments.created_at.desc()",
        cascade="all, delete-orphan"
    )
    
    # Тикеты поддержки
    VPNUsers.support_tickets = relationship(
        "SupportTickets",
        foreign_keys=[SupportTickets.user_id],
        back_populates="user",
        lazy="dynamic",
        order_by="SupportTickets.created_at.desc()",
        cascade="all, delete-orphan"
    )
    
    # Созданные промокоды (для админов)
    VPNUsers.created_promocodes = relationship(
        "Promocodes",
        back_populates="created_by",
        lazy="dynamic",
        cascade="all, delete-orphan"
    )
    
    # История использования промокодов
    VPNUsers.promocode_usage = relationship(
        "PromocodeUsage",
        back_populates="user",
        lazy="dynamic",
        order_by="PromocodeUsage.used_at.desc()"
    )
    
    # =============================================================================
    # Tariffs relationships
    # =============================================================================
    
    # Платежи по тарифу
    Tariffs.payments = relationship(
        "Payments",
        back_populates="tariff",
        lazy="dynamic",
        order_by="Payments.created_at.desc()"
    )
    
    # =============================================================================
    # Referrals relationships
    # =============================================================================
    
    # Реферер
    Referrals.referrer = relationship(
        "VPNUsers",
        foreign_keys=[Referrals.referrer_id],
        backref=backref(
            "referrer_relations",
            lazy="dynamic",
            cascade="all, delete-orphan"
        ),
        lazy="select"
    )
    
    # Реферал
    Referrals.referred = relationship(
        "VPNUsers",
        foreign_keys=[Referrals.referred_id],
        backref=backref(
            "referred_relations",
            lazy="select",
            uselist=False  # Один пользователь может быть рефералом только один раз
        ),
        lazy="select"
    )
    
    # =============================================================================
    # Promocodes relationships
    # =============================================================================
    
    # Создатель промокода
    Promocodes.created_by = relationship(
        "VPNUsers",
        back_populates="created_promocodes",
        lazy="select"
    )
    
    # История использования
    Promocodes.usage_history = relationship(
        "PromocodeUsage",
        back_populates="promocode",
        lazy="select",
        order_by="PromocodeUsage.used_at.desc()",
        cascade="all, delete-orphan"
    )
    
    # =============================================================================
    # SupportTickets relationships
    # =============================================================================
    
    # Пользователь, создавший тикет
    SupportTickets.user = relationship(
        "VPNUsers",
        foreign_keys=[SupportTickets.user_id],
        back_populates="support_tickets",
        lazy="select"
    )
    
    # Назначенный сотрудник
    SupportTickets.assigned_to = relationship(
        "VPNUsers",
        foreign_keys=[SupportTickets.assigned_to_id],
        backref=backref(
            "assigned_tickets",
            lazy="dynamic",
            order_by="SupportTickets.created_at.desc()"
        ),
        lazy="select"
    )
    
    # =============================================================================
    # Payments relationships
    # =============================================================================
    
    # Пользователь
    Payments.user = relationship(
        "VPNUsers",
        back_populates="payments",
        lazy="select"
    )
    
    # Тариф
    Payments.tariff = relationship(
        "Tariffs",
        back_populates="payments",
        lazy="select"
    )
    
    # Промокод
    Payments.promocode = relationship(
        "Promocodes",
        lazy="select"
    )
    
    # История использования промокода
    Payments.promocode_usage = relationship(
        "PromocodeUsage",
        back_populates="payment",
        uselist=False,  # Один платеж - одно использование промокода
        lazy="select"
    )
    
    # =============================================================================
    # PromocodeUsage relationships
    # =============================================================================
    
    # Промокод
    PromocodeUsage.promocode = relationship(
        "Promocodes",
        back_populates="usage_history",
        lazy="select"
    )
    
    # Пользователь
    PromocodeUsage.user = relationship(
        "VPNUsers",
        back_populates="promocode_usage",
        lazy="select"
    )
    
    # Платеж
    PromocodeUsage.payment = relationship(
        "Payments",
        back_populates="promocode_usage",
        lazy="select"
    )


def get_optimized_query_options():
    """
    Возвращает опции для оптимизированных запросов
    """
    from sqlalchemy.orm import selectinload, joinedload, subqueryload
    
    return {
        # Для загрузки пользователя с платежами
        'user_with_payments': [
            selectinload(VPNUsers.payments).selectinload(Payments.tariff),
            selectinload(VPNUsers.payments).selectinload(Payments.promocode)
        ],
        
        # Для загрузки пользователя с рефералами
        'user_with_referrals': [
            selectinload(VPNUsers.direct_referrals),
            joinedload(VPNUsers.referred_by)
        ],
        
        # Для загрузки тикета с пользователем
        'ticket_with_user': [
            joinedload(SupportTickets.user),
            joinedload(SupportTickets.assigned_to)
        ],
        
        # Для загрузки платежа со всеми связями
        'payment_full': [
            joinedload(Payments.user),
            joinedload(Payments.tariff),
            joinedload(Payments.promocode),
            joinedload(Payments.promocode_usage)
        ]
    }


# Функции для работы с составными запросами
def get_user_referral_tree(user_id, max_depth=3):
    """
    Получение дерева рефералов пользователя
    """
    from sqlalchemy import text
    from .models import VPNUsers, Referrals
    
    # Рекурсивный CTE запрос для получения дерева рефералов
    query = text("""
        WITH RECURSIVE referral_tree AS (
            -- Базовый случай: прямые рефералы
            SELECT 
                r.referrer_id,
                r.referred_id,
                r.level,
                r.bonus_earned,
                1 as depth,
                ARRAY[r.referred_id] as path
            FROM referrals r
            WHERE r.referrer_id = :user_id AND r.is_active = true
            
            UNION ALL
            
            -- Рекурсивный случай: рефералы рефералов
            SELECT 
                rt.referrer_id,
                r.referred_id,
                r.level,
                r.bonus_earned,
                rt.depth + 1,
                rt.path || r.referred_id
            FROM referral_tree rt
            JOIN referrals r ON rt.referred_id = r.referrer_id
            WHERE rt.depth < :max_depth 
                AND r.is_active = true
                AND NOT r.referred_id = ANY(rt.path)  -- Предотвращение циклов
        )
        SELECT * FROM referral_tree
        ORDER BY depth, level;
    """)
    
    return query.params(user_id=user_id, max_depth=max_depth)


def get_tariff_popularity_stats():
    """
    Статистика популярности тарифов
    """
    from sqlalchemy import text
    
    query = text("""
        SELECT 
            t.id,
            t.name,
            t.duration_days,
            COUNT(p.id) as total_purchases,
            SUM(p.final_amount) as total_revenue,
            AVG(p.final_amount) as avg_purchase_amount,
            COUNT(DISTINCT p.user_id) as unique_customers,
            COUNT(p.id) FILTER (WHERE p.created_at >= NOW() - INTERVAL '30 days') as purchases_last_30d
        FROM tariffs t
        LEFT JOIN payments p ON t.id = p.tariff_id AND p.status = 'completed'
        WHERE t.is_active = true
        GROUP BY t.id, t.name, t.duration_days
        ORDER BY total_purchases DESC;
    """)
    
    return query
