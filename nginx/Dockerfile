# =============================================================================
# UnveilVPN Shop - Production Nginx Dockerfile
# =============================================================================

FROM nginx:1.25-alpine

# Установка дополнительных пакетов
RUN apk add --no-cache \
    curl \
    openssl \
    ca-certificates

# Создание директорий
RUN mkdir -p /var/www/certbot \
    && mkdir -p /etc/ssl/certs \
    && mkdir -p /etc/ssl/private \
    && mkdir -p /var/log/nginx

# Копирование конфигурации
COPY nginx.conf /etc/nginx/nginx.conf

# Создание самоподписанного сертификата для первоначального запуска
RUN openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
    -keyout /etc/ssl/private/unveilvpn.com.key \
    -out /etc/ssl/certs/unveilvpn.com.crt \
    -subj "/C=US/ST=State/L=City/O=Organization/CN=unveilvpn.com"

# Установка правильных прав доступа
RUN chmod 600 /etc/ssl/private/unveilvpn.com.key \
    && chmod 644 /etc/ssl/certs/unveilvpn.com.crt

# Проверка конфигурации Nginx будет выполнена при запуске
# RUN nginx -t

# Экспорт портов
EXPOSE 80 443

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost/health || exit 1

# Запуск Nginx
CMD ["nginx", "-g", "daemon off;"]
