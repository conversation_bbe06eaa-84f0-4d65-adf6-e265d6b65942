<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Browser Diagnostic - UnveilVPN</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #1a1a1a;
            color: #ffffff;
        }
        .section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #333;
            border-radius: 5px;
            background: #2a2a2a;
        }
        .success { color: #4CAF50; }
        .error { color: #f44336; }
        .warning { color: #ff9800; }
        .info { color: #2196F3; }
        pre {
            background: #1a1a1a;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        button {
            background: #2196F3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #1976D2;
        }
        .test-result {
            margin: 5px 0;
            padding: 5px;
            border-radius: 3px;
        }
        .test-pass {
            background: rgba(76, 175, 80, 0.2);
            border-left: 4px solid #4CAF50;
        }
        .test-fail {
            background: rgba(244, 67, 54, 0.2);
            border-left: 4px solid #f44336;
        }
        .test-warn {
            background: rgba(255, 152, 0, 0.2);
            border-left: 4px solid #ff9800;
        }
    </style>
</head>
<body>
    <h1>🔍 Browser Diagnostic Tool</h1>
    <p>Диагностика различий между Playwright и реальным браузером</p>

    <div class="section">
        <h2>📊 Основная информация о браузере</h2>
        <div id="browser-info"></div>
    </div>

    <div class="section">
        <h2>🌐 Сетевые запросы и ресурсы</h2>
        <button onclick="testResourceLoading()">Проверить загрузку ресурсов</button>
        <div id="resource-tests"></div>
    </div>

    <div class="section">
        <h2>⚙️ JavaScript возможности</h2>
        <button onclick="testJavaScriptFeatures()">Проверить JS возможности</button>
        <div id="js-tests"></div>
    </div>

    <div class="section">
        <h2>🎨 CSS и DOM</h2>
        <button onclick="testDOMAndCSS()">Проверить DOM и CSS</button>
        <div id="dom-tests"></div>
    </div>

    <div class="section">
        <h2>⚛️ React диагностика</h2>
        <button onclick="testReactApp()">Проверить React приложение</button>
        <div id="react-tests"></div>
    </div>

    <div class="section">
        <h2>🔒 Безопасность и политики</h2>
        <button onclick="testSecurityPolicies()">Проверить политики безопасности</button>
        <div id="security-tests"></div>
    </div>

    <div class="section">
        <h2>📝 Консольные сообщения</h2>
        <div id="console-messages"></div>
    </div>

    <div class="section">
        <h2>📋 Итоговый отчет</h2>
        <button onclick="generateReport()">Сгенерировать отчет</button>
        <div id="final-report"></div>
    </div>

    <script>
        // Глобальные переменные для сбора данных
        let diagnosticData = {
            browser: {},
            resources: {},
            javascript: {},
            dom: {},
            react: {},
            security: {},
            console: []
        };

        // Перехват консольных сообщений
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;

        console.log = function(...args) {
            diagnosticData.console.push({type: 'log', message: args.join(' '), timestamp: new Date()});
            originalLog.apply(console, args);
            updateConsoleDisplay();
        };

        console.error = function(...args) {
            diagnosticData.console.push({type: 'error', message: args.join(' '), timestamp: new Date()});
            originalError.apply(console, args);
            updateConsoleDisplay();
        };

        console.warn = function(...args) {
            diagnosticData.console.push({type: 'warn', message: args.join(' '), timestamp: new Date()});
            originalWarn.apply(console, args);
            updateConsoleDisplay();
        };

        function updateConsoleDisplay() {
            const container = document.getElementById('console-messages');
            container.innerHTML = diagnosticData.console.map(msg => 
                `<div class="test-result test-${msg.type === 'error' ? 'fail' : msg.type === 'warn' ? 'warn' : 'pass'}">
                    <strong>[${msg.type.toUpperCase()}]</strong> ${msg.message}
                    <small>(${msg.timestamp.toLocaleTimeString()})</small>
                </div>`
            ).join('');
        }

        // Инициализация при загрузке страницы
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Diagnostic page loaded');
            collectBrowserInfo();
        });

        function collectBrowserInfo() {
            const info = {
                userAgent: navigator.userAgent,
                platform: navigator.platform,
                language: navigator.language,
                cookieEnabled: navigator.cookieEnabled,
                onLine: navigator.onLine,
                hardwareConcurrency: navigator.hardwareConcurrency,
                maxTouchPoints: navigator.maxTouchPoints,
                vendor: navigator.vendor,
                vendorSub: navigator.vendorSub,
                product: navigator.product,
                productSub: navigator.productSub,
                appName: navigator.appName,
                appVersion: navigator.appVersion,
                appCodeName: navigator.appCodeName,
                
                // Screen info
                screenWidth: screen.width,
                screenHeight: screen.height,
                screenColorDepth: screen.colorDepth,
                screenPixelDepth: screen.pixelDepth,
                
                // Window info
                windowWidth: window.innerWidth,
                windowHeight: window.innerHeight,
                devicePixelRatio: window.devicePixelRatio,
                
                // Document info
                documentReadyState: document.readyState,
                documentURL: document.URL,
                documentDomain: document.domain,
                documentReferrer: document.referrer,
                
                // Location info
                locationHref: location.href,
                locationProtocol: location.protocol,
                locationHost: location.host,
                locationHostname: location.hostname,
                locationPort: location.port,
                locationPathname: location.pathname,
                locationSearch: location.search,
                locationHash: location.hash,
                
                // Timing info
                performanceNow: performance.now(),
                dateNow: Date.now(),
                timezoneOffset: new Date().getTimezoneOffset()
            };

            diagnosticData.browser = info;

            const container = document.getElementById('browser-info');
            container.innerHTML = `
                <div class="test-result test-pass">
                    <strong>User-Agent:</strong> ${info.userAgent}
                </div>
                <div class="test-result test-pass">
                    <strong>Platform:</strong> ${info.platform}
                </div>
                <div class="test-result test-pass">
                    <strong>Language:</strong> ${info.language}
                </div>
                <div class="test-result test-pass">
                    <strong>Screen:</strong> ${info.screenWidth}x${info.screenHeight} (${info.screenColorDepth}-bit)
                </div>
                <div class="test-result test-pass">
                    <strong>Window:</strong> ${info.windowWidth}x${info.windowHeight}
                </div>
                <div class="test-result test-pass">
                    <strong>Device Pixel Ratio:</strong> ${info.devicePixelRatio}
                </div>
                <div class="test-result test-pass">
                    <strong>Document Ready State:</strong> ${info.documentReadyState}
                </div>
                <div class="test-result test-pass">
                    <strong>Location:</strong> ${info.locationHref}
                </div>
                <div class="test-result test-pass">
                    <strong>Performance Now:</strong> ${info.performanceNow.toFixed(2)}ms
                </div>
            `;
        }
        async function testResourceLoading() {
            const container = document.getElementById('resource-tests');
            container.innerHTML = '<div class="info">🔄 Тестирование загрузки ресурсов...</div>';

            const resources = [
                { url: '/assets/index-BCO3Xl9b.css', type: 'CSS' },
                { url: '/assets/index-Cu4kIiZ8.js', type: 'JavaScript' },
                { url: '/api/v1/public/tariffs', type: 'API' },
                { url: '/api/v1/public/faq', type: 'API' }
            ];

            const results = [];

            for (const resource of resources) {
                try {
                    const response = await fetch(resource.url);
                    const contentType = response.headers.get('content-type');
                    const contentLength = response.headers.get('content-length');

                    results.push({
                        url: resource.url,
                        type: resource.type,
                        status: response.status,
                        statusText: response.statusText,
                        contentType: contentType,
                        contentLength: contentLength,
                        ok: response.ok,
                        headers: Object.fromEntries(response.headers.entries())
                    });
                } catch (error) {
                    results.push({
                        url: resource.url,
                        type: resource.type,
                        error: error.message,
                        ok: false
                    });
                }
            }

            diagnosticData.resources = results;

            container.innerHTML = results.map(result => {
                const statusClass = result.ok ? 'test-pass' : 'test-fail';
                return `
                    <div class="test-result ${statusClass}">
                        <strong>${result.type}:</strong> ${result.url}<br>
                        <small>
                            ${result.ok ?
                                `✅ ${result.status} ${result.statusText} | ${result.contentType} | ${result.contentLength || 'unknown'} bytes` :
                                `❌ ${result.error || result.status + ' ' + result.statusText}`
                            }
                        </small>
                    </div>
                `;
            }).join('');
        }

        function testJavaScriptFeatures() {
            const container = document.getElementById('js-tests');
            container.innerHTML = '<div class="info">🔄 Тестирование JavaScript возможностей...</div>';

            const tests = [
                {
                    name: 'ES6 Arrow Functions',
                    test: () => { const fn = () => true; return fn(); }
                },
                {
                    name: 'ES6 Template Literals',
                    test: () => { const test = `template`; return test === 'template'; }
                },
                {
                    name: 'ES6 Destructuring',
                    test: () => { const [a] = [1]; return a === 1; }
                },
                {
                    name: 'ES6 Spread Operator',
                    test: () => { const arr = [1, 2]; return [...arr].length === 2; }
                },
                {
                    name: 'ES6 Classes',
                    test: () => { class Test {} return new Test() instanceof Test; }
                },
                {
                    name: 'ES6 Modules (import/export)',
                    test: () => { return typeof import !== 'undefined'; }
                },
                {
                    name: 'ES2017 Async/Await',
                    test: () => { return typeof (async () => {}) === 'function'; }
                },
                {
                    name: 'ES2020 Optional Chaining',
                    test: () => { try { const obj = {}; return obj?.prop === undefined; } catch { return false; } }
                },
                {
                    name: 'ES2020 Nullish Coalescing',
                    test: () => { try { return (null ?? 'default') === 'default'; } catch { return false; } }
                },
                {
                    name: 'Fetch API',
                    test: () => { return typeof fetch === 'function'; }
                },
                {
                    name: 'Promise',
                    test: () => { return typeof Promise === 'function'; }
                },
                {
                    name: 'Symbol',
                    test: () => { return typeof Symbol === 'function'; }
                },
                {
                    name: 'Map/Set',
                    test: () => { return typeof Map === 'function' && typeof Set === 'function'; }
                },
                {
                    name: 'WeakMap/WeakSet',
                    test: () => { return typeof WeakMap === 'function' && typeof WeakSet === 'function'; }
                },
                {
                    name: 'Proxy',
                    test: () => { return typeof Proxy === 'function'; }
                },
                {
                    name: 'Reflect',
                    test: () => { return typeof Reflect === 'object'; }
                }
            ];

            const results = tests.map(test => {
                try {
                    const passed = test.test();
                    return { name: test.name, passed, error: null };
                } catch (error) {
                    return { name: test.name, passed: false, error: error.message };
                }
            });

            diagnosticData.javascript = results;

            container.innerHTML = results.map(result => {
                const statusClass = result.passed ? 'test-pass' : 'test-fail';
                return `
                    <div class="test-result ${statusClass}">
                        ${result.passed ? '✅' : '❌'} <strong>${result.name}</strong>
                        ${result.error ? `<br><small>Error: ${result.error}</small>` : ''}
                    </div>
                `;
            }).join('');
        }

        function testDOMAndCSS() {
            const container = document.getElementById('dom-tests');
            container.innerHTML = '<div class="info">🔄 Тестирование DOM и CSS...</div>';

            const tests = [
                {
                    name: 'Document Ready State',
                    test: () => document.readyState === 'complete'
                },
                {
                    name: 'Root Element Exists',
                    test: () => document.getElementById('root') !== null
                },
                {
                    name: 'CSS Grid Support',
                    test: () => CSS.supports('display', 'grid')
                },
                {
                    name: 'CSS Flexbox Support',
                    test: () => CSS.supports('display', 'flex')
                },
                {
                    name: 'CSS Custom Properties',
                    test: () => CSS.supports('--custom-property', 'value')
                },
                {
                    name: 'CSS Calc Support',
                    test: () => CSS.supports('width', 'calc(100% - 10px)')
                },
                {
                    name: 'Viewport Meta Tag',
                    test: () => document.querySelector('meta[name="viewport"]') !== null
                },
                {
                    name: 'CSS Stylesheet Count',
                    test: () => document.styleSheets.length > 0
                }
            ];

            const results = tests.map(test => {
                try {
                    const passed = test.test();
                    return { name: test.name, passed, error: null };
                } catch (error) {
                    return { name: test.name, passed: false, error: error.message };
                }
            });

            // Дополнительная информация о DOM
            const domInfo = {
                rootElement: document.getElementById('root'),
                rootChildren: document.getElementById('root')?.children.length || 0,
                bodyChildren: document.body.children.length,
                stylesheetCount: document.styleSheets.length,
                scriptCount: document.scripts.length
            };

            diagnosticData.dom = { tests: results, info: domInfo };

            container.innerHTML = results.map(result => {
                const statusClass = result.passed ? 'test-pass' : 'test-fail';
                return `
                    <div class="test-result ${statusClass}">
                        ${result.passed ? '✅' : '❌'} <strong>${result.name}</strong>
                        ${result.error ? `<br><small>Error: ${result.error}</small>` : ''}
                    </div>
                `;
            }).join('') + `
                <div class="test-result test-pass">
                    <strong>DOM Info:</strong><br>
                    <small>
                        Root element: ${domInfo.rootElement ? 'Found' : 'Not found'}<br>
                        Root children: ${domInfo.rootChildren}<br>
                        Body children: ${domInfo.bodyChildren}<br>
                        Stylesheets: ${domInfo.stylesheetCount}<br>
                        Scripts: ${domInfo.scriptCount}
                    </small>
                </div>
            `;
        }

        async function testReactApp() {
            const container = document.getElementById('react-tests');
            container.innerHTML = '<div class="info">🔄 Тестирование React приложения...</div>';

            const tests = [];

            // Проверка загрузки React bundle
            try {
                const response = await fetch('/assets/index-Cu4kIiZ8.js');
                const jsContent = await response.text();

                tests.push({
                    name: 'React Bundle Loaded',
                    passed: response.ok && jsContent.length > 0,
                    details: `Size: ${jsContent.length} bytes, Status: ${response.status}`
                });

                tests.push({
                    name: 'React Keywords in Bundle',
                    passed: jsContent.includes('React') || jsContent.includes('createElement'),
                    details: 'Checking for React-related code'
                });

            } catch (error) {
                tests.push({
                    name: 'React Bundle Loaded',
                    passed: false,
                    error: error.message
                });
            }

            // Проверка состояния root элемента
            const rootElement = document.getElementById('root');
            tests.push({
                name: 'Root Element Found',
                passed: rootElement !== null,
                details: rootElement ? `Tag: ${rootElement.tagName}, Children: ${rootElement.children.length}` : 'Element not found'
            });

            if (rootElement) {
                tests.push({
                    name: 'Root Element Has Content',
                    passed: rootElement.children.length > 0 || rootElement.textContent.trim().length > 0,
                    details: `Children: ${rootElement.children.length}, Text length: ${rootElement.textContent.trim().length}`
                });
            }

            // Проверка глобальных React объектов
            tests.push({
                name: 'React Global Available',
                passed: typeof window.React !== 'undefined',
                details: 'Checking window.React'
            });

            tests.push({
                name: 'ReactDOM Global Available',
                passed: typeof window.ReactDOM !== 'undefined',
                details: 'Checking window.ReactDOM'
            });

            diagnosticData.react = tests;

            container.innerHTML = tests.map(result => {
                const statusClass = result.passed ? 'test-pass' : 'test-fail';
                return `
                    <div class="test-result ${statusClass}">
                        ${result.passed ? '✅' : '❌'} <strong>${result.name}</strong>
                        ${result.details ? `<br><small>${result.details}</small>` : ''}
                        ${result.error ? `<br><small>Error: ${result.error}</small>` : ''}
                    </div>
                `;
            }).join('');
        }

        async function testSecurityPolicies() {
            const container = document.getElementById('security-tests');
            container.innerHTML = '<div class="info">🔄 Тестирование политик безопасности...</div>';

            const tests = [];

            // Проверка HTTP заголовков
            try {
                const response = await fetch(window.location.href);
                const headers = Object.fromEntries(response.headers.entries());

                tests.push({
                    name: 'Content-Security-Policy',
                    passed: !headers['content-security-policy'],
                    details: headers['content-security-policy'] || 'Not set (good for development)',
                    warning: !!headers['content-security-policy']
                });

                tests.push({
                    name: 'X-Frame-Options',
                    passed: true,
                    details: headers['x-frame-options'] || 'Not set'
                });

                tests.push({
                    name: 'X-Content-Type-Options',
                    passed: true,
                    details: headers['x-content-type-options'] || 'Not set'
                });

            } catch (error) {
                tests.push({
                    name: 'HTTP Headers Check',
                    passed: false,
                    error: error.message
                });
            }

            // Проверка возможности выполнения inline scripts
            tests.push({
                name: 'Inline Script Execution',
                passed: true,
                details: 'This script is running, so inline scripts work'
            });

            // Проверка CORS
            tests.push({
                name: 'Same-Origin Policy',
                passed: true,
                details: `Origin: ${window.location.origin}`
            });

            // Проверка localStorage
            try {
                localStorage.setItem('test', 'value');
                localStorage.removeItem('test');
                tests.push({
                    name: 'LocalStorage Access',
                    passed: true,
                    details: 'Can read/write localStorage'
                });
            } catch (error) {
                tests.push({
                    name: 'LocalStorage Access',
                    passed: false,
                    error: error.message
                });
            }

            diagnosticData.security = tests;

            container.innerHTML = tests.map(result => {
                const statusClass = result.passed ? 'test-pass' : result.warning ? 'test-warn' : 'test-fail';
                return `
                    <div class="test-result ${statusClass}">
                        ${result.passed ? (result.warning ? '⚠️' : '✅') : '❌'} <strong>${result.name}</strong>
                        ${result.details ? `<br><small>${result.details}</small>` : ''}
                        ${result.error ? `<br><small>Error: ${result.error}</small>` : ''}
                    </div>
                `;
            }).join('');
        }

        function generateReport() {
            const container = document.getElementById('final-report');
            container.innerHTML = '<div class="info">📋 Генерация итогового отчета...</div>';

            const report = {
                timestamp: new Date().toISOString(),
                browser: diagnosticData.browser,
                summary: {
                    resourcesOk: diagnosticData.resources?.every(r => r.ok) || false,
                    jsFeatures: diagnosticData.javascript?.filter(t => t.passed).length || 0,
                    jsFeaturesTotal: diagnosticData.javascript?.length || 0,
                    domTestsPassed: diagnosticData.dom?.tests?.filter(t => t.passed).length || 0,
                    domTestsTotal: diagnosticData.dom?.tests?.length || 0,
                    reactTestsPassed: diagnosticData.react?.filter(t => t.passed).length || 0,
                    reactTestsTotal: diagnosticData.react?.length || 0,
                    securityIssues: diagnosticData.security?.filter(t => !t.passed).length || 0,
                    consoleErrors: diagnosticData.console?.filter(m => m.type === 'error').length || 0
                },
                fullData: diagnosticData
            };

            const isPlaywright = navigator.userAgent.includes('HeadlessChrome');

            container.innerHTML = `
                <div class="test-result ${isPlaywright ? 'test-warn' : 'test-pass'}">
                    <h3>${isPlaywright ? '🤖 Playwright Browser' : '👤 Real User Browser'}</h3>
                    <strong>User Agent:</strong> ${navigator.userAgent}<br>
                    <strong>Platform:</strong> ${navigator.platform}
                </div>

                <div class="test-result ${report.summary.resourcesOk ? 'test-pass' : 'test-fail'}">
                    <strong>📦 Resources:</strong> ${report.summary.resourcesOk ? 'All loaded successfully' : 'Some resources failed to load'}
                </div>

                <div class="test-result ${report.summary.jsFeatures === report.summary.jsFeaturesTotal ? 'test-pass' : 'test-warn'}">
                    <strong>⚙️ JavaScript:</strong> ${report.summary.jsFeatures}/${report.summary.jsFeaturesTotal} features supported
                </div>

                <div class="test-result ${report.summary.domTestsPassed === report.summary.domTestsTotal ? 'test-pass' : 'test-warn'}">
                    <strong>🎨 DOM/CSS:</strong> ${report.summary.domTestsPassed}/${report.summary.domTestsTotal} tests passed
                </div>

                <div class="test-result ${report.summary.reactTestsPassed === report.summary.reactTestsTotal ? 'test-pass' : 'test-fail'}">
                    <strong>⚛️ React:</strong> ${report.summary.reactTestsPassed}/${report.summary.reactTestsTotal} tests passed
                </div>

                <div class="test-result ${report.summary.securityIssues === 0 ? 'test-pass' : 'test-warn'}">
                    <strong>🔒 Security:</strong> ${report.summary.securityIssues} issues found
                </div>

                <div class="test-result ${report.summary.consoleErrors === 0 ? 'test-pass' : 'test-fail'}">
                    <strong>📝 Console:</strong> ${report.summary.consoleErrors} errors logged
                </div>

                <button onclick="copyReportToClipboard()">📋 Копировать отчет</button>
                <button onclick="downloadReport()">💾 Скачать отчет</button>

                <pre id="report-json" style="display: none;">${JSON.stringify(report, null, 2)}</pre>
            `;
        }

        function copyReportToClipboard() {
            const reportJson = document.getElementById('report-json').textContent;
            navigator.clipboard.writeText(reportJson).then(() => {
                alert('Отчет скопирован в буфер обмена!');
            }).catch(err => {
                console.error('Ошибка копирования:', err);
                alert('Ошибка копирования отчета');
            });
        }

        function downloadReport() {
            const reportJson = document.getElementById('report-json').textContent;
            const blob = new Blob([reportJson], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `browser-diagnostic-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }
    </script>
</body>
</html>
