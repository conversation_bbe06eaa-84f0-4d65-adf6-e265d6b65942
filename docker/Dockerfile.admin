# =============================================================================
# UnveilVPN Shop - Admin Bot Production Dockerfile
# =============================================================================

FROM python:3.11-slim

# Установка системных зависимостей
RUN apt-get update && apt-get install -y     curl     gcc     g++     libpq-dev     ca-certificates     && rm -rf /var/lib/apt/lists/*

# Создание пользователя приложения
RUN groupadd -r appuser && useradd -r -g appuser appuser

# Установка рабочей директории
WORKDIR /app

# Копирование requirements и установка зависимостей
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Копирование кода приложения
COPY bot/ ./bot/
COPY locales/ ./locales/

# Создание директории для логов
RUN mkdir -p /app/logs && chown -R appuser:appuser /app

# Переключение на пользователя приложения
USER appuser

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python -c "import sys; sys.exit(0)"

# Запуск приложения
CMD ["python", "-m", "bot.admin_bot.main"]
