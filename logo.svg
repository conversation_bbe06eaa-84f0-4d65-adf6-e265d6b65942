<svg width="512" height="512" viewBox="0 0 512 512" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <filter id="glow" x="-40%" y="-40%" width="180%" height="180%">
      <feGaussianBlur stdDeviation="32" result="coloredBlur"/>
      <feMerge>
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  <!-- Щит -->
  <path filter="url(#glow)" d="M256 60 L426 140 Q426 320 256 452 Q86 320 86 140 Z" fill="#0090ff"/>
  <!-- Внутренняя волна -->
  <path d="M140 170 Q220 220 256 340 Q292 220 372 170 Q320 210 256 210 Q192 210 140 170 Z" fill="#0090ff" fill-opacity="0.5"/>
</svg> 