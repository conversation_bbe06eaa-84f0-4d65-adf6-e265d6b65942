<svg width="512" height="512" viewBox="0 0 512 512" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Градиент для основного щита -->
    <linearGradient id="shieldGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1E90FF;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#0080FF;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0070FF;stop-opacity:1" />
    </linearGradient>

    <!-- Градиент для внутренних элементов -->
    <linearGradient id="innerGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#0080FF;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0060FF;stop-opacity:1" />
    </linearGradient>

    <!-- Фильтр свечения -->
    <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="8" result="coloredBlur"/>
      <feMerge>
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>

  <!-- Основной щит -->
  <path d="M256 50 L420 130 Q420 300 256 450 Q92 300 92 130 Z"
        fill="url(#shieldGradient)"
        filter="url(#glow)"/>

  <!-- Белая внутренняя область -->
  <path d="M256 90 L380 160 Q380 280 256 400 Q132 280 132 160 Z"
        fill="white"
        fill-opacity="0.95"/>

  <!-- Левая волна -->
  <path d="M160 200 Q200 240 240 280 Q220 320 180 360 Q140 320 120 280 Q140 240 160 200 Z"
        fill="url(#innerGradient)"/>

  <!-- Правая волна -->
  <path d="M320 180 Q360 220 380 260 Q360 300 320 340 Q280 300 260 260 Q280 220 320 180 Z"
        fill="url(#innerGradient)"/>

  <!-- Центральная волна -->
  <path d="M200 320 Q240 340 280 360 Q320 340 360 320 Q320 380 280 400 Q240 380 200 360 Q180 340 200 320 Z"
        fill="url(#innerGradient)"
        fill-opacity="0.8"/>
</svg>