# Руководство по устранению неполадок - UnveilVPN Landing

## Обзор

Данное руководство поможет диагностировать и решить проблемы совместимости браузеров для лендинговой страницы UnveilVPN.

## Быстрая диагностика

### 1. Проверка загрузки страницы

```bash
# Проверить доступность сайта
curl -I http://localhost:8080

# Ожидаемый результат:
# HTTP/1.1 200 OK
# Content-Type: text/html
```

### 2. Проверка JavaScript ошибок

Откройте Developer Tools (F12) и проверьте консоль на наличие ошибок:

- **Красные ошибки**: Критичные проблемы, требующие исправления
- **Желтые предупреждения**: Некритичные, но желательно исправить
- **Синие сообщения**: Информационные, обычно безопасны

### 3. Проверка загрузки ресурсов

В Network tab проверьте, что все ресурсы загружаются с кодом 200:

```
✅ index.html (200)
✅ polyfills-*.js (200)
✅ index-*.js (200)
✅ vendor-*.js (200)
✅ favicon.svg (200)
```

## Проблемы совместимости браузеров

### Проблема: Белый экран в старых браузерах

**Симптомы:**
- Страница загружается, но отображается белый экран
- В консоли ошибки типа "Unexpected token" или "SyntaxError"

**Причина:** Браузер не поддерживает ES6+ синтаксис

**Решение:**
1. Проверьте, что загружается legacy версия:
```javascript
// В консоли браузера
console.log(window.browserSupport);
// Должно показать: {modules: false, es6: false, ...}
```

2. Если legacy версия не загружается, проверьте HTML:
```html
<!-- Должны быть оба скрипта -->
<script type="module" src="/assets/index-*.js"></script>
<script nomodule src="/assets/index-legacy-*.js"></script>
```

### Проблема: Анимации не работают

**Симптомы:**
- Контент отображается, но нет плавных переходов
- Элементы появляются резко

**Причина:** Браузер не поддерживает CSS animations или пользователь отключил анимации

**Решение:**
1. Проверьте поддержку CSS:
```javascript
// В консоли браузера
console.log(CSS.supports('animation', 'fadeIn 1s'));
```

2. Проверьте настройки пользователя:
```javascript
console.log(window.matchMedia('(prefers-reduced-motion: reduce)').matches);
```

### Проблема: Изображения не загружаются

**Симптомы:**
- Отсутствуют логотипы или иконки
- Broken image placeholders

**Решение:**
1. Проверьте пути к изображениям в Network tab
2. Убедитесь, что файлы существуют в `/assets/` директории
3. Проверьте MIME types в nginx конфигурации

### Проблема: Медленная загрузка

**Симптомы:**
- Страница загружается более 3 секунд
- Пользователи жалуются на производительность

**Диагностика:**
```bash
# Проверить размер bundle
npm run analyze

# Проверить gzip сжатие
curl -H "Accept-Encoding: gzip" -I http://localhost:8080
```

**Решение:**
1. Убедитесь, что gzip включен в nginx
2. Проверьте размер изображений
3. Оптимизируйте JavaScript bundle

## Специфичные проблемы браузеров

### Internet Explorer 11

**Проблемы:**
- Не поддерживает ES6 modules
- Ограниченная поддержка CSS Grid
- Проблемы с fetch API

**Решение:**
- Используется legacy сборка с polyfills
- CSS Grid заменяется на Flexbox
- fetch заменяется на XMLHttpRequest

### Safari < 12

**Проблемы:**
- Ограниченная поддержка CSS custom properties
- Проблемы с IntersectionObserver

**Решение:**
- Используются CSS fallbacks
- Добавлены соответствующие polyfills

### Chrome < 60

**Проблемы:**
- Нет поддержки ES6 modules
- Ограниченная поддержка async/await

**Решение:**
- Автоматическое переключение на legacy сборку
- Transpilation в ES5

## Инструменты диагностики

### 1. Browser Support Detection

Скрипт автоматически определяет возможности браузера:

```javascript
// Проверить результат детекции
console.log(window.browserSupport);

// Ожидаемый формат:
{
  modules: boolean,    // Поддержка ES6 modules
  es6: boolean,       // Поддержка ES6 синтаксиса
  fetch: boolean,     // Поддержка fetch API
  promise: boolean,   // Поддержка Promise
  cssGrid: boolean    // Поддержка CSS Grid
}
```

### 2. Playwright тесты

Запуск автоматизированных тестов:

```bash
# Все тесты
npm test

# Только совместимость
npm run test:compatibility

# Только мобильные браузеры
npm run test:mobile

# С отчетом
npm run test:report
```

### 3. Bundle анализ

```bash
# Анализ размера
npm run analyze

# Результат покажет:
# - Размер каждого файла
# - Gzip сжатие
# - Рекомендации по оптимизации
```

## Мониторинг в production

### 1. Логи nginx

```bash
# Проверить ошибки
tail -f /var/log/nginx/error.log

# Проверить доступ
tail -f /var/log/nginx/access.log
```

### 2. JavaScript ошибки

Добавьте обработчик глобальных ошибок:

```javascript
window.addEventListener('error', (event) => {
  console.error('Global error:', event.error);
  // Отправить в систему мониторинга
});
```

### 3. Performance мониторинг

```javascript
// Время загрузки страницы
window.addEventListener('load', () => {
  const loadTime = performance.now();
  console.log(`Page loaded in ${loadTime}ms`);
});
```

## Контрольный список для deployment

- [ ] Все тесты проходят успешно
- [ ] Bundle размер < 100KB (gzipped)
- [ ] Нет JavaScript ошибок в консоли
- [ ] Все изображения загружаются
- [ ] Responsive дизайн работает на всех разрешениях
- [ ] Accessibility проверки пройдены
- [ ] Legacy браузеры поддерживаются
- [ ] Performance метрики в норме

## Получение помощи

При возникновении проблем:

1. Проверьте этот документ
2. Запустите автоматизированные тесты
3. Проверьте логи браузера и сервера
4. Создайте issue с подробным описанием проблемы

## Полезные ссылки

- [Can I Use](https://caniuse.com/) - проверка поддержки браузерами
- [Playwright Documentation](https://playwright.dev/) - документация по тестированию
- [Vite Troubleshooting](https://vitejs.dev/guide/troubleshooting.html) - решение проблем Vite
- [MDN Browser Compatibility](https://developer.mozilla.org/en-US/docs/Web/Guide/Browser_compatibility) - совместимость браузеров
