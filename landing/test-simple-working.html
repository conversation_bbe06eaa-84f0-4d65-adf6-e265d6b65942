<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Simple Working</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: linear-gradient(135deg, #007acc 0%, #0ea5e9 50%, #005a9e 100%);
            color: white;
            min-height: 100vh;
            margin: 0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            text-align: center;
        }
        .success {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        button {
            background: #ffffff;
            color: #007acc;
            border: none;
            padding: 15px 30px;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px;
        }
        button:hover {
            background: #f0f0f0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>UnveilVPN - Тестовая страница</h1>
        
        <div class="success">
            <h2>✅ Страница загружена успешно!</h2>
            <p>Это означает, что:</p>
            <ul style="text-align: left; display: inline-block;">
                <li>HTML загружается корректно</li>
                <li>CSS стили применяются</li>
                <li>Nginx работает правильно</li>
                <li>Docker контейнер функционирует</li>
            </ul>
        </div>

        <div class="success">
            <h3>Тест JavaScript</h3>
            <button onclick="testJS()">Проверить JavaScript</button>
            <div id="js-result"></div>
        </div>

        <div class="success">
            <h3>Информация о браузере</h3>
            <div id="browser-info"></div>
        </div>
    </div>

    <script>
        function testJS() {
            const result = document.getElementById('js-result');
            result.innerHTML = '<p style="color: #90EE90; margin-top: 10px;">✅ JavaScript работает!</p>';
            
            // Тест ES6
            try {
                const arrow = () => 'ES6 стрелочные функции работают';
                const template = `Template literals работают: ${arrow()}`;
                result.innerHTML += `<p style="color: #90EE90;">${template}</p>`;
            } catch (e) {
                result.innerHTML += '<p style="color: #FFB6C1;">❌ ES6 не поддерживается</p>';
            }
        }

        // Информация о браузере
        document.addEventListener('DOMContentLoaded', function() {
            const browserInfo = document.getElementById('browser-info');
            const info = {
                userAgent: navigator.userAgent,
                modules: 'noModule' in HTMLScriptElement.prototype,
                es6: (function() {
                    try {
                        new Function('(a = 0) => a');
                        return true;
                    } catch (e) {
                        return false;
                    }
                })(),
                fetch: 'fetch' in window,
                promise: 'Promise' in window
            };
            
            browserInfo.innerHTML = `
                <p><strong>User Agent:</strong> ${info.userAgent}</p>
                <p><strong>Поддержка модулей:</strong> ${info.modules ? '✅' : '❌'}</p>
                <p><strong>Поддержка ES6:</strong> ${info.es6 ? '✅' : '❌'}</p>
                <p><strong>Поддержка Fetch:</strong> ${info.fetch ? '✅' : '❌'}</p>
                <p><strong>Поддержка Promise:</strong> ${info.promise ? '✅' : '❌'}</p>
            `;
        });
    </script>
</body>
</html>
