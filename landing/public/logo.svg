<svg width="128" height="128" viewBox="0 0 128 128" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Фон -->
  <rect width="128" height="128" rx="24" fill="url(#gradient)" />
  
  <!-- Щит -->
  <path d="M64 20L44 28V52C44 68 52 82 64 88C76 82 84 68 84 52V28L64 20Z" fill="white" opacity="0.9" />
  
  <!-- VPN символ -->
  <circle cx="64" cy="48" r="8" fill="url(#gradient)" />
  <path d="M56 56L64 64L72 56" stroke="url(#gradient)" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" fill="none" />
  
  <!-- Текст UnveilVPN -->
  <text x="64" y="108" font-family="Arial, sans-serif" font-size="12" font-weight="bold" text-anchor="middle" fill="white">UnveilVPN</text>
  
  <!-- Градиенты -->
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1E40AF;stop-opacity:1" />
    </linearGradient>
  </defs>
</svg>
