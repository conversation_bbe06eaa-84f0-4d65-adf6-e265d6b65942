// Polyfills для старых браузеров
// Этот файл загружается только для браузеров без поддержки ES6 модулей

// Polyfill для require() в браузере
(function() {
  'use strict';

  // Создаем простой модульный реестр
  const moduleRegistry = {};

  // Регистрируем основные модули
  function registerModule(id, moduleExports) {
    moduleRegistry[id] = moduleExports;
  }

  // Реализация require()
  function requirePolyfill(id) {
    // Если модуль уже зарегистрирован, возвращаем его
    if (moduleRegistry[id]) {
      return moduleRegistry[id];
    }

    // Обработка специальных случаев
    switch (id) {
      case 'react':
        // Возвращаем заглушку React
        const reactStub = {
          createElement: function(type, props) {
            // Более реалистичная реализация createElement
            const element = {
              type: type,
              props: props || {},
              key: (props && props.key) || null,
              ref: (props && props.ref) || null,
              $$typeof: Symbol.for('react.element')
            };

            // Обрабатываем children
            const children = Array.prototype.slice.call(arguments, 2);
            if (children.length > 0) {
              element.props.children = children.length === 1 ? children[0] : children;
            }

            return element;
          },
          StrictMode: function(props) { return props.children; },
          Fragment: function(props) { return props.children; },
          Component: function Component(props) {
            this.props = props;
          },
          PureComponent: function PureComponent(props) {
            this.props = props;
          }
        };
        registerModule('react', reactStub);
        return reactStub;

      case 'react-dom/client':
        // Возвращаем заглушку ReactDOM
        const reactDOMStub = {
          createRoot: function(container) {
            return {
              render: function(element) {
                if (container) {
                  console.log('Rendering React element:', element);

                  // Простой рендерер для React элементов
                  function renderElement(el) {
                    if (typeof el === 'string' || typeof el === 'number') {
                      return String(el);
                    }

                    if (!el || !el.type) {
                      return '';
                    }

                    const type = el.type;
                    const props = el.props || {};
                    const children = props.children || [];

                    if (typeof type === 'string') {
                      // HTML элемент
                      const childrenHtml = Array.isArray(children)
                        ? children.map(renderElement).join('')
                        : renderElement(children);

                      return '<' + type + '>' + childrenHtml + '</' + type + '>';
                    } else if (typeof type === 'function') {
                      // React компонент
                      try {
                        const result = type(props);
                        return renderElement(result);
                      } catch (e) {
                        return '<div>Component: ' + (type.name || 'Unknown') + '</div>';
                      }
                    } else {
                      return '<div>Unsupported element type</div>';
                    }
                  }

                  const html = renderElement(element);
                  container.innerHTML = html || '<div>React App Loaded Successfully!</div>';
                }
              }
            };
          }
        };
        registerModule('react-dom/client', reactDOMStub);
        return reactDOMStub;

      case './App':
        // Заглушка для главного компонента App
        const AppStub = {
          default: function() {
            return {
              type: 'div',
              props: {
                children: [
                  {
                    type: 'h1',
                    props: { children: 'UnveilVPN Landing Page' }
                  },
                  {
                    type: 'p',
                    props: { children: 'Добро пожаловать в UnveilVPN! Ваш надежный VPN сервис.' }
                  },
                  {
                    type: 'div',
                    props: {
                      style: { 
                        marginTop: '20px', 
                        padding: '15px', 
                        background: 'rgba(0,122,204,0.1)', 
                        borderRadius: '8px' 
                      },
                      children: 'React приложение загружено и работает!'
                    }
                  }
                ]
              }
            };
          }
        };
        registerModule('./App', AppStub);
        return AppStub;

      case './components/BrowserCompatibilityWrapper':
        // Заглушка для BrowserCompatibilityWrapper
        const WrapperStub = {
          default: function(props) { return props.children; }
        };
        registerModule('./components/BrowserCompatibilityWrapper', WrapperStub);
        return WrapperStub;

      case './index.css':
      case './utils/browserDetection':
      case './polyfills':
      case './utils/cssPolyfills':
      case './styles/polyfills.css':
        // Для CSS и других файлов возвращаем пустой объект
        console.log('Loading module: ' + id);
        return {};

      default:
        console.warn('Module not found: ' + id + ', returning empty object');
        return {};
    }
  }

  // Устанавливаем require в глобальную область
  if (typeof window !== 'undefined') {
    window.require = requirePolyfill;
    window.module = { exports: {} };
    window.exports = window.module.exports;
  }

  console.log('Polyfills loaded for legacy browser');
})();
