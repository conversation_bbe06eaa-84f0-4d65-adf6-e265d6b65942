<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Simple React</title>
    <script crossorigin src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
</head>
<body>
    <div id="root"></div>
    
    <script type="text/babel">
        const { useState } = React;
        
        function App() {
            const [count, setCount] = useState(0);
            
            return React.createElement('div', {
                style: { padding: '20px', fontFamily: 'Arial, sans-serif' }
            }, [
                React.createElement('h1', { key: 'title' }, 'UnveilVPN Test'),
                React.createElement('p', { key: 'desc' }, 'Это тестовая страница для проверки React'),
                React.createElement('button', {
                    key: 'btn',
                    onClick: () => setCount(count + 1),
                    style: { padding: '10px 20px', fontSize: '16px' }
                }, `Счетчик: ${count}`)
            ]);
        }
        
        const root = ReactDOM.createRoot(document.getElementById('root'));
        root.render(React.createElement(App));
    </script>
</body>
</html>
