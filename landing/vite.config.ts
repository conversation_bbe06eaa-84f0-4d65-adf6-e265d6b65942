import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
// import legacy from '@vitejs/plugin-legacy'
import { visualizer } from 'rollup-plugin-visualizer'
import path from 'path'

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    react({
      // Настройки React для совместимости
      babel: {
        presets: [
          ['@babel/preset-env', {
            targets: {
              chrome: '60',
              firefox: '55',
              safari: '12',
              edge: '79'
            },
            // Принудительно использовать ES модули
            modules: false
          }]
        ]
      }
    }),
    // Legacy plugin для поддержки старых браузеров
    // ВРЕМЕННО ОТКЛЮЧЕН ДЛЯ ДИАГНОСТИКИ
    /*
    legacy({
      targets: ['chrome >= 60', 'firefox >= 55', 'safari >= 12', 'edge >= 79'],
      additionalLegacyPolyfills: ['regenerator-runtime/runtime'],
      renderLegacyChunks: true,
      modernPolyfills: [
        'es.array.flat',
        'es.array.flat-map',
        'es.object.from-entries',
        'es.string.match-all'
      ],
      polyfills: [
        'es.symbol',
        'es.symbol.description',
        'es.symbol.iterator',
        'es.array.filter',
        'es.array.find',
        'es.array.find-index',
        'es.array.for-each',
        'es.array.includes',
        'es.array.map',
        'es.array.reduce',
        'es.array.some',
        'es.promise',
        'es.promise.finally',
        'es.map',
        'es.set',
        'es.object.assign',
        'es.object.define-properties',
        'es.object.define-property',
        'es.object.get-own-property-descriptor',
        'es.object.get-own-property-descriptors',
        'es.object.keys',
        'es.object.values',
        'es.object.entries',
        'es.object.to-string',
        'es.string.includes',
        'es.string.starts-with',
        'es.string.ends-with',
        'web.dom-collections.for-each',
        'web.dom-collections.iterator',
        'esnext.global-this'
      ]
    }),
    */
    // Bundle analyzer для анализа размера
    visualizer({
      filename: 'dist/stats.html',
      open: false,
      gzipSize: true,
      brotliSize: true,
    })
  ],
  define: {
    // Принудительно определяем, что мы используем ES модули
    'process.env.NODE_ENV': '"production"',
    'global': 'globalThis',
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  server: {
    port: 3000,
    host: true,
  },
  build: {
    outDir: 'dist',
    sourcemap: true,
    target: 'es2020',
    // Минификация для лучшей совместимости
    minify: 'terser',
    // Отключаем CommonJS трансформацию для принудительного использования ES модулей
    // commonjsOptions: {
    //   transformMixedEsModules: true,
    //   requireReturnsDefault: false,
    //   defaultIsModuleExports: false,
    // },


    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true,
        pure_funcs: ['console.log', 'console.info', 'console.debug'],
        passes: 2,
        dead_code: true,
        collapse_vars: true,
        reduce_vars: true,
        sequences: true,
        properties: true,
        conditionals: true,
        comparisons: true,
        evaluate: true,
        booleans: true,
        loops: true,
        unused: true,
        hoist_funs: true,
        if_return: true,
        inline: true,
        side_effects: true,
        pure_getters: true,
      },
      mangle: {
        toplevel: true,
        safari10: true,
      },
      format: {
        comments: false,
        ecma: 2015,
      },
    },
    rollupOptions: {
      // Принудительно использовать ES модули для всех зависимостей
      external: [],
      output: {
        // Создание отдельных чанков для лучшего кэширования
        manualChunks: (id) => {
          // React и React DOM в отдельный чанк
          if (id.includes('react') || id.includes('react-dom')) {
            return 'vendor';
          }
          // Framer Motion в отдельный чанк
          if (id.includes('framer-motion') || id.includes('motion-dom')) {
            return 'framer';
          }
          // Иконки в отдельный чанк
          if (id.includes('lucide-react')) {
            return 'icons';
          }
          // Утилиты в отдельный чанк
          if (id.includes('node_modules') &&
              (id.includes('clsx') || id.includes('tailwind-merge') || id.includes('class-variance-authority'))) {
            return 'utils';
          }
          // Остальные node_modules в vendor
          if (id.includes('node_modules')) {
            return 'vendor';
          }
        },
        // Настройки для совместимости
        format: 'es',
        entryFileNames: 'assets/[name]-[hash].js',
        chunkFileNames: 'assets/[name]-[hash].js',
        assetFileNames: 'assets/[name]-[hash].[ext]',
        // Принудительно использовать ES модули
        exports: 'named',
        interop: 'esModule',
        // Принудительно использовать ES модули
        generatedCode: {
          constBindings: true,
          objectShorthand: true,
          reservedNamesAsProps: false,
          symbols: true,
        },
        // Принудительно использовать ES модули для всех внешних зависимостей
        externalLiveBindings: false,
        freeze: false,
      },
    },
    // Увеличиваем лимит для предупреждений о размере
    chunkSizeWarningLimit: 1000,
    // Дополнительные оптимизации
    cssCodeSplit: true,
    assetsInlineLimit: 4096, // 4KB
    reportCompressedSize: true,
  },
  // Оптимизация зависимостей
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      'react/jsx-runtime',
      'react-dom/client',
      // Оптимизируем только используемые иконки
      'lucide-react/dist/esm/icons/check',
      'lucide-react/dist/esm/icons/star',
      'lucide-react/dist/esm/icons/zap',
      'lucide-react/dist/esm/icons/menu',
      'lucide-react/dist/esm/icons/x',
      'lucide-react/dist/esm/icons/alert-triangle',
      'lucide-react/dist/esm/icons/refresh-cw',
      'lucide-react/dist/esm/icons/download',
      'lucide-react/dist/esm/icons/loader-2',
      'lucide-react/dist/esm/icons/external-link',
      'lucide-react/dist/esm/icons/message-circle',
      'lucide-react/dist/esm/icons/settings',
      'lucide-react/dist/esm/icons/mail'
    ],
    esbuildOptions: {
      target: 'es2020',
      format: 'esm',
      // Дополнительные оптимизации
      treeShaking: true,
      minify: true,
      // Принудительно использовать ES модули для всех зависимостей
      mainFields: ['module', 'main'],
      conditions: ['import', 'module', 'default'],
      drop: ['console', 'debugger'],
    },
  },
  // CSS настройки
  css: {
    postcss: {
      plugins: [
        // Autoprefixer будет настроен через postcss.config.js
      ]
    }
  }
})
