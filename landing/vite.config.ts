import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { legacy } from '@vitejs/plugin-legacy'
import path from 'path'

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    react({
      // Настройки React для совместимости
      babel: {
        presets: [
          ['@babel/preset-env', {
            targets: {
              chrome: '60',
              firefox: '55',
              safari: '12',
              edge: '79'
            }
          }]
        ]
      }
    }),
    // Legacy plugin для поддержки старых браузеров
    legacy({
      targets: ['chrome >= 60', 'firefox >= 55', 'safari >= 12', 'edge >= 79'],
      additionalLegacyPolyfills: ['regenerator-runtime/runtime'],
      renderLegacyChunks: true,
      polyfills: [
        'es.symbol',
        'es.array.filter',
        'es.promise',
        'es.promise.finally',
        'es/map',
        'es/set',
        'es.array.for-each',
        'es.object.define-properties',
        'es.object.define-property',
        'es.object.get-own-property-descriptor',
        'es.object.get-own-property-descriptors',
        'es.object.keys',
        'es.object.to-string',
        'web.dom-collections.for-each',
        'esnext.global-this',
        'esnext.string.match-all'
      ]
    })
  ],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  server: {
    port: 3000,
    host: true,
  },
  build: {
    outDir: 'dist',
    sourcemap: true,
    target: 'es2015',
    // Минификация для лучшей совместимости
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: false,
        drop_debugger: true,
      },
      format: {
        comments: false,
      },
    },
    rollupOptions: {
      output: {
        // Создание отдельных чанков для лучшего кэширования
        manualChunks: {
          vendor: ['react', 'react-dom'],
          framer: ['framer-motion'],
          icons: ['lucide-react'],
        },
        // Настройки для совместимости
        format: 'es',
        entryFileNames: 'assets/[name]-[hash].js',
        chunkFileNames: 'assets/[name]-[hash].js',
        assetFileNames: 'assets/[name]-[hash].[ext]',
      },
    },
    // Увеличиваем лимит для предупреждений о размере
    chunkSizeWarningLimit: 1000,
  },
  // Оптимизация зависимостей
  optimizeDeps: {
    include: ['react', 'react-dom', 'framer-motion', 'lucide-react'],
    esbuildOptions: {
      target: 'es2015',
    },
  },
  // CSS настройки
  css: {
    postcss: {
      plugins: [
        require('autoprefixer')({
          overrideBrowserslist: [
            'Chrome >= 60',
            'Firefox >= 55',
            'Safari >= 12',
            'Edge >= 79',
            'iOS >= 12',
            'Android >= 6'
          ]
        })
      ]
    }
  }
})
