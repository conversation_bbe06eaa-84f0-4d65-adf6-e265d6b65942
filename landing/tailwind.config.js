/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        // Основная палитра UnveilVPN на основе логотипа
        primary: {
          50: '#e6f3ff',   // Очень светлый синий для фонов
          100: '#cce7ff',  // Светлый синий для hover состояний
          200: '#99d0ff',  // Светлый акцент
          300: '#66b8ff',  // Средний светлый
          400: '#33a1ff',  // Яркий синий
          500: '#007acc',  // Основной цвет бренда (из логотипа)
          600: '#0066b3',  // Темнее основного
          700: '#005a9e',  // Темно-синий для заголовков
          800: '#004d85',  // Очень темный синий
          900: '#003d6b',  // Самый темный
          950: '#002952',  // Почти черный синий
        },
        // Дополнительная палитра
        secondary: {
          50: '#f0f9ff',   // Очень светлый голубой
          100: '#e0f2fe',  // Светлый голубой
          200: '#bae6fd',  // Средний голубой
          300: '#7dd3fc',  // Яркий голубой
          400: '#38bdf8',  // Основной голубой
          500: '#0ea5e9',  // Темный голубой
          600: '#0284c7',  // Очень темный голубой
          700: '#0369a1',  // Темно-голубой
          800: '#075985',  // Очень темный
          900: '#0c4a6e',  // Самый темный
        },
        // Акцентные цвета
        accent: {
          50: '#fef2f2',   // Светлый красный для ошибок
          100: '#fee2e2',  // Светлый красный
          200: '#fecaca',  // Средний красный
          300: '#fca5a5',  // Яркий красный
          400: '#f87171',  // Основной красный
          500: '#ef4444',  // Темный красный
          600: '#dc2626',  // Очень темный красный
          700: '#b91c1c',  // Темно-красный
          800: '#991b1b',  // Очень темный
          900: '#7f1d1d',  // Самый темный
        },
        // Нейтральные цвета (серые)
        neutral: {
          50: '#f7fafc',   // Фон секций (из анализа)
          100: '#f1f5f9',  // Очень светлый серый
          200: '#e2e8f0',  // Светлый серый
          300: '#cbd5e1',  // Средний светлый серый
          400: '#94a3b8',  // Средний серый
          500: '#64748b',  // Основной серый
          600: '#4a5568',  // Серый текст (из анализа)
          700: '#374151',  // Темный серый
          800: '#1f2937',  // Очень темный серый
          900: '#111827',  // Почти черный
          950: '#030712',  // Черный
        },
        // Цвета состояний
        success: {
          50: '#f0fdf4',   // Светлый зеленый
          100: '#dcfce7',  // Очень светлый зеленый
          200: '#bbf7d0',  // Светлый зеленый
          300: '#86efac',  // Средний зеленый
          400: '#4ade80',  // Яркий зеленый
          500: '#22c55e',  // Основной зеленый
          600: '#16a34a',  // Темный зеленый
          700: '#15803d',  // Очень темный зеленый
          800: '#166534',  // Темно-зеленый
          900: '#14532d',  // Самый темный
        },
        warning: {
          50: '#fffbeb',   // Светлый желтый
          100: '#fef3c7',  // Очень светлый желтый
          200: '#fde68a',  // Светлый желтый
          300: '#fcd34d',  // Средний желтый
          400: '#fbbf24',  // Яркий желтый
          500: '#f59e0b',  // Основной желтый
          600: '#d97706',  // Темный желтый
          700: '#b45309',  // Очень темный желтый
          800: '#92400e',  // Темно-желтый
          900: '#78350f',  // Самый темный
        },
        // Градиентные цвета
        gradient: {
          'primary-start': '#007acc',
          'primary-end': '#005a9e',
          'secondary-start': '#e6f3ff',
          'secondary-end': '#007acc',
          'hero-start': '#007acc',
          'hero-end': '#0ea5e9',
        }
      },
      fontFamily: {
        sans: ['Inter', 'system-ui', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'sans-serif'],
        mono: ['JetBrains Mono', 'Fira Code', 'Consolas', 'Monaco', 'monospace'],
      },
      // Градиенты для фонов
      backgroundImage: {
        'gradient-primary': 'linear-gradient(135deg, var(--tw-gradient-stops))',
        'gradient-radial': 'radial-gradient(ellipse at center, var(--tw-gradient-stops))',
        'gradient-hero': 'linear-gradient(135deg, #007acc 0%, #0ea5e9 50%, #005a9e 100%)',
        'gradient-card': 'linear-gradient(145deg, #ffffff 0%, #f7fafc 100%)',
        'gradient-button': 'linear-gradient(135deg, #007acc 0%, #005a9e 100%)',
        'gradient-button-hover': 'linear-gradient(135deg, #0066b3 0%, #004d85 100%)',
      },
      // Тени
      boxShadow: {
        'card': '0 4px 6px -1px rgba(0, 122, 204, 0.1), 0 2px 4px -1px rgba(0, 122, 204, 0.06)',
        'card-hover': '0 10px 15px -3px rgba(0, 122, 204, 0.1), 0 4px 6px -2px rgba(0, 122, 204, 0.05)',
        'button': '0 4px 14px 0 rgba(0, 122, 204, 0.39)',
        'button-hover': '0 6px 20px rgba(0, 122, 204, 0.23)',
        'inner-light': 'inset 0 2px 4px 0 rgba(255, 255, 255, 0.06)',
      },
      // Анимации
      animation: {
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'fade-in-up': 'fadeInUp 0.6s ease-out',
        'slide-up': 'slideUp 0.5s ease-out',
        'slide-down': 'slideDown 0.5s ease-out',
        'slide-left': 'slideLeft 0.5s ease-out',
        'slide-right': 'slideRight 0.5s ease-out',
        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
        'bounce-slow': 'bounce 2s infinite',
        'spin-slow': 'spin 3s linear infinite',
        'ping-slow': 'ping 2s cubic-bezier(0, 0, 0.2, 1) infinite',
        'float': 'float 6s ease-in-out infinite',
        'glow': 'glow 2s ease-in-out infinite alternate',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        fadeInUp: {
          '0%': { opacity: '0', transform: 'translateY(30px)' },
          '100%': { opacity: '1', transform: 'translateY(0)' },
        },
        slideUp: {
          '0%': { transform: 'translateY(20px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        slideDown: {
          '0%': { transform: 'translateY(-20px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        slideLeft: {
          '0%': { transform: 'translateX(20px)', opacity: '0' },
          '100%': { transform: 'translateX(0)', opacity: '1' },
        },
        slideRight: {
          '0%': { transform: 'translateX(-20px)', opacity: '0' },
          '100%': { transform: 'translateX(0)', opacity: '1' },
        },
        float: {
          '0%, 100%': { transform: 'translateY(0px)' },
          '50%': { transform: 'translateY(-10px)' },
        },
        glow: {
          '0%': { boxShadow: '0 0 5px rgba(0, 122, 204, 0.5)' },
          '100%': { boxShadow: '0 0 20px rgba(0, 122, 204, 0.8)' },
        },
      },
    },
  },
  plugins: [
    require('@tailwindcss/typography'),
  ],
}
