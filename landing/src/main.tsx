import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import './index.css';
import App from './App';
import BrowserCompatibilityWrapper from './components/BrowserCompatibilityWrapper';

// Импортируем утилиты для определения браузера
import './utils/browserDetection';

// Импортируем CSS polyfills
import './styles/polyfills.css';

// Условно загружаем полифиллы только для старых браузеров
async function loadPolyfillsIfNeeded() {
  // Проверяем поддержку ES6 модулей
  const supportsModules = 'noModule' in HTMLScriptElement.prototype;

  if (!supportsModules) {
    console.log('Loading polyfills for legacy browser');
    // Загружаем полифиллы через script tag для старых браузеров
    return new Promise((resolve, reject) => {
      const script = document.createElement('script');
      script.src = '/polyfills.js';
      script.onload = () => {
        console.log('Polyfills loaded successfully');
        resolve(undefined);
      };
      script.onerror = () => {
        console.error('Failed to load polyfills');
        reject(new Error('Failed to load polyfills'));
      };
      document.head.appendChild(script);
    });
  } else {
    console.log('Modern browser detected, skipping polyfills');
    return Promise.resolve();
  }
}

// Основная функция инициализации приложения
async function initializeApp() {
  // Сначала загружаем полифиллы если нужно
  await loadPolyfillsIfNeeded();

  // CSS polyfills загружаются через отдельный файл для старых браузеров
  console.log('Modern browser detected, using native React');

  const rootElement = document.getElementById('root');

  if (!rootElement) {
    throw new Error('Root element not found');
  }

  // Скрываем loading fallback из HTML
  const loadingFallback = document.querySelector('.loading-fallback');
  if (loadingFallback) {
    (loadingFallback as HTMLElement).style.display = 'none';
  }

  // Помечаем что React загружен
  document.documentElement.classList.add('react-loaded');

  createRoot(rootElement).render(
    <StrictMode>
      <BrowserCompatibilityWrapper>
        <App />
      </BrowserCompatibilityWrapper>
    </StrictMode>
  );
}

// Запускаем приложение
initializeApp().catch(error => {
  console.error('Failed to initialize app:', error);

  // Показываем fallback в случае ошибки
  const rootElement = document.getElementById('root');
  if (rootElement) {
    rootElement.innerHTML = `
      <div style="padding: 20px; text-align: center; color: #dc2626;">
        <h1>Ошибка загрузки приложения</h1>
        <p>Пожалуйста, обновите страницу или попробуйте позже.</p>
        <details style="margin-top: 10px;">
          <summary>Техническая информация</summary>
          <pre style="text-align: left; background: #f3f4f6; padding: 10px; border-radius: 4px; margin-top: 10px;">${error.message}</pre>
        </details>
      </div>
    `;
  }
});
