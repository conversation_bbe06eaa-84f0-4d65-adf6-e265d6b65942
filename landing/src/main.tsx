import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import './index.css'
import App from './App.tsx'

console.log('React main.tsx loaded');
console.log('Document ready state:', document.readyState);
console.log('Root element:', document.getElementById('root'));

// Функция для инициализации React приложения
function initializeApp() {
  const rootElement = document.getElementById('root');

  if (!rootElement) {
    console.error('Root element not found!');
    // Создаем элемент root если его нет
    const newRoot = document.createElement('div');
    newRoot.id = 'root';
    document.body.appendChild(newRoot);
    console.log('Created new root element');

    createRoot(newRoot).render(
      <StrictMode>
        <App />
      </StrictMode>,
    );
  } else {
    console.log('Root element found, rendering React app');
    createRoot(rootElement).render(
      <StrictMode>
        <App />
      </StrictMode>,
    );
  }
}

// Инициализируем приложение когда DOM готов
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeApp);
} else {
  initializeApp();
}
