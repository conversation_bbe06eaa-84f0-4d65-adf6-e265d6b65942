import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import './index.css';
import App from './App';
import BrowserCompatibilityWrapper from './components/BrowserCompatibilityWrapper';

// Импортируем утилиты для определения браузера
import './utils/browserDetection';

// Импортируем polyfills
import './polyfills';
import './utils/cssPolyfills';

// Импортируем CSS polyfills
import './styles/polyfills.css';

const rootElement = document.getElementById('root');

if (!rootElement) {
  throw new Error('Root element not found');
}

// Скрываем loading fallback из HTML
const loadingFallback = document.querySelector('.loading-fallback');
if (loadingFallback) {
  (loadingFallback as HTMLElement).style.display = 'none';
}

// Помечаем что React загружен
document.documentElement.classList.add('react-loaded');

createRoot(rootElement).render(
  <StrictMode>
    <BrowserCompatibilityWrapper>
      <App />
    </BrowserCompatibilityWrapper>
  </StrictMode>
);
