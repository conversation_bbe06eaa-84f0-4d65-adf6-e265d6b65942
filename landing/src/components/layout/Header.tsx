import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Menu, X } from 'lucide-react';
import { Button, Logo, HideMobile, MobileOnly } from '@/components/ui';
import { useSmoothScroll } from '@/hooks/useSmoothScroll';
import { safeStartsWith } from '@/utils/stringUtils';
import type { NavItem } from '@/types';

const Header: React.FC = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const { scrollToSection } = useSmoothScroll();

  const navItems: NavItem[] = [
    { label: 'Главная', href: '#hero' },
    { label: 'Тарифы', href: '#pricing' },
    { label: 'Возможности', href: '#features' },
    { label: 'FAQ', href: '#faq' },
  ];

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const handleScrollToSection = (href: string) => {
    if (safeStartsWith(href, '#')) {
      scrollToSection(href.slice(1));
    }
    setIsMenuOpen(false);
  };

  return (
    <motion.header
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
        isScrolled
          ? 'bg-white/95 backdrop-blur-md border-b border-neutral-200 shadow-card'
          : 'bg-transparent'
      }`}
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      transition={{ type: 'spring' as const, stiffness: 300, damping: 30 }}
    >
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16 md:h-20">
          {/* Logo */}
          <Logo
            size="md"
            variant={isScrolled ? 'default' : 'white'}
            animated={true}
            className="cursor-pointer"
          />

          {/* Desktop Navigation */}
          <HideMobile>
            <nav className="flex items-center space-x-8">
              {navItems.map((item) => (
                <motion.button
                  key={item.label}
                  onClick={() => handleScrollToSection(item.href)}
                  className={`font-medium transition-colors duration-200 relative ${
                    isScrolled
                      ? 'text-neutral-600 hover:text-primary-600'
                      : 'text-white/80 hover:text-white'
                  }`}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  {item.label}
                  <motion.div
                    className="absolute -bottom-1 left-0 right-0 h-0.5 bg-primary-500 origin-left"
                    initial={{ scaleX: 0 }}
                    whileHover={{ scaleX: 1 }}
                    transition={{ duration: 0.2 }}
                  />
                </motion.button>
              ))}
            </nav>
          </HideMobile>

          {/* CTA Button */}
          <HideMobile>
            <Button
              variant="primary"
              size="md"
              onClick={() => scrollToSection('pricing')}
              className="shadow-button hover:shadow-button-hover"
            >
              Попробовать бесплатно
            </Button>
          </HideMobile>

          {/* Mobile Menu Button */}
          <MobileOnly>
            <motion.button
              className={`p-2 rounded-lg transition-colors ${
                isScrolled
                  ? 'text-neutral-600 hover:text-primary-600 hover:bg-primary-50'
                  : 'text-white/80 hover:text-white hover:bg-white/10'
              }`}
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              whileTap={{ scale: 0.95 }}
            >
              {isMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
            </motion.button>
          </MobileOnly>
        </div>

        {/* Mobile Menu */}
        <AnimatePresence>
          {isMenuOpen && (
            <motion.div
              className="absolute top-full left-0 right-0 bg-white/95 backdrop-blur-md border-b border-neutral-200 shadow-card-hover mx-4 mt-2 rounded-xl overflow-hidden"
              initial={{ opacity: 0, y: -20, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: -20, scale: 0.95 }}
              transition={{ duration: 0.2, ease: 'easeOut' }}
            >
              <div className="px-6 py-6 space-y-4">
                {navItems.map((item, index) => (
                  <motion.button
                    key={item.label}
                    onClick={() => handleScrollToSection(item.href)}
                    className="block w-full text-left text-neutral-600 hover:text-primary-600 transition-colors duration-200 font-medium py-3 px-4 rounded-lg hover:bg-primary-50"
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    {item.label}
                  </motion.button>
                ))}
                <motion.div
                  className="pt-4 border-t border-neutral-200"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: navItems.length * 0.1 }}
                >
                  <Button
                    variant="primary"
                    size="md"
                    className="w-full shadow-button"
                    onClick={() => scrollToSection('pricing')}
                  >
                    Попробовать бесплатно
                  </Button>
                </motion.div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </motion.header>
  );
};

export default Header;
