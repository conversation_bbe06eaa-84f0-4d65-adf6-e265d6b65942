import React from 'react';
import { motion } from 'framer-motion';
import { Shield, MessageCircle, Settings, Github, Twitter } from 'lucide-react';

const Footer: React.FC = () => {
  const currentYear = new Date().getFullYear();

  const socialLinks = [
    {
      name: 'Клиентский бот',
      icon: MessageCircle,
      href: import.meta.env.VITE_TELEGRAM_BOT_URL || 'https://t.me/unveilvpn_bot',
      description: 'Управление подпиской'
    },
    {
      name: 'Поддержка',
      icon: MessageCircle,
      href: import.meta.env.VITE_SUPPORT_BOT_URL || 'https://t.me/unveilvpn_support_bot',
      description: 'Техническая поддержка'
    },
    {
      name: '<PERSON>д<PERSON><PERSON><PERSON> панель',
      icon: Settings,
      href: import.meta.env.VITE_ADMIN_BOT_URL || 'https://t.me/unveilvpn_admin_bot',
      description: 'Для администраторов'
    }
  ];

  const footerLinks = [
    {
      title: 'Продукт',
      links: [
        { name: 'Возможности', href: '#features' },
        { name: 'Тарифы', href: '#pricing' },
        { name: 'Пробный период', href: '#trial' },
        { name: 'FAQ', href: '#faq' }
      ]
    },
    {
      title: 'Поддержка',
      links: [
        { name: 'Документация', href: '#' },
        { name: 'Техподдержка', href: import.meta.env.VITE_SUPPORT_BOT_URL || '#' },
        { name: 'Статус сервиса', href: '#' },
        { name: 'Обратная связь', href: '#' }
      ]
    },
    {
      title: 'Компания',
      links: [
        { name: 'О нас', href: '#' },
        { name: 'Политика конфиденциальности', href: '#' },
        { name: 'Условия использования', href: '#' },
        { name: 'Возврат средств', href: '#' }
      ]
    }
  ];

  const scrollToSection = (href: string) => {
    if (href.startsWith('#')) {
      const element = document.querySelector(href);
      if (element) {
        element.scrollIntoView({ behavior: 'smooth' });
      }
    } else {
      window.open(href, '_blank');
    }
  };

  return (
    <footer className="bg-dark-900 border-t border-dark-700">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Main Footer Content */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8">
          {/* Brand Section */}
          <div className="lg:col-span-2">
            <motion.div
              className="flex items-center space-x-2 mb-4"
              whileHover={{ scale: 1.05 }}
              transition={{ type: 'spring', stiffness: 400, damping: 17 }}
            >
              <div className="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
                <Shield className="w-5 h-5 text-white" />
              </div>
              <span className="text-xl font-bold text-white">UnveilVPN</span>
            </motion.div>
            
            <p className="text-gray-400 mb-6 max-w-md">
              Безопасный и быстрый VPN сервис для защиты вашей приватности в интернете. 
              Попробуйте 7 дней бесплатно.
            </p>

            {/* Telegram Bots */}
            <div className="space-y-3">
              <h4 className="text-white font-semibold mb-3">Telegram боты</h4>
              {socialLinks.map((link) => (
                <motion.a
                  key={link.name}
                  href={link.href}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center space-x-3 text-gray-400 hover:text-primary-400 transition-colors group"
                  whileHover={{ x: 5 }}
                  transition={{ type: 'spring', stiffness: 400, damping: 17 }}
                >
                  <link.icon className="w-5 h-5 group-hover:text-primary-400" />
                  <div>
                    <div className="font-medium">{link.name}</div>
                    <div className="text-sm text-gray-500">{link.description}</div>
                  </div>
                </motion.a>
              ))}
            </div>
          </div>

          {/* Footer Links */}
          {footerLinks.map((section) => (
            <div key={section.title}>
              <h4 className="text-white font-semibold mb-4">{section.title}</h4>
              <ul className="space-y-2">
                {section.links.map((link) => (
                  <li key={link.name}>
                    <button
                      onClick={() => scrollToSection(link.href)}
                      className="text-gray-400 hover:text-primary-400 transition-colors"
                    >
                      {link.name}
                    </button>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        {/* Bottom Section */}
        <div className="border-t border-dark-700 mt-12 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-400 text-sm">
              © {currentYear} UnveilVPN. Все права защищены.
            </p>
            
            <div className="flex items-center space-x-4 mt-4 md:mt-0">
              <span className="text-gray-400 text-sm">Следите за нами:</span>
              <div className="flex space-x-3">
                <motion.a
                  href="#"
                  className="text-gray-400 hover:text-primary-400 transition-colors"
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Github className="w-5 h-5" />
                </motion.a>
                <motion.a
                  href="#"
                  className="text-gray-400 hover:text-primary-400 transition-colors"
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Twitter className="w-5 h-5" />
                </motion.a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
