import React from 'react';
import { motion } from 'framer-motion';
import { Shield, MessageCircle, Settings, Github, Twitter, Mail, ExternalLink } from 'lucide-react';
import { Logo, SectionContainer, AnimatedSection } from '@/components/ui';
import { safeStartsWith } from '@/utils/stringUtils';

const Footer: React.FC = () => {
  const currentYear = new Date().getFullYear();

  const socialLinks = [
    {
      name: 'Клиентский бот',
      icon: MessageCircle,
      href: import.meta.env.VITE_TELEGRAM_BOT_URL || 'https://t.me/unveilvpn_bot',
      description: 'Управление подпиской'
    },
    {
      name: 'Поддержка',
      icon: MessageCircle,
      href: import.meta.env.VITE_SUPPORT_BOT_URL || 'https://t.me/unveilvpn_support_bot',
      description: 'Техническая поддержка'
    },
    {
      name: 'Админ панель',
      icon: Settings,
      href: import.meta.env.VITE_ADMIN_BOT_URL || 'https://t.me/unveilvpn_admin_bot',
      description: 'Для администраторов'
    }
  ];

  const footerLinks = [
    {
      title: 'Продукт',
      links: [
        { name: 'Возможности', href: '#features' },
        { name: 'Тарифы', href: '#pricing' },
        { name: 'Пробный период', href: '#trial' },
        { name: 'FAQ', href: '#faq' }
      ]
    },
    {
      title: 'Поддержка',
      links: [
        { name: 'Документация', href: '#' },
        { name: 'Техподдержка', href: import.meta.env.VITE_SUPPORT_BOT_URL || '#' },
        { name: 'Статус сервиса', href: '#' },
        { name: 'Обратная связь', href: '#' }
      ]
    },
    {
      title: 'Компания',
      links: [
        { name: 'О нас', href: '#' },
        { name: 'Политика конфиденциальности', href: '#' },
        { name: 'Условия использования', href: '#' },
        { name: 'Возврат средств', href: '#' }
      ]
    }
  ];

  const scrollToSection = (href: string) => {
    if (safeStartsWith(href, '#')) {
      const element = document.querySelector(href);
      if (element) {
        element.scrollIntoView({ behavior: 'smooth' });
      }
    } else {
      window.open(href, '_blank');
    }
  };

  return (
    <footer className="bg-neutral-900 border-t border-neutral-800">
      <SectionContainer background="dark" padding="xl">
        {/* Main Footer Content */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8">
          {/* Brand Section */}
          <div className="lg:col-span-2">
            <AnimatedSection animation="fadeInUp" className="mb-6">
              <Logo
                size="md"
                variant="white"
                animated={true}
                className="mb-4"
              />
            </AnimatedSection>

            <p className="text-neutral-400 mb-8 max-w-md leading-relaxed">
              Безопасный и быстрый VPN сервис для защиты вашей приватности в интернете.
              Попробуйте 7 дней бесплатно.
            </p>

            {/* Telegram Bots */}
            <div className="space-y-4">
              <h4 className="text-white font-semibold mb-4">Telegram боты</h4>
              {socialLinks.map((link, index) => (
                <AnimatedSection
                  key={link.name}
                  animation="fadeInUp"
                  delay={index * 0.1}
                >
                  <motion.a
                    href={link.href}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center space-x-3 text-neutral-400 hover:text-primary-400 transition-colors group p-3 rounded-lg hover:bg-neutral-800/50"
                    whileHover={{ x: 5 }}
                    transition={{ type: 'spring' as const, stiffness: 400, damping: 17 }}
                  >
                    <link.icon className="w-5 h-5 group-hover:text-primary-400 transition-colors" />
                    <div>
                      <div className="font-medium text-white group-hover:text-primary-400 transition-colors">{link.name}</div>
                      <div className="text-sm text-neutral-500">{link.description}</div>
                    </div>
                    <ExternalLink className="w-4 h-4 opacity-0 group-hover:opacity-100 transition-opacity ml-auto" />
                  </motion.a>
                </AnimatedSection>
              ))}
            </div>
          </div>

          {/* Footer Links */}
          {footerLinks.map((section, sectionIndex) => (
            <AnimatedSection
              key={section.title}
              animation="fadeInUp"
              delay={0.2 + sectionIndex * 0.1}
            >
              <h4 className="text-white font-semibold mb-4">{section.title}</h4>
              <ul className="space-y-3">
                {section.links.map((link) => (
                  <li key={link.name}>
                    <motion.button
                      onClick={() => scrollToSection(link.href)}
                      className="text-neutral-400 hover:text-primary-400 transition-colors text-left"
                      whileHover={{ x: 3 }}
                      transition={{ type: 'spring' as const, stiffness: 400, damping: 17 }}
                    >
                      {link.name}
                    </motion.button>
                  </li>
                ))}
              </ul>
            </AnimatedSection>
          ))}
        </div>

        {/* Bottom Section */}
        <AnimatedSection animation="fadeInUp" delay={0.6}>
          <div className="border-t border-neutral-800 mt-12 pt-8">
            <div className="flex flex-col md:flex-row justify-between items-center gap-6">
              <div className="flex flex-col sm:flex-row items-center gap-4">
                <p className="text-neutral-400 text-sm">
                  © {currentYear} UnveilVPN. Все права защищены.
                </p>
                <div className="flex items-center gap-4 text-sm">
                  <button
                    onClick={() => scrollToSection('#')}
                    className="text-neutral-400 hover:text-primary-400 transition-colors"
                  >
                    Политика конфиденциальности
                  </button>
                  <span className="text-neutral-600">•</span>
                  <button
                    onClick={() => scrollToSection('#')}
                    className="text-neutral-400 hover:text-primary-400 transition-colors"
                  >
                    Условия использования
                  </button>
                </div>
              </div>

              <div className="flex items-center space-x-4">
                <span className="text-neutral-400 text-sm">Связаться с нами:</span>
                <div className="flex space-x-3">
                  <motion.a
                    href="mailto:<EMAIL>"
                    className="text-neutral-400 hover:text-primary-400 transition-colors p-2 rounded-lg hover:bg-neutral-800/50"
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.95 }}
                    title="Email"
                  >
                    <Mail className="w-5 h-5" />
                  </motion.a>
                  <motion.a
                    href={import.meta.env.VITE_SUPPORT_BOT_URL || 'https://t.me/unveilvpn_support_bot'}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-neutral-400 hover:text-primary-400 transition-colors p-2 rounded-lg hover:bg-neutral-800/50"
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.95 }}
                    title="Telegram Support"
                  >
                    <MessageCircle className="w-5 h-5" />
                  </motion.a>
                </div>
              </div>
            </div>
          </div>
        </AnimatedSection>
      </SectionContainer>
    </footer>
  );
};

export default Footer;
