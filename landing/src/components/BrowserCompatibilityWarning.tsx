/**
 * Компонент предупреждения о совместимости браузера
 */

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { AlertTriangle, X, RefreshCw, Download } from 'lucide-react';
import { detectBrowserCapabilities, getBrowserSupportLevel } from '@/utils/browserDetection';
import type { BrowserCapabilities } from '@/utils/browserDetection';

interface BrowserCompatibilityWarningProps {
  className?: string;
}

const BrowserCompatibilityWarning: React.FC<BrowserCompatibilityWarningProps> = ({
  className = '',
}) => {
  const [capabilities, setCapabilities] = useState<BrowserCapabilities | null>(null);
  const [isVisible, setIsVisible] = useState(false);
  const [isDismissed, setIsDismissed] = useState(false);

  useEffect(() => {
    // Проверяем, было ли предупреждение уже отклонено
    const dismissed = localStorage.getItem('browser-warning-dismissed');
    if (dismissed) {
      setIsDismissed(true);
      return;
    }

    // Определяем возможности браузера
    const browserCapabilities = detectBrowserCapabilities();
    setCapabilities(browserCapabilities);

    // Показываем предупреждение только для неподдерживаемых или legacy браузеров
    const supportLevel = getBrowserSupportLevel(browserCapabilities);
    if (supportLevel === 'unsupported' || supportLevel === 'legacy') {
      setIsVisible(true);
    }
  }, []);

  const handleDismiss = () => {
    setIsVisible(false);
    setIsDismissed(true);
    localStorage.setItem('browser-warning-dismissed', 'true');
  };

  const handleRefresh = () => {
    window.location.reload();
  };

  if (!capabilities || isDismissed || !isVisible) {
    return null;
  }

  const supportLevel = getBrowserSupportLevel(capabilities);
  const isUnsupported = supportLevel === 'unsupported';

  const recommendedBrowsers = [
    { name: 'Chrome', url: 'https://www.google.com/chrome/', version: '80+' },
    { name: 'Firefox', url: 'https://www.mozilla.org/firefox/', version: '75+' },
    { name: 'Safari', url: 'https://www.apple.com/safari/', version: '13+' },
    { name: 'Edge', url: 'https://www.microsoft.com/edge/', version: '80+' },
  ];

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0, y: -100 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -100 }}
          className={`fixed top-0 left-0 right-0 z-50 ${className}`}
        >
          <div
            className={`${
              isUnsupported
                ? 'bg-red-600 border-red-700'
                : 'bg-yellow-600 border-yellow-700'
            } border-b text-white`}
          >
            <div className="max-w-7xl mx-auto px-4 py-3">
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-3">
                  <AlertTriangle className="w-6 h-6 mt-0.5 flex-shrink-0" />
                  <div className="flex-1">
                    <h3 className="font-semibold text-lg mb-1">
                      {isUnsupported
                        ? 'Браузер не поддерживается'
                        : 'Ограниченная поддержка браузера'}
                    </h3>
                    <p className="text-sm opacity-90 mb-3">
                      {isUnsupported
                        ? 'Ваш браузер не поддерживает современные веб-технологии, необходимые для корректной работы сайта.'
                        : 'Ваш браузер поддерживается, но некоторые функции могут работать медленнее или быть недоступными.'}
                    </p>

                    {/* Список проблем */}
                    <div className="mb-3">
                      <p className="text-sm font-medium mb-1">Обнаруженные проблемы:</p>
                      <ul className="text-xs space-y-1 opacity-90">
                        {!capabilities.es6 && (
                          <li>• ES6 не поддерживается - используется ES5 fallback</li>
                        )}
                        {!capabilities.fetch && (
                          <li>• Fetch API недоступен - используется XMLHttpRequest</li>
                        )}
                        {!capabilities.cssFlexbox && (
                          <li>• CSS Flexbox не поддерживается - используется table layout</li>
                        )}
                        {!capabilities.cssGrid && (
                          <li>• CSS Grid недоступен - используется блочная верстка</li>
                        )}
                        {!capabilities.localStorage && (
                          <li>• LocalStorage недоступен - кэширование отключено</li>
                        )}
                        {!capabilities.intersectionObserver && (
                          <li>• Intersection Observer недоступен - анимации упрощены</li>
                        )}
                      </ul>
                    </div>

                    {/* Рекомендуемые браузеры */}
                    <div className="mb-3">
                      <p className="text-sm font-medium mb-2">
                        Рекомендуем обновить браузер для лучшего опыта:
                      </p>
                      <div className="flex flex-wrap gap-2">
                        {recommendedBrowsers.map((browser) => (
                          <a
                            key={browser.name}
                            href={browser.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="inline-flex items-center space-x-1 bg-white bg-opacity-20 hover:bg-opacity-30 px-2 py-1 rounded text-xs transition-colors"
                          >
                            <Download className="w-3 h-3" />
                            <span>
                              {browser.name} {browser.version}
                            </span>
                          </a>
                        ))}
                      </div>
                    </div>

                    {/* Действия */}
                    <div className="flex items-center space-x-3">
                      <button
                        onClick={handleRefresh}
                        className="inline-flex items-center space-x-1 bg-white bg-opacity-20 hover:bg-opacity-30 px-3 py-1 rounded text-sm transition-colors"
                      >
                        <RefreshCw className="w-4 h-4" />
                        <span>Обновить страницу</span>
                      </button>
                      {!isUnsupported && (
                        <button
                          onClick={handleDismiss}
                          className="text-sm underline opacity-75 hover:opacity-100 transition-opacity"
                        >
                          Продолжить с текущим браузером
                        </button>
                      )}
                    </div>
                  </div>
                </div>

                {/* Кнопка закрытия */}
                {!isUnsupported && (
                  <button
                    onClick={handleDismiss}
                    className="ml-4 p-1 hover:bg-white hover:bg-opacity-20 rounded transition-colors"
                    aria-label="Закрыть предупреждение"
                  >
                    <X className="w-5 h-5" />
                  </button>
                )}
              </div>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default BrowserCompatibilityWarning;
