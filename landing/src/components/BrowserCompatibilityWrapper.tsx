import React, { useEffect, useState } from 'react';
import {
  detectBrowserCapabilities,
  getBrowserSupportLevel,
  type BrowserCapabilities,
} from '@/utils/browserDetection';
import UnsupportedBrowser from '@/components/fallback/UnsupportedBrowser';

interface BrowserCompatibilityWrapperProps {
  children: React.ReactNode;
}

const BrowserCompatibilityWrapper: React.FC<BrowserCompatibilityWrapperProps> = ({ children }) => {
  const [capabilities, setCapabilities] = useState<BrowserCapabilities | null>(null);
  const [supportLevel, setSupportLevel] = useState<'modern' | 'legacy' | 'unsupported' | 'loading'>(
    'loading'
  );

  useEffect(() => {
    // Определяем возможности браузера
    const browserCapabilities = detectBrowserCapabilities();
    const level = getBrowserSupportLevel(browserCapabilities);

    setCapabilities(browserCapabilities);
    setSupportLevel(level);

    // Добавляем CSS классы для стилизации
    document.documentElement.classList.add(`browser-${level}`);

    // Добавляем информацию о возможностях в data атрибуты
    document.documentElement.setAttribute('data-browser-support', level);
    document.documentElement.setAttribute(
      'data-has-modules',
      browserCapabilities.modules.toString()
    );
    document.documentElement.setAttribute('data-has-es6', browserCapabilities.es6.toString());
    document.documentElement.setAttribute('data-has-fetch', browserCapabilities.fetch.toString());
    document.documentElement.setAttribute('data-has-grid', browserCapabilities.cssGrid.toString());

    // Логируем в development режиме
    if (import.meta.env.DEV) {
      console.log(`🌐 Browser support level: ${level}`);
      console.log('Browser capabilities:', browserCapabilities);
    }

    // Отправляем аналитику (если нужно)
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'browser_compatibility', {
        support_level: level,
        user_agent: browserCapabilities.userAgent,
        has_modules: browserCapabilities.modules,
        has_es6: browserCapabilities.es6,
        has_fetch: browserCapabilities.fetch,
        has_grid: browserCapabilities.cssGrid,
      });
    }
  }, []);

  // Показываем loading состояние
  if (supportLevel === 'loading') {
    return (
      <div
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: '100vh',
          background: 'linear-gradient(135deg, #007acc 0%, #0ea5e9 50%, #005a9e 100%)',
          color: 'white',
          fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
        }}
      >
        <div style={{ textAlign: 'center' }}>
          <div
            style={{
              width: '40px',
              height: '40px',
              border: '4px solid rgba(255, 255, 255, 0.3)',
              borderTop: '4px solid white',
              borderRadius: '50%',
              animation: 'spin 1s linear infinite',
              margin: '0 auto 20px',
            }}
          />
          <div style={{ fontSize: '18px', fontWeight: '600' }}>
            Проверка совместимости браузера...
          </div>
        </div>
      </div>
    );
  }

  // Показываем страницу для неподдерживаемых браузеров
  if (supportLevel === 'unsupported') {
    return (
      <UnsupportedBrowser
        capabilities={
          capabilities
            ? {
                userAgent: capabilities.userAgent,
                chrome: capabilities.chrome,
                firefox: capabilities.firefox,
                safari: capabilities.safari,
                edge: capabilities.edge,
              }
            : undefined
        }
      />
    );
  }

  // Для поддерживаемых браузеров показываем основное приложение
  return (
    <>
      {/* Добавляем предупреждение для legacy браузеров */}
      {supportLevel === 'legacy' && <LegacyBrowserNotice capabilities={capabilities} />}
      {children}
    </>
  );
};

// Компонент уведомления для legacy браузеров
const LegacyBrowserNotice: React.FC<{ capabilities: BrowserCapabilities | null }> = () => {
  const [isVisible, setIsVisible] = useState(true);
  const [isDismissed, setIsDismissed] = useState(false);

  useEffect(() => {
    // Проверяем, было ли уведомление уже скрыто
    const dismissed = localStorage.getItem('legacy-browser-notice-dismissed');
    if (dismissed === 'true') {
      setIsVisible(false);
      setIsDismissed(true);
    }
  }, []);

  const handleDismiss = () => {
    setIsVisible(false);
    setIsDismissed(true);
    localStorage.setItem('legacy-browser-notice-dismissed', 'true');
  };

  if (!isVisible || isDismissed) {
    return null;
  }

  return (
    <div
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        background: 'linear-gradient(90deg, #f59e0b 0%, #d97706 100%)',
        color: 'white',
        padding: '12px 20px',
        zIndex: 9999,
        fontSize: '14px',
        textAlign: 'center',
        boxShadow: '0 2px 10px rgba(0, 0, 0, 0.1)',
      }}
    >
      <div
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          gap: '12px',
          maxWidth: '1200px',
          margin: '0 auto',
        }}
      >
        <span>⚠️</span>
        <span>
          Ваш браузер поддерживается, но некоторые функции могут работать медленнее. Рекомендуем
          обновить браузер для лучшего опыта.
        </span>
        <button
          onClick={handleDismiss}
          style={{
            background: 'rgba(255, 255, 255, 0.2)',
            border: 'none',
            borderRadius: '4px',
            color: 'white',
            padding: '4px 8px',
            fontSize: '12px',
            cursor: 'pointer',
            marginLeft: 'auto',
          }}
        >
          ✕
        </button>
      </div>
    </div>
  );
};

export default BrowserCompatibilityWrapper;
