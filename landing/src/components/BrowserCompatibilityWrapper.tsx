import React, { useMemo } from 'react';
import {
  detectBrowserCapabilities,
  getBrowserSupportLevel,
  type BrowserCapabilities,
} from '@/utils/browserDetection';
import UnsupportedBrowser from '@/components/fallback/UnsupportedBrowser';

interface BrowserCompatibilityWrapperProps {
  children: React.ReactNode;
}

const BrowserCompatibilityWrapper: React.FC<BrowserCompatibilityWrapperProps> = ({ children }) => {
  const { capabilities, supportLevel } = useMemo(() => {
    const browserCapabilities = detectBrowserCapabilities();
    const level = getBrowserSupportLevel(browserCapabilities);
    
    // Добавляем CSS классы и data-атрибуты синхронно
    if (typeof document !== 'undefined') {
      document.documentElement.classList.add(`browser-${level}`);
      document.documentElement.setAttribute('data-browser-support', level);
      document.documentElement.setAttribute('data-has-modules', browserCapabilities.modules.toString());
      document.documentElement.setAttribute('data-has-es6', browserCapabilities.es6.toString());
      document.documentElement.setAttribute('data-has-fetch', browserCapabilities.fetch.toString());
      document.documentElement.setAttribute('data-has-grid', browserCapabilities.cssGrid.toString());
    }

    return { capabilities: browserCapabilities, supportLevel: level };
  }, []);

  if (supportLevel === 'unsupported') {
    return (
      <UnsupportedBrowser
        capabilities={{
          userAgent: capabilities.userAgent,
          chrome: capabilities.chrome,
          firefox: capabilities.firefox,
          safari: capabilities.safari,
          edge: capabilities.edge,
        }}
      />
    );
  }

  return (
    <>
      {supportLevel === 'legacy' && <LegacyBrowserNotice capabilities={capabilities} />}
      {children}
    </>
  );
};

const LegacyBrowserNotice: React.FC<{ capabilities: BrowserCapabilities | null }> = () => {
  // Этот компонент остается без изменений, так как он не влияет на первоначальную загрузку
  const [isVisible, setIsVisible] = React.useState(true);
  const handleDismiss = () => {
    setIsVisible(false);
    try {
      localStorage.setItem('legacy-browser-notice-dismissed', 'true');
    } catch (e) {}
  };

  React.useEffect(() => {
    try {
      if (localStorage.getItem('legacy-browser-notice-dismissed') === 'true') {
        setIsVisible(false);
      }
    } catch (e) {}
  }, []);

  if (!isVisible) return null;

  return (
    <div
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        background: 'linear-gradient(90deg, #f59e0b 0%, #d97706 100%)',
        color: 'white',
        padding: '12px 20px',
        zIndex: 9999,
        fontSize: '14px',
        textAlign: 'center',
        boxShadow: '0 2px 10px rgba(0, 0, 0, 0.1)',
      }}
    >
      <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: '12px', maxWidth: '1200px', margin: '0 auto' }}>
        <span>⚠️</span>
        <span>
          Ваш браузер поддерживается, но некоторые функции могут работать медленнее. Рекомендуем обновить браузер для лучшего опыта.
        </span>
        <button
          onClick={handleDismiss}
          style={{
            background: 'rgba(255, 255, 255, 0.2)',
            border: 'none',
            borderRadius: '4px',
            color: 'white',
            padding: '4px 8px',
            fontSize: '12px',
            cursor: 'pointer',
            marginLeft: 'auto',
          }}
        >
          ✕
        </button>
      </div>
    </div>
  );
};

export default BrowserCompatibilityWrapper;
