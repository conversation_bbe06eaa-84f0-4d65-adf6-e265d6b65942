import React from 'react';
import { motion } from 'framer-motion';
import type { CardProps } from '@/types';

const Card: React.FC<CardProps> = ({
  children,
  className = '',
  hover = true,
  variant = 'default',
  padding = 'md',
}) => {
  const baseClasses = 'rounded-xl transition-all duration-300';

  const variantClasses = {
    default: 'bg-white shadow-card hover:shadow-card-hover border border-neutral-200',
    featured: 'bg-gradient-card border-2 border-primary-200 shadow-card-hover',
    glass: 'bg-white/80 backdrop-blur-sm border border-white/20 shadow-lg',
    dark: 'bg-neutral-800 border border-neutral-700 text-white shadow-lg',
    outline: 'bg-transparent border-2 border-neutral-200 hover:border-primary-300',
  };

  const paddingClasses = {
    none: '',
    sm: 'p-4',
    md: 'p-6',
    lg: 'p-8',
    xl: 'p-10',
  };

  const classes = `${baseClasses} ${variantClasses[variant]} ${paddingClasses[padding]} ${className}`;

  if (hover) {
    return (
      <motion.div
        className={classes}
        whileHover={{
          scale: 1.02,
          y: -4,
        }}
        transition={{ type: 'spring', stiffness: 300, damping: 20 }}
      >
        {children}
      </motion.div>
    );
  }

  return (
    <div className={classes}>
      {children}
    </div>
  );
};

export default Card;
