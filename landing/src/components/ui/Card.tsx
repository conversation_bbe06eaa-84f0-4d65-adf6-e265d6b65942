import React from 'react';
import { motion } from 'framer-motion';
import type { CardProps } from '@/types';

const Card: React.FC<CardProps> = ({
  children,
  className = '',
  hover = true,
}) => {
  const baseClasses = 'bg-dark-800 border border-dark-700 rounded-xl p-6 shadow-lg';
  const classes = `${baseClasses} ${className}`;
  
  if (hover) {
    return (
      <motion.div
        className={classes}
        whileHover={{ 
          scale: 1.02,
          boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
        }}
        transition={{ type: 'spring', stiffness: 300, damping: 20 }}
      >
        {children}
      </motion.div>
    );
  }
  
  return (
    <div className={classes}>
      {children}
    </div>
  );
};

export default Card;
