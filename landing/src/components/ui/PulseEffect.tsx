/**
 * Компонент пульсирующих эффектов
 */

import React from 'react';
import { motion } from 'framer-motion';

interface PulseEffectProps {
  children: React.ReactNode;
  variant?: 'subtle' | 'normal' | 'strong' | 'glow';
  duration?: number;
  className?: string;
  disabled?: boolean;
  trigger?: 'hover' | 'always' | 'focus';
}

const PulseEffect: React.FC<PulseEffectProps> = ({
  children,
  variant = 'normal',
  duration = 2,
  className = '',
  disabled = false,
  trigger = 'always',
}) => {
  if (disabled) {
    return <div className={className}>{children}</div>;
  }

  const getVariants = () => {
    switch (variant) {
      case 'subtle':
        return {
          animate: {
            scale: [1, 1.02, 1],
            opacity: [1, 0.95, 1],
          },
        };
      case 'normal':
        return {
          animate: {
            scale: [1, 1.05, 1],
            opacity: [1, 0.9, 1],
          },
        };
      case 'strong':
        return {
          animate: {
            scale: [1, 1.1, 1],
            opacity: [1, 0.8, 1],
          },
        };
      case 'glow':
        return {
          animate: {
            boxShadow: [
              '0 0 0 0 rgba(59, 130, 246, 0)',
              '0 0 0 10px rgba(59, 130, 246, 0.1)',
              '0 0 0 20px rgba(59, 130, 246, 0)',
            ],
          },
        };
      default:
        return {
          animate: {
            scale: [1, 1.05, 1],
          },
        };
    }
  };

  const variants = getVariants();

  const getAnimationProps = () => {
    const baseProps = {
      transition: {
        duration,
        repeat: Infinity,
      },
    };

    switch (trigger) {
      case 'hover':
        return {
          whileHover: variants.animate,
          transition: baseProps.transition,
        };
      case 'focus':
        return {
          whileFocus: variants.animate,
          transition: baseProps.transition,
        };
      case 'always':
      default:
        return {
          animate: variants.animate,
          transition: baseProps.transition,
        };
    }
  };

  return (
    <motion.div className={className} {...getAnimationProps()}>
      {children}
    </motion.div>
  );
};

// Компонент для пульсирующей точки (индикатор статуса)
export const PulseDot: React.FC<{
  color?: 'green' | 'red' | 'yellow' | 'blue' | 'purple';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}> = ({ color = 'green', size = 'md', className = '' }) => {
  const colorClasses = {
    green: 'bg-green-500',
    red: 'bg-red-500',
    yellow: 'bg-yellow-500',
    blue: 'bg-blue-500',
    purple: 'bg-purple-500',
  };

  const sizeClasses = {
    sm: 'w-2 h-2',
    md: 'w-3 h-3',
    lg: 'w-4 h-4',
  };

  return (
    <div className={`relative ${className}`}>
      <motion.div
        className={`rounded-full ${colorClasses[color]} ${sizeClasses[size]}`}
        animate={{
          scale: [1, 1.2, 1],
          opacity: [1, 0.7, 1],
        }}
        transition={{
          duration: 2,
          repeat: Infinity,
          ease: 'easeInOut',
        }}
      />
      <motion.div
        className={`absolute inset-0 rounded-full ${colorClasses[color]} opacity-30`}
        animate={{
          scale: [1, 2, 1],
          opacity: [0.3, 0, 0.3],
        }}
        transition={{
          duration: 2,
          repeat: Infinity,
          ease: 'easeInOut',
        }}
      />
    </div>
  );
};

// Компонент для пульсирующего кольца
export const PulseRing: React.FC<{
  children?: React.ReactNode;
  color?: string;
  size?: number;
  className?: string;
}> = ({ children, color = 'rgb(59, 130, 246)', size = 100, className = '' }) => {
  return (
    <div className={`relative inline-flex items-center justify-center ${className}`}>
      {/* Основной элемент */}
      {children}
      
      {/* Пульсирующие кольца */}
      {[0, 1, 2].map((index) => (
        <motion.div
          key={index}
          className="absolute rounded-full border-2 pointer-events-none"
          style={{
            width: size,
            height: size,
            borderColor: color,
          }}
          animate={{
            scale: [1, 2, 1],
            opacity: [0.7, 0, 0.7],
          }}
          transition={{
            duration: 3,
            repeat: Infinity,
            delay: index * 1,
            ease: 'easeInOut',
          }}
        />
      ))}
    </div>
  );
};

// Компонент для пульсирующего фона
export const PulseBackground: React.FC<{
  children: React.ReactNode;
  color?: string;
  intensity?: 'low' | 'medium' | 'high';
  className?: string;
}> = ({ children, color = 'rgba(59, 130, 246, 0.1)', intensity = 'medium', className = '' }) => {
  const getIntensityValues = () => {
    switch (intensity) {
      case 'low':
        return [0.05, 0.1, 0.05];
      case 'medium':
        return [0.1, 0.2, 0.1];
      case 'high':
        return [0.2, 0.3, 0.2];
      default:
        return [0.1, 0.2, 0.1];
    }
  };

  const intensityValues = getIntensityValues();

  return (
    <motion.div
      className={`relative ${className}`}
      animate={{
        backgroundColor: [
          color.replace(/[\d.]+\)$/, `${intensityValues[0]})`),
          color.replace(/[\d.]+\)$/, `${intensityValues[1]})`),
          color.replace(/[\d.]+\)$/, `${intensityValues[2]})`),
        ],
      }}
      transition={{
        duration: 4,
        repeat: Infinity,
        ease: 'easeInOut',
      }}
    >
      {children}
    </motion.div>
  );
};

export default PulseEffect;
