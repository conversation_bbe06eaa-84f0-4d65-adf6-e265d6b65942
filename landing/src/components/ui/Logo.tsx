import React from 'react';
import { motion } from 'framer-motion';

interface LogoProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  variant?: 'default' | 'white' | 'dark';
  animated?: boolean;
  className?: string;
}

const Logo: React.FC<LogoProps> = ({
  size = 'md',
  variant = 'default',
  animated = true,
  className = '',
}) => {
  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-10 h-10',
    lg: 'w-12 h-12',
    xl: 'w-16 h-16',
  };

  const textSizeClasses = {
    sm: 'text-lg',
    md: 'text-xl',
    lg: 'text-2xl',
    xl: 'text-3xl',
  };

  const variantClasses = {
    default: 'text-primary-600',
    white: 'text-white',
    dark: 'text-neutral-800',
  };

  const logoClasses = `${sizeClasses[size]} ${className}`;
  const textClasses = `font-bold ${textSizeClasses[size]} ${variantClasses[variant]} ml-3`;

  const LogoComponent = animated ? motion.div : 'div';
  const animationProps = animated ? {
    whileHover: { scale: 1.05 },
    transition: { type: 'spring' as const, stiffness: 400, damping: 17 }
  } : {};

  return (
    <LogoComponent 
      className="flex items-center"
      {...animationProps}
    >
      {/* Логотип изображение */}
      <img
        src="/logo.png"
        alt="UnveilVPN"
        className={logoClasses}
        loading="eager"
      />
      
      {/* Текст логотипа */}
      <span className={textClasses}>
        UnveilVPN
      </span>
    </LogoComponent>
  );
};

export default Logo;
