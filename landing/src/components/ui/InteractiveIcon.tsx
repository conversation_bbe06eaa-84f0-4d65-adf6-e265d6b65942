/**
 * Интерактивная иконка с анимациями hover
 */

import React from 'react';
import { motion } from 'framer-motion';
import { iconFloat, hoverScale } from '@/utils/animations';

interface InteractiveIconProps {
  children: React.ReactNode;
  onClick?: () => void;
  variant?: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  disabled?: boolean;
  tooltip?: string;
  animation?: 'float' | 'scale' | 'rotate' | 'pulse';
}

const InteractiveIcon: React.FC<InteractiveIconProps> = ({
  children,
  onClick,
  variant = 'default',
  size = 'md',
  className = '',
  disabled = false,
  tooltip,
  animation = 'float',
}) => {
  const baseClasses = 'inline-flex items-center justify-center rounded-lg transition-all duration-200 cursor-pointer';

  const variantClasses = {
    default: 'text-neutral-600 hover:text-primary-600 hover:bg-primary-50',
    primary: 'text-primary-600 hover:text-primary-700 hover:bg-primary-100',
    secondary: 'text-secondary-600 hover:text-secondary-700 hover:bg-secondary-100',
    success: 'text-success-600 hover:text-success-700 hover:bg-success-100',
    warning: 'text-warning-600 hover:text-warning-700 hover:bg-warning-100',
    danger: 'text-danger-600 hover:text-danger-700 hover:bg-danger-100',
  };

  const sizeClasses = {
    sm: 'w-8 h-8 p-1',
    md: 'w-10 h-10 p-2',
    lg: 'w-12 h-12 p-2.5',
    xl: 'w-16 h-16 p-3',
  };

  const disabledClasses = disabled ? 'opacity-50 cursor-not-allowed' : '';

  const classes = `${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${disabledClasses} ${className}`;

  const getAnimationVariants = () => {
    switch (animation) {
      case 'float':
        return iconFloat;
      case 'scale':
        return hoverScale;
      case 'rotate':
        return {
          rest: { rotate: 0 },
          hover: {
            rotate: 15,
          },
        };
      case 'pulse':
        return {
          rest: { scale: 1 },
          hover: {
            scale: [1, 1.1, 1],
          },
        };
      default:
        return iconFloat;
    }
  };

  const animationVariants = getAnimationVariants();

  return (
    <motion.div
      className={classes}
      variants={disabled ? undefined : animationVariants}
      initial="rest"
      whileHover={disabled ? undefined : "hover"}
      whileTap={disabled ? undefined : { scale: 0.95 }}
      onClick={disabled ? undefined : onClick}
      title={tooltip}
    >
      {children}
    </motion.div>
  );
};

export default InteractiveIcon;
