import React from 'react';
import { motion } from 'framer-motion';
import { useInViewAnimation } from '@/hooks/useInViewAnimation';
import {
  fadeInUp,
  fadeInDown,
  fadeInLeft,
  fadeInRight,
  scaleIn,
  staggerContainer,
  textReveal,
  createDelayedVariant,
} from '@/utils/animations';

interface AnimatedSectionProps {
  children: React.ReactNode;
  className?: string;
  animation?: 'fadeInUp' | 'fadeInDown' | 'fadeInLeft' | 'fadeInRight' | 'scaleIn' | 'stagger' | 'textReveal';
  delay?: number;
  duration?: number;
  once?: boolean;
  threshold?: number;
  staggerChildren?: boolean;
}

const AnimatedSection: React.FC<AnimatedSectionProps> = ({
  children,
  className = '',
  animation = 'fadeInUp',
  delay = 0,
  duration = 0.6,
  once = true,
  threshold = 0.1,
  staggerChildren = false,
}) => {
  const { ref, controls } = useInViewAnimation({
    threshold,
    triggerOnce: once,
    delay,
  });

  // Выбираем анимационный вариант
  const getAnimationVariant = () => {
    const variants = {
      fadeInUp,
      fadeInDown,
      fadeInLeft,
      fadeInRight,
      scaleIn,
      stagger: staggerContainer,
      textReveal,
    };

    let selectedVariant = variants[animation];

    // Применяем задержку если нужно
    if (delay > 0 && animation !== 'stagger') {
      selectedVariant = createDelayedVariant(selectedVariant, delay);
    }

    return selectedVariant;
  };

  const animationVariant = getAnimationVariant();

  return (
    <motion.div
      ref={ref}
      className={className}
      variants={animationVariant}
      initial="hidden"
      animate={controls}
    >
      {children}
    </motion.div>
  );
};

export default AnimatedSection;
