import React from 'react';
import { motion } from 'framer-motion';
import { Loader2 } from 'lucide-react';
import type { ButtonProps } from '@/types';

const Button: React.FC<ButtonProps> = ({
  variant = 'primary',
  size = 'md',
  children,
  onClick,
  disabled = false,
  loading = false,
  className = '',
}) => {
  const baseClasses =
    'inline-flex items-center justify-center font-semibold rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';

  const variantClasses = {
    primary:
      'bg-gradient-button hover:bg-gradient-button-hover text-white shadow-button hover:shadow-button-hover focus:ring-primary-500',
    secondary:
      'bg-white border-2 border-primary-500 text-primary-500 hover:bg-primary-50 hover:border-primary-600 focus:ring-primary-500',
    outline:
      'bg-transparent border border-neutral-300 text-neutral-600 hover:bg-neutral-50 hover:border-neutral-400 focus:ring-neutral-500',
    ghost:
      'bg-transparent text-primary-500 hover:bg-primary-50 hover:text-primary-600 focus:ring-primary-500',
    success:
      'bg-success-500 hover:bg-success-600 text-white shadow-lg hover:shadow-xl focus:ring-success-500',
    warning:
      'bg-warning-500 hover:bg-warning-600 text-white shadow-lg hover:shadow-xl focus:ring-warning-500',
    danger:
      'bg-accent-500 hover:bg-accent-600 text-white shadow-lg hover:shadow-xl focus:ring-accent-500',
  };

  const sizeClasses = {
    xs: 'px-3 py-1.5 text-xs',
    sm: 'px-4 py-2 text-sm',
    md: 'px-6 py-3 text-base',
    lg: 'px-8 py-4 text-lg',
    xl: 'px-10 py-5 text-xl',
  };

  const classes = `${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${className}`;

  return (
    <motion.button
      className={classes}
      onClick={onClick}
      disabled={disabled || loading}
      whileHover={{ scale: disabled || loading ? 1 : 1.02 }}
      whileTap={{ scale: disabled || loading ? 1 : 0.98 }}
      transition={{ type: 'spring', stiffness: 400, damping: 17 }}
    >
      {loading && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
      {children}
    </motion.button>
  );
};

export default Button;
