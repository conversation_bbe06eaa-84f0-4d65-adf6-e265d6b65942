import React from 'react';
import useResponsive from '@/hooks/useResponsive';

interface ResponsiveProps {
  children: React.ReactNode;
  // Показывать только на определенных размерах
  only?: 'mobile' | 'tablet' | 'desktop' | 'large-desktop';
  // Показывать начиная с определенного размера
  from?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';
  // Показывать до определенного размера
  to?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';
  // Скрывать на определенных размерах
  hide?: 'mobile' | 'tablet' | 'desktop' | 'large-desktop';
}

const Responsive: React.FC<ResponsiveProps> = ({
  children,
  only,
  from,
  to,
  hide,
}) => {
  const {
    isMobile,
    isTablet,
    isDesktop,
    isLargeDesktop,
    isSmUp,
    isMdUp,
    isLgUp,
    isXlUp,
    is2XlUp,
    isSmDown,
    isMdDown,
    isLgDown,
    isXlDown,
  } = useResponsive();

  // Проверка "only" условия
  if (only) {
    const shouldShow = {
      'mobile': isMobile,
      'tablet': isTablet,
      'desktop': isDesktop,
      'large-desktop': isLargeDesktop,
    }[only];

    if (!shouldShow) return null;
  }

  // Проверка "from" условия
  if (from) {
    const shouldShow = {
      'xs': true, // всегда показывать от xs
      'sm': isSmUp,
      'md': isMdUp,
      'lg': isLgUp,
      'xl': isXlUp,
      '2xl': is2XlUp,
    }[from];

    if (!shouldShow) return null;
  }

  // Проверка "to" условия
  if (to) {
    const shouldShow = {
      'xs': true, // всегда показывать до xs (не имеет смысла, но для полноты)
      'sm': isSmDown,
      'md': isMdDown,
      'lg': isLgDown,
      'xl': isXlDown,
      '2xl': true, // всегда показывать до 2xl
    }[to];

    if (!shouldShow) return null;
  }

  // Проверка "hide" условия
  if (hide) {
    const shouldHide = {
      'mobile': isMobile,
      'tablet': isTablet,
      'desktop': isDesktop,
      'large-desktop': isLargeDesktop,
    }[hide];

    if (shouldHide) return null;
  }

  return <>{children}</>;
};

// Удобные компоненты для частых случаев
export const MobileOnly: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <Responsive only="mobile">{children}</Responsive>
);

export const TabletOnly: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <Responsive only="tablet">{children}</Responsive>
);

export const DesktopOnly: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <Responsive only="desktop">{children}</Responsive>
);

export const MobileUp: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <Responsive from="xs">{children}</Responsive>
);

export const TabletUp: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <Responsive from="md">{children}</Responsive>
);

export const DesktopUp: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <Responsive from="lg">{children}</Responsive>
);

export const HideMobile: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <Responsive hide="mobile">{children}</Responsive>
);

export const HideDesktop: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <Responsive hide="desktop">{children}</Responsive>
);

export default Responsive;
