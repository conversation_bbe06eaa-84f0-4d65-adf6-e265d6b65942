/**
 * Компонент анимированного счетчика
 */

import React from 'react';
import { motion } from 'framer-motion';
import { useCounterAnimation } from '@/hooks/useInViewAnimation';

interface AnimatedCounterProps {
  value: number;
  duration?: number;
  suffix?: string;
  prefix?: string;
  className?: string;
  decimals?: number;
  separator?: string;
  startOnView?: boolean;
}

const AnimatedCounter: React.FC<AnimatedCounterProps> = ({
  value,
  duration = 2000,
  suffix = '',
  prefix = '',
  className = '',
  decimals = 0,
  separator = ',',
  startOnView = true,
}) => {
  const { count, ref } = useCounterAnimation(value, duration, startOnView);

  const formatNumber = (num: number): string => {
    // Округляем до нужного количества знаков после запятой
    const rounded = Number(num.toFixed(decimals));
    
    // Преобразуем в строку и добавляем разделители тысяч
    const parts = rounded.toString().split('.');
    parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, separator);
    
    return parts.join('.');
  };

  return (
    <motion.span
      ref={ref}
      className={className}
      initial={{ opacity: 0, scale: 0.5 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.5, ease: 'easeOut' }}
    >
      {prefix}{formatNumber(count)}{suffix}
    </motion.span>
  );
};

// Компонент для статистики с иконкой
export const StatCounter: React.FC<{
  icon?: React.ReactNode;
  value: number;
  label: string;
  suffix?: string;
  prefix?: string;
  className?: string;
}> = ({ icon, value, label, suffix, prefix, className = '' }) => {
  return (
    <motion.div
      className={`text-center ${className}`}
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.6, ease: 'easeOut' }}
    >
      {icon && (
        <motion.div
          className="flex justify-center mb-3"
          initial={{ scale: 0 }}
          whileInView={{ scale: 1 }}
          viewport={{ once: true }}
          transition={{ delay: 0.2, duration: 0.5, ease: 'easeOut' }}
        >
          {icon}
        </motion.div>
      )}
      
      <motion.div
        className="text-3xl md:text-4xl font-bold text-neutral-900 mb-2"
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        viewport={{ once: true }}
        transition={{ delay: 0.3, duration: 0.5 }}
      >
        <AnimatedCounter
          value={value}
          prefix={prefix}
          suffix={suffix}
          duration={2500}
        />
      </motion.div>
      
      <motion.p
        className="text-neutral-600"
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        viewport={{ once: true }}
        transition={{ delay: 0.5, duration: 0.5 }}
      >
        {label}
      </motion.p>
    </motion.div>
  );
};

export default AnimatedCounter;
