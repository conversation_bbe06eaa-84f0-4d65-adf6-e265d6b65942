/**
 * Компоненты для floating элементов
 */

import React from 'react';
import { motion } from 'framer-motion';

interface FloatingElementProps {
  children: React.ReactNode;
  direction?: 'up' | 'down' | 'left' | 'right' | 'diagonal';
  intensity?: 'subtle' | 'normal' | 'strong';
  duration?: number;
  delay?: number;
  className?: string;
  style?: React.CSSProperties;
}

const FloatingElement: React.FC<FloatingElementProps> = ({
  children,
  direction = 'up',
  intensity = 'normal',
  duration = 3,
  delay = 0,
  className = '',
  style,
}) => {
  const getMovement = () => {
    const intensityValues = {
      subtle: 5,
      normal: 10,
      strong: 20,
    };

    const movement = intensityValues[intensity];

    switch (direction) {
      case 'up':
        return { y: [-movement, movement, -movement] };
      case 'down':
        return { y: [movement, -movement, movement] };
      case 'left':
        return { x: [-movement, movement, -movement] };
      case 'right':
        return { x: [movement, -movement, movement] };
      case 'diagonal':
        return {
          x: [-movement, movement, -movement],
          y: [-movement, movement, -movement],
        };
      default:
        return { y: [-movement, movement, -movement] };
    }
  };

  return (
    <motion.div
      className={className}
      style={style}
      animate={getMovement()}
      transition={{
        duration,
        repeat: Infinity,
        ease: 'easeInOut',
        delay,
      }}
    >
      {children}
    </motion.div>
  );
};

// Компонент для создания множественных floating элементов
export const FloatingBackground: React.FC<{
  count?: number;
  children?: React.ReactNode;
  className?: string;
}> = ({ count = 10, children, className = '' }) => {
  const elements = [...Array(count)].map((_, index) => ({
    id: index,
    size: Math.random() * 20 + 10,
    left: Math.random() * 100,
    top: Math.random() * 100,
    duration: Math.random() * 3 + 2,
    delay: Math.random() * 2,
    opacity: Math.random() * 0.3 + 0.1,
  }));

  return (
    <div className={`relative overflow-hidden ${className}`}>
      {/* Floating элементы */}
      <div className="absolute inset-0 pointer-events-none">
        {elements.map((element) => (
          <FloatingElement
            key={element.id}
            direction="up"
            intensity="subtle"
            duration={element.duration}
            delay={element.delay}
            className="absolute"
            style={{
              left: `${element.left}%`,
              top: `${element.top}%`,
            }}
          >
            <div
              className="rounded-full bg-primary-400"
              style={{
                width: element.size,
                height: element.size,
                opacity: element.opacity,
              }}
            />
          </FloatingElement>
        ))}
      </div>

      {/* Основной контент */}
      <div className="relative z-10">{children}</div>
    </div>
  );
};

// Компонент для floating иконок
export const FloatingIcon: React.FC<{
  icon: React.ReactNode;
  position?: { x: string; y: string };
  animation?: 'float' | 'rotate' | 'pulse' | 'bounce';
  className?: string;
}> = ({ icon, position = { x: '50%', y: '50%' }, animation = 'float', className = '' }) => {
  const getAnimation = () => {
    switch (animation) {
      case 'float':
        return {
          animate: {
            y: [-10, 10, -10],
          },
          transition: {
            duration: 3,
            repeat: Infinity,
          },
        };
      case 'rotate':
        return {
          animate: {
            rotate: [0, 360],
          },
          transition: {
            duration: 8,
            repeat: Infinity,
          },
        };
      case 'pulse':
        return {
          animate: {
            scale: [1, 1.1, 1],
            opacity: [0.7, 1, 0.7],
          },
          transition: {
            duration: 2,
            repeat: Infinity,
          },
        };
      case 'bounce':
        return {
          animate: {
            y: [0, -20, 0],
          },
          transition: {
            duration: 1.5,
            repeat: Infinity,
          },
        };
      default:
        return {};
    }
  };

  return (
    <motion.div
      className={`absolute pointer-events-none ${className}`}
      style={{
        left: position.x,
        top: position.y,
        transform: 'translate(-50%, -50%)',
      }}
      {...getAnimation()}
    >
      {icon}
    </motion.div>
  );
};

// Компонент для floating текста
export const FloatingText: React.FC<{
  text: string;
  position?: { x: string; y: string };
  className?: string;
  duration?: number;
}> = ({ text, position = { x: '50%', y: '50%' }, className = '', duration = 4 }) => {
  return (
    <motion.div
      className={`absolute pointer-events-none text-sm font-medium text-primary-600 ${className}`}
      style={{
        left: position.x,
        top: position.y,
        transform: 'translate(-50%, -50%)',
      }}
      initial={{ opacity: 0, y: 20, scale: 0.8 }}
      animate={{ opacity: 1, y: -20, scale: 1 }}
      exit={{ opacity: 0, y: -40, scale: 0.8 }}
      transition={{ duration }}
    >
      {text}
    </motion.div>
  );
};

// Компонент для создания floating частиц
export const FloatingParticles: React.FC<{
  count?: number;
  color?: string;
  size?: { min: number; max: number };
  speed?: { min: number; max: number };
  className?: string;
}> = ({
  count = 20,
  color = 'rgba(59, 130, 246, 0.3)',
  size = { min: 2, max: 6 },
  speed = { min: 2, max: 5 },
  className = '',
}) => {
  const particles = [...Array(count)].map((_, index) => ({
    id: index,
    size: Math.random() * (size.max - size.min) + size.min,
    left: Math.random() * 100,
    top: Math.random() * 100,
    duration: Math.random() * (speed.max - speed.min) + speed.min,
    delay: Math.random() * 2,
  }));

  return (
    <div className={`absolute inset-0 overflow-hidden pointer-events-none ${className}`}>
      {particles.map((particle) => (
        <motion.div
          key={particle.id}
          className="absolute rounded-full"
          style={{
            width: particle.size,
            height: particle.size,
            backgroundColor: color,
            left: `${particle.left}%`,
            top: `${particle.top}%`,
          }}
          animate={{
            y: [-20, -100],
            opacity: [0, 1, 0],
          }}
          transition={{
            duration: particle.duration,
            repeat: Infinity,
            delay: particle.delay,
            ease: 'easeOut',
          }}
        />
      ))}
    </div>
  );
};

export default FloatingElement;
