/**
 * Интерактивная ссылка с анимациями hover
 */

import React from 'react';
import { motion } from 'framer-motion';
import { ExternalLink } from 'lucide-react';

interface InteractiveLinkProps {
  children: React.ReactNode;
  href?: string;
  onClick?: () => void;
  external?: boolean;
  variant?: 'default' | 'primary' | 'secondary' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  showIcon?: boolean;
  underline?: boolean;
}

const InteractiveLink: React.FC<InteractiveLinkProps> = ({
  children,
  href,
  onClick,
  external = false,
  variant = 'default',
  size = 'md',
  className = '',
  showIcon = false,
  underline = true,
}) => {
  const baseClasses = 'inline-flex items-center gap-1 font-medium transition-all duration-200 cursor-pointer';

  const variantClasses = {
    default: 'text-neutral-600 hover:text-primary-600',
    primary: 'text-primary-600 hover:text-primary-700',
    secondary: 'text-secondary-600 hover:text-secondary-700',
    ghost: 'text-neutral-400 hover:text-neutral-600',
  };

  const sizeClasses = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg',
  };

  const classes = `${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${className}`;

  const linkVariants = {
    rest: {
      scale: 1,
    },
    hover: {
      scale: 1.05,
    },
    tap: {
      scale: 0.95,
    },
  };

  const underlineVariants = {
    rest: {
      scaleX: 0,
      originX: 0,
    },
    hover: {
      scaleX: 1,
    },
  };

  const iconVariants = {
    rest: {
      x: 0,
      opacity: showIcon ? 1 : 0,
    },
    hover: {
      x: 2,
      opacity: 1,
    },
  };

  const handleClick = () => {
    if (onClick) {
      onClick();
    } else if (href) {
      if (external) {
        window.open(href, '_blank', 'noopener,noreferrer');
      } else {
        window.location.href = href;
      }
    }
  };

  return (
    <motion.span
      className={classes}
      variants={linkVariants}
      initial="rest"
      whileHover="hover"
      whileTap="tap"
      onClick={handleClick}
    >
      <span className="relative">
        {children}
        {underline && (
          <motion.div
            className="absolute bottom-0 left-0 right-0 h-0.5 bg-current"
            variants={underlineVariants}
          />
        )}
      </span>
      
      {(showIcon || external) && (
        <motion.div variants={iconVariants}>
          <ExternalLink className="w-4 h-4" />
        </motion.div>
      )}
    </motion.span>
  );
};

export default InteractiveLink;
