/**
 * Компонент загрузочного спиннера
 */

import React from 'react';
import { motion } from 'framer-motion';
import { Loader2, RefreshCw, RotateCw } from 'lucide-react';
import { loadingSpinner, pulseVariants } from '@/utils/animations';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  variant?: 'spinner' | 'dots' | 'pulse' | 'bars' | 'icon';
  color?: 'primary' | 'secondary' | 'neutral' | 'white';
  className?: string;
  text?: string;
  icon?: 'loader' | 'refresh' | 'rotate';
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'md',
  variant = 'spinner',
  color = 'primary',
  className = '',
  text,
  icon = 'loader',
}) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8',
    xl: 'w-12 h-12',
  };

  const colorClasses = {
    primary: 'text-primary-600',
    secondary: 'text-secondary-600',
    neutral: 'text-neutral-600',
    white: 'text-white',
  };

  const textSizeClasses = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg',
    xl: 'text-xl',
  };

  const iconComponents = {
    loader: Loader2,
    refresh: RefreshCw,
    rotate: RotateCw,
  };

  const renderSpinner = () => {
    switch (variant) {
      case 'spinner':
        const IconComponent = iconComponents[icon];
        return (
          <motion.div
            variants={loadingSpinner}
            animate="animate"
            className={`${sizeClasses[size]} ${colorClasses[color]}`}
          >
            <IconComponent className="w-full h-full" />
          </motion.div>
        );

      case 'dots':
        return (
          <div className="flex space-x-1">
            {[0, 1, 2].map((index) => (
              <motion.div
                key={index}
                className={`w-2 h-2 rounded-full ${colorClasses[color].replace('text-', 'bg-')}`}
                animate={{
                  scale: [1, 1.2, 1],
                  opacity: [0.5, 1, 0.5],
                }}
                transition={{
                  duration: 1,
                  repeat: Infinity,
                  delay: index * 0.2,
                  ease: 'easeInOut',
                }}
              />
            ))}
          </div>
        );

      case 'pulse':
        return (
          <motion.div
            variants={pulseVariants}
            animate="animate"
            className={`${sizeClasses[size]} rounded-full ${colorClasses[color].replace('text-', 'bg-')}`}
          />
        );

      case 'bars':
        return (
          <div className="flex space-x-1 items-end">
            {[0, 1, 2, 3].map((index) => (
              <motion.div
                key={index}
                className={`w-1 ${colorClasses[color].replace('text-', 'bg-')}`}
                animate={{
                  height: ['0.5rem', '1.5rem', '0.5rem'],
                }}
                transition={{
                  duration: 1,
                  repeat: Infinity,
                  delay: index * 0.1,
                  ease: 'easeInOut',
                }}
              />
            ))}
          </div>
        );

      case 'icon':
        return (
          <motion.div
            animate={{
              rotate: 360,
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: 'linear',
            }}
            className={`${sizeClasses[size]} ${colorClasses[color]}`}
          >
            <div className={`w-full h-full border-2 border-current border-t-transparent rounded-full`} />
          </motion.div>
        );

      default:
        return null;
    }
  };

  return (
    <div className={`flex flex-col items-center justify-center ${className}`}>
      {renderSpinner()}
      {text && (
        <motion.p
          className={`mt-3 ${textSizeClasses[size]} ${colorClasses[color]} font-medium`}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.2 }}
        >
          {text}
        </motion.p>
      )}
    </div>
  );
};

// Компонент для полноэкранной загрузки
export const FullScreenLoader: React.FC<{
  text?: string;
  variant?: LoadingSpinnerProps['variant'];
}> = ({ text = 'Загрузка...', variant = 'spinner' }) => {
  return (
    <motion.div
      className="fixed inset-0 bg-white/80 backdrop-blur-sm flex items-center justify-center z-50"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
    >
      <LoadingSpinner
        size="xl"
        variant={variant}
        text={text}
        className="text-center"
      />
    </motion.div>
  );
};

// Компонент для inline загрузки
export const InlineLoader: React.FC<{
  text?: string;
  size?: LoadingSpinnerProps['size'];
  variant?: LoadingSpinnerProps['variant'];
}> = ({ text, size = 'sm', variant = 'spinner' }) => {
  return (
    <div className="flex items-center space-x-2">
      <LoadingSpinner size={size} variant={variant} />
      {text && <span className="text-sm text-neutral-600">{text}</span>}
    </div>
  );
};

// Компонент для кнопки с загрузкой
export const ButtonLoader: React.FC<{
  loading: boolean;
  children: React.ReactNode;
  size?: LoadingSpinnerProps['size'];
}> = ({ loading, children, size = 'sm' }) => {
  return (
    <div className="flex items-center space-x-2">
      {loading && <LoadingSpinner size={size} color="white" />}
      <span className={loading ? 'opacity-75' : ''}>{children}</span>
    </div>
  );
};

export default LoadingSpinner;
