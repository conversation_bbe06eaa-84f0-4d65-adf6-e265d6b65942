/**
 * Компоненты скелетонов для loading состояний
 */

import React from 'react';
import { motion } from 'framer-motion';

interface SkeletonProps {
  className?: string;
  width?: string | number;
  height?: string | number;
  rounded?: boolean;
  animated?: boolean;
}

const Skeleton: React.FC<SkeletonProps> = ({
  className = '',
  width = '100%',
  height = '1rem',
  rounded = false,
  animated = true,
}) => {
  const baseClasses = 'bg-neutral-200 animate-pulse';
  const roundedClasses = rounded ? 'rounded-full' : 'rounded';
  const classes = `${baseClasses} ${roundedClasses} ${className}`;

  const style = {
    width: typeof width === 'number' ? `${width}px` : width,
    height: typeof height === 'number' ? `${height}px` : height,
  };

  if (animated) {
    return (
      <motion.div
        className={classes}
        style={style}
        animate={{
          opacity: [0.5, 1, 0.5],
        }}
        transition={{
          duration: 1.5,
          repeat: Infinity,
          ease: 'easeInOut',
        }}
      />
    );
  }

  return <div className={classes} style={style} />;
};

// Скелетон для текста
export const TextSkeleton: React.FC<{
  lines?: number;
  className?: string;
}> = ({ lines = 1, className = '' }) => {
  return (
    <div className={`space-y-2 ${className}`}>
      {[...Array(lines)].map((_, index) => (
        <Skeleton
          key={index}
          height="1rem"
          width={index === lines - 1 ? '75%' : '100%'}
        />
      ))}
    </div>
  );
};

// Скелетон для карточки
export const CardSkeleton: React.FC<{
  className?: string;
  showImage?: boolean;
  imageHeight?: string;
}> = ({ className = '', showImage = false, imageHeight = '12rem' }) => {
  return (
    <div className={`bg-white rounded-xl border border-neutral-200 p-6 ${className}`}>
      {showImage && (
        <Skeleton height={imageHeight} className="mb-4" rounded />
      )}
      <Skeleton height="1.5rem" width="60%" className="mb-3" />
      <TextSkeleton lines={3} className="mb-4" />
      <Skeleton height="2.5rem" width="40%" />
    </div>
  );
};

// Скелетон для тарифной карточки
export const PricingCardSkeleton: React.FC<{
  className?: string;
  featured?: boolean;
}> = ({ className = '', featured = false }) => {
  const cardClasses = featured
    ? 'bg-gradient-card border-2 border-primary-200'
    : 'bg-white border border-neutral-200';

  return (
    <div className={`rounded-xl p-6 ${cardClasses} ${className}`}>
      {/* Badge для популярного тарифа */}
      {featured && (
        <div className="flex justify-center mb-4">
          <Skeleton width="6rem" height="1.5rem" rounded />
        </div>
      )}

      {/* Название тарифа */}
      <div className="text-center mb-6">
        <Skeleton height="2rem" width="60%" className="mx-auto mb-2" />
        <Skeleton height="1rem" width="80%" className="mx-auto" />
      </div>

      {/* Цена */}
      <div className="text-center mb-8">
        <Skeleton height="3rem" width="50%" className="mx-auto mb-2" />
        <Skeleton height="1rem" width="30%" className="mx-auto" />
      </div>

      {/* Характеристики */}
      <div className="bg-neutral-50 rounded-lg p-4 mb-8">
        <div className="grid grid-cols-2 gap-4">
          {[...Array(4)].map((_, index) => (
            <div key={index}>
              <Skeleton height="0.75rem" width="60%" className="mb-1" />
              <Skeleton height="1rem" width="80%" />
            </div>
          ))}
        </div>
      </div>

      {/* Возможности */}
      <div className="space-y-3 mb-8">
        {[...Array(5)].map((_, index) => (
          <div key={index} className="flex items-center space-x-3">
            <Skeleton width="1.25rem" height="1.25rem" rounded />
            <Skeleton height="1rem" width="85%" />
          </div>
        ))}
      </div>

      {/* Кнопка */}
      <Skeleton height="3rem" width="100%" />
    </div>
  );
};

// Скелетон для FAQ элемента
export const FAQSkeleton: React.FC<{
  className?: string;
}> = ({ className = '' }) => {
  return (
    <div className={`bg-white rounded-xl border border-neutral-200 p-6 ${className}`}>
      <div className="flex items-center justify-between">
        <Skeleton height="1.5rem" width="80%" />
        <Skeleton width="1.25rem" height="1.25rem" rounded />
      </div>
    </div>
  );
};

// Скелетон для feature карточки
export const FeatureSkeleton: React.FC<{
  className?: string;
}> = ({ className = '' }) => {
  return (
    <div className={`bg-white rounded-xl border border-neutral-200 p-6 text-center ${className}`}>
      {/* Иконка */}
      <div className="flex justify-center mb-6">
        <Skeleton width="4rem" height="4rem" rounded />
      </div>

      {/* Заголовок */}
      <Skeleton height="1.5rem" width="70%" className="mx-auto mb-4" />

      {/* Описание */}
      <TextSkeleton lines={3} />
    </div>
  );
};

// Скелетон для header
export const HeaderSkeleton: React.FC = () => {
  return (
    <div className="bg-white border-b border-neutral-200 shadow-sm">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Skeleton width="8rem" height="2rem" />

          {/* Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            {[...Array(4)].map((_, index) => (
              <Skeleton key={index} width="4rem" height="1rem" />
            ))}
          </div>

          {/* CTA Button */}
          <div className="hidden md:block">
            <Skeleton width="8rem" height="2.5rem" />
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <Skeleton width="2rem" height="2rem" />
          </div>
        </div>
      </div>
    </div>
  );
};

// Скелетон для hero section
export const HeroSkeleton: React.FC = () => {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-hero">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center max-w-5xl mx-auto">
          {/* Badge */}
          <div className="flex justify-center mb-8">
            <Skeleton width="12rem" height="2.5rem" rounded />
          </div>

          {/* Title */}
          <Skeleton height="4rem" width="80%" className="mx-auto mb-8" />

          {/* Subtitle */}
          <TextSkeleton lines={2} className="max-w-4xl mx-auto mb-10" />

          {/* Features */}
          <div className="flex flex-wrap justify-center gap-8 mb-12">
            {[...Array(4)].map((_, index) => (
              <Skeleton key={index} width="8rem" height="2.5rem" rounded />
            ))}
          </div>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-6 justify-center items-center mb-16">
            <Skeleton width="12rem" height="3rem" />
            <Skeleton width="10rem" height="3rem" />
          </div>

          {/* Stats */}
          <div className="grid grid-cols-3 gap-8 max-w-md mx-auto">
            {[...Array(3)].map((_, index) => (
              <div key={index} className="text-center">
                <Skeleton height="2rem" width="100%" className="mb-2" />
                <Skeleton height="1rem" width="80%" className="mx-auto" />
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Skeleton;
