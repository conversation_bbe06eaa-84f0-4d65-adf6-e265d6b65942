import React from 'react';
import AnimatedSection from './AnimatedSection';

interface SectionContainerProps {
  children: React.ReactNode;
  className?: string;
  containerClassName?: string;
  background?: 'default' | 'gray' | 'gradient' | 'primary' | 'dark';
  padding?: 'sm' | 'md' | 'lg' | 'xl';
  animated?: boolean;
  animation?: 'fadeInUp' | 'fadeInDown' | 'fadeInLeft' | 'fadeInRight' | 'scaleIn' | 'stagger' | 'textReveal';
  id?: string;
}

const SectionContainer: React.FC<SectionContainerProps> = ({
  children,
  className = '',
  containerClassName = '',
  background = 'default',
  padding = 'lg',
  animated = true,
  animation = 'fadeInUp',
  id,
}) => {
  const backgroundClasses = {
    default: 'bg-white',
    gray: 'bg-neutral-50',
    gradient: 'bg-gradient-to-br from-primary-50 to-secondary-50',
    primary: 'bg-primary-500 text-white',
    dark: 'bg-neutral-900 text-white',
  };

  const paddingClasses = {
    sm: 'py-12 md:py-16',
    md: 'py-16 md:py-20',
    lg: 'py-20 md:py-24',
    xl: 'py-24 md:py-32',
  };

  const sectionClasses = `${backgroundClasses[background]} ${paddingClasses[padding]} ${className}`;
  const containerClasses = `container mx-auto px-4 md:px-6 lg:px-8 ${containerClassName}`;

  const content = (
    <section className={sectionClasses} id={id}>
      <div className={containerClasses}>{children}</div>
    </section>
  );

  if (animated) {
    return <AnimatedSection animation={animation}>{content}</AnimatedSection>;
  }

  return content;
};

export default SectionContainer;
