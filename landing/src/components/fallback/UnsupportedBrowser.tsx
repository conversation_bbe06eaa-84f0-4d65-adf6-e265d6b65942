import React from 'react';
import { safeIncludes } from '@/utils/stringUtils';

interface UnsupportedBrowserProps {
  capabilities?: {
    userAgent: string;
    chrome: number | null;
    firefox: number | null;
    safari: number | null;
    edge: number | null;
  };
}

const UnsupportedBrowser: React.FC<UnsupportedBrowserProps> = ({ capabilities }) => {
  const recommendedBrowsers = [
    {
      name: 'Google Chrome',
      version: '60+',
      downloadUrl: 'https://www.google.com/chrome/',
      icon: '🌐'
    },
    {
      name: 'Mozilla Firefox',
      version: '55+',
      downloadUrl: 'https://www.mozilla.org/firefox/',
      icon: '🦊'
    },
    {
      name: 'Safari',
      version: '12+',
      downloadUrl: 'https://www.apple.com/safari/',
      icon: '🧭'
    },
    {
      name: 'Microsoft Edge',
      version: '79+',
      downloadUrl: 'https://www.microsoft.com/edge/',
      icon: '🔷'
    }
  ];

  const getCurrentBrowserInfo = () => {
    if (!capabilities) return null;

    const { userAgent, chrome, firefox, safari, edge } = capabilities;

    if (chrome) return { name: 'Chrome', version: chrome, required: 60 };
    if (firefox) return { name: 'Firefox', version: firefox, required: 55 };
    if (safari) return { name: 'Safari', version: safari, required: 12 };
    if (edge) return { name: 'Edge', version: edge, required: 79 };

    // Попытка определить IE
    if (safeIncludes(userAgent, 'MSIE') || safeIncludes(userAgent, 'Trident')) {
      return { name: 'Internet Explorer', version: 0, required: 'не поддерживается' };
    }

    return { name: 'Неизвестный браузер', version: 0, required: 'обновление' };
  };

  const currentBrowser = getCurrentBrowserInfo();

  return (
    <div style={{
      minHeight: '100vh',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      background: 'linear-gradient(135deg, #007acc 0%, #0ea5e9 50%, #005a9e 100%)',
      color: 'white',
      fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
      padding: '20px'
    }}>
      <div style={{
        maxWidth: '600px',
        textAlign: 'center',
        background: 'rgba(255, 255, 255, 0.1)',
        backdropFilter: 'blur(10px)',
        borderRadius: '20px',
        padding: '40px',
        border: '1px solid rgba(255, 255, 255, 0.2)'
      }}>
        {/* Иконка */}
        <div style={{
          fontSize: '64px',
          marginBottom: '20px'
        }}>
          ⚠️
        </div>

        {/* Заголовок */}
        <h1 style={{
          fontSize: '32px',
          fontWeight: 'bold',
          marginBottom: '16px',
          margin: '0 0 16px 0'
        }}>
          Браузер не поддерживается
        </h1>

        {/* Описание проблемы */}
        <p style={{
          fontSize: '18px',
          marginBottom: '24px',
          opacity: 0.9,
          lineHeight: 1.6
        }}>
          {currentBrowser ? (
            <>
              Ваш браузер <strong>{currentBrowser.name} {currentBrowser.version}</strong> не поддерживает
              современные веб-технологии, необходимые для работы UnveilVPN.
            </>
          ) : (
            'Ваш браузер не поддерживает современные веб-технологии, необходимые для работы UnveilVPN.'
          )}
        </p>

        {/* Информация о требованиях */}
        <div style={{
          background: 'rgba(255, 255, 255, 0.1)',
          borderRadius: '12px',
          padding: '20px',
          marginBottom: '32px',
          textAlign: 'left'
        }}>
          <h3 style={{
            fontSize: '18px',
            fontWeight: '600',
            marginBottom: '12px',
            margin: '0 0 12px 0'
          }}>
            Необходимые возможности:
          </h3>
          <ul style={{
            listStyle: 'none',
            padding: 0,
            margin: 0
          }}>
            <li style={{ marginBottom: '8px' }}>✅ ES2015 (ES6) поддержка</li>
            <li style={{ marginBottom: '8px' }}>✅ Promise API</li>
            <li style={{ marginBottom: '8px' }}>✅ CSS Flexbox</li>
            <li style={{ marginBottom: '8px' }}>✅ Local Storage</li>
            <li style={{ marginBottom: '8px' }}>✅ Современные CSS возможности</li>
          </ul>
        </div>

        {/* Рекомендуемые браузеры */}
        <h3 style={{
          fontSize: '20px',
          fontWeight: '600',
          marginBottom: '20px',
          margin: '0 0 20px 0'
        }}>
          Рекомендуемые браузеры:
        </h3>

        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(120px, 1fr))',
          gap: '16px',
          marginBottom: '32px'
        }}>
          {recommendedBrowsers.map((browser) => (
            <a
              key={browser.name}
              href={browser.downloadUrl}
              target="_blank"
              rel="noopener noreferrer"
              style={{
                display: 'block',
                background: 'rgba(255, 255, 255, 0.1)',
                borderRadius: '12px',
                padding: '16px',
                textDecoration: 'none',
                color: 'white',
                transition: 'all 0.3s ease',
                border: '1px solid rgba(255, 255, 255, 0.2)'
              }}
              onMouseOver={(e) => {
                e.currentTarget.style.background = 'rgba(255, 255, 255, 0.2)';
                e.currentTarget.style.transform = 'translateY(-2px)';
              }}
              onMouseOut={(e) => {
                e.currentTarget.style.background = 'rgba(255, 255, 255, 0.1)';
                e.currentTarget.style.transform = 'translateY(0)';
              }}
            >
              <div style={{ fontSize: '24px', marginBottom: '8px' }}>
                {browser.icon}
              </div>
              <div style={{ fontSize: '14px', fontWeight: '600' }}>
                {browser.name}
              </div>
              <div style={{ fontSize: '12px', opacity: 0.8 }}>
                {browser.version}
              </div>
            </a>
          ))}
        </div>

        {/* Дополнительная информация */}
        <div style={{
          fontSize: '14px',
          opacity: 0.8,
          lineHeight: 1.5
        }}>
          <p style={{ margin: '0 0 8px 0' }}>
            После обновления браузера перезагрузите эту страницу.
          </p>
          <p style={{ margin: 0 }}>
            Если проблема сохраняется, обратитесь в службу поддержки.
          </p>
        </div>

        {/* Кнопка перезагрузки */}
        <button
          onClick={() => window.location.reload()}
          style={{
            marginTop: '24px',
            background: 'rgba(255, 255, 255, 0.2)',
            border: '1px solid rgba(255, 255, 255, 0.3)',
            borderRadius: '8px',
            color: 'white',
            padding: '12px 24px',
            fontSize: '16px',
            fontWeight: '600',
            cursor: 'pointer',
            transition: 'all 0.3s ease'
          }}
          onMouseOver={(e) => {
            e.currentTarget.style.background = 'rgba(255, 255, 255, 0.3)';
          }}
          onMouseOut={(e) => {
            e.currentTarget.style.background = 'rgba(255, 255, 255, 0.2)';
          }}
        >
          🔄 Перезагрузить страницу
        </button>
      </div>
    </div>
  );
};

export default UnsupportedBrowser;
