import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronDown, Search, MessageCircle } from 'lucide-react';
import { Card, Button } from '@/components/ui';
import { useScrollAnimation } from '@/hooks';
import apiService from '@/services/api';
import type { FAQItem } from '@/types';

const FAQSection: React.FC = () => {
  const { ref, isVisible } = useScrollAnimation();
  const [faqItems, setFaqItems] = useState<FAQItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [openItems, setOpenItems] = useState<Set<string>>(new Set());
  const [searchQuery, setSearchQuery] = useState('');

  useEffect(() => {
    const fetchFAQ = async () => {
      try {
        const data = await apiService.getFAQ();
        setFaqItems(data);
      } catch (error) {
        console.error('Error fetching FAQ:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchFAQ();
  }, []);

  const toggleItem = (id: string) => {
    const newOpenItems = new Set(openItems);
    if (newOpenItems.has(id)) {
      newOpenItems.delete(id);
    } else {
      newOpenItems.add(id);
    }
    setOpenItems(newOpenItems);
  };

  const filteredFAQ = faqItems.filter(
    (item) =>
      item.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
      item.answer.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const categories = [
    { id: 'all', name: 'Все вопросы' },
    { id: 'general', name: 'Общие' },
    { id: 'trial', name: 'Пробный период' },
    { id: 'payment', name: 'Оплата' },
    { id: 'devices', name: 'Устройства' },
    { id: 'refund', name: 'Возврат' },
  ];

  const [selectedCategory, setSelectedCategory] = useState('all');

  const categoryFilteredFAQ = filteredFAQ.filter(
    (item) => selectedCategory === 'all' || item.category === selectedCategory
  );

  if (loading) {
    return (
      <section className="py-20 bg-dark-900">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="animate-pulse">
              <div className="h-8 bg-dark-700 rounded w-64 mx-auto mb-4"></div>
              <div className="h-4 bg-dark-700 rounded w-96 mx-auto"></div>
            </div>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section id="faq" className="py-20 bg-dark-900" ref={ref}>
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          animate={isVisible ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.8 }}
        >
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-6">
            Часто задаваемые
            <span className="bg-gradient-to-r from-primary-400 to-primary-600 bg-clip-text text-transparent">
              {' '}вопросы
            </span>
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto mb-8">
            Найдите ответы на самые популярные вопросы о UnveilVPN
          </p>

          {/* Search */}
          <div className="max-w-md mx-auto relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              placeholder="Поиск по вопросам..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-3 bg-dark-800 border border-dark-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            />
          </div>
        </motion.div>

        {/* Category Filter */}
        <motion.div
          className="flex flex-wrap justify-center gap-2 mb-12"
          initial={{ opacity: 0, y: 20 }}
          animate={isVisible ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          {categories.map((category) => (
            <button
              key={category.id}
              onClick={() => setSelectedCategory(category.id)}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-all ${
                selectedCategory === category.id
                  ? 'bg-primary-600 text-white'
                  : 'bg-dark-800 text-gray-400 hover:text-white hover:bg-dark-700'
              }`}
            >
              {category.name}
            </button>
          ))}
        </motion.div>

        {/* FAQ Items */}
        <motion.div
          className="max-w-4xl mx-auto space-y-4"
          initial={{ opacity: 0, y: 30 }}
          animate={isVisible ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.8, delay: 0.4 }}
        >
          {categoryFilteredFAQ.length === 0 ? (
            <Card className="text-center py-12">
              <p className="text-gray-400 text-lg">
                {searchQuery
                  ? 'По вашему запросу ничего не найдено'
                  : 'Вопросы в этой категории отсутствуют'}
              </p>
            </Card>
          ) : (
            categoryFilteredFAQ.map((item, index) => (
              <motion.div
                key={item.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                <Card className="overflow-hidden">
                  <button
                    onClick={() => toggleItem(item.id)}
                    className="w-full text-left p-6 flex items-center justify-between hover:bg-dark-700/50 transition-colors"
                  >
                    <h3 className="text-lg font-semibold text-white pr-4">
                      {item.question}
                    </h3>
                    <motion.div
                      animate={{ rotate: openItems.has(item.id) ? 180 : 0 }}
                      transition={{ duration: 0.2 }}
                    >
                      <ChevronDown className="w-5 h-5 text-gray-400 flex-shrink-0" />
                    </motion.div>
                  </button>

                  <AnimatePresence>
                    {openItems.has(item.id) && (
                      <motion.div
                        initial={{ height: 0, opacity: 0 }}
                        animate={{ height: 'auto', opacity: 1 }}
                        exit={{ height: 0, opacity: 0 }}
                        transition={{ duration: 0.3 }}
                        className="overflow-hidden"
                      >
                        <div className="px-6 pb-6 border-t border-dark-700">
                          <p className="text-gray-300 leading-relaxed pt-4">
                            {item.answer}
                          </p>
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </Card>
              </motion.div>
            ))
          )}
        </motion.div>

        {/* Contact Support */}
        <motion.div
          className="text-center mt-16"
          initial={{ opacity: 0, y: 30 }}
          animate={isVisible ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.8, delay: 0.6 }}
        >
          <Card className="max-w-2xl mx-auto bg-gradient-to-r from-primary-900/20 to-primary-800/20 border-primary-500/30">
            <div className="text-center">
              <MessageCircle className="w-12 h-12 text-primary-400 mx-auto mb-4" />
              <h3 className="text-xl font-bold text-white mb-4">
                Не нашли ответ на свой вопрос?
              </h3>
              <p className="text-gray-300 mb-6">
                Наша служба поддержки работает 24/7 и готова помочь вам с любыми вопросами
              </p>
              <Button
                variant="primary"
                onClick={() => {
                  window.open(
                    import.meta.env.VITE_SUPPORT_BOT_URL || 'https://t.me/unveilvpn_support_bot',
                    '_blank'
                  );
                }}
              >
                Связаться с поддержкой
              </Button>
            </div>
          </Card>
        </motion.div>
      </div>
    </section>
  );
};

export default FAQSection;
