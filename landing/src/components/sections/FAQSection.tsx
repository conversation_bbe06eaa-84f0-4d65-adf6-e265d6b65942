import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronDown, Search, MessageCircle } from 'lucide-react';
import { Card, Button, SectionContainer, AnimatedSection } from '@/components/ui';
import { FAQSkeleton } from '@/components/ui/Skeleton';

import { faqService } from '@/services/faqService';
import type { FAQItem, FAQCategory } from '@/types/api';

const FAQSection: React.FC = () => {
  const [faqItems, setFaqItems] = useState<FAQItem[]>([]);
  const [categories, setCategories] = useState<FAQCategory[]>([]);
  const [loading, setLoading] = useState(true);
  const [openItems, setOpenItems] = useState<Set<string>>(new Set());
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [searchLoading, setSearchLoading] = useState(false);



  useEffect(() => {
    const fetchFAQ = async () => {
      try {
        setLoading(true);
        const data = await faqService.getFAQ();

        setFaqItems(data.items);
        setCategories(data.categories);
      } catch (error) {
        console.error('Error fetching FAQ:', error);
        setFaqItems([]);
        setCategories([]); // Устанавливаем пустой массив при ошибке
      } finally {
        setLoading(false);
      }
    };

    fetchFAQ();
  }, []);

  const toggleItem = (id: string) => {
    const newOpenItems = new Set(openItems);
    if (newOpenItems.has(id)) {
      newOpenItems.delete(id);
    } else {
      newOpenItems.add(id);
    }
    setOpenItems(newOpenItems);
  };

  // Функция поиска с использованием API
  const handleSearch = async (query: string) => {
    setSearchQuery(query);

    if (!query.trim()) {
      // Если запрос пустой, загружаем все FAQ
      try {
        setSearchLoading(true);
        const data = await faqService.getFAQ({
          category: selectedCategory === 'all' ? undefined : selectedCategory
        });
        setFaqItems(data.items);
      } catch (error) {
        console.error('Error fetching FAQ:', error);
      } finally {
        setSearchLoading(false);
      }
      return;
    }

    try {
      setSearchLoading(true);
      const results = await faqService.searchFAQ(
        query,
        selectedCategory === 'all' ? undefined : selectedCategory
      );
      setFaqItems(results);
    } catch (error) {
      console.error('Error searching FAQ:', error);
    } finally {
      setSearchLoading(false);
    }
  };

  // Функция фильтрации по категории
  const handleCategoryChange = async (category: string) => {
    setSelectedCategory(category);

    try {
      setSearchLoading(true);
      const data = await faqService.getFAQ({
        query: searchQuery || undefined,
        category: category === 'all' ? undefined : category
      });
      setFaqItems(data.items);
    } catch (error) {
      console.error('Error filtering FAQ:', error);
    } finally {
      setSearchLoading(false);
    }
  };

  // Убеждаемся, что faqItems всегда является массивом
  const safeFaqItems = Array.isArray(faqItems) ? faqItems : [];

  // Создаем список категорий из API данных
  const allCategories = [
    { id: 'all', name: 'Все вопросы' },
    ...categories.map(cat => ({ id: cat.id, name: cat.name }))
  ];



  if (loading) {
    return (
      <SectionContainer id="faq" background="gray" padding="xl">
        {/* Section Header Skeleton */}
        <div className="text-center mb-16">
          <div className="h-12 bg-neutral-200 rounded w-80 mx-auto mb-6 animate-pulse"></div>
          <div className="h-6 bg-neutral-200 rounded w-96 mx-auto mb-8 animate-pulse"></div>

          {/* Search Skeleton */}
          <div className="max-w-md mx-auto relative mb-12">
            <div className="h-12 bg-neutral-200 rounded-lg animate-pulse"></div>
          </div>
        </div>

        {/* Category Filter Skeleton */}
        <div className="flex flex-wrap justify-center gap-2 mb-12">
          {[...Array(4)].map((_, index) => (
            <div key={index} className="w-20 h-8 bg-neutral-200 rounded-full animate-pulse"></div>
          ))}
        </div>

        {/* FAQ Items Skeleton */}
        <div className="max-w-4xl mx-auto space-y-4">
          {[...Array(6)].map((_, index) => (
            <FAQSkeleton key={index} />
          ))}
        </div>
      </SectionContainer>
    );
  }

  return (
    <SectionContainer id="faq" background="gray" padding="xl" animated={true} animation="fadeInUp">
      {/* Section Header */}
      <AnimatedSection animation="fadeInUp" className="text-center mb-16">
        <h2 className="heading-lg text-neutral-900 mb-6">
          Часто задаваемые
          <span className="gradient-text"> вопросы</span>
        </h2>
        <p className="text-body max-w-3xl mx-auto mb-8">
          Найдите ответы на самые популярные вопросы о UnveilVPN
        </p>

        {/* Search */}
        <div className="max-w-md mx-auto relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 w-5 h-5" />
          <input
            type="text"
            placeholder="Поиск по вопросам..."
            value={searchQuery}
            onChange={(e) => handleSearch(e.target.value)}
            className="w-full pl-10 pr-4 py-3 bg-white border border-neutral-300 rounded-lg text-neutral-900 placeholder-neutral-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 shadow-sm"
          />
          {searchLoading && (
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
              <div className="w-5 h-5 border-2 border-primary-500 border-t-transparent rounded-full animate-spin"></div>
            </div>
          )}
        </div>
      </AnimatedSection>

      {/* Category Filter */}
      <AnimatedSection
        animation="fadeInUp"
        delay={0.2}
        className="flex flex-wrap justify-center gap-2 mb-12"
      >
        {allCategories.map((category) => (
          <motion.button
            key={category.id}
            onClick={() => handleCategoryChange(category.id)}
            className={`px-4 py-2 rounded-full text-sm font-medium transition-all ${
              selectedCategory === category.id
                ? 'bg-gradient-button text-white shadow-button'
                : 'bg-white text-neutral-600 hover:text-primary-600 hover:bg-primary-50 border border-neutral-200'
            }`}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            {category.name}
          </motion.button>
        ))}
      </AnimatedSection>

      {/* FAQ Items */}
      <AnimatedSection animation="fadeInUp" delay={0.4} className="max-w-4xl mx-auto space-y-4">
        {safeFaqItems.length === 0 ? (
          <Card className="text-center py-12" padding="xl">
            <p className="text-neutral-600 text-lg">
              {searchQuery
                ? 'По вашему запросу ничего не найдено'
                : 'Вопросы в этой категории отсутствуют'}
            </p>
          </Card>
        ) : (
          safeFaqItems.map((item, index) => (
            <AnimatedSection key={item.id} animation="fadeInUp" delay={0.4 + index * 0.1}>
              <Card className="overflow-hidden" hover={true} padding="none">
                <button
                  onClick={() => toggleItem(item.id)}
                  className="w-full text-left p-6 flex items-center justify-between hover:bg-neutral-50 transition-colors"
                >
                  <h3 className="text-lg font-semibold text-neutral-900 pr-4">{item.question}</h3>
                  <motion.div
                    animate={{ rotate: openItems.has(item.id) ? 180 : 0 }}
                    transition={{ duration: 0.2 }}
                  >
                    <ChevronDown className="w-5 h-5 text-neutral-400 flex-shrink-0" />
                  </motion.div>
                </button>

                <AnimatePresence>
                  {openItems.has(item.id) && (
                    <motion.div
                      initial={{ height: 0, opacity: 0 }}
                      animate={{ height: 'auto', opacity: 1 }}
                      exit={{ height: 0, opacity: 0 }}
                      transition={{ duration: 0.3 }}
                      className="overflow-hidden"
                    >
                      <div className="px-6 pb-6 border-t border-neutral-200">
                        <p className="text-neutral-600 leading-relaxed pt-4">{item.answer}</p>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </Card>
            </AnimatedSection>
          ))
        )}
      </AnimatedSection>

      {/* Contact Support */}
      <AnimatedSection animation="fadeInUp" delay={0.6} className="text-center mt-16">
        <Card variant="featured" padding="xl" className="max-w-2xl mx-auto text-center">
          <MessageCircle className="w-12 h-12 text-primary-600 mx-auto mb-4" />
          <h3 className="heading-md text-neutral-900 mb-4">Не нашли ответ на свой вопрос?</h3>
          <p className="text-body mb-6">
            Наша служба поддержки работает 24/7 и готова помочь вам с любыми вопросами
          </p>
          <Button
            variant="primary"
            size="lg"
            className="shadow-button hover:shadow-button-hover"
            onClick={() => {
              window.open(
                import.meta.env.VITE_SUPPORT_BOT_URL || 'https://t.me/unveilvpn_support_bot',
                '_blank'
              );
            }}
          >
            Связаться с поддержкой
          </Button>
        </Card>
      </AnimatedSection>
    </SectionContainer>
  );
};

export default FAQSection;
