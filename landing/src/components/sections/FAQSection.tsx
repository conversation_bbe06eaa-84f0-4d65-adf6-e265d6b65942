import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronDown, Search, MessageCircle } from 'lucide-react';
import { Card, Button, SectionContainer, AnimatedSection } from '@/components/ui';
import { safeIncludes, safeLowerCase } from '@/utils/stringUtils';
import apiService from '@/services/api';
import type { FAQItem } from '@/types';

const FAQSection: React.FC = () => {
  const [faqItems, setFaqItems] = useState<FAQItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [openItems, setOpenItems] = useState<Set<string>>(new Set());
  const [searchQuery, setSearchQuery] = useState('');

  // Убеждаемся, что faqItems всегда является массивом
  const safeFaqItems = Array.isArray(faqItems) ? faqItems : [];

  useEffect(() => {
    const fetchFAQ = async () => {
      try {
        const data = await apiService.getFAQ();

        // Убеждаемся, что data является массивом
        if (Array.isArray(data)) {
          setFaqItems(data);
        } else {
          setFaqItems([]);
        }
      } catch (error) {
        console.error('Error fetching FAQ:', error);
        setFaqItems([]); // Устанавливаем пустой массив при ошибке
      } finally {
        setLoading(false);
      }
    };

    fetchFAQ();
  }, []);

  const toggleItem = (id: string) => {
    const newOpenItems = new Set(openItems);
    if (newOpenItems.has(id)) {
      newOpenItems.delete(id);
    } else {
      newOpenItems.add(id);
    }
    setOpenItems(newOpenItems);
  };

  // Безопасная фильтрация с использованием safeFaqItems
  const filteredFAQ = (() => {
    try {
      return safeFaqItems.filter(
        (item) =>
          safeIncludes(safeLowerCase(item?.question), safeLowerCase(searchQuery)) ||
          safeIncludes(safeLowerCase(item?.answer), safeLowerCase(searchQuery))
      );
    } catch (error) {
      console.error('FAQSection: Error filtering FAQ items:', error);
      return [];
    }
  })();

  const categories = [
    { id: 'all', name: 'Все вопросы' },
    { id: 'general', name: 'Общие' },
    { id: 'trial', name: 'Пробный период' },
    { id: 'payment', name: 'Оплата' },
    { id: 'devices', name: 'Устройства' },
    { id: 'refund', name: 'Возврат' },
  ];

  const [selectedCategory, setSelectedCategory] = useState('all');

  // Безопасная фильтрация по категориям
  const safeFilteredFAQ = Array.isArray(filteredFAQ) ? filteredFAQ : [];
  const categoryFilteredFAQ = (() => {
    try {
      return safeFilteredFAQ.filter(
        (item) => selectedCategory === 'all' || item?.category === selectedCategory
      );
    } catch (error) {
      console.error('FAQSection: Error filtering FAQ by category:', error);
      return [];
    }
  })();

  if (loading) {
    return (
      <SectionContainer id="faq" background="gray" padding="xl">
        <div className="text-center">
          <div className="animate-pulse">
            <div className="h-8 bg-neutral-200 rounded w-64 mx-auto mb-4"></div>
            <div className="h-4 bg-neutral-200 rounded w-96 mx-auto"></div>
          </div>
        </div>
      </SectionContainer>
    );
  }

  return (
    <SectionContainer id="faq" background="gray" padding="xl" animated={true} animation="fadeInUp">
      {/* Section Header */}
      <AnimatedSection animation="fadeInUp" className="text-center mb-16">
        <h2 className="heading-lg text-neutral-900 mb-6">
          Часто задаваемые
          <span className="gradient-text"> вопросы</span>
        </h2>
        <p className="text-body max-w-3xl mx-auto mb-8">
          Найдите ответы на самые популярные вопросы о UnveilVPN
        </p>

        {/* Search */}
        <div className="max-w-md mx-auto relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 w-5 h-5" />
          <input
            type="text"
            placeholder="Поиск по вопросам..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-10 pr-4 py-3 bg-white border border-neutral-300 rounded-lg text-neutral-900 placeholder-neutral-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 shadow-sm"
          />
        </div>
      </AnimatedSection>

      {/* Category Filter */}
      <AnimatedSection
        animation="fadeInUp"
        delay={0.2}
        className="flex flex-wrap justify-center gap-2 mb-12"
      >
        {categories.map((category) => (
          <motion.button
            key={category.id}
            onClick={() => setSelectedCategory(category.id)}
            className={`px-4 py-2 rounded-full text-sm font-medium transition-all ${
              selectedCategory === category.id
                ? 'bg-gradient-button text-white shadow-button'
                : 'bg-white text-neutral-600 hover:text-primary-600 hover:bg-primary-50 border border-neutral-200'
            }`}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            {category.name}
          </motion.button>
        ))}
      </AnimatedSection>

      {/* FAQ Items */}
      <AnimatedSection animation="fadeInUp" delay={0.4} className="max-w-4xl mx-auto space-y-4">
        {categoryFilteredFAQ.length === 0 ? (
          <Card className="text-center py-12" padding="xl">
            <p className="text-neutral-600 text-lg">
              {searchQuery
                ? 'По вашему запросу ничего не найдено'
                : 'Вопросы в этой категории отсутствуют'}
            </p>
          </Card>
        ) : (
          categoryFilteredFAQ.map((item, index) => (
            <AnimatedSection key={item.id} animation="fadeInUp" delay={0.4 + index * 0.1}>
              <Card className="overflow-hidden" hover={true} padding="none">
                <button
                  onClick={() => toggleItem(item.id)}
                  className="w-full text-left p-6 flex items-center justify-between hover:bg-neutral-50 transition-colors"
                >
                  <h3 className="text-lg font-semibold text-neutral-900 pr-4">{item.question}</h3>
                  <motion.div
                    animate={{ rotate: openItems.has(item.id) ? 180 : 0 }}
                    transition={{ duration: 0.2 }}
                  >
                    <ChevronDown className="w-5 h-5 text-neutral-400 flex-shrink-0" />
                  </motion.div>
                </button>

                <AnimatePresence>
                  {openItems.has(item.id) && (
                    <motion.div
                      initial={{ height: 0, opacity: 0 }}
                      animate={{ height: 'auto', opacity: 1 }}
                      exit={{ height: 0, opacity: 0 }}
                      transition={{ duration: 0.3 }}
                      className="overflow-hidden"
                    >
                      <div className="px-6 pb-6 border-t border-neutral-200">
                        <p className="text-neutral-600 leading-relaxed pt-4">{item.answer}</p>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </Card>
            </AnimatedSection>
          ))
        )}
      </AnimatedSection>

      {/* Contact Support */}
      <AnimatedSection animation="fadeInUp" delay={0.6} className="text-center mt-16">
        <Card variant="featured" padding="xl" className="max-w-2xl mx-auto text-center">
          <MessageCircle className="w-12 h-12 text-primary-600 mx-auto mb-4" />
          <h3 className="heading-md text-neutral-900 mb-4">Не нашли ответ на свой вопрос?</h3>
          <p className="text-body mb-6">
            Наша служба поддержки работает 24/7 и готова помочь вам с любыми вопросами
          </p>
          <Button
            variant="primary"
            size="lg"
            className="shadow-button hover:shadow-button-hover"
            onClick={() => {
              window.open(
                import.meta.env.VITE_SUPPORT_BOT_URL || 'https://t.me/unveilvpn_support_bot',
                '_blank'
              );
            }}
          >
            Связаться с поддержкой
          </Button>
        </Card>
      </AnimatedSection>
    </SectionContainer>
  );
};

export default FAQSection;
