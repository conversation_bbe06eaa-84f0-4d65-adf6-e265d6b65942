import React from 'react';
import { motion } from 'framer-motion';
import { Shield, Zap, Globe, Lock, Smartphone, Headphones } from 'lucide-react';
import { Card } from '@/components/ui';
import { useScrollAnimation } from '@/hooks';

const FeaturesSection: React.FC = () => {
  const { ref, isVisible } = useScrollAnimation();

  const features = [
    {
      icon: Shield,
      title: 'Не заблокируют',
      description: 'Современные протоколы обхода блокировок обеспечивают стабильное соединение даже в условиях ограничений.',
      color: 'text-blue-400',
      bgColor: 'bg-blue-500/10',
    },
    {
      icon: Zap,
      title: 'Высокая скорость',
      description: 'Оптимизированные серверы обеспечивают скорость до 1 Гбит/с без потери качества соединения.',
      color: 'text-yellow-400',
      bgColor: 'bg-yellow-500/10',
    },
    {
      icon: Globe,
      title: '10+ стран',
      description: 'Серверы в 10+ странах мира для доступа к любому контенту и обхода географических ограничений.',
      color: 'text-green-400',
      bgColor: 'bg-green-500/10',
    },
    {
      icon: Lock,
      title: 'Политика No-Logs',
      description: 'Мы не ведем логи вашей активности. Ваша приватность - наш главный приоритет.',
      color: 'text-purple-400',
      bgColor: 'bg-purple-500/10',
    },
    {
      icon: Smartphone,
      title: 'Все устройства',
      description: 'Поддержка Windows, macOS, iOS, Android, Linux. До 10 устройств одновременно.',
      color: 'text-pink-400',
      bgColor: 'bg-pink-500/10',
    },
    {
      icon: Headphones,
      title: '24/7 поддержка',
      description: 'Круглосуточная техническая поддержка через Telegram бот. Быстрые ответы на любые вопросы.',
      color: 'text-cyan-400',
      bgColor: 'bg-cyan-500/10',
    },
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: { opacity: 1, y: 0 },
  };

  return (
    <section id="features" className="py-20 bg-dark-900" ref={ref}>
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          animate={isVisible ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.8 }}
        >
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-6">
            Почему выбирают
            <span className="bg-gradient-to-r from-primary-400 to-primary-600 bg-clip-text text-transparent">
              {' '}UnveilVPN
            </span>
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Мы предоставляем лучшие в классе функции безопасности и производительности 
            для защиты вашей цифровой жизни.
          </p>
        </motion.div>

        {/* Features Grid */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
          variants={containerVariants}
          initial="hidden"
          animate={isVisible ? "visible" : "hidden"}
          transition={{ staggerChildren: 0.1, duration: 0.6 }}
        >
          {features.map((feature, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
              transition={{ duration: 0.6, ease: [0.4, 0, 0.2, 1] }}
            >
              <Card className="h-full group cursor-pointer">
                <div className="text-center">
                  {/* Icon */}
                  <div className={`inline-flex items-center justify-center w-16 h-16 rounded-2xl ${feature.bgColor} mb-6 group-hover:scale-110 transition-transform duration-300`}>
                    <feature.icon className={`w-8 h-8 ${feature.color}`} />
                  </div>

                  {/* Title */}
                  <h3 className="text-xl font-bold text-white mb-4 group-hover:text-primary-400 transition-colors">
                    {feature.title}
                  </h3>

                  {/* Description */}
                  <p className="text-gray-400 leading-relaxed">
                    {feature.description}
                  </p>
                </div>
              </Card>
            </motion.div>
          ))}
        </motion.div>

        {/* Bottom CTA */}
        <motion.div
          className="text-center mt-16"
          initial={{ opacity: 0, y: 30 }}
          animate={isVisible ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.8, delay: 0.6 }}
        >
          <div className="bg-gradient-to-r from-primary-600/20 to-primary-800/20 border border-primary-600/30 rounded-2xl p-8 max-w-4xl mx-auto">
            <h3 className="text-2xl md:text-3xl font-bold text-white mb-4">
              Готовы защитить свою приватность?
            </h3>
            <p className="text-gray-300 mb-6">
              Присоединяйтесь к пользователям, которые уже доверяют UnveilVPN
            </p>
            <motion.button
              className="btn-primary"
              onClick={() => {
                const element = document.querySelector('#pricing');
                if (element) {
                  element.scrollIntoView({ behavior: 'smooth' });
                }
              }}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              Начать бесплатный период
            </motion.button>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default FeaturesSection;
