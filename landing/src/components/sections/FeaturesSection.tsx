import React from 'react';
import { motion } from 'framer-motion';
import { Shield, Zap, Globe, Lock, Smartphone, Headphones } from 'lucide-react';
import { Card, Button, SectionContainer, AnimatedSection } from '@/components/ui';

const FeaturesSection: React.FC = () => {

  const features = [
    {
      icon: Shield,
      title: 'Не заблокируют',
      description: 'Современные протоколы обхода блокировок обеспечивают стабильное соединение даже в условиях ограничений.',
      gradient: 'from-primary-500 to-primary-600',
    },
    {
      icon: Zap,
      title: 'Высокая скорость',
      description: 'Оптимизированные серверы обеспечивают скорость до 1 Гбит/с без потери качества соединения.',
      gradient: 'from-warning-500 to-warning-600',
    },
    {
      icon: Globe,
      title: '10+ стран',
      description: 'Серверы в 10+ странах мира для доступа к любому контенту и обхода географических ограничений.',
      gradient: 'from-success-500 to-success-600',
    },
    {
      icon: Lock,
      title: 'Политика No-Logs',
      description: 'Мы не ведем логи вашей активности. Ваша приватность - наш главный приоритет.',
      gradient: 'from-secondary-500 to-secondary-600',
    },
    {
      icon: Smartphone,
      title: 'Все устройства',
      description: 'Поддержка Windows, macOS, iOS, Android, Linux. До 10 устройств одновременно.',
      gradient: 'from-accent-500 to-accent-600',
    },
    {
      icon: Headphones,
      title: '24/7 поддержка',
      description: 'Круглосуточная техническая поддержка через Telegram бот. Быстрые ответы на любые вопросы.',
      gradient: 'from-primary-600 to-secondary-500',
    },
  ];



  return (
    <SectionContainer
      id="features"
      background="gray"
      padding="xl"
      animated={true}
      animation="fadeInUp"
    >
      {/* Header */}
      <AnimatedSection animation="fadeInUp" className="text-center mb-16">
        <h2 className="heading-lg text-neutral-900 mb-6">
          Почему выбирают
          <span className="gradient-text"> UnveilVPN</span>
        </h2>
        <p className="text-body max-w-3xl mx-auto">
          Мы предоставляем лучшие в классе функции безопасности и производительности
          для защиты вашей цифровой жизни.
        </p>
      </AnimatedSection>

      {/* Features Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {features.map((feature, index) => (
          <AnimatedSection
            key={index}
            animation="fadeInUp"
            delay={index * 0.1}
          >
            <Card
              className="h-full group cursor-pointer hover:shadow-card-hover transition-all duration-300"
              hover={true}
              padding="lg"
            >
              <div className="text-center">
                {/* Icon */}
                <div className={`inline-flex items-center justify-center w-16 h-16 rounded-2xl bg-gradient-to-br ${feature.gradient} mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg`}>
                  <feature.icon className="w-8 h-8 text-white" />
                </div>

                {/* Title */}
                <h3 className="heading-md text-neutral-900 mb-4 group-hover:text-primary-600 transition-colors">
                  {feature.title}
                </h3>

                {/* Description */}
                <p className="text-body">
                  {feature.description}
                </p>
              </div>
            </Card>
          </AnimatedSection>
        ))}
      </div>

      {/* Bottom CTA */}
      <AnimatedSection animation="fadeInUp" delay={0.6} className="text-center mt-16">
        <Card
          variant="featured"
          padding="xl"
          className="max-w-4xl mx-auto text-center"
        >
          <h3 className="heading-md text-neutral-900 mb-4">
            Готовы защитить свою приватность?
          </h3>
          <p className="text-body mb-8">
            Присоединяйтесь к пользователям, которые уже доверяют UnveilVPN
          </p>
          <Button
            variant="primary"
            size="lg"
            onClick={() => {
              const element = document.querySelector('#pricing');
              if (element) {
                element.scrollIntoView({ behavior: 'smooth' });
              }
            }}
            className="shadow-button hover:shadow-button-hover"
          >
            Начать бесплатный период
          </Button>
        </Card>
      </AnimatedSection>
    </SectionContainer>
  );
};

export default FeaturesSection;
