import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Gift, Clock, Shield, Zap, Check } from 'lucide-react';
import { But<PERSON>, Card, SectionContainer, AnimatedSection } from '@/components/ui';
import { apiClient } from '@/services/apiClient';
import type { TrialInfo } from '@/types';

const TrialSection: React.FC = () => {
  const [trialInfo, setTrialInfo] = useState<TrialInfo | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchTrialInfo = async () => {
      try {
        const response = await apiClient.get<TrialInfo>('/public/trial-info');
        if (response.success && response.data) {
          setTrialInfo(response.data);
        } else {
          // Fallback данные
          setTrialInfo({
            enabled: true,
            duration_days: 7,
            description: '7 дней бесплатного использования всех возможностей VPN',
            terms: ['Полный доступ ко всем функциям', 'Без ограничений скорости', 'Техподдержка 24/7']
          });
        }
      } catch (error) {
        console.error('Error fetching trial info:', error);
        // Fallback данные при ошибке
        setTrialInfo({
          enabled: true,
          duration_days: 7,
          description: '7 дней бесплатного использования всех возможностей VPN',
          terms: ['Полный доступ ко всем функциям', 'Без ограничений скорости', 'Техподдержка 24/7']
        });
      } finally {
        setLoading(false);
      }
    };

    fetchTrialInfo();
  }, []);

  const benefits = [
    {
      icon: Shield,
      title: 'Полная защита',
      description: 'Все функции безопасности доступны с первого дня',
    },
    {
      icon: Zap,
      title: 'Максимальная скорость',
      description: 'Доступ к самым быстрым серверам без ограничений',
    },
    {
      icon: Clock,
      title: 'Без обязательств',
      description: 'Отмените в любое время в течение пробного периода',
    },
  ];

  if (loading) {
    return (
      <SectionContainer id="trial" background="default" padding="xl">
        <div className="text-center">
          <div className="animate-pulse">
            <div className="h-8 bg-neutral-200 rounded w-64 mx-auto mb-4"></div>
            <div className="h-4 bg-neutral-200 rounded w-96 mx-auto"></div>
          </div>
        </div>
      </SectionContainer>
    );
  }

  if (!trialInfo?.enabled) {
    return null;
  }

  return (
    <SectionContainer
      id="trial"
      background="default"
      padding="xl"
      animated={true}
      animation="fadeInUp"
    >
      <div className="max-w-6xl mx-auto">
        {/* Main Trial Card */}
        <AnimatedSection animation="fadeInUp" className="mb-12">
          <Card variant="featured" padding="xl" className="overflow-hidden">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              {/* Left Content */}
              <div>
                <div className="flex items-center space-x-4 mb-6">
                  <div className="w-16 h-16 bg-gradient-to-br from-primary-500 to-primary-600 rounded-2xl flex items-center justify-center shadow-lg">
                    <Gift className="w-8 h-8 text-white" />
                  </div>
                  <div>
                    <h3 className="heading-md text-neutral-900">
                      {trialInfo.duration_days} дней бесплатно
                    </h3>
                    <p className="text-primary-600 font-medium">Полный доступ ко всем функциям</p>
                  </div>
                </div>

                <p className="text-body mb-8">{trialInfo.description}</p>

                <div className="space-y-4 mb-8">
                  {trialInfo.terms.map((term, index) => (
                    <div key={index} className="flex items-center space-x-3">
                      <Check className="w-5 h-5 text-success-500 flex-shrink-0" />
                      <span className="text-neutral-600">{term}</span>
                    </div>
                  ))}
                </div>

                <Button
                  variant="primary"
                  size="xl"
                  onClick={() => {
                    const element = document.querySelector('#pricing');
                    if (element) {
                      element.scrollIntoView({ behavior: 'smooth' });
                    }
                  }}
                  className="w-full sm:w-auto shadow-button hover:shadow-button-hover"
                >
                  Начать бесплатный период
                </Button>
              </div>

              {/* Right Visual */}
              <div className="relative">
                <div className="bg-gradient-to-br from-primary-50 to-primary-100 rounded-3xl p-8 border border-primary-200 relative overflow-hidden">
                  <div className="text-center relative z-10">
                    <div className="text-6xl font-bold text-primary-600 mb-2">
                      {trialInfo.duration_days}
                    </div>
                    <div className="text-xl text-neutral-900 mb-4">дней бесплатно</div>
                    <div className="text-neutral-600">Затем от 299₽/месяц</div>
                  </div>

                  {/* Floating Elements */}
                  <motion.div
                    className="absolute -top-4 -right-4 w-8 h-8 bg-primary-400 rounded-full"
                    animate={{ y: [0, -10, 0] }}
                    transition={{ duration: 3, repeat: Infinity }}
                  />
                  <motion.div
                    className="absolute -bottom-4 -left-4 w-6 h-6 bg-primary-300 rounded-full"
                    animate={{ y: [0, 10, 0] }}
                    transition={{ duration: 2, repeat: Infinity, delay: 1 }}
                  />
                  <motion.div
                    className="absolute top-1/2 -left-6 w-4 h-4 bg-primary-200 rounded-full"
                    animate={{ x: [0, 5, 0] }}
                    transition={{ duration: 4, repeat: Infinity, delay: 0.5 }}
                  />
                </div>
              </div>
            </div>
          </Card>
        </AnimatedSection>

        {/* Benefits Grid */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {benefits.map((benefit, index) => (
            <AnimatedSection key={index} animation="fadeInUp" delay={0.3 + index * 0.1}>
              <Card className="text-center h-full" hover={true} padding="lg">
                <div className="w-16 h-16 bg-gradient-to-br from-primary-500 to-primary-600 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg">
                  <benefit.icon className="w-8 h-8 text-white" />
                </div>
                <h4 className="heading-sm text-neutral-900 mb-4">{benefit.title}</h4>
                <p className="text-body">{benefit.description}</p>
              </Card>
            </AnimatedSection>
          ))}
        </div>

        {/* FAQ about Trial */}
        <AnimatedSection animation="fadeInUp" delay={0.6} className="mt-16 text-center">
          <h3 className="heading-md text-neutral-900 mb-8">
            Часто задаваемые вопросы о пробном периоде
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-4xl mx-auto">
            {[
              {
                question: 'Нужна ли банковская карта?',
                answer:
                  'Нет, для активации пробного периода банковская карта не требуется. Просто зарегистрируйтесь через Telegram бот.',
              },
              {
                question: 'Что происходит после окончания?',
                answer:
                  'После окончания пробного периода доступ автоматически прекращается. Для продолжения выберите подходящий тариф.',
              },
              {
                question: 'Есть ли ограничения?',
                answer:
                  'Никаких ограничений! Полный доступ ко всем серверам, безлимитный трафик и все функции премиум-аккаунта.',
              },
              {
                question: 'Можно ли отменить?',
                answer:
                  'Да, вы можете отменить пробный период в любое время через Telegram бот без каких-либо обязательств.',
              },
            ].map((faq, index) => (
              <Card key={index} className="text-left" padding="lg" hover={true}>
                <h4 className="font-semibold text-neutral-900 mb-3">{faq.question}</h4>
                <p className="text-body">{faq.answer}</p>
              </Card>
            ))}
          </div>
        </AnimatedSection>
      </div>
    </SectionContainer>
  );
};

export default TrialSection;
