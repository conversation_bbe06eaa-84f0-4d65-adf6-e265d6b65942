import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Gift, Clock, Shield, Zap } from 'lucide-react';
import { Button, Card } from '@/components/ui';
import { useScrollAnimation } from '@/hooks';
import apiService from '@/services/api';
import type { TrialInfo } from '@/types';

const TrialSection: React.FC = () => {
  const { ref, isVisible } = useScrollAnimation();
  const [trialInfo, setTrialInfo] = useState<TrialInfo | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchTrialInfo = async () => {
      try {
        const data = await apiService.getTrialInfo();
        setTrialInfo(data);
      } catch (error) {
        console.error('Error fetching trial info:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchTrialInfo();
  }, []);

  const benefits = [
    {
      icon: Shield,
      title: 'Полная защита',
      description: 'Все функции безопасности доступны с первого дня',
    },
    {
      icon: Zap,
      title: 'Максимальная скорость',
      description: 'Доступ к самым быстрым серверам без ограничений',
    },
    {
      icon: Clock,
      title: 'Без обязательств',
      description: 'Отмените в любое время в течение пробного периода',
    },
  ];

  if (loading) {
    return (
      <section className="py-20 bg-dark-800">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="animate-pulse">
              <div className="h-8 bg-dark-700 rounded w-64 mx-auto mb-4"></div>
              <div className="h-4 bg-dark-700 rounded w-96 mx-auto"></div>
            </div>
          </div>
        </div>
      </section>
    );
  }

  if (!trialInfo?.enabled) {
    return null;
  }

  return (
    <section id="trial" className="py-20 bg-dark-800" ref={ref}>
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          className="max-w-6xl mx-auto"
          initial={{ opacity: 0, y: 30 }}
          animate={isVisible ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.8 }}
        >
          {/* Main Trial Card */}
          <Card className="bg-gradient-to-r from-primary-900/30 to-primary-800/30 border-primary-500/50 mb-12">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
              {/* Left Content */}
              <div>
                <div className="flex items-center space-x-3 mb-6">
                  <div className="w-12 h-12 bg-primary-600 rounded-xl flex items-center justify-center">
                    <Gift className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h3 className="text-2xl font-bold text-white">
                      {trialInfo.duration_days} дней бесплатно
                    </h3>
                    <p className="text-primary-400">Полный доступ ко всем функциям</p>
                  </div>
                </div>

                <p className="text-xl text-gray-300 mb-6">
                  {trialInfo.description}
                </p>

                <div className="space-y-3 mb-8">
                  {trialInfo.terms.map((term, index) => (
                    <div key={index} className="flex items-center space-x-3">
                      <div className="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center">
                        <span className="text-white text-xs">✓</span>
                      </div>
                      <span className="text-gray-300">{term}</span>
                    </div>
                  ))}
                </div>

                <Button
                  variant="primary"
                  size="lg"
                  onClick={() => {
                    const element = document.querySelector('#pricing');
                    if (element) {
                      element.scrollIntoView({ behavior: 'smooth' });
                    }
                  }}
                  className="w-full sm:w-auto"
                >
                  Начать бесплатный период
                </Button>
              </div>

              {/* Right Visual */}
              <div className="relative">
                <div className="bg-gradient-to-br from-primary-600/20 to-primary-800/20 rounded-2xl p-8 border border-primary-500/30">
                  <div className="text-center">
                    <div className="text-6xl font-bold text-primary-400 mb-2">
                      {trialInfo.duration_days}
                    </div>
                    <div className="text-xl text-white mb-4">дней бесплатно</div>
                    <div className="text-gray-400">
                      Затем от 299₽/месяц
                    </div>
                  </div>

                  {/* Floating Elements */}
                  <div className="absolute -top-4 -right-4 w-8 h-8 bg-primary-500 rounded-full opacity-60"></div>
                  <div className="absolute -bottom-4 -left-4 w-6 h-6 bg-primary-400 rounded-full opacity-40"></div>
                  <div className="absolute top-1/2 -left-6 w-4 h-4 bg-primary-300 rounded-full opacity-30"></div>
                </div>
              </div>
            </div>
          </Card>

          {/* Benefits Grid */}
          <motion.div
            className="grid grid-cols-1 md:grid-cols-3 gap-8"
            initial={{ opacity: 0, y: 30 }}
            animate={isVisible ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
            transition={{ duration: 0.8, delay: 0.3 }}
          >
            {benefits.map((benefit, index) => (
              <motion.div
                key={index}
                whileHover={{ scale: 1.05 }}
                transition={{ type: 'spring', stiffness: 300, damping: 20 }}
              >
                <Card className="text-center h-full">
                  <div className="w-16 h-16 bg-primary-600/20 rounded-2xl flex items-center justify-center mx-auto mb-6">
                    <benefit.icon className="w-8 h-8 text-primary-400" />
                  </div>
                  <h4 className="text-xl font-bold text-white mb-4">{benefit.title}</h4>
                  <p className="text-gray-400">{benefit.description}</p>
                </Card>
              </motion.div>
            ))}
          </motion.div>

          {/* FAQ about Trial */}
          <motion.div
            className="mt-16 text-center"
            initial={{ opacity: 0, y: 30 }}
            animate={isVisible ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
            transition={{ duration: 0.8, delay: 0.6 }}
          >
            <h3 className="text-2xl font-bold text-white mb-8">
              Часто задаваемые вопросы о пробном периоде
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-4xl mx-auto">
              <Card className="text-left">
                <h4 className="font-semibold text-white mb-3">
                  Нужна ли банковская карта?
                </h4>
                <p className="text-gray-400">
                  Нет, для активации пробного периода банковская карта не требуется. 
                  Просто зарегистрируйтесь через Telegram бот.
                </p>
              </Card>

              <Card className="text-left">
                <h4 className="font-semibold text-white mb-3">
                  Что происходит после окончания?
                </h4>
                <p className="text-gray-400">
                  После окончания пробного периода доступ автоматически прекращается. 
                  Для продолжения выберите подходящий тариф.
                </p>
              </Card>

              <Card className="text-left">
                <h4 className="font-semibold text-white mb-3">
                  Есть ли ограничения?
                </h4>
                <p className="text-gray-400">
                  Никаких ограничений! Полный доступ ко всем серверам, 
                  безлимитный трафик и все функции премиум-аккаунта.
                </p>
              </Card>

              <Card className="text-left">
                <h4 className="font-semibold text-white mb-3">
                  Можно ли отменить?
                </h4>
                <p className="text-gray-400">
                  Да, вы можете отменить пробный период в любое время 
                  через Telegram бот без каких-либо обязательств.
                </p>
              </Card>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default TrialSection;
