import React from 'react';
import { motion } from 'framer-motion';
import { Check, Star, Zap } from 'lucide-react';
import { Card, Button, Badge, SectionContainer, AnimatedSection } from '@/components/ui';
import { PricingCardSkeleton } from '@/components/ui/Skeleton';
import { useTariffs } from '@/hooks';
import type { Currency } from '@/types';

const PricingSection: React.FC = () => {
  const { tariffs, loading, selectedCurrency, setSelectedCurrency } = useTariffs();

  const currencies = [
    { code: 'RUB' as Currency, label: 'Рубли', symbol: '₽' },
    { code: 'USD' as Currency, label: 'Доллары', symbol: '$' },
    { code: 'STARS' as Currency, label: 'Telegram Stars', symbol: '⭐' },
  ];

  const handleSelectTariff = (tariffId: string) => {
    // Здесь будет логика перехода к оплате
    console.log('Selected tariff:', tariffId, 'Currency:', selectedCurrency);
  };

  const formatPrice = (price: number, currency: Currency) => {
    const currencyData = currencies.find((c) => c.code === currency);
    if (!currencyData) return price.toString();

    if (currency === 'STARS') {
      return `${price} ${currencyData.symbol}`;
    }

    return `${price} ${currencyData.symbol}`;
  };

  if (loading) {
    return (
      <SectionContainer id="pricing" background="default" padding="xl">
        {/* Section Header Skeleton */}
        <div className="text-center mb-16">
          <div className="h-12 bg-neutral-200 rounded w-80 mx-auto mb-6 animate-pulse"></div>
          <div className="h-6 bg-neutral-200 rounded w-96 mx-auto mb-8 animate-pulse"></div>

          {/* Currency Selector Skeleton */}
          <div className="flex justify-center">
            <div className="bg-neutral-200 rounded-full p-1 inline-flex animate-pulse">
              {[...Array(3)].map((_, index) => (
                <div key={index} className="w-20 h-10 bg-neutral-300 rounded-full mx-1"></div>
              ))}
            </div>
          </div>
        </div>

        {/* Pricing Cards Skeleton */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {[...Array(3)].map((_, index) => (
            <PricingCardSkeleton key={index} featured={index === 1} />
          ))}
        </div>
      </SectionContainer>
    );
  }

  return (
    <SectionContainer
      id="pricing"
      background="default"
      padding="xl"
      animated={true}
      animation="fadeInUp"
    >
      {/* Section Header */}
      <AnimatedSection animation="fadeInUp" className="text-center mb-16">
        <h2 className="heading-lg text-neutral-900 mb-6">
          Выберите свой
          <span className="gradient-text"> тариф</span>
        </h2>
        <p className="text-body max-w-3xl mx-auto mb-8">
          Все тарифы включают 7-дневный бесплатный период и 30-дневную гарантию возврата средств
        </p>

        {/* Currency Selector */}
        <div className="flex justify-center">
          <div className="bg-white shadow-card border border-neutral-200 rounded-full p-1 inline-flex">
            {currencies.map((currency) => (
              <motion.button
                key={currency.code}
                onClick={() => setSelectedCurrency(currency.code)}
                className={`px-6 py-3 rounded-full text-sm font-medium transition-all ${
                  selectedCurrency === currency.code
                    ? 'bg-gradient-button text-white shadow-button'
                    : 'text-neutral-600 hover:text-primary-600 hover:bg-primary-50'
                }`}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                {currency.label}
              </motion.button>
            ))}
          </div>
        </div>
      </AnimatedSection>

      {/* Pricing Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
        {tariffs.map((tariff, index) => (
          <AnimatedSection key={tariff.id} animation="fadeInUp" delay={index * 0.1}>
            <Card
              className="relative h-full"
              variant={tariff.popular ? 'featured' : 'default'}
              hover={true}
              padding="lg"
            >
              {/* Popular Badge */}
              {tariff.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <Badge className="flex items-center space-x-1 bg-gradient-button text-white shadow-button">
                    <Star className="w-3 h-3" />
                    <span>Популярный</span>
                  </Badge>
                </div>
              )}

              <div className="text-center">
                {/* Plan Name */}
                <h3 className="heading-md text-neutral-900 mb-2">{tariff.name}</h3>
                <p className="text-body mb-6">{tariff.description}</p>

                {/* Price */}
                <div className="mb-8">
                  <div className="text-4xl font-bold text-neutral-900 mb-2">
                    {formatPrice(
                      tariff.prices[selectedCurrency.toLowerCase() as keyof typeof tariff.prices],
                      selectedCurrency
                    )}
                  </div>
                  <div className="text-neutral-500">за месяц</div>
                </div>

                {/* Features */}
                <div className="space-y-4 mb-8">
                  {tariff.features.map((feature, featureIndex) => (
                    <div key={featureIndex} className="flex items-center space-x-3">
                      <Check className="w-5 h-5 text-success-500 flex-shrink-0" />
                      <span className="text-neutral-600">{feature}</span>
                    </div>
                  ))}
                </div>

                {/* Characteristics */}
                {tariff.characteristics && (
                  <div className="bg-neutral-50 rounded-lg p-4 mb-8">
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      {tariff.characteristics.speed && (
                        <div>
                          <div className="text-neutral-500">Скорость</div>
                          <div className="text-neutral-900 font-medium">
                            {tariff.characteristics.speed}
                          </div>
                        </div>
                      )}
                      {tariff.characteristics.devices && (
                        <div>
                          <div className="text-neutral-500">Устройства</div>
                          <div className="text-neutral-900 font-medium">
                            {tariff.characteristics.devices}
                          </div>
                        </div>
                      )}
                      {tariff.characteristics.locations && (
                        <div>
                          <div className="text-neutral-500">Локации</div>
                          <div className="text-neutral-900 font-medium">
                            {tariff.characteristics.locations}
                          </div>
                        </div>
                      )}
                      {tariff.characteristics.support && (
                        <div>
                          <div className="text-neutral-500">Поддержка</div>
                          <div className="text-neutral-900 font-medium">24/7</div>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* CTA Button */}
                <Button
                  variant={tariff.popular ? 'primary' : 'secondary'}
                  size="lg"
                  className="w-full shadow-button hover:shadow-button-hover"
                  onClick={() => handleSelectTariff(tariff.id)}
                >
                  {tariff.popular && <Zap className="w-4 h-4 mr-2" />}
                  Выбрать тариф
                </Button>
              </div>
            </Card>
          </AnimatedSection>
        ))}
      </div>

      {/* Bottom Info */}
      <AnimatedSection animation="fadeInUp" delay={0.6} className="text-center mt-12">
        <p className="text-neutral-600 mb-4">
          Все платежи защищены SSL шифрованием. Отмена в любое время.
        </p>
        <div className="flex flex-wrap justify-center items-center gap-6 text-sm text-neutral-500">
          <span className="flex items-center">
            <Check className="w-4 h-4 text-success-500 mr-1" />7 дней бесплатно
          </span>
          <span className="flex items-center">
            <Check className="w-4 h-4 text-success-500 mr-1" />
            Возврат в течение 30 дней
          </span>
          <span className="flex items-center">
            <Check className="w-4 h-4 text-success-500 mr-1" />
            Без скрытых платежей
          </span>
        </div>
      </AnimatedSection>
    </SectionContainer>
  );
};

export default PricingSection;
