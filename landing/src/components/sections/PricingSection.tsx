import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Check, Star, Zap } from 'lucide-react';
import { Card, Button, Badge, SectionContainer, AnimatedSection } from '@/components/ui';
import { useTariffs } from '@/hooks';
import type { Currency } from '@/types';

const PricingSection: React.FC = () => {
  const { tariffs, loading, selectedCurrency, setSelectedCurrency } = useTariffs();
  const [_selectedTariff, setSelectedTariff] = useState<string | null>(null);

  const currencies = [
    { code: 'RUB' as Currency, label: 'Рубли', symbol: '₽' },
    { code: 'USD' as Currency, label: 'Доллары', symbol: '$' },
    { code: 'STARS' as Currency, label: 'Telegram Stars', symbol: '⭐' },
  ];

  const handleSelectTariff = (tariffId: string) => {
    setSelectedTariff(tariffId);
    // Здесь будет логика перехода к оплате
    console.log('Selected tariff:', tariffId, 'Currency:', selectedCurrency);
  };

  const formatPrice = (price: number, currency: Currency) => {
    const currencyData = currencies.find(c => c.code === currency);
    if (!currencyData) return price.toString();
    
    if (currency === 'STARS') {
      return `${price} ${currencyData.symbol}`;
    }
    
    return `${price} ${currencyData.symbol}`;
  };



  if (loading) {
    return (
      <SectionContainer id="pricing" background="default" padding="xl">
        <div className="text-center">
          <div className="animate-pulse">
            <div className="h-8 bg-neutral-200 rounded w-64 mx-auto mb-4"></div>
            <div className="h-4 bg-neutral-200 rounded w-96 mx-auto"></div>
          </div>
        </div>
      </SectionContainer>
    );
  }

  return (
    <SectionContainer
      id="pricing"
      background="default"
      padding="xl"
      animated={true}
      animation="fadeInUp"
    >
      {/* Section Header */}
      <AnimatedSection animation="fadeInUp" className="text-center mb-16">
        <h2 className="heading-lg text-neutral-900 mb-6">
          Выберите свой
          <span className="gradient-text"> тариф</span>
        </h2>
        <p className="text-body max-w-3xl mx-auto mb-8">
          Все тарифы включают 7-дневный бесплатный период и 30-дневную гарантию возврата средств
        </p>

          {/* Currency Selector */}
          <div className="flex justify-center">
            <div className="bg-white shadow-card border border-neutral-200 rounded-full p-1 inline-flex">
              {currencies.map((currency) => (
                <motion.button
                  key={currency.code}
                  onClick={() => setSelectedCurrency(currency.code)}
                  className={`px-6 py-3 rounded-full text-sm font-medium transition-all ${
                    selectedCurrency === currency.code
                      ? 'bg-gradient-button text-white shadow-button'
                      : 'text-neutral-600 hover:text-primary-600 hover:bg-primary-50'
                  }`}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  {currency.label}
                </motion.button>
              ))}
            </div>
          </div>
      </AnimatedSection>

      {/* Pricing Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
        {tariffs.map((tariff, index) => (
          <AnimatedSection
            key={tariff.id}
            animation="fadeInUp"
            delay={index * 0.1}
          >
            <Card
              className="relative h-full"
              variant={tariff.popular ? 'featured' : 'default'}
              hover={true}
              padding="lg"
            >
                {/* Popular Badge */}
                {tariff.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <Badge variant="primary" className="flex items-center space-x-1">
                      <Star className="w-3 h-3" />
                      <span>Популярный</span>
                    </Badge>
                  </div>
                )}

                <div className="text-center">
                  {/* Plan Name */}
                  <h3 className="text-2xl font-bold text-white mb-2">{tariff.name}</h3>
                  <p className="text-gray-400 mb-6">{tariff.description}</p>

                  {/* Price */}
                  <div className="mb-8">
                    <div className="text-4xl font-bold text-white mb-2">
                      {formatPrice(
                        tariff.prices[selectedCurrency.toLowerCase() as keyof typeof tariff.prices],
                        selectedCurrency
                      )}
                    </div>
                    <div className="text-gray-400">за месяц</div>
                  </div>

                  {/* Features */}
                  <div className="space-y-4 mb-8">
                    {tariff.features.map((feature, featureIndex) => (
                      <div key={featureIndex} className="flex items-center space-x-3">
                        <Check className="w-5 h-5 text-green-400 flex-shrink-0" />
                        <span className="text-gray-300">{feature}</span>
                      </div>
                    ))}
                  </div>

                  {/* Characteristics */}
                  {tariff.characteristics && (
                    <div className="bg-dark-700/50 rounded-lg p-4 mb-8">
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        {tariff.characteristics.speed && (
                          <div>
                            <div className="text-gray-400">Скорость</div>
                            <div className="text-white font-medium">{tariff.characteristics.speed}</div>
                          </div>
                        )}
                        {tariff.characteristics.devices && (
                          <div>
                            <div className="text-gray-400">Устройства</div>
                            <div className="text-white font-medium">{tariff.characteristics.devices}</div>
                          </div>
                        )}
                        {tariff.characteristics.locations && (
                          <div>
                            <div className="text-gray-400">Локации</div>
                            <div className="text-white font-medium">{tariff.characteristics.locations}</div>
                          </div>
                        )}
                        {tariff.characteristics.support && (
                          <div>
                            <div className="text-gray-400">Поддержка</div>
                            <div className="text-white font-medium">24/7</div>
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  {/* CTA Button */}
                  <Button
                    variant={tariff.popular ? 'primary' : 'secondary'}
                    className="w-full"
                    onClick={() => handleSelectTariff(tariff.id)}
                  >
                    {tariff.popular && <Zap className="w-4 h-4 mr-2" />}
                    Выбрать тариф
                  </Button>
                </div>
              </Card>
            </motion.div>
          ))}
        </motion.div>

        {/* Bottom Info */}
        <motion.div
          className="text-center mt-12"
          initial={{ opacity: 0, y: 30 }}
          animate={isVisible ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.8, delay: 0.6 }}
        >
          <p className="text-gray-400 mb-4">
            Все платежи защищены SSL шифрованием. Отмена в любое время.
          </p>
          <div className="flex justify-center items-center space-x-6 text-sm text-gray-500">
            <span>✓ 7 дней бесплатно</span>
            <span>✓ Возврат в течение 30 дней</span>
            <span>✓ Без скрытых платежей</span>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default PricingSection;
