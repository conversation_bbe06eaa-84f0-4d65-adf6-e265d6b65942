import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Check, Star, Zap } from 'lucide-react';
import { Card, Button, Badge } from '@/components/ui';
import { useTariffs } from '@/hooks';
import { useScrollAnimation } from '@/hooks';
import type { Currency } from '@/types';

const PricingSection: React.FC = () => {
  const { ref, isVisible } = useScrollAnimation();
  const { tariffs, loading, selectedCurrency, setSelectedCurrency } = useTariffs();
  const [_selectedTariff, setSelectedTariff] = useState<string | null>(null);

  const currencies = [
    { code: 'RUB' as Currency, label: 'Рубли', symbol: '₽' },
    { code: 'USD' as Currency, label: 'Доллары', symbol: '$' },
    { code: 'STARS' as Currency, label: 'Telegram Stars', symbol: '⭐' },
  ];

  const handleSelectTariff = (tariffId: string) => {
    setSelectedTariff(tariffId);
    // Здесь будет логика перехода к оплате
    console.log('Selected tariff:', tariffId, 'Currency:', selectedCurrency);
  };

  const formatPrice = (price: number, currency: Currency) => {
    const currencyData = currencies.find(c => c.code === currency);
    if (!currencyData) return price.toString();
    
    if (currency === 'STARS') {
      return `${price} ${currencyData.symbol}`;
    }
    
    return `${price} ${currencyData.symbol}`;
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: { opacity: 1, y: 0 },
  };

  if (loading) {
    return (
      <section id="pricing" className="py-20 bg-gradient-to-b from-dark-900 to-dark-800">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="animate-pulse">
              <div className="h-8 bg-dark-700 rounded w-64 mx-auto mb-4"></div>
              <div className="h-4 bg-dark-700 rounded w-96 mx-auto"></div>
            </div>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section id="pricing" className="py-20 bg-gradient-to-b from-dark-900 to-dark-800" ref={ref}>
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          animate={isVisible ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.8 }}
        >
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-6">
            Выберите свой
            <span className="bg-gradient-to-r from-primary-400 to-primary-600 bg-clip-text text-transparent">
              {' '}тариф
            </span>
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto mb-8">
            Все тарифы включают 7-дневный бесплатный период и 30-дневную гарантию возврата средств
          </p>

          {/* Currency Selector */}
          <div className="flex justify-center">
            <div className="bg-dark-800 border border-dark-700 rounded-lg p-1 inline-flex">
              {currencies.map((currency) => (
                <button
                  key={currency.code}
                  onClick={() => setSelectedCurrency(currency.code)}
                  className={`px-4 py-2 rounded-md text-sm font-medium transition-all ${
                    selectedCurrency === currency.code
                      ? 'bg-primary-600 text-white'
                      : 'text-gray-400 hover:text-white'
                  }`}
                >
                  {currency.label}
                </button>
              ))}
            </div>
          </div>
        </motion.div>

        {/* Pricing Cards */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto"
          variants={containerVariants}
          initial="hidden"
          animate={isVisible ? "visible" : "hidden"}
          transition={{ staggerChildren: 0.1, duration: 0.6 }}
        >
          {tariffs.map((tariff) => (
            <motion.div
              key={tariff.id}
              variants={itemVariants}
              transition={{ duration: 0.6, ease: [0.4, 0, 0.2, 1] }}
            >
              <Card 
                className={`relative h-full ${
                  tariff.popular 
                    ? 'border-primary-500 bg-gradient-to-b from-primary-900/20 to-dark-800' 
                    : ''
                }`}
                hover={true}
              >
                {/* Popular Badge */}
                {tariff.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <Badge variant="primary" className="flex items-center space-x-1">
                      <Star className="w-3 h-3" />
                      <span>Популярный</span>
                    </Badge>
                  </div>
                )}

                <div className="text-center">
                  {/* Plan Name */}
                  <h3 className="text-2xl font-bold text-white mb-2">{tariff.name}</h3>
                  <p className="text-gray-400 mb-6">{tariff.description}</p>

                  {/* Price */}
                  <div className="mb-8">
                    <div className="text-4xl font-bold text-white mb-2">
                      {formatPrice(
                        tariff.prices[selectedCurrency.toLowerCase() as keyof typeof tariff.prices],
                        selectedCurrency
                      )}
                    </div>
                    <div className="text-gray-400">за месяц</div>
                  </div>

                  {/* Features */}
                  <div className="space-y-4 mb-8">
                    {tariff.features.map((feature, featureIndex) => (
                      <div key={featureIndex} className="flex items-center space-x-3">
                        <Check className="w-5 h-5 text-green-400 flex-shrink-0" />
                        <span className="text-gray-300">{feature}</span>
                      </div>
                    ))}
                  </div>

                  {/* Characteristics */}
                  {tariff.characteristics && (
                    <div className="bg-dark-700/50 rounded-lg p-4 mb-8">
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        {tariff.characteristics.speed && (
                          <div>
                            <div className="text-gray-400">Скорость</div>
                            <div className="text-white font-medium">{tariff.characteristics.speed}</div>
                          </div>
                        )}
                        {tariff.characteristics.devices && (
                          <div>
                            <div className="text-gray-400">Устройства</div>
                            <div className="text-white font-medium">{tariff.characteristics.devices}</div>
                          </div>
                        )}
                        {tariff.characteristics.locations && (
                          <div>
                            <div className="text-gray-400">Локации</div>
                            <div className="text-white font-medium">{tariff.characteristics.locations}</div>
                          </div>
                        )}
                        {tariff.characteristics.support && (
                          <div>
                            <div className="text-gray-400">Поддержка</div>
                            <div className="text-white font-medium">24/7</div>
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  {/* CTA Button */}
                  <Button
                    variant={tariff.popular ? 'primary' : 'secondary'}
                    className="w-full"
                    onClick={() => handleSelectTariff(tariff.id)}
                  >
                    {tariff.popular && <Zap className="w-4 h-4 mr-2" />}
                    Выбрать тариф
                  </Button>
                </div>
              </Card>
            </motion.div>
          ))}
        </motion.div>

        {/* Bottom Info */}
        <motion.div
          className="text-center mt-12"
          initial={{ opacity: 0, y: 30 }}
          animate={isVisible ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.8, delay: 0.6 }}
        >
          <p className="text-gray-400 mb-4">
            Все платежи защищены SSL шифрованием. Отмена в любое время.
          </p>
          <div className="flex justify-center items-center space-x-6 text-sm text-gray-500">
            <span>✓ 7 дней бесплатно</span>
            <span>✓ Возврат в течение 30 дней</span>
            <span>✓ Без скрытых платежей</span>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default PricingSection;
