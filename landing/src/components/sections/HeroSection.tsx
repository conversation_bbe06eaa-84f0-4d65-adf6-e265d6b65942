import React from 'react';
import { motion } from 'framer-motion';
import { Shield, Zap, Globe, Lock, ArrowDown } from 'lucide-react';
import { Button, AnimatedSection } from '@/components/ui';
import { StatCounter } from '@/components/ui/AnimatedCounter';
import { FloatingBackground } from '@/components/ui/FloatingElements';

const HeroSection: React.FC = () => {
  const scrollToPricing = () => {
    const element = document.querySelector('#pricing');
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const features = [
    { icon: Shield, text: 'Не заблокируют' },
    { icon: Zap, text: 'Высокая скорость' },
    { icon: Globe, text: '10+ стран' },
    { icon: Lock, text: 'Без логов' },
  ];

  return (
    <section
      id="hero"
      className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-hero"
    >
      {/* Floating Background */}
      <FloatingBackground count={15} className="absolute inset-0 opacity-20" />

      {/* Floating Elements */}
      <div className="absolute inset-0 pointer-events-none">
        {[...Array(15)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-3 h-3 bg-white/20 rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [0, -40, 0],
              opacity: [0.1, 0.4, 0.1],
              scale: [1, 1.2, 1],
            }}
            transition={{
              duration: 4 + Math.random() * 3,
              repeat: Infinity,
              delay: Math.random() * 3,
              ease: 'easeInOut',
            }}
          />
        ))}
      </div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="text-center max-w-5xl mx-auto">
          {/* Badge */}
          <AnimatedSection animation="fadeInUp" delay={0}>
            <motion.div
              className="inline-flex items-center px-6 py-3 bg-white/10 backdrop-blur-sm border border-white/20 rounded-full text-white text-sm font-medium mb-8 shadow-lg"
              whileHover={{ scale: 1.05, backgroundColor: 'rgba(255,255,255,0.15)' }}
              transition={{ duration: 0.2 }}
            >
              <Shield className="w-4 h-4 mr-2" />7 дней бесплатно • Без обязательств
            </motion.div>
          </AnimatedSection>

          {/* Main Heading */}
          <AnimatedSection animation="fadeInUp" delay={0.2}>
            <h1 className="heading-xl text-white mb-8 leading-tight">
              Безопасный VPN
              <br />
              <span className="bg-gradient-to-r from-white to-white/80 bg-clip-text text-transparent">
                для всех устройств
              </span>
            </h1>
          </AnimatedSection>

          {/* Subtitle */}
          <AnimatedSection animation="fadeInUp" delay={0.4}>
            <p className="text-xl md:text-2xl text-white/90 mb-10 max-w-4xl mx-auto leading-relaxed">
              Защитите свою приватность в интернете с помощью быстрого и надежного VPN сервиса.
              Попробуйте бесплатно в течение 7 дней.
            </p>
          </AnimatedSection>

          {/* Features */}
          <AnimatedSection animation="fadeInUp" delay={0.6}>
            <div className="flex flex-wrap justify-center gap-8 mb-12">
              {features.map((feature, index) => (
                <motion.div
                  key={index}
                  className="flex items-center space-x-3 text-white/90 bg-white/10 backdrop-blur-sm px-4 py-3 rounded-full border border-white/20"
                  whileHover={{ scale: 1.05, backgroundColor: 'rgba(255,255,255,0.15)' }}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.6 + index * 0.1 }}
                >
                  <feature.icon className="w-5 h-5 text-white" />
                  <span className="font-medium">{feature.text}</span>
                </motion.div>
              ))}
            </div>
          </AnimatedSection>

          {/* CTA Buttons */}
          <AnimatedSection animation="fadeInUp" delay={0.8}>
            <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <Button
                  variant="secondary"
                  size="xl"
                  onClick={scrollToPricing}
                  className="w-full sm:w-auto bg-white text-primary-600 hover:bg-neutral-50 shadow-xl hover:shadow-2xl border-0"
                >
                  Попробовать 7 дней бесплатно
                </Button>
              </motion.div>
              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <Button
                  variant="ghost"
                  size="xl"
                  onClick={() => {
                    const element = document.querySelector('#features');
                    if (element) {
                      element.scrollIntoView({ behavior: 'smooth' });
                    }
                  }}
                  className="w-full sm:w-auto text-white border-white/30 hover:bg-white/10"
                >
                  Узнать больше
                </Button>
              </motion.div>
            </div>
          </AnimatedSection>

          {/* Trust Indicators */}
          <AnimatedSection animation="fadeInUp" delay={1}>
            <div className="mt-16 pt-8 border-t border-white/20">
              <p className="text-white/70 text-sm mb-6">Нам доверяют пользователи по всему миру</p>
              <div className="grid grid-cols-3 gap-8 max-w-md mx-auto">
                {[
                  { value: 500, label: 'Пользователей', suffix: '+' },
                  { value: 10, label: 'Стран', suffix: '+' },
                  { value: 99.9, label: 'Uptime', suffix: '%', decimals: 1 },
                ].map((stat, index) => (
                  <StatCounter
                    key={index}
                    value={stat.value}
                    label={stat.label}
                    suffix={stat.suffix}
                    className="text-white"
                  />
                ))}
              </div>
            </div>
          </AnimatedSection>
        </div>
      </div>

      {/* Scroll Indicator */}
      <motion.div
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2 cursor-pointer"
        animate={{ y: [0, 10, 0] }}
        transition={{ duration: 2, repeat: Infinity }}
        onClick={() => {
          const element = document.querySelector('#features');
          if (element) {
            element.scrollIntoView({ behavior: 'smooth' });
          }
        }}
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
      >
        <div className="w-8 h-12 border-2 border-white/50 rounded-full flex justify-center items-start pt-2 bg-white/10 backdrop-blur-sm">
          <ArrowDown className="w-4 h-4 text-white/70" />
        </div>
      </motion.div>
    </section>
  );
};

export default HeroSection;
