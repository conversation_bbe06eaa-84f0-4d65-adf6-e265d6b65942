import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'

function App() {
  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h1>UnveilVPN Landing Page</h1>
      <p>Это простая тестовая версия лендинговой страницы</p>
      <button onClick={() => alert('Кнопка работает!')}>
        Тест кнопки
      </button>
    </div>
  )
}

const container = document.getElementById('root')
if (!container) throw new Error('Root element not found')

const root = createRoot(container)
root.render(
  <StrictMode>
    <App />
  </StrictMode>
)
