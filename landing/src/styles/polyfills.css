/* CSS Polyfills для старых браузеров */

/* ===== FLEXBOX FALLBACKS ===== */
/* Для браузеров без поддержки flexbox */
.no-flexbox .flex {
  display: block;
}

.no-flexbox .flex-row {
  display: block;
}

.no-flexbox .flex-col {
  display: block;
}

.no-flexbox .items-center {
  text-align: center;
}

.no-flexbox .justify-center {
  text-align: center;
}

.no-flexbox .justify-between > * {
  display: inline-block;
  width: 48%;
}

.no-flexbox .justify-between > *:first-child {
  float: left;
}

.no-flexbox .justify-between > *:last-child {
  float: right;
}

/* ===== GRID FALLBACKS ===== */
/* Для браузеров без поддержки CSS Grid */
.no-grid .grid {
  display: block;
}

.no-grid .grid-cols-1 > * {
  width: 100%;
  margin-bottom: 1rem;
}

.no-grid .grid-cols-2 > * {
  width: 48%;
  display: inline-block;
  vertical-align: top;
  margin-right: 2%;
  margin-bottom: 1rem;
}

.no-grid .grid-cols-2 > *:nth-child(2n) {
  margin-right: 0;
}

.no-grid .grid-cols-3 > * {
  width: 31%;
  display: inline-block;
  vertical-align: top;
  margin-right: 2%;
  margin-bottom: 1rem;
}

.no-grid .grid-cols-3 > *:nth-child(3n) {
  margin-right: 0;
}

.no-grid .grid-cols-4 > * {
  width: 23%;
  display: inline-block;
  vertical-align: top;
  margin-right: 2%;
  margin-bottom: 1rem;
}

.no-grid .grid-cols-4 > *:nth-child(4n) {
  margin-right: 0;
}

/* ===== CSS CUSTOM PROPERTIES FALLBACKS ===== */
/* Для браузеров без поддержки CSS переменных */
.no-css-variables {
  /* Основные цвета */
  --primary-50: #e6f3ff;
  --primary-500: #007acc;
  --primary-700: #005a9e;

  /* Нейтральные цвета */
  --neutral-50: #f7fafc;
  --neutral-600: #4a5568;
  --neutral-900: #1f2937;
}

.no-css-variables .bg-primary-500 {
  background-color: #007acc;
}

.no-css-variables .text-primary-500 {
  color: #007acc;
}

.no-css-variables .border-primary-500 {
  border-color: #007acc;
}

.no-css-variables .bg-neutral-50 {
  background-color: #f7fafc;
}

.no-css-variables .text-neutral-600 {
  color: #4a5568;
}

.no-css-variables .text-neutral-900 {
  color: #1f2937;
}

/* ===== TRANSFORM FALLBACKS ===== */
/* Для браузеров без поддержки transform */
.no-transforms .transform {
  /* Убираем transform анимации */
}

.no-transforms .hover\:scale-105:hover {
  /* Заменяем на простые эффекты */
  opacity: 0.9;
}

.no-transforms .translate-y-0 {
  top: 0;
}

.no-transforms .translate-y-4 {
  top: 1rem;
}

/* ===== TRANSITION FALLBACKS ===== */
/* Для браузеров без поддержки transitions */
.no-transitions .transition-all {
  /* Убираем transitions */
}

.no-transitions .duration-200 {
  /* Убираем duration */
}

.no-transitions .ease-in-out {
  /* Убираем easing */
}

/* ===== BORDER-RADIUS FALLBACKS ===== */
/* Для очень старых браузеров без border-radius */
.no-border-radius .rounded {
  /* Убираем скругления */
  border-radius: 0;
}

.no-border-radius .rounded-lg {
  border-radius: 0;
}

.no-border-radius .rounded-xl {
  border-radius: 0;
}

.no-border-radius .rounded-full {
  border-radius: 0;
}

/* ===== BOX-SHADOW FALLBACKS ===== */
/* Для браузеров без поддержки box-shadow */
.no-box-shadow .shadow {
  border: 1px solid #e2e8f0;
}

.no-box-shadow .shadow-lg {
  border: 1px solid #cbd5e1;
}

.no-box-shadow .shadow-xl {
  border: 2px solid #94a3b8;
}

/* ===== OPACITY FALLBACKS ===== */
/* Для IE8 и ниже */
.no-opacity .opacity-50 {
  filter: alpha(opacity=50);
}

.no-opacity .opacity-75 {
  filter: alpha(opacity=75);
}

.no-opacity .opacity-90 {
  filter: alpha(opacity=90);
}

/* ===== RESPONSIVE FALLBACKS ===== */
/* Для браузеров без поддержки media queries */
.no-media-queries .hidden {
  display: none;
}

.no-media-queries .md\:block {
  display: block;
}

.no-media-queries .md\:flex {
  display: block;
}

.no-media-queries .md\:grid {
  display: block;
}

.no-media-queries .lg\:text-6xl {
  font-size: 3rem;
}

.no-media-queries .md\:text-4xl {
  font-size: 2.25rem;
}

/* ===== CLEARFIX ===== */
/* Для float layouts */
.clearfix::after {
  content: '';
  display: table;
  clear: both;
}

/* ===== IE SPECIFIC FIXES ===== */
/* Для Internet Explorer */
.ie .flex {
  display: -ms-flexbox;
  display: flex;
}

.ie .flex-row {
  -ms-flex-direction: row;
  flex-direction: row;
}

.ie .flex-col {
  -ms-flex-direction: column;
  flex-direction: column;
}

.ie .items-center {
  -ms-flex-align: center;
  align-items: center;
}

.ie .justify-center {
  -ms-flex-pack: center;
  justify-content: center;
}

.ie .justify-between {
  -ms-flex-pack: justify;
  justify-content: space-between;
}

/* ===== PRINT STYLES ===== */
/* Стили для печати */
@media print {
  .no-print {
    display: none !important;
  }

  .print-block {
    display: block !important;
  }

  * {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }

  a,
  a:visited {
    text-decoration: underline;
  }

  a[href]:after {
    content: ' (' attr(href) ')';
  }

  abbr[title]:after {
    content: ' (' attr(title) ')';
  }

  .ir a:after,
  a[href^='javascript:']:after,
  a[href^='#']:after {
    content: '';
  }

  pre,
  blockquote {
    border: 1px solid #999;
    page-break-inside: avoid;
  }

  thead {
    display: table-header-group;
  }

  tr,
  img {
    page-break-inside: avoid;
  }

  img {
    max-width: 100% !important;
  }

  @page {
    margin: 0.5cm;
  }

  p,
  h2,
  h3 {
    orphans: 3;
    widows: 3;
  }

  h2,
  h3 {
    page-break-after: avoid;
  }
}
