/* Responsive Design System для UnveilVPN Landing */

/* ===== BREAKPOINTS ===== */
/* 
  xs: 0px     - Очень маленькие мобильные
  sm: 640px   - Мобильные (портрет)
  md: 768px   - Планшеты (портрет)
  lg: 1024px  - Планшеты (альбом) / Маленькие десктопы
  xl: 1280px  - Десктопы
  2xl: 1536px - Большие экраны
*/

/* ===== CONTAINER SIZES ===== */
.container-responsive {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 640px) {
  .container-responsive {
    max-width: 640px;
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 768px) {
  .container-responsive {
    max-width: 768px;
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

@media (min-width: 1024px) {
  .container-responsive {
    max-width: 1024px;
  }
}

@media (min-width: 1280px) {
  .container-responsive {
    max-width: 1280px;
  }
}

@media (min-width: 1536px) {
  .container-responsive {
    max-width: 1536px;
  }
}

/* ===== TYPOGRAPHY RESPONSIVE ===== */
.text-responsive-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}

.text-responsive-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.text-responsive-base {
  font-size: 1rem;
  line-height: 1.5rem;
}

.text-responsive-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.text-responsive-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}

/* Заголовки */
.heading-responsive-h1 {
  font-size: 1.875rem;
  line-height: 2.25rem;
  font-weight: 800;
}

@media (min-width: 768px) {
  .heading-responsive-h1 {
    font-size: 3rem;
    line-height: 1;
  }
}

@media (min-width: 1024px) {
  .heading-responsive-h1 {
    font-size: 3.75rem;
    line-height: 1;
  }
}

.heading-responsive-h2 {
  font-size: 1.5rem;
  line-height: 2rem;
  font-weight: 700;
}

@media (min-width: 768px) {
  .heading-responsive-h2 {
    font-size: 2.25rem;
    line-height: 2.5rem;
  }
}

@media (min-width: 1024px) {
  .heading-responsive-h2 {
    font-size: 3rem;
    line-height: 1;
  }
}

.heading-responsive-h3 {
  font-size: 1.25rem;
  line-height: 1.75rem;
  font-weight: 600;
}

@media (min-width: 768px) {
  .heading-responsive-h3 {
    font-size: 1.875rem;
    line-height: 2.25rem;
  }
}

@media (min-width: 1024px) {
  .heading-responsive-h3 {
    font-size: 2.25rem;
    line-height: 2.5rem;
  }
}

/* ===== SPACING RESPONSIVE ===== */
.spacing-responsive-section {
  padding-top: 3rem;
  padding-bottom: 3rem;
}

@media (min-width: 768px) {
  .spacing-responsive-section {
    padding-top: 4rem;
    padding-bottom: 4rem;
  }
}

@media (min-width: 1024px) {
  .spacing-responsive-section {
    padding-top: 6rem;
    padding-bottom: 6rem;
  }
}

.spacing-responsive-large {
  padding-top: 4rem;
  padding-bottom: 4rem;
}

@media (min-width: 768px) {
  .spacing-responsive-large {
    padding-top: 6rem;
    padding-bottom: 6rem;
  }
}

@media (min-width: 1024px) {
  .spacing-responsive-large {
    padding-top: 8rem;
    padding-bottom: 8rem;
  }
}

/* ===== GRID RESPONSIVE ===== */
.grid-responsive-1-2-3 {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

@media (min-width: 768px) {
  .grid-responsive-1-2-3 {
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
  }
}

@media (min-width: 1024px) {
  .grid-responsive-1-2-3 {
    grid-template-columns: repeat(3, 1fr);
    gap: 2.5rem;
  }
}

.grid-responsive-1-2 {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

@media (min-width: 768px) {
  .grid-responsive-1-2 {
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
  }
}

/* ===== FLEX RESPONSIVE ===== */
.flex-responsive-column-row {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

@media (min-width: 768px) {
  .flex-responsive-column-row {
    flex-direction: row;
    align-items: center;
    gap: 1.5rem;
  }
}

/* ===== VISIBILITY RESPONSIVE ===== */
.hidden-mobile {
  display: none;
}

@media (min-width: 768px) {
  .hidden-mobile {
    display: block;
  }
}

.hidden-desktop {
  display: block;
}

@media (min-width: 768px) {
  .hidden-desktop {
    display: none;
  }
}

/* ===== BUTTON RESPONSIVE ===== */
.button-responsive {
  padding: 0.75rem 1.5rem;
  font-size: 0.875rem;
}

@media (min-width: 768px) {
  .button-responsive {
    padding: 1rem 2rem;
    font-size: 1rem;
  }
}

@media (min-width: 1024px) {
  .button-responsive {
    padding: 1.25rem 2.5rem;
    font-size: 1.125rem;
  }
}

/* ===== CARD RESPONSIVE ===== */
.card-responsive {
  padding: 1.5rem;
}

@media (min-width: 768px) {
  .card-responsive {
    padding: 2rem;
  }
}

@media (min-width: 1024px) {
  .card-responsive {
    padding: 2.5rem;
  }
}

/* ===== HERO RESPONSIVE ===== */
.hero-responsive {
  padding-top: 4rem;
  padding-bottom: 4rem;
  text-align: center;
}

@media (min-width: 768px) {
  .hero-responsive {
    padding-top: 6rem;
    padding-bottom: 6rem;
  }
}

@media (min-width: 1024px) {
  .hero-responsive {
    padding-top: 8rem;
    padding-bottom: 8rem;
    text-align: left;
  }
}

/* ===== NAVIGATION RESPONSIVE ===== */
.nav-responsive {
  padding: 1rem;
}

@media (min-width: 768px) {
  .nav-responsive {
    padding: 1.5rem 2rem;
  }
}

/* ===== UTILITIES ===== */
.aspect-responsive-video {
  aspect-ratio: 16 / 9;
}

@media (max-width: 767px) {
  .aspect-responsive-video {
    aspect-ratio: 4 / 3;
  }
}

.max-width-responsive {
  max-width: 100%;
}

@media (min-width: 768px) {
  .max-width-responsive {
    max-width: 42rem;
  }
}

@media (min-width: 1024px) {
  .max-width-responsive {
    max-width: 56rem;
  }
}
