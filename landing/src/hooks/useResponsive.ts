import { useState, useEffect } from 'react';

// Breakpoints соответствуют Tailwind CSS
const breakpoints = {
  xs: 0,
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536,
} as const;

type Breakpoint = keyof typeof breakpoints;

interface UseResponsiveReturn {
  // Текущий размер экрана
  width: number;
  height: number;

  // Текущий breakpoint
  currentBreakpoint: Breakpoint;

  // Проверки размеров
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  isLargeDesktop: boolean;

  // Проверки breakpoints
  isXs: boolean;
  isSm: boolean;
  isMd: boolean;
  isLg: boolean;
  isXl: boolean;
  is2Xl: boolean;

  // Проверки "больше чем"
  isSmUp: boolean;
  isMdUp: boolean;
  isLgUp: boolean;
  isXlUp: boolean;
  is2XlUp: boolean;

  // Проверки "меньше чем"
  isSmDown: boolean;
  isMdDown: boolean;
  isLgDown: boolean;
  isXlDown: boolean;
}

const useResponsive = (): UseResponsiveReturn => {
  const [windowSize, setWindowSize] = useState({
    width: typeof window !== 'undefined' ? window.innerWidth : 1024,
    height: typeof window !== 'undefined' ? window.innerHeight : 768,
  });

  useEffect(() => {
    if (typeof window === 'undefined') return;

    const handleResize = () => {
      setWindowSize({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    };

    // Добавляем debounce для оптимизации
    let timeoutId: NodeJS.Timeout;
    const debouncedHandleResize = () => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(handleResize, 150);
    };

    window.addEventListener('resize', debouncedHandleResize);

    // Вызываем сразу для получения актуального размера
    handleResize();

    return () => {
      window.removeEventListener('resize', debouncedHandleResize);
      clearTimeout(timeoutId);
    };
  }, []);

  const { width, height } = windowSize;

  // Определяем текущий breakpoint
  const getCurrentBreakpoint = (width: number): Breakpoint => {
    if (width >= breakpoints['2xl']) return '2xl';
    if (width >= breakpoints.xl) return 'xl';
    if (width >= breakpoints.lg) return 'lg';
    if (width >= breakpoints.md) return 'md';
    if (width >= breakpoints.sm) return 'sm';
    return 'xs';
  };

  const currentBreakpoint = getCurrentBreakpoint(width);

  // Основные категории устройств
  const isMobile = width < breakpoints.md;
  const isTablet = width >= breakpoints.md && width < breakpoints.lg;
  const isDesktop = width >= breakpoints.lg && width < breakpoints['2xl'];
  const isLargeDesktop = width >= breakpoints['2xl'];

  // Точные breakpoints
  const isXs = width < breakpoints.sm;
  const isSm = width >= breakpoints.sm && width < breakpoints.md;
  const isMd = width >= breakpoints.md && width < breakpoints.lg;
  const isLg = width >= breakpoints.lg && width < breakpoints.xl;
  const isXl = width >= breakpoints.xl && width < breakpoints['2xl'];
  const is2Xl = width >= breakpoints['2xl'];

  // "Больше чем" проверки
  const isSmUp = width >= breakpoints.sm;
  const isMdUp = width >= breakpoints.md;
  const isLgUp = width >= breakpoints.lg;
  const isXlUp = width >= breakpoints.xl;
  const is2XlUp = width >= breakpoints['2xl'];

  // "Меньше чем" проверки
  const isSmDown = width < breakpoints.sm;
  const isMdDown = width < breakpoints.md;
  const isLgDown = width < breakpoints.lg;
  const isXlDown = width < breakpoints.xl;

  return {
    width,
    height,
    currentBreakpoint,
    isMobile,
    isTablet,
    isDesktop,
    isLargeDesktop,
    isXs,
    isSm,
    isMd,
    isLg,
    isXl,
    is2Xl,
    isSmUp,
    isMdUp,
    isLgUp,
    isXlUp,
    is2XlUp,
    isSmDown,
    isMdDown,
    isLgDown,
    isXlDown,
  };
};

export default useResponsive;
