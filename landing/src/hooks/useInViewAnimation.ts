/**
 * Хук для анимаций на основе видимости элементов
 * Использует Intersection Observer для определения видимости
 */

import { useEffect, useRef, useState } from 'react';
import { useAnimation } from 'framer-motion';

interface UseInViewAnimationOptions {
  threshold?: number;
  triggerOnce?: boolean;
  rootMargin?: string;
  delay?: number;
}

interface UseInViewAnimationReturn {
  ref: React.RefObject<HTMLElement | null>;
  controls: any;
  isInView: boolean;
}

export const useInViewAnimation = (
  options: UseInViewAnimationOptions = {}
): UseInViewAnimationReturn => {
  const {
    threshold = 0.1,
    triggerOnce = true,
    rootMargin = '0px 0px -100px 0px',
    delay = 0,
  } = options;

  const ref = useRef<HTMLElement | null>(null);
  const controls = useAnimation();
  const [isInView, setIsInView] = useState(false);
  const [hasTriggered, setHasTriggered] = useState(false);

  useEffect(() => {
    const element = ref.current;
    if (!element) return;

    // Проверяем поддержку Intersection Observer
    if (!window.IntersectionObserver) {
      // Fallback для старых браузеров - сразу показываем элемент
      setIsInView(true);
      setTimeout(() => {
        controls.start('visible');
      }, delay);
      return;
    }

    const observer = new IntersectionObserver(
      ([entry]) => {
        const inView = entry.isIntersecting;
        setIsInView(inView);

        if (inView && (!triggerOnce || !hasTriggered)) {
          setHasTriggered(true);
          setTimeout(() => {
            controls.start('visible');
          }, delay);
        } else if (!inView && !triggerOnce) {
          controls.start('hidden');
        }
      },
      {
        threshold,
        rootMargin,
      }
    );

    observer.observe(element);

    return () => {
      observer.unobserve(element);
    };
  }, [controls, threshold, triggerOnce, rootMargin, delay, hasTriggered]);

  // Инициализируем анимацию в скрытом состоянии
  useEffect(() => {
    controls.start('hidden');
  }, [controls]);

  return { ref, controls, isInView };
};

// Хук для анимации счетчиков
export const useCounterAnimation = (
  endValue: number,
  duration: number = 2000,
  startOnView: boolean = true
) => {
  const [count, setCount] = useState(0);
  const { ref, isInView } = useInViewAnimation({ threshold: 0.5 });
  const [hasStarted, setHasStarted] = useState(false);

  useEffect(() => {
    if ((startOnView && isInView && !hasStarted) || (!startOnView && !hasStarted)) {
      setHasStarted(true);
      const startTime = Date.now();
      const startValue = 0;

      const updateCount = () => {
        const elapsed = Date.now() - startTime;
        const progress = Math.min(elapsed / duration, 1);

        // Easing function для плавности
        const easeOutQuart = 1 - Math.pow(1 - progress, 4);
        const currentValue = Math.floor(startValue + (endValue - startValue) * easeOutQuart);

        setCount(currentValue);

        if (progress < 1) {
          requestAnimationFrame(updateCount);
        } else {
          setCount(endValue);
        }
      };

      requestAnimationFrame(updateCount);
    }
  }, [isInView, endValue, duration, startOnView, hasStarted]);

  return { count, ref, isInView };
};

// Хук для анимации прогресс-бара
export const useProgressAnimation = (
  targetProgress: number,
  duration: number = 1500,
  startOnView: boolean = true
) => {
  const [progress, setProgress] = useState(0);
  const { ref, isInView } = useInViewAnimation({ threshold: 0.3 });
  const [hasStarted, setHasStarted] = useState(false);

  useEffect(() => {
    if ((startOnView && isInView && !hasStarted) || (!startOnView && !hasStarted)) {
      setHasStarted(true);
      const startTime = Date.now();

      const updateProgress = () => {
        const elapsed = Date.now() - startTime;
        const progressRatio = Math.min(elapsed / duration, 1);

        // Easing function
        const easeOutCubic = 1 - Math.pow(1 - progressRatio, 3);
        const currentProgress = targetProgress * easeOutCubic;

        setProgress(currentProgress);

        if (progressRatio < 1) {
          requestAnimationFrame(updateProgress);
        } else {
          setProgress(targetProgress);
        }
      };

      requestAnimationFrame(updateProgress);
    }
  }, [isInView, targetProgress, duration, startOnView, hasStarted]);

  return { progress, ref, isInView };
};

// Хук для анимации типизации текста
export const useTypewriterAnimation = (
  text: string,
  speed: number = 50,
  startOnView: boolean = true
) => {
  const [displayText, setDisplayText] = useState('');
  const [currentIndex, setCurrentIndex] = useState(0);
  const { ref, isInView } = useInViewAnimation({ threshold: 0.5 });
  const [hasStarted, setHasStarted] = useState(false);

  useEffect(() => {
    let timer: NodeJS.Timeout;

    if ((startOnView && isInView && !hasStarted) || (!startOnView && !hasStarted)) {
      setHasStarted(true);
      timer = setInterval(() => {
        setCurrentIndex((prevIndex) => {
          if (prevIndex < text.length) {
            setDisplayText(text.slice(0, prevIndex + 1));
            return prevIndex + 1;
          } else {
            clearInterval(timer);
            return prevIndex;
          }
        });
      }, speed);
    }

    return () => {
      if (timer) {
        clearInterval(timer);
      }
    };
  }, [isInView, text, speed, startOnView, hasStarted]);

  return { displayText, ref, isInView, isComplete: currentIndex >= text.length };
};

// Хук для последовательной анимации элементов
export const useSequentialAnimation = (
  itemsCount: number,
  delay: number = 100,
  startOnView: boolean = true
) => {
  const [visibleItems, setVisibleItems] = useState<boolean[]>(new Array(itemsCount).fill(false));
  const { ref, isInView } = useInViewAnimation({ threshold: 0.2 });
  const [hasStarted, setHasStarted] = useState(false);

  useEffect(() => {
    if ((startOnView && isInView && !hasStarted) || (!startOnView && !hasStarted)) {
      setHasStarted(true);
      visibleItems.forEach((_, index) => {
        setTimeout(() => {
          setVisibleItems((prev) => {
            const newVisible = [...prev];
            newVisible[index] = true;
            return newVisible;
          });
        }, index * delay);
      });
    }
  }, [isInView, itemsCount, delay, startOnView, hasStarted, visibleItems]);

  return { visibleItems, ref, isInView };
};

// Хук для анимации при скролле
export const useScrollAnimation = (
  options: UseInViewAnimationOptions = {}
) => {
  const [scrollY, setScrollY] = useState(0);
  const [isScrolling, setIsScrolling] = useState(false);
  const { ref, isInView } = useInViewAnimation(options);

  useEffect(() => {
    let timeoutId: NodeJS.Timeout;

    const handleScroll = () => {
      setScrollY(window.scrollY);
      setIsScrolling(true);

      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        setIsScrolling(false);
      }, 150);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });

    return () => {
      window.removeEventListener('scroll', handleScroll);
      clearTimeout(timeoutId);
    };
  }, []);

  return { scrollY, isScrolling, ref, isInView };
};

export default useInViewAnimation;
