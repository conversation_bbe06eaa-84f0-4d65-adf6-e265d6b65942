/**
 * Хук для мониторинга и управления кэшем
 */

import { useState, useEffect, useCallback } from 'react';
import { apiClient } from '@/services/apiClient';

interface CacheStats {
  totalItems: number;
  totalSize: number;
  oldestItem?: string;
  newestItem?: string;
}

interface UseCacheReturn {
  stats: CacheStats;
  isLoading: boolean;
  error: string | null;
  refreshStats: () => void;
  clearCache: () => void;
  cleanExpired: () => Promise<number>;
  cleanup: (maxItems?: number, maxSize?: number) => void;
  formatSize: (bytes: number) => string;
}

export const useCache = (): UseCacheReturn => {
  const [stats, setStats] = useState<CacheStats>({
    totalItems: 0,
    totalSize: 0,
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Форматирование размера в человекочитаемый вид
  const formatSize = useCallback((bytes: number): string => {
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(1))} ${sizes[i]}`;
  }, []);

  // Обновление статистики
  const refreshStats = useCallback(() => {
    try {
      setIsLoading(true);
      setError(null);
      
      const newStats = apiClient.getCacheStats();
      setStats(newStats);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to get cache stats');
      console.error('Error getting cache stats:', err);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Очистка всего кэша
  const clearCache = useCallback(() => {
    try {
      setError(null);
      apiClient.clearCache();
      refreshStats();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to clear cache');
      console.error('Error clearing cache:', err);
    }
  }, [refreshStats]);

  // Очистка устаревших элементов
  const cleanExpired = useCallback(async (): Promise<number> => {
    try {
      setError(null);
      const cleaned = apiClient.cleanExpiredCache();
      refreshStats();
      return cleaned;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to clean expired cache');
      console.error('Error cleaning expired cache:', err);
      return 0;
    }
  }, [refreshStats]);

  // Очистка с лимитами
  const cleanup = useCallback((maxItems = 50, maxSize = 5 * 1024 * 1024) => {
    try {
      setError(null);
      apiClient.cleanupCache(maxItems, maxSize);
      refreshStats();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to cleanup cache');
      console.error('Error cleaning up cache:', err);
    }
  }, [refreshStats]);

  // Автоматическое обновление статистики при монтировании
  useEffect(() => {
    refreshStats();
    
    // Обновляем статистику каждые 30 секунд
    const interval = setInterval(refreshStats, 30000);
    
    return () => clearInterval(interval);
  }, [refreshStats]);

  return {
    stats,
    isLoading,
    error,
    refreshStats,
    clearCache,
    cleanExpired,
    cleanup,
    formatSize,
  };
};

// Хук для мониторинга размера кэша с предупреждениями
export const useCacheMonitor = (
  maxSize = 5 * 1024 * 1024, // 5MB
  maxItems = 50
): {
  isOverLimit: boolean;
  sizeWarning: boolean;
  itemsWarning: boolean;
  autoCleanup: () => void;
} => {
  const { stats, cleanup } = useCache();
  
  const sizeWarning = stats.totalSize > maxSize * 0.8; // 80% от лимита
  const itemsWarning = stats.totalItems > maxItems * 0.8; // 80% от лимита
  const isOverLimit = stats.totalSize > maxSize || stats.totalItems > maxItems;
  
  const autoCleanup = useCallback(() => {
    if (isOverLimit) {
      cleanup(maxItems, maxSize);
    }
  }, [isOverLimit, cleanup, maxItems, maxSize]);
  
  // Автоматическая очистка при превышении лимитов
  useEffect(() => {
    if (isOverLimit) {
      console.warn('Cache limit exceeded, performing auto cleanup');
      autoCleanup();
    }
  }, [isOverLimit, autoCleanup]);
  
  return {
    isOverLimit,
    sizeWarning,
    itemsWarning,
    autoCleanup,
  };
};

// Хук для отслеживания производительности кэша
export const useCachePerformance = (): {
  hitRate: number;
  missRate: number;
  totalRequests: number;
  cacheHits: number;
  cacheMisses: number;
  resetStats: () => void;
} => {
  const [cacheHits, setCacheHits] = useState(0);
  const [cacheMisses, setCacheMisses] = useState(0);
  
  const totalRequests = cacheHits + cacheMisses;
  const hitRate = totalRequests > 0 ? (cacheHits / totalRequests) * 100 : 0;
  const missRate = totalRequests > 0 ? (cacheMisses / totalRequests) * 100 : 0;
  
  const resetStats = useCallback(() => {
    setCacheHits(0);
    setCacheMisses(0);
  }, []);
  
  // В реальном приложении здесь бы была интеграция с API клиентом
  // для отслеживания попаданий и промахов кэша
  
  return {
    hitRate,
    missRate,
    totalRequests,
    cacheHits,
    cacheMisses,
    resetStats,
  };
};

export default useCache;
