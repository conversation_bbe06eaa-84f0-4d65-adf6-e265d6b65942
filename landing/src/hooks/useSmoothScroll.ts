/**
 * Хук для плавной прокрутки
 */

import { useCallback } from 'react';
import { scrollToElement, scrollToTop, scrollBy } from '@/utils/smoothScroll';
import type { SmoothScrollOptions } from '@/utils/smoothScroll';

interface UseSmoothScrollReturn {
  scrollToElement: (target: string | HTMLElement, options?: SmoothScrollOptions) => Promise<void>;
  scrollToTop: (options?: SmoothScrollOptions) => Promise<void>;
  scrollBy: (pixels: number, options?: SmoothScrollOptions) => Promise<void>;
  scrollToSection: (sectionId: string, options?: SmoothScrollOptions) => Promise<void>;
}

export const useSmoothScroll = (): UseSmoothScrollReturn => {
  const scrollToElementHandler = useCallback(
    (target: string | HTMLElement, options?: SmoothScrollOptions) => {
      return scrollToElement(target, options);
    },
    []
  );

  const scrollToTopHandler = useCallback((options?: SmoothScrollOptions) => {
    return scrollToTop(options);
  }, []);

  const scrollByHandler = useCallback(
    (pixels: number, options?: SmoothScrollOptions) => {
      return scrollBy(pixels, options);
    },
    []
  );

  const scrollToSection = useCallback(
    (sectionId: string, options?: SmoothScrollOptions) => {
      const target = (sectionId && sectionId.startsWith && sectionId.startsWith('#')) ? sectionId : `#${sectionId}`;
      return scrollToElement(target, {
        offset: 80, // Учитываем высоту header
        duration: 800,
        easing: 'easeInOutCubic',
        ...options,
      });
    },
    []
  );

  return {
    scrollToElement: scrollToElementHandler,
    scrollToTop: scrollToTopHandler,
    scrollBy: scrollByHandler,
    scrollToSection,
  };
};

export default useSmoothScroll;
