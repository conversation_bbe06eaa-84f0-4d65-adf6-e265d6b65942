import { useState, useEffect } from 'react';
import type { Tariff, Currency } from '@/types';
import apiService from '@/services/api';

interface UseTariffsReturn {
  tariffs: Tariff[];
  loading: boolean;
  error: string | null;
  selectedCurrency: Currency;
  setSelectedCurrency: (currency: Currency) => void;
  refetch: () => Promise<void>;
}

export const useTariffs = (): UseTariffsReturn => {
  const [tariffs, setTariffs] = useState<Tariff[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedCurrency, setSelectedCurrency] = useState<Currency>('RUB');

  const fetchTariffs = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await apiService.getTariffs();
      setTariffs(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Ошибка загрузки тарифов');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTariffs();
  }, []);

  return {
    tariffs,
    loading,
    error,
    selectedCurrency,
    setSelectedCurrency,
    refetch: fetchTariffs,
  };
};
