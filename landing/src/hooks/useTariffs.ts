import { useState, useEffect } from 'react';
import { tariffsService } from '@/services/tariffsService';
import type { Currency, Tariff } from '@/types/api';

interface UseTariffsReturn {
  tariffs: Tariff[];
  loading: boolean;
  error: string | null;
  currency: Currency;
  setCurrency: (currency: Currency) => void;
  availableCurrencies: Currency[];
  formatPrice: (amount: number, curr?: Currency) => string;
  refreshTariffs: () => Promise<void>;
  getBestDeal: (tariff: Tariff) => { period: 'monthly' | 'quarterly' | 'yearly'; discount: number };
}

export const useTariffs = (): UseTariffsReturn => {
  const [tariffs, setTariffs] = useState<Tariff[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currency, setCurrency] = useState<Currency>('RUB');
  const [availableCurrencies, setAvailableCurrencies] = useState<Currency[]>(['RUB', 'USD', 'STARS']);

  const fetchTariffs = async () => {
    try {
      setLoading(true);
      setError(null);

      const data = await tariffsService.getTariffs();

      setTariffs(data.tariffs);
      setAvailableCurrencies(data.currencies);

      // Устанавливаем валюту по умолчанию, если текущая не поддерживается
      if (data.currencies && data.currencies.includes && !data.currencies.includes(currency)) {
        setCurrency(data.defaultCurrency);
      }
    } catch (err) {
      console.error('Error fetching tariffs:', err);
      setError(err instanceof Error ? err.message : 'Ошибка загрузки тарифов');
    } finally {
      setLoading(false);
    }
  };


  // Форматирование цены
  const formatPrice = (amount: number, curr?: Currency): string => {
    return tariffsService.formatPrice(amount, curr || currency);
  };

  // Получение лучшего предложения
  const getBestDeal = (tariff: Tariff) => {
    return tariffsService.getBestDeal(tariff, currency);
  };

  // Обновление тарифов
  const refreshTariffs = async () => {
    await fetchTariffs();
  };

  useEffect(() => {
    fetchTariffs();
  }, []);

  // Обновляем тарифы при смене валюты
  useEffect(() => {
    if (availableCurrencies && availableCurrencies.length > 0 && availableCurrencies.includes && !availableCurrencies.includes(currency)) {
      setCurrency(availableCurrencies[0]);
    }
  }, [availableCurrencies, currency]);

  return {
    tariffs,
    loading,
    error,
    currency,
    setCurrency,
    availableCurrencies,
    formatPrice,
    refreshTariffs,
    getBestDeal,
  };
};
