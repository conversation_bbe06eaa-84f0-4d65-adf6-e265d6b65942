import { useState, useEffect } from 'react';
import type { Tariff, Currency } from '@/types';
import apiService from '@/services/api';

interface UseTariffsReturn {
  tariffs: Tariff[];
  loading: boolean;
  error: string | null;
  selectedCurrency: Currency;
  setSelectedCurrency: (currency: Currency) => void;
  refetch: () => Promise<void>;
}

export const useTariffs = (): UseTariffsReturn => {
  const [tariffs, setTariffs] = useState<Tariff[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedCurrency, setSelectedCurrency] = useState<Currency>('RUB');

  const fetchTariffs = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await apiService.getTariffs();

      // Если API вернул данные, используем их
      if (data && data.length > 0) {
        setTariffs(data);
      } else {
        // Fallback данные, если API не работает
        console.warn('API returned empty tariffs, using fallback data');
        setTariffs([
          {
            id: 'fallback-1',
            name: 'Базовый',
            description: 'Идеальный выбор для начинающих',
            duration_days: 30,
            prices: { rub: 299, usd: 3.99, stars: 300 },
            features: [
              '5 устройств одновременно',
              'Безлимитная скорость',
              'Безлимитный трафик',
              'Стандартная поддержка',
            ],
            popular: false,
            characteristics: {
              speed: 'Безлимитная',
              devices: 5,
              locations: 10,
              support: true,
            },
          },
          {
            id: 'fallback-2',
            name: 'Премиум',
            description: 'Максимальная защита и скорость',
            duration_days: 30,
            prices: { rub: 599, usd: 7.99, stars: 600 },
            features: [
              '10 устройств одновременно',
              'Максимальная скорость',
              'Безлимитный трафик',
              'Приоритетная поддержка',
            ],
            popular: true,
            characteristics: {
              speed: 'Максимальная',
              devices: 10,
              locations: 10,
              support: true,
            },
          },
        ]);
      }
    } catch (err) {
      console.error('Error fetching tariffs:', err);
      setError(err instanceof Error ? err.message : 'Ошибка загрузки тарифов');

      // В случае ошибки также используем fallback данные
      setTariffs([
        {
          id: 'fallback-1',
          name: 'Базовый',
          description: 'Идеальный выбор для начинающих',
          duration_days: 30,
          prices: { rub: 299, usd: 3.99, stars: 300 },
          features: [
            '5 устройств одновременно',
            'Безлимитная скорость',
            'Безлимитный трафик',
            'Стандартная поддержка',
          ],
          popular: false,
          characteristics: {
            speed: 'Безлимитная',
            devices: 5,
            locations: 10,
            support: true,
          },
        },
        {
          id: 'fallback-2',
          name: 'Премиум',
          description: 'Максимальная защита и скорость',
          duration_days: 30,
          prices: { rub: 599, usd: 7.99, stars: 600 },
          features: [
            '10 устройств одновременно',
            'Максимальная скорость',
            'Безлимитный трафик',
            'Приоритетная поддержка',
          ],
          popular: true,
          characteristics: {
            speed: 'Максимальная',
            devices: 10,
            locations: 10,
            support: true,
          },
        },
      ]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTariffs();
  }, []);

  return {
    tariffs,
    loading,
    error,
    selectedCurrency,
    setSelectedCurrency,
    refetch: fetchTariffs,
  };
};
