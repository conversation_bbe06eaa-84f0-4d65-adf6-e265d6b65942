// Используем глобальные переменные React вместо импортов
declare global {
  interface Window {
    React: any;
    ReactDOM: any;
  }
}

function App() {
  return window.React.createElement('div', {
    style: { padding: '20px', fontFamily: 'Arial, sans-serif' }
  }, [
    window.React.createElement('h1', { key: 'title' }, 'UnveilVPN Landing Page'),
    window.React.createElement('p', { key: 'desc' }, 'Это простая тестовая версия лендинговой страницы'),
    window.React.createElement('button', {
      key: 'btn',
      onClick: () => alert('Кнопка работает!')
    }, 'Тест кнопки')
  ]);
}

const container = document.getElementById('root');
if (!container) throw new Error('Root element not found');

// Проверяем, что React загружен
if (typeof window.React !== 'undefined' && typeof window.ReactDOM !== 'undefined') {
  const root = window.ReactDOM.createRoot(container);
  root.render(window.React.createElement(App));
} else {
  // Fallback если React не загружен
  container.innerHTML = `
    <div style="padding: 20px; font-family: Arial, sans-serif;">
      <h1>UnveilVPN Landing Page</h1>
      <p>React не загружен. Показываем fallback версию.</p>
      <button onclick="alert('Кнопка работает!')">Тест кнопки</button>
    </div>
  `;
}
