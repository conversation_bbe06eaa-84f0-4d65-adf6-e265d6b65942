/**
 * Polyfills для обеспечения совместимости со старыми браузерами
 * Загружаются только при необходимости для оптимизации производительности
 */

// import { detectBrowserCapabilities } from '@/utils/browserDetection'; // Не используется в текущей реализации

// Типы для polyfills
interface PolyfillConfig {
  name: string;
  check: () => boolean;
  load: () => Promise<void>;
  priority: 'critical' | 'important' | 'optional';
}

/**
 * Конфигурация polyfills
 */
const polyfillConfigs: PolyfillConfig[] = [
  // Promise polyfill (критический)
  {
    name: 'Promise',
    check: () => 'Promise' in window,
    load: async () => {
      if (!('Promise' in window)) {
        // Встроенный Promise polyfill
        (window as any).Promise = class Promise {
          constructor(executor: (resolve: (value?: any) => void, reject: (reason?: any) => void) => void) {
            let state = 'pending';
            let value: any;
            const handlers: Array<{ onFulfilled?: (value: any) => any; onRejected?: (reason: any) => any; resolve: (value: any) => void; reject: (reason: any) => void }> = [];

            const resolve = (result: any) => {
              if (state === 'pending') {
                state = 'fulfilled';
                value = result;
                handlers.forEach(handle);
                handlers.length = 0;
              }
            };

            const reject = (error: any) => {
              if (state === 'pending') {
                state = 'rejected';
                value = error;
                handlers.forEach(handle);
                handlers.length = 0;
              }
            };

            const handle = (handler: any) => {
              if (state === 'pending') {
                handlers.push(handler);
              } else {
                if (state === 'fulfilled' && typeof handler.onFulfilled === 'function') {
                  handler.onFulfilled(value);
                } else if (state === 'rejected' && typeof handler.onRejected === 'function') {
                  handler.onRejected(value);
                }
              }
            };

            this.then = (onFulfilled?: (value: any) => any, onRejected?: (reason: any) => any) => {
              return new (window as any).Promise((resolve: any, reject: any) => {
                handle({
                  onFulfilled: (result: any) => {
                    if (onFulfilled) {
                      try {
                        resolve(onFulfilled(result));
                      } catch (ex) {
                        reject(ex);
                      }
                    } else {
                      resolve(result);
                    }
                  },
                  onRejected: (error: any) => {
                    if (onRejected) {
                      try {
                        resolve(onRejected(error));
                      } catch (ex) {
                        reject(ex);
                      }
                    } else {
                      reject(error);
                    }
                  },
                  resolve,
                  reject
                });
              });
            };

            this.catch = (onRejected: (reason: any) => any) => {
              return this.then(undefined, onRejected);
            };

            try {
              executor(resolve, reject);
            } catch (error) {
              reject(error);
            }
          }

          then: (onFulfilled?: (value: any) => any, onRejected?: (reason: any) => any) => any;
          catch: (onRejected: (reason: any) => any) => any;

          static resolve(value: any) {
            return new (window as any).Promise((resolve: any) => resolve(value));
          }

          static reject(reason: any) {
            return new (window as any).Promise((_: any, reject: any) => reject(reason));
          }

          static all(promises: any[]) {
            return new (window as any).Promise((resolve: any, reject: any) => {
              if (promises.length === 0) {
                resolve([]);
                return;
              }
              let remaining = promises.length;
              const results: any[] = [];
              promises.forEach((promise, index) => {
                (window as any).Promise.resolve(promise).then((value: any) => {
                  results[index] = value;
                  remaining--;
                  if (remaining === 0) {
                    resolve(results);
                  }
                }).catch(reject);
              });
            });
          }
        };
      }
    },
    priority: 'critical',
  },

  // Fetch polyfill (критический)
  {
    name: 'fetch',
    check: () => 'fetch' in window,
    load: async () => {
      if (!('fetch' in window)) {
        // Встроенный fetch polyfill
        (window as any).fetch = function(url: string, options: any = {}) {
          return new (window as any).Promise((resolve: any, reject: any) => {
            const xhr = new XMLHttpRequest();
            xhr.open(options.method || 'GET', url);

            if (options && options.headers && typeof options.headers === 'object') {
              const headers = options.headers as Record<string, string>;
              if (headers && Object.keys) {
                Object.keys(headers).forEach(key => {
                  xhr.setRequestHeader(key, headers[key]);
                });
              }
            }

            xhr.onload = () => {
              resolve({
                ok: xhr.status >= 200 && xhr.status < 300,
                status: xhr.status,
                statusText: xhr.statusText,
                text: () => (window as any).Promise.resolve(xhr.responseText),
                json: () => (window as any).Promise.resolve(JSON.parse(xhr.responseText))
              });
            };

            xhr.onerror = () => reject(new Error('Network error'));
            xhr.send(options.body || null);
          });
        };
      }
    },
    priority: 'critical',
  },

  // Object.assign polyfill (критический)
  {
    name: 'Object.assign',
    check: () => 'assign' in Object,
    load: async () => {
      if (!('assign' in Object)) {
        Object.assign = function (target: object, ...sources: object[]) {
          if (target == null) {
            throw new TypeError('Cannot convert undefined or null to object');
          }
          const to = Object(target);
          for (let index = 0; index < sources.length; index++) {
            const nextSource = sources[index];
            if (nextSource != null) {
              for (const nextKey in nextSource) {
                if (Object.prototype.hasOwnProperty.call(nextSource, nextKey)) {
                  (to as any)[nextKey] = (nextSource as any)[nextKey];
                }
              }
            }
          }
          return to;
        };
      }
    },
    priority: 'critical',
  },

  // Array.from polyfill (важный)
  {
    name: 'Array.from',
    check: () => 'from' in Array,
    load: async () => {
      if (!('from' in Array)) {
        Array.from = function <T>(
          arrayLike: ArrayLike<T>,
          mapFn?: (v: T, k: number) => T,
          thisArg?: unknown
        ) {
          const items = Object(arrayLike);
          if (arrayLike == null) {
            throw new TypeError('Array.from requires an array-like object - not null or undefined');
          }
          const mapFunction = mapFn === undefined ? undefined : mapFn;
          if (typeof mapFunction !== 'undefined' && typeof mapFunction !== 'function') {
            throw new TypeError(
              'Array.from: when provided, the second argument must be a function'
            );
          }
          const len = parseInt(items.length) || 0;
          const A = new Array(len);
          let k = 0;
          while (k < len) {
            const kValue = items[k];
            if (mapFunction) {
              A[k] =
                typeof thisArg === 'undefined'
                  ? mapFunction(kValue, k)
                  : mapFunction.call(thisArg, kValue, k);
            } else {
              A[k] = kValue;
            }
            k += 1;
          }
          A.length = len;
          return A;
        };
      }
    },
    priority: 'important',
  },

  // Array.includes polyfill (важный)
  {
    name: 'Array.includes',
    check: () => 'includes' in Array.prototype,
    load: async () => {
      if (!('includes' in Array.prototype)) {
        Array.prototype.includes = function <T>(this: T[], searchElement: T, fromIndex?: number) {
          const O = Object(this);
          const len = parseInt(O.length) || 0;
          if (len === 0) return false;
          const n = parseInt(String(fromIndex || 0)) || 0;
          let k = n >= 0 ? n : Math.max(len + n, 0);
          while (k < len) {
            if (O[k] === searchElement) return true;
            k++;
          }
          return false;
        };
      }
    },
    priority: 'important',
  },

  // String.includes polyfill (важный)
  {
    name: 'String.includes',
    check: () => 'includes' in String.prototype,
    load: async () => {
      if (!('includes' in String.prototype)) {
        String.prototype.includes = function (search: string, start?: number) {
          if (typeof start !== 'number') {
            start = 0;
          }
          if (start + search.length > this.length) {
            return false;
          } else {
            return this.indexOf(search, start) !== -1;
          }
        };
      }
    },
    priority: 'important',
  },

  // String.startsWith polyfill (важный)
  {
    name: 'String.startsWith',
    check: () => 'startsWith' in String.prototype,
    load: async () => {
      if (!('startsWith' in String.prototype)) {
        String.prototype.startsWith = function (search: string, pos?: number) {
          return this.substr(!pos || pos < 0 ? 0 : +pos, search.length) === search;
        };
      }
    },
    priority: 'important',
  },

  // String.endsWith polyfill (важный)
  {
    name: 'String.endsWith',
    check: () => 'endsWith' in String.prototype,
    load: async () => {
      if (!('endsWith' in String.prototype)) {
        String.prototype.endsWith = function (search: string, this_len?: number) {
          if (this_len === undefined || this_len > this.length) {
            this_len = this.length;
          }
          return this.substring(this_len - search.length, this_len) === search;
        };
      }
    },
    priority: 'important',
  },

  // IntersectionObserver polyfill (опциональный)
  {
    name: 'IntersectionObserver',
    check: () => 'IntersectionObserver' in window,
    load: async () => {
      if (!('IntersectionObserver' in window)) {
        // Простая заглушка для IntersectionObserver
        (window as any).IntersectionObserver = class IntersectionObserver {
          constructor(callback: any, _options: any = {}) {
            this.callback = callback;
            this.elements = new Set();
          }

          observe(element: Element) {
            this.elements.add(element);
            // Простая эмуляция - сразу вызываем callback
            setTimeout(() => {
              this.callback([{
                target: element,
                isIntersecting: true,
                intersectionRatio: 1,
                boundingClientRect: element.getBoundingClientRect(),
                intersectionRect: element.getBoundingClientRect(),
                rootBounds: null,
                time: Date.now()
              }]);
            }, 100);
          }

          unobserve(element: Element) {
            this.elements.delete(element);
          }

          disconnect() {
            this.elements.clear();
          }

          private callback: any;
          private elements: Set<Element>;
        };
      }
    },
    priority: 'optional',
  },

  // ResizeObserver polyfill (опциональный)
  {
    name: 'ResizeObserver',
    check: () => 'ResizeObserver' in window,
    load: async () => {
      if (!('ResizeObserver' in window)) {
        // Простая заглушка для ResizeObserver
        (window as any).ResizeObserver = class ResizeObserver {
          constructor(callback: any) {
            this.callback = callback;
            this.elements = new Set();
          }

          observe(element: Element) {
            this.elements.add(element);
            // Простая эмуляция - вызываем callback при изменении размера окна
            const resizeHandler = () => {
              this.callback([{
                target: element,
                contentRect: element.getBoundingClientRect(),
                borderBoxSize: [{ inlineSize: element.clientWidth, blockSize: element.clientHeight }],
                contentBoxSize: [{ inlineSize: element.clientWidth, blockSize: element.clientHeight }],
                devicePixelContentBoxSize: [{ inlineSize: element.clientWidth, blockSize: element.clientHeight }]
              }]);
            };

            window.addEventListener('resize', resizeHandler);
            (element as any).__resizeHandler = resizeHandler;
          }

          unobserve(element: Element) {
            this.elements.delete(element);
            if ((element as any).__resizeHandler) {
              window.removeEventListener('resize', (element as any).__resizeHandler);
              delete (element as any).__resizeHandler;
            }
          }

          disconnect() {
            this.elements.forEach(element => this.unobserve(element));
            this.elements.clear();
          }

          private callback: any;
          private elements: Set<Element>;
        };
      }
    },
    priority: 'optional',
  },

  // CSS.supports polyfill (опциональный)
  {
    name: 'CSS.supports',
    check: () => 'CSS' in window && 'supports' in CSS,
    load: async () => {
      if (!('CSS' in window)) {
        (window as any).CSS = {};
      }
      if (!('supports' in CSS)) {
        CSS.supports = function (property: string, value?: string) {
          // Простая реализация для базовой совместимости
          const element = document.createElement('div');
          if (value) {
            try {
              element.style.setProperty(property, value);
              return element.style.getPropertyValue(property) === value;
            } catch {
              return false;
            }
          } else {
            // Для CSS селекторов
            try {
              document.querySelector(property);
              return true;
            } catch {
              return false;
            }
          }
        };
      }
    },
    priority: 'optional',
  },
];

/**
 * Загружает polyfills по приоритету
 */
export async function loadPolyfills(
  priority: 'critical' | 'important' | 'optional' = 'critical'
): Promise<void> {
  // const capabilities = detectBrowserCapabilities(); // Не используется в текущей реализации

  // Определяем какие polyfills нужно загрузить
  const polyfillsToLoad = polyfillConfigs.filter((config) => {
    // Проверяем приоритет
    const priorityOrder = { critical: 0, important: 1, optional: 2 };
    if (priorityOrder[config.priority] > priorityOrder[priority]) {
      return false;
    }

    // Проверяем нужен ли polyfill
    return !config.check();
  });

  if (polyfillsToLoad.length === 0) {
    console.log('🎉 No polyfills needed for this browser');
    return;
  }

  console.log(
    `🔧 Loading ${polyfillsToLoad.length} polyfills:`,
    polyfillsToLoad.map((p) => p.name)
  );

  // Загружаем polyfills параллельно
  const loadPromises = polyfillsToLoad.map(async (config) => {
    try {
      await config.load();
      console.log(`✅ Loaded polyfill: ${config.name}`);
    } catch (error) {
      console.error(`❌ Failed to load polyfill: ${config.name}`, error);
    }
  });

  await Promise.all(loadPromises);
  console.log('🎉 All polyfills loaded successfully');
}

/**
 * Загружает критические polyfills синхронно
 */
export function loadCriticalPolyfillsSync(): void {
  const criticalPolyfills = polyfillConfigs.filter(
    (config) => config.priority === 'critical' && !config.check()
  );

  criticalPolyfills.forEach((config) => {
    try {
      // Для критических polyfills используем синхронную загрузку
      if (config.name === 'Object.assign' && !config.check()) {
        config.load();
      }
    } catch (error) {
      console.error(`❌ Failed to load critical polyfill: ${config.name}`, error);
    }
  });
}

/**
 * Проверяет какие polyfills нужны для текущего браузера
 */
export function getRequiredPolyfills(): string[] {
  return polyfillConfigs.filter((config) => !config.check()).map((config) => config.name);
}

/**
 * Автоматическая загрузка polyfills при импорте модуля
 * ОТКЛЮЧЕНО: полифиллы загружаются только при необходимости для старых браузеров
 */
// if (typeof window !== 'undefined') {
//   // Загружаем критические polyfills сразу
//   loadCriticalPolyfillsSync();

//   // Загружаем остальные polyfills асинхронно
//   loadPolyfills('important').catch((error) => {
//     console.error('Failed to load polyfills:', error);
//   });
// }
