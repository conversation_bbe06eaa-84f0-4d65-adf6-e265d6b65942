/**
 * Polyfills для обеспечения совместимости со старыми браузерами
 * Загружаются только при необходимости для оптимизации производительности
 */

// import { detectBrowserCapabilities } from '@/utils/browserDetection'; // Не используется в текущей реализации

// Типы для polyfills
interface PolyfillConfig {
  name: string;
  check: () => boolean;
  load: () => Promise<void>;
  priority: 'critical' | 'important' | 'optional';
}

/**
 * Конфигурация polyfills
 */
const polyfillConfigs: PolyfillConfig[] = [
  // Promise polyfill (критический)
  {
    name: 'Promise',
    check: () => 'Promise' in window,
    load: async () => {
      if (!('Promise' in window)) {
        await import('es6-promise/auto' as any);
      }
    },
    priority: 'critical'
  },

  // Fetch polyfill (критический)
  {
    name: 'fetch',
    check: () => 'fetch' in window,
    load: async () => {
      if (!('fetch' in window)) {
        await import('whatwg-fetch' as any);
      }
    },
    priority: 'critical'
  },

  // Object.assign polyfill (критический)
  {
    name: 'Object.assign',
    check: () => 'assign' in Object,
    load: async () => {
      if (!('assign' in Object)) {
        Object.assign = function(target: any, ...sources: any[]) {
          if (target == null) {
            throw new TypeError('Cannot convert undefined or null to object');
          }
          const to = Object(target);
          for (let index = 0; index < sources.length; index++) {
            const nextSource = sources[index];
            if (nextSource != null) {
              for (const nextKey in nextSource) {
                if (Object.prototype.hasOwnProperty.call(nextSource, nextKey)) {
                  to[nextKey] = nextSource[nextKey];
                }
              }
            }
          }
          return to;
        };
      }
    },
    priority: 'critical'
  },

  // Array.from polyfill (важный)
  {
    name: 'Array.from',
    check: () => 'from' in Array,
    load: async () => {
      if (!('from' in Array)) {
        Array.from = function(arrayLike: any, mapFn?: any, thisArg?: any) {
          const C = this;
          const items = Object(arrayLike);
          if (arrayLike == null) {
            throw new TypeError('Array.from requires an array-like object - not null or undefined');
          }
          const mapFunction = mapFn === undefined ? undefined : mapFn;
          if (typeof mapFunction !== 'undefined' && typeof mapFunction !== 'function') {
            throw new TypeError('Array.from: when provided, the second argument must be a function');
          }
          const len = parseInt(items.length) || 0;
          const A = typeof C === 'function' ? Object(new C(len)) : new Array(len);
          let k = 0;
          while (k < len) {
            const kValue = items[k];
            if (mapFunction) {
              A[k] = typeof thisArg === 'undefined' ? mapFunction(kValue, k) : mapFunction.call(thisArg, kValue, k);
            } else {
              A[k] = kValue;
            }
            k += 1;
          }
          A.length = len;
          return A;
        };
      }
    },
    priority: 'important'
  },

  // Array.includes polyfill (важный)
  {
    name: 'Array.includes',
    check: () => 'includes' in Array.prototype,
    load: async () => {
      if (!('includes' in Array.prototype)) {
        Array.prototype.includes = function(searchElement: any, fromIndex?: number) {
          const O = Object(this);
          const len = parseInt(O.length) || 0;
          if (len === 0) return false;
          const n = parseInt(String(fromIndex || 0)) || 0;
          let k = n >= 0 ? n : Math.max(len + n, 0);
          while (k < len) {
            if (O[k] === searchElement) return true;
            k++;
          }
          return false;
        };
      }
    },
    priority: 'important'
  },

  // String.includes polyfill (важный)
  {
    name: 'String.includes',
    check: () => 'includes' in String.prototype,
    load: async () => {
      if (!('includes' in String.prototype)) {
        String.prototype.includes = function(search: string, start?: number) {
          if (typeof start !== 'number') {
            start = 0;
          }
          if (start + search.length > this.length) {
            return false;
          } else {
            return this.indexOf(search, start) !== -1;
          }
        };
      }
    },
    priority: 'important'
  },

  // String.startsWith polyfill (важный)
  {
    name: 'String.startsWith',
    check: () => 'startsWith' in String.prototype,
    load: async () => {
      if (!('startsWith' in String.prototype)) {
        String.prototype.startsWith = function(search: string, pos?: number) {
          return this.substr(!pos || pos < 0 ? 0 : +pos, search.length) === search;
        };
      }
    },
    priority: 'important'
  },

  // String.endsWith polyfill (важный)
  {
    name: 'String.endsWith',
    check: () => 'endsWith' in String.prototype,
    load: async () => {
      if (!('endsWith' in String.prototype)) {
        String.prototype.endsWith = function(search: string, this_len?: number) {
          if (this_len === undefined || this_len > this.length) {
            this_len = this.length;
          }
          return this.substring(this_len - search.length, this_len) === search;
        };
      }
    },
    priority: 'important'
  },

  // IntersectionObserver polyfill (опциональный)
  {
    name: 'IntersectionObserver',
    check: () => 'IntersectionObserver' in window,
    load: async () => {
      if (!('IntersectionObserver' in window)) {
        await import('intersection-observer' as any);
      }
    },
    priority: 'optional'
  },

  // ResizeObserver polyfill (опциональный)
  {
    name: 'ResizeObserver',
    check: () => 'ResizeObserver' in window,
    load: async () => {
      if (!('ResizeObserver' in window)) {
        const { ResizeObserver } = await import('@juggle/resize-observer');
        window.ResizeObserver = ResizeObserver;
      }
    },
    priority: 'optional'
  },

  // CSS.supports polyfill (опциональный)
  {
    name: 'CSS.supports',
    check: () => 'CSS' in window && 'supports' in CSS,
    load: async () => {
      if (!('CSS' in window)) {
        (window as any).CSS = {};
      }
      if (!('supports' in CSS)) {
        CSS.supports = function(property: string, value?: string) {
          // Простая реализация для базовой совместимости
          const element = document.createElement('div');
          if (value) {
            try {
              element.style.setProperty(property, value);
              return element.style.getPropertyValue(property) === value;
            } catch (e) {
              return false;
            }
          } else {
            // Для CSS селекторов
            try {
              document.querySelector(property);
              return true;
            } catch (e) {
              return false;
            }
          }
        };
      }
    },
    priority: 'optional'
  }
];

/**
 * Загружает polyfills по приоритету
 */
export async function loadPolyfills(priority: 'critical' | 'important' | 'optional' = 'critical'): Promise<void> {
  // const capabilities = detectBrowserCapabilities(); // Не используется в текущей реализации
  
  // Определяем какие polyfills нужно загрузить
  const polyfillsToLoad = polyfillConfigs.filter(config => {
    // Проверяем приоритет
    const priorityOrder = { critical: 0, important: 1, optional: 2 };
    if (priorityOrder[config.priority] > priorityOrder[priority]) {
      return false;
    }
    
    // Проверяем нужен ли polyfill
    return !config.check();
  });

  if (polyfillsToLoad.length === 0) {
    console.log('🎉 No polyfills needed for this browser');
    return;
  }

  console.log(`🔧 Loading ${polyfillsToLoad.length} polyfills:`, polyfillsToLoad.map(p => p.name));

  // Загружаем polyfills параллельно
  const loadPromises = polyfillsToLoad.map(async (config) => {
    try {
      await config.load();
      console.log(`✅ Loaded polyfill: ${config.name}`);
    } catch (error) {
      console.error(`❌ Failed to load polyfill: ${config.name}`, error);
    }
  });

  await Promise.all(loadPromises);
  console.log('🎉 All polyfills loaded successfully');
}

/**
 * Загружает критические polyfills синхронно
 */
export function loadCriticalPolyfillsSync(): void {
  const criticalPolyfills = polyfillConfigs.filter(config => 
    config.priority === 'critical' && !config.check()
  );

  criticalPolyfills.forEach(config => {
    try {
      // Для критических polyfills используем синхронную загрузку
      if (config.name === 'Object.assign' && !config.check()) {
        config.load();
      }
    } catch (error) {
      console.error(`❌ Failed to load critical polyfill: ${config.name}`, error);
    }
  });
}

/**
 * Проверяет какие polyfills нужны для текущего браузера
 */
export function getRequiredPolyfills(): string[] {
  return polyfillConfigs
    .filter(config => !config.check())
    .map(config => config.name);
}

/**
 * Автоматическая загрузка polyfills при импорте модуля
 */
if (typeof window !== 'undefined') {
  // Загружаем критические polyfills сразу
  loadCriticalPolyfillsSync();
  
  // Загружаем остальные polyfills асинхронно
  loadPolyfills('important').catch(error => {
    console.error('Failed to load polyfills:', error);
  });
}
