// Polyfill для require() в браузере
// Это решает проблему "require is not defined" напрямую

declare global {
  interface Window {
    require: any;
    module: any;
    exports: any;
  }
}

// Создаем простой модульный реестр
const moduleRegistry: Record<string, any> = {};

// Регистрируем основные модули
function registerModule(id: string, moduleExports: any) {
  moduleRegistry[id] = moduleExports;
}

// Динамический импорт React
async function loadReact() {
  try {
    // Пытаемся загрузить React динамически
    const React = await import('react');
    registerModule('react', React.default || React);
    return React.default || React;
  } catch (error) {
    console.warn('Failed to load React dynamically:', error);
    return null;
  }
}

// Динамический импорт ReactDOM
async function loadReactDOM() {
  try {
    const ReactDOM = await import('react-dom/client');
    registerModule('react-dom/client', ReactDOM);
    return ReactDOM;
  } catch (error) {
    console.warn('Failed to load ReactDOM dynamically:', error);
    return null;
  }
}

// Реализация require()
function requirePolyfill(id: string): any {
  // Если модуль уже зарегистрирован, возвращаем его
  if (moduleRegistry[id]) {
    return moduleRegistry[id];
  }

  // Обработка специальных случаев
  switch (id) {
    case 'react':
      // Пытаемся загрузить React динамически
      loadReact().then(react => {
        if (react) {
          console.log('React loaded successfully');
        }
      });

      // Возвращаем заглушку с базовой функциональностью
      const reactStub = {
        createElement: (type: any, props: any, ...children: any[]) => {
          return { type, props: { ...props, children }, key: props?.key || null };
        },
        StrictMode: ({ children }: any) => children,
        Fragment: ({ children }: any) => children,
      };
      registerModule('react', reactStub);
      return reactStub;

    case 'react-dom/client':
      // Пытаемся загрузить ReactDOM динамически
      loadReactDOM().then(reactDOM => {
        if (reactDOM) {
          console.log('ReactDOM loaded successfully');
        }
      });

      // Возвращаем заглушку
      const reactDOMStub = {
        createRoot: (container: Element) => ({
          render: (element: any) => {
            if (container) {
              // Простой рендеринг для тестирования
              if (element && element.type) {
                container.innerHTML = `<div>React App Loaded (${element.type})</div>`;
              } else {
                container.innerHTML = '<div>React App Loaded</div>';
              }
            }
          }
        })
      };
      registerModule('react-dom/client', reactDOMStub);
      return reactDOMStub;

    case './index.css':
    case './App':
    case './components/BrowserCompatibilityWrapper':
    case './utils/browserDetection':
    case './polyfills':
    case './utils/cssPolyfills':
    case './styles/polyfills.css':
      // Для CSS и других файлов возвращаем пустой объект
      console.log(`Loading module: ${id}`);
      return {};

    default:
      console.warn(`Module not found: ${id}, returning empty object`);
      return {};
  }
}

// Устанавливаем require в глобальную область
if (typeof window !== 'undefined') {
  window.require = requirePolyfill;
  window.module = { exports: {} };
  window.exports = window.module.exports;
}

// Экспортируем для использования в модулях
export { requirePolyfill as require, registerModule };
