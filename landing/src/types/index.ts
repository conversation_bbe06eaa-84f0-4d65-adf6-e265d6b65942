// Типы для тарифов
export interface Tariff {
  id: string;
  name: string;
  description: string;
  duration_days: number;
  prices: {
    rub: number;
    usd: number;
    stars: number;
  };
  features: string[];
  popular?: boolean;
  characteristics?: {
    speed?: string;
    devices?: number;
    locations?: number;
    support?: boolean;
  };
}

// Типы для платежных методов
export type PaymentMethod = 'yookassa' | 'cryptomus' | 'telegram_stars';
export type Currency = 'RUB' | 'USD' | 'STARS';

export interface PaymentConfig {
  type: PaymentMethod;
  currency: Currency;
  enabled: boolean;
}

// Типы для API ответов
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface TariffsResponse {
  tariffs: Tariff[];
}

export interface TrialInfo {
  enabled: boolean;
  duration_days: number;
  description: string;
  terms: string[];
}

// Типы для FAQ
export interface FAQItem {
  id: string;
  question: string;
  answer: string;
  category?: string;
}

// Типы для компонентов
export interface ButtonProps {
  variant?: 'primary' | 'secondary' | 'outline';
  size?: 'sm' | 'md' | 'lg';
  children: React.ReactNode;
  onClick?: () => void;
  disabled?: boolean;
  loading?: boolean;
  className?: string;
}

export interface CardProps {
  children: React.ReactNode;
  className?: string;
  hover?: boolean;
}

export interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
}

// Типы для форм
export interface ContactForm {
  name: string;
  email: string;
  message: string;
}

// Типы для навигации
export interface NavItem {
  label: string;
  href: string;
  external?: boolean;
}

// Типы для анимаций
export interface AnimationProps {
  delay?: number;
  duration?: number;
  direction?: 'up' | 'down' | 'left' | 'right';
}

// Типы для статистики
export interface Stats {
  users: number;
  countries: number;
  servers: number;
  uptime: number;
}

// Типы для отзывов
export interface Testimonial {
  id: string;
  name: string;
  avatar?: string;
  rating: number;
  comment: string;
  date: string;
}

// Типы для конфигурации приложения
export interface AppConfig {
  api: {
    baseUrl: string;
    timeout: number;
  };
  features: {
    trial: boolean;
    testimonials: boolean;
    analytics: boolean;
  };
  social: {
    telegram?: string;
    twitter?: string;
    github?: string;
  };
}
