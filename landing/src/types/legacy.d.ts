/**
 * Типы для поддержки старых браузеров и polyfills
 */

// Расширение глобального объекта Window
declare global {
  interface Window {
    // Browser capabilities detection
    browserCapabilities?: {
      modules: boolean;
      es6: boolean;
      es2015: boolean;
      es2017: boolean;
      es2020: boolean;
      fetch: boolean;
      promise: boolean;
      asyncAwait: boolean;
      intersectionObserver: boolean;
      cssGrid: boolean;
      cssFlexbox: boolean;
      cssCustomProperties: boolean;
      cssSupports: boolean;
      localStorage: boolean;
      sessionStorage: boolean;
      webWorkers: boolean;
      serviceWorkers: boolean;
      userAgent: string;
      isMobile: boolean;
      isTablet: boolean;
      isDesktop: boolean;
      chrome: number | null;
      firefox: number | null;
      safari: number | null;
      edge: number | null;
    };

    // Legacy browser support
    MSInputMethodContext?: any;
    DocumentTouch?: any;
    
    // IE specific
    attachEvent?: (event: string, listener: EventListener) => void;
    detachEvent?: (event: string, listener: EventListener) => void;
    
    // Analytics
    gtag?: (...args: any[]) => void;
    
    // Polyfills
    ResizeObserver?: any;
  }

  // CSS interface extensions
  interface CSS {
    supports?: (property: string, value?: string) => boolean;
  }

  // HTMLScriptElement extensions
  interface HTMLScriptElement {
    noModule?: boolean;
  }

  // Navigator extensions
  interface Navigator {
    maxTouchPoints?: number;
  }
}

// Polyfill типы для Array методов
declare global {
  interface Array<T> {
    includes?: (searchElement: T, fromIndex?: number) => boolean;
    from?: <U>(arrayLike: ArrayLike<U>, mapfn?: (v: U, k: number) => T, thisArg?: any) => T[];
  }

  interface ArrayConstructor {
    from?: <T, U>(arrayLike: ArrayLike<U>, mapfn?: (v: U, k: number) => T, thisArg?: any) => T[];
  }
}

// Polyfill типы для String методов
declare global {
  interface String {
    includes?: (search: string, start?: number) => boolean;
    startsWith?: (search: string, pos?: number) => boolean;
    endsWith?: (search: string, length?: number) => boolean;
    padStart?: (targetLength: number, padString?: string) => string;
    padEnd?: (targetLength: number, padString?: string) => string;
  }
}

// Polyfill типы для Object методов
declare global {
  interface ObjectConstructor {
    assign?: <T, U>(target: T, ...sources: U[]) => T & U;
    keys?: (o: any) => string[];
    values?: <T>(o: { [s: string]: T } | ArrayLike<T>) => T[];
    entries?: <T>(o: { [s: string]: T } | ArrayLike<T>) => [string, T][];
  }
}

// Типы для legacy API
export interface LegacyXMLHttpRequest {
  open(method: string, url: string, async?: boolean): void;
  send(data?: any): void;
  setRequestHeader(header: string, value: string): void;
  onreadystatechange: (() => void) | null;
  readyState: number;
  status: number;
  statusText: string;
  responseText: string;
  responseXML: Document | null;
}

// Типы для feature detection
export interface FeatureDetection {
  hasES6: boolean;
  hasModules: boolean;
  hasFetch: boolean;
  hasPromise: boolean;
  hasLocalStorage: boolean;
  hasSessionStorage: boolean;
  hasIntersectionObserver: boolean;
  hasResizeObserver: boolean;
  hasCSSGrid: boolean;
  hasCSSFlexbox: boolean;
  hasCSSCustomProperties: boolean;
  hasMediaQueries: boolean;
  hasTouch: boolean;
  isRetina: boolean;
}

// Типы для browser info
export interface BrowserInfo {
  name: string;
  version: number;
  engine: string;
  platform: string;
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  supportLevel: 'modern' | 'legacy' | 'unsupported';
}

// Типы для polyfill configuration
export interface PolyfillConfig {
  name: string;
  check: () => boolean;
  load: () => Promise<void>;
  priority: 'critical' | 'important' | 'optional';
  size?: number;
  dependencies?: string[];
}

// Типы для CSS feature support
export interface CSSFeatureSupport {
  flexbox: boolean;
  grid: boolean;
  customProperties: boolean;
  transforms: boolean;
  transitions: boolean;
  borderRadius: boolean;
  boxShadow: boolean;
  opacity: boolean;
  mediaQueries: boolean;
  calc: boolean;
  viewport: boolean;
}

// Типы для legacy event handling
export interface LegacyEventTarget {
  addEventListener?: (type: string, listener: EventListener, options?: boolean | AddEventListenerOptions) => void;
  removeEventListener?: (type: string, listener: EventListener, options?: boolean | EventListenerOptions) => void;
  attachEvent?: (type: string, listener: EventListener) => void;
  detachEvent?: (type: string, listener: EventListener) => void;
}

// Типы для compatibility wrapper
export interface CompatibilityWrapper {
  isSupported: boolean;
  supportLevel: 'modern' | 'legacy' | 'unsupported';
  requiredPolyfills: string[];
  missingFeatures: string[];
  recommendations: string[];
}

// Типы для API client compatibility
export interface APIClientOptions {
  baseURL: string;
  timeout?: number;
  retries?: number;
  useFetch?: boolean;
  useXHR?: boolean;
  headers?: Record<string, string>;
}

// Типы для responsive breakpoints
export type Breakpoint = 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';

export interface ResponsiveConfig {
  breakpoints: Record<Breakpoint, number>;
  containerMaxWidths: Record<Breakpoint, number>;
  gridColumns: Record<Breakpoint, number>;
}

// Типы для animation preferences
export interface AnimationPreferences {
  prefersReducedMotion: boolean;
  enableAnimations: boolean;
  animationDuration: 'fast' | 'normal' | 'slow';
  enableParallax: boolean;
  enableAutoplay: boolean;
}

// Типы для accessibility
export interface AccessibilityFeatures {
  screenReader: boolean;
  highContrast: boolean;
  largeText: boolean;
  keyboardNavigation: boolean;
  focusVisible: boolean;
  colorBlindness: boolean;
}

// Экспорт всех типов
export type {
  FeatureDetection,
  BrowserInfo,
  PolyfillConfig,
  CSSFeatureSupport,
  LegacyEventTarget,
  CompatibilityWrapper,
  APIClientOptions,
  ResponsiveConfig,
  AnimationPreferences,
  AccessibilityFeatures
};
