@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
/* Импорт responsive стилей */
@import './styles/responsive.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    @apply bg-white text-neutral-900 font-sans antialiased;
    margin: 0;
    min-height: 100vh;
  }

  html {
    scroll-behavior: smooth;
  }

  /* Улучшенная типографика */
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    @apply font-bold text-neutral-900;
  }

  p {
    @apply text-neutral-600 leading-relaxed;
  }

  /* Accessibility улучшения */
  *:focus {
    @apply outline-none;
  }

  /* Анимации для пользователей с ограниченными возможностями */
  @media (prefers-reduced-motion: reduce) {
    * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }
}

@layer components {
  /* Кнопки с новой дизайн-системой */
  .btn-primary {
    @apply bg-gradient-button hover:bg-gradient-button-hover text-white font-semibold py-3 px-6 rounded-lg shadow-button hover:shadow-button-hover transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }

  .btn-secondary {
    @apply bg-white border-2 border-primary-500 text-primary-500 hover:bg-primary-50 hover:border-primary-600 font-semibold py-3 px-6 rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }

  .btn-ghost {
    @apply bg-transparent text-primary-500 hover:bg-primary-50 hover:text-primary-600 font-semibold py-3 px-6 rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }

  /* Карточки */
  .card {
    @apply bg-white rounded-xl shadow-card hover:shadow-card-hover border border-neutral-200 p-6 transition-all duration-300;
  }

  .card-featured {
    @apply bg-gradient-card border-2 border-primary-200 shadow-card-hover p-6 rounded-xl;
  }

  /* Градиенты */
  .gradient-bg {
    @apply bg-gradient-to-br from-primary-50 to-secondary-50;
  }

  .hero-gradient {
    @apply bg-gradient-hero;
  }

  .gradient-text {
    @apply bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent;
  }

  /* Утилиты для анимаций */
  .animate-on-scroll {
    @apply opacity-0 translate-y-8 transition-all duration-700 ease-out;
  }

  .animate-on-scroll.in-view {
    @apply opacity-100 translate-y-0;
  }

  /* Responsive контейнеры */
  .section-container {
    @apply py-16 md:py-20 lg:py-24;
  }

  .content-container {
    @apply container mx-auto px-4 md:px-6 lg:px-8;
  }

  /* Типографика */
  .heading-xl {
    @apply text-3xl md:text-5xl lg:text-6xl font-bold text-neutral-900 leading-tight;
  }

  .heading-lg {
    @apply text-2xl md:text-4xl lg:text-5xl font-bold text-neutral-800 leading-tight;
  }

  .heading-md {
    @apply text-xl md:text-2xl lg:text-3xl font-semibold text-neutral-700 leading-tight;
  }

  .text-body {
    @apply text-base md:text-lg text-neutral-600 leading-relaxed;
  }

  .text-small {
    @apply text-sm md:text-base text-neutral-500;
  }
}
