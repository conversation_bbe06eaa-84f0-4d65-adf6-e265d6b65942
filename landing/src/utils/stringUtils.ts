/**
 * Утилиты для безопасной работы со строками в старых браузерах
 */

/**
 * Безопасная проверка включения подстроки
 */
export function safeIncludes(str: string | undefined | null, search: string): boolean {
  if (!str || typeof str !== 'string') return false;
  
  // Для старых браузеров без поддержки includes
  if (typeof str.includes === 'function') {
    return str.includes(search);
  }
  
  // Fallback для IE
  return str.indexOf(search) !== -1;
}

/**
 * Безопасная проверка начала строки
 */
export function safeStartsWith(str: string | undefined | null, search: string): boolean {
  if (!str || typeof str !== 'string') return false;
  
  // Для старых браузеров без поддержки startsWith
  if (typeof str.startsWith === 'function') {
    return str.startsWith(search);
  }
  
  // Fallback для IE
  return str.substr(0, search.length) === search;
}

/**
 * Безопасная проверка окончания строки
 */
export function safeEndsWith(str: string | undefined | null, search: string): boolean {
  if (!str || typeof str !== 'string') return false;
  
  // Для старых браузеров без поддержки endsWith
  if (typeof str.endsWith === 'function') {
    return str.endsWith(search);
  }
  
  // Fallback для IE
  return str.substr(str.length - search.length) === search;
}

/**
 * Безопасное приведение к нижнему регистру
 */
export function safeLowerCase(str: string | undefined | null): string {
  if (!str || typeof str !== 'string') return '';
  
  return str.toLowerCase();
}

/**
 * Безопасное приведение к верхнему регистру
 */
export function safeUpperCase(str: string | undefined | null): string {
  if (!str || typeof str !== 'string') return '';
  
  return str.toUpperCase();
}

/**
 * Безопасная обрезка строки
 */
export function safeTrim(str: string | undefined | null): string {
  if (!str || typeof str !== 'string') return '';
  
  // Для старых браузеров без поддержки trim
  if (typeof str.trim === 'function') {
    return str.trim();
  }
  
  // Fallback для IE8
  return str.replace(/^\s+|\s+$/g, '');
}

/**
 * Безопасная замена подстроки
 */
export function safeReplace(str: string | undefined | null, search: string | RegExp, replace: string): string {
  if (!str || typeof str !== 'string') return '';
  
  return str.replace(search, replace);
}

/**
 * Безопасное разделение строки
 */
export function safeSplit(str: string | undefined | null, separator: string | RegExp): string[] {
  if (!str || typeof str !== 'string') return [];
  
  return str.split(separator);
}

/**
 * Безопасное получение подстроки
 */
export function safeSubstring(str: string | undefined | null, start: number, end?: number): string {
  if (!str || typeof str !== 'string') return '';
  
  return str.substring(start, end);
}

/**
 * Безопасное получение символа по индексу
 */
export function safeCharAt(str: string | undefined | null, index: number): string {
  if (!str || typeof str !== 'string') return '';
  
  return str.charAt(index);
}

/**
 * Безопасное получение длины строки
 */
export function safeLength(str: string | undefined | null): number {
  if (!str || typeof str !== 'string') return 0;
  
  return str.length;
}

/**
 * Проверка на пустую строку
 */
export function isEmpty(str: string | undefined | null): boolean {
  return !str || typeof str !== 'string' || safeTrim(str).length === 0;
}

/**
 * Проверка на не пустую строку
 */
export function isNotEmpty(str: string | undefined | null): boolean {
  return !isEmpty(str);
}

/**
 * Безопасное извлечение числа из строки
 */
export function safeParseInt(str: string | undefined | null, radix?: number): number {
  if (!str || typeof str !== 'string') return 0;
  
  const result = parseInt(str, radix);
  return isNaN(result) ? 0 : result;
}

/**
 * Безопасное извлечение float из строки
 */
export function safeParseFloat(str: string | undefined | null): number {
  if (!str || typeof str !== 'string') return 0;
  
  const result = parseFloat(str);
  return isNaN(result) ? 0 : result;
}

/**
 * Экранирование HTML символов
 */
export function escapeHtml(str: string | undefined | null): string {
  if (!str || typeof str !== 'string') return '';
  
  const div = document.createElement('div');
  div.textContent = str;
  return div.innerHTML;
}

/**
 * Удаление HTML тегов
 */
export function stripHtml(str: string | undefined | null): string {
  if (!str || typeof str !== 'string') return '';
  
  const div = document.createElement('div');
  div.innerHTML = str;
  return div.textContent || div.innerText || '';
}

/**
 * Капитализация первой буквы
 */
export function capitalize(str: string | undefined | null): string {
  if (!str || typeof str !== 'string') return '';
  
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
}

/**
 * Преобразование в camelCase
 */
export function toCamelCase(str: string | undefined | null): string {
  if (!str || typeof str !== 'string') return '';
  
  return str.replace(/[-_\s]+(.)?/g, (_, char) => char ? char.toUpperCase() : '');
}

/**
 * Преобразование в kebab-case
 */
export function toKebabCase(str: string | undefined | null): string {
  if (!str || typeof str !== 'string') return '';
  
  return str
    .replace(/([a-z])([A-Z])/g, '$1-$2')
    .replace(/[\s_]+/g, '-')
    .toLowerCase();
}

/**
 * Усечение строки с многоточием
 */
export function truncate(str: string | undefined | null, maxLength: number, suffix: string = '...'): string {
  if (!str || typeof str !== 'string') return '';
  
  if (str.length <= maxLength) return str;
  
  return str.substring(0, maxLength - suffix.length) + suffix;
}

/**
 * Безопасное сравнение строк (case-insensitive)
 */
export function safeEquals(str1: string | undefined | null, str2: string | undefined | null): boolean {
  const s1 = safeLowerCase(str1);
  const s2 = safeLowerCase(str2);
  
  return s1 === s2;
}

/**
 * Генерация случайной строки
 */
export function generateRandomString(length: number = 8): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  
  return result;
}

/**
 * Форматирование строки с параметрами
 */
export function formatString(template: string, ...args: any[]): string {
  if (!template || typeof template !== 'string') return '';
  
  return template.replace(/{(\d+)}/g, (match, index) => {
    const argIndex = parseInt(index, 10);
    return args[argIndex] !== undefined ? String(args[argIndex]) : match;
  });
}
