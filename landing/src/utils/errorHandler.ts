/**
 * Утилиты для обработки ошибок
 */

export interface ErrorInfo {
  message: string;
  code?: string | number;
  type: 'network' | 'api' | 'validation' | 'timeout' | 'unknown';
  retryable: boolean;
  userMessage: string;
}

// Типы ошибок
export class NetworkError extends Error {
  public code?: string | number;

  constructor(message: string, code?: string | number) {
    super(message);
    this.name = 'NetworkError';
    this.code = code;
  }
}

export class APIError extends Error {
  public code?: string | number;
  public status?: number;

  constructor(message: string, code?: string | number, status?: number) {
    super(message);
    this.name = 'APIError';
    this.code = code;
    this.status = status;
  }
}

export class ValidationError extends Error {
  public field?: string;

  constructor(message: string, field?: string) {
    super(message);
    this.name = 'ValidationError';
    this.field = field;
  }
}

export class TimeoutError extends Error {
  constructor(message: string = 'Request timeout') {
    super(message);
    this.name = 'TimeoutError';
  }
}

// Анализ ошибок
export const analyzeError = (error: any): ErrorInfo => {
  // Сетевые ошибки
  if (error instanceof NetworkError || error.name === 'NetworkError') {
    return {
      message: error.message,
      code: error.code,
      type: 'network',
      retryable: true,
      userMessage: 'Проблемы с подключением к интернету. Проверьте соединение и попробуйте снова.',
    };
  }

  // API ошибки
  if (error instanceof APIError || error.name === 'APIError') {
    const status = error.status || 0;
    
    if (status >= 500) {
      return {
        message: error.message,
        code: error.code || status,
        type: 'api',
        retryable: true,
        userMessage: 'Временные проблемы на сервере. Попробуйте снова через несколько минут.',
      };
    }
    
    if (status >= 400 && status < 500) {
      return {
        message: error.message,
        code: error.code || status,
        type: 'api',
        retryable: false,
        userMessage: 'Ошибка запроса. Обновите страницу и попробуйте снова.',
      };
    }
  }

  // Ошибки валидации
  if (error instanceof ValidationError || error.name === 'ValidationError') {
    return {
      message: error.message,
      code: error.field,
      type: 'validation',
      retryable: false,
      userMessage: error.message,
    };
  }

  // Ошибки таймаута
  if (error instanceof TimeoutError || error.name === 'TimeoutError' || error.message?.includes('timeout')) {
    return {
      message: error.message,
      type: 'timeout',
      retryable: true,
      userMessage: 'Запрос выполняется слишком долго. Проверьте соединение и попробуйте снова.',
    };
  }

  // Ошибки fetch
  if (error.name === 'TypeError' && error.message?.includes('fetch')) {
    return {
      message: error.message,
      type: 'network',
      retryable: true,
      userMessage: 'Не удается подключиться к серверу. Проверьте интернет-соединение.',
    };
  }

  // Ошибки CORS
  if (error.message?.includes('CORS') || error.message?.includes('cross-origin')) {
    return {
      message: error.message,
      type: 'network',
      retryable: false,
      userMessage: 'Ошибка безопасности. Обновите страницу и попробуйте снова.',
    };
  }

  // Неизвестные ошибки
  return {
    message: error.message || 'Unknown error',
    type: 'unknown',
    retryable: true,
    userMessage: 'Произошла неожиданная ошибка. Попробуйте обновить страницу.',
  };
};

// Retry логика
export interface RetryOptions {
  maxAttempts?: number;
  baseDelay?: number;
  maxDelay?: number;
  backoffFactor?: number;
  retryCondition?: (error: any) => boolean;
}

export const withRetry = async <T>(
  operation: () => Promise<T>,
  options: RetryOptions = {}
): Promise<T> => {
  const {
    maxAttempts = 3,
    baseDelay = 1000,
    maxDelay = 10000,
    backoffFactor = 2,
    retryCondition = (error) => analyzeError(error).retryable,
  } = options;

  let lastError: any;
  
  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error;
      
      // Не повторяем на последней попытке
      if (attempt === maxAttempts) {
        break;
      }
      
      // Проверяем, можно ли повторить
      if (!retryCondition(error)) {
        break;
      }
      
      // Вычисляем задержку с экспоненциальным backoff
      const delay = Math.min(
        baseDelay * Math.pow(backoffFactor, attempt - 1),
        maxDelay
      );
      
      // Добавляем случайный jitter (±25%)
      const jitter = delay * 0.25 * (Math.random() * 2 - 1);
      const finalDelay = Math.max(0, delay + jitter);
      
      console.warn(`Attempt ${attempt} failed, retrying in ${Math.round(finalDelay)}ms:`, error);
      
      await new Promise(resolve => setTimeout(resolve, finalDelay));
    }
  }
  
  throw lastError;
};

// Обработчик глобальных ошибок
export class GlobalErrorHandler {
  private static instance: GlobalErrorHandler;
  private errorCallbacks: ((error: ErrorInfo) => void)[] = [];

  static getInstance(): GlobalErrorHandler {
    if (!GlobalErrorHandler.instance) {
      GlobalErrorHandler.instance = new GlobalErrorHandler();
    }
    return GlobalErrorHandler.instance;
  }

  // Подписка на ошибки
  onError(callback: (error: ErrorInfo) => void): () => void {
    this.errorCallbacks.push(callback);
    
    // Возвращаем функцию отписки
    return () => {
      const index = this.errorCallbacks.indexOf(callback);
      if (index > -1) {
        this.errorCallbacks.splice(index, 1);
      }
    };
  }

  // Обработка ошибки
  handleError(error: any, context?: string): void {
    const errorInfo = analyzeError(error);
    
    // Логируем ошибку
    console.error(`Error in ${context || 'unknown context'}:`, {
      error,
      errorInfo,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
    });

    // Уведомляем подписчиков
    this.errorCallbacks.forEach(callback => {
      try {
        callback(errorInfo);
      } catch (callbackError) {
        console.error('Error in error callback:', callbackError);
      }
    });
  }

  // Инициализация глобальных обработчиков
  init(): void {
    // Обработка необработанных промисов
    window.addEventListener('unhandledrejection', (event) => {
      this.handleError(event.reason, 'unhandledrejection');
    });

    // Обработка JavaScript ошибок
    window.addEventListener('error', (event) => {
      this.handleError(event.error || event.message, 'javascript');
    });
  }
}

// Экспорт единственного экземпляра
export const globalErrorHandler = GlobalErrorHandler.getInstance();

// Утилита для безопасного выполнения операций
export const safeExecute = async <T>(
  operation: () => Promise<T>,
  fallback: T,
  context?: string
): Promise<T> => {
  try {
    return await operation();
  } catch (error) {
    globalErrorHandler.handleError(error, context);
    return fallback;
  }
};

// Утилита для создания безопасных API вызовов
export const createSafeApiCall = <T>(
  apiCall: () => Promise<T>,
  fallbackData: T,
  retryOptions?: RetryOptions
) => {
  return async (): Promise<T> => {
    try {
      return await withRetry(apiCall, retryOptions);
    } catch (error) {
      globalErrorHandler.handleError(error, 'api-call');
      return fallbackData;
    }
  };
};

export default {
  analyzeError,
  withRetry,
  globalErrorHandler,
  safeExecute,
  createSafeApiCall,
  NetworkError,
  APIError,
  ValidationError,
  TimeoutError,
};
