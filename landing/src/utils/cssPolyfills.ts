/**
 * CSS Polyfills и feature detection для старых браузеров
 */

import { safeIncludes } from './stringUtils';

interface CSSFeatureSupport {
  flexbox: boolean;
  grid: boolean;
  customProperties: boolean;
  transforms: boolean;
  transitions: boolean;
  borderRadius: boolean;
  boxShadow: boolean;
  opacity: boolean;
  mediaQueries: boolean;
  calc: boolean;
  viewport: boolean;
}

/**
 * Определяет поддержку CSS возможностей
 */
export function detectCSSFeatures(): CSSFeatureSupport {
  const testElement = document.createElement('div');
  const testStyle = testElement.style;

  // Проверка поддержки CSS.supports
  const supportsAPI = 'CSS' in window && 'supports' in CSS;

  return {
    // Flexbox
    flexbox: supportsAPI
      ? CSS.supports('display', 'flex')
      : 'flex' in testStyle || 'webkitFlex' in testStyle || 'msFlex' in testStyle,

    // CSS Grid
    grid: supportsAPI
      ? CSS.supports('display', 'grid')
      : 'grid' in testStyle || 'msGrid' in testStyle,

    // CSS Custom Properties (переменные)
    customProperties: supportsAPI
      ? CSS.supports('--test', 'red')
      : window.CSS && CSS.supports && CSS.supports('--test', 'red'),

    // CSS Transforms
    transforms: supportsAPI
      ? CSS.supports('transform', 'translateX(1px)')
      : 'transform' in testStyle || 'webkitTransform' in testStyle || 'msTransform' in testStyle,

    // CSS Transitions
    transitions: supportsAPI
      ? CSS.supports('transition', 'all 0.3s')
      : 'transition' in testStyle || 'webkitTransition' in testStyle || 'msTransition' in testStyle,

    // Border Radius
    borderRadius: supportsAPI
      ? CSS.supports('border-radius', '5px')
      : 'borderRadius' in testStyle || 'webkitBorderRadius' in testStyle,

    // Box Shadow
    boxShadow: supportsAPI
      ? CSS.supports('box-shadow', '0 0 5px black')
      : 'boxShadow' in testStyle || 'webkitBoxShadow' in testStyle,

    // Opacity
    opacity: 'opacity' in testStyle,

    // Media Queries
    mediaQueries: window.matchMedia && window.matchMedia('(min-width: 1px)').matches,

    // CSS calc()
    calc: supportsAPI
      ? CSS.supports('width', 'calc(100% - 10px)')
      : (() => {
          testStyle.width = 'calc(10px)';
          return testStyle.width === 'calc(10px)';
        })(),

    // Viewport units
    viewport: supportsAPI
      ? CSS.supports('width', '100vw')
      : (() => {
          testStyle.width = '100vw';
          return testStyle.width === '100vw';
        })(),
  };
}

/**
 * Применяет CSS классы на основе поддержки возможностей
 */
export function applyCSSPolyfillClasses(): void {
  const features = detectCSSFeatures();
  const htmlElement = document.documentElement;

  // Добавляем классы для каждой возможности
  if (features && typeof features === 'object' && Object.entries) {
    Object.entries(features).forEach(([feature, supported]: [string, boolean]) => {
      const className = supported ? `supports-${feature}` : `no-${feature}`;
      htmlElement.classList.add(className);
    });
  }

  // Специальные классы для браузеров
  const userAgent = navigator.userAgent;

  // Internet Explorer
  if (safeIncludes(userAgent, 'MSIE') || safeIncludes(userAgent, 'Trident')) {
    htmlElement.classList.add('ie');

    // Версии IE
    if (safeIncludes(userAgent, 'MSIE 8')) htmlElement.classList.add('ie8');
    if (safeIncludes(userAgent, 'MSIE 9')) htmlElement.classList.add('ie9');
    if (safeIncludes(userAgent, 'MSIE 10')) htmlElement.classList.add('ie10');
    if (safeIncludes(userAgent, 'Trident/7')) htmlElement.classList.add('ie11');
  }

  // Edge Legacy
  if (safeIncludes(userAgent, 'Edge/')) {
    htmlElement.classList.add('edge-legacy');
  }

  // Safari
  if (safeIncludes(userAgent, 'Safari') && !safeIncludes(userAgent, 'Chrome')) {
    htmlElement.classList.add('safari');
  }

  // Firefox
  if (safeIncludes(userAgent, 'Firefox')) {
    htmlElement.classList.add('firefox');
  }

  // Chrome
  if (safeIncludes(userAgent, 'Chrome') && !safeIncludes(userAgent, 'Edge')) {
    htmlElement.classList.add('chrome');
  }

  // Мобильные браузеры
  if (/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent)) {
    htmlElement.classList.add('mobile');
  }

  // Touch устройства
  if ('ontouchstart' in window || navigator.maxTouchPoints > 0) {
    htmlElement.classList.add('touch');
  } else {
    htmlElement.classList.add('no-touch');
  }

  // Retina дисплеи
  if (window.devicePixelRatio && window.devicePixelRatio > 1) {
    htmlElement.classList.add('retina');
  }

  console.log('🎨 CSS Features detected:', features);
}

/**
 * Загружает CSS polyfills для неподдерживаемых возможностей
 */
export function loadCSSPolyfills(): void {
  const features = detectCSSFeatures();

  // Flexbox polyfill для IE9
  if (!features.flexbox) {
    console.log('📦 Loading Flexbox polyfill');
    // В реальном проекте здесь можно загрузить flexbox polyfill
  }

  // CSS Grid polyfill для старых браузеров
  if (!features.grid) {
    console.log('📦 Loading CSS Grid polyfill');
    // В реальном проекте здесь можно загрузить CSS Grid polyfill
  }

  // Media Queries polyfill для IE8
  if (!features.mediaQueries) {
    console.log('📦 Loading Media Queries polyfill');
    // В реальном проекте здесь можно загрузить respond.js
  }
}

/**
 * Создает fallback стили для критических возможностей
 */
export function createCriticalFallbacks(): void {
  const features = detectCSSFeatures();
  const style = document.createElement('style');
  let css = '';

  // Fallback для flexbox
  if (!features.flexbox) {
    css += `
      .flex { display: block; }
      .flex-row > * { display: inline-block; vertical-align: top; }
      .flex-col > * { display: block; }
      .items-center { text-align: center; }
      .justify-center { text-align: center; }
    `;
  }

  // Fallback для grid
  if (!features.grid) {
    css += `
      .grid { display: block; }
      .grid > * { margin-bottom: 1rem; }
    `;
  }

  // Fallback для custom properties
  if (!features.customProperties) {
    css += `
      :root {
        /* Статические значения вместо переменных */
      }
    `;
  }

  // Fallback для transforms
  if (!features.transforms) {
    css += `
      .transform { position: relative; }
      .translate-y-4 { top: 1rem; }
      .scale-105 { zoom: 1.05; } /* IE fallback */
    `;
  }

  // Fallback для transitions
  if (!features.transitions) {
    css += `
      .transition-all { /* Убираем transitions */ }
      .duration-200 { /* Убираем duration */ }
    `;
  }

  // Fallback для border-radius
  if (!features.borderRadius) {
    css += `
      .rounded, .rounded-lg, .rounded-xl { border-radius: 0; }
    `;
  }

  // Fallback для box-shadow
  if (!features.boxShadow) {
    css += `
      .shadow { border: 1px solid #e2e8f0; }
      .shadow-lg { border: 1px solid #cbd5e1; }
    `;
  }

  // Fallback для opacity в IE8
  if (!features.opacity) {
    css += `
      .opacity-50 { filter: alpha(opacity=50); }
      .opacity-75 { filter: alpha(opacity=75); }
    `;
  }

  if (css) {
    style.textContent = css;
    document.head.appendChild(style);
    console.log('🎨 Critical CSS fallbacks applied');
  }
}

/**
 * Инициализирует CSS polyfills
 */
export function initCSSPolyfills(): void {
  // Применяем классы для feature detection
  applyCSSPolyfillClasses();

  // Создаем критические fallbacks
  createCriticalFallbacks();

  // Загружаем дополнительные polyfills
  loadCSSPolyfills();

  console.log('🎨 CSS polyfills initialized');
}

// Автоматическая инициализация при загрузке
if (typeof document !== 'undefined') {
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initCSSPolyfills);
  } else {
    initCSSPolyfills();
  }
}
