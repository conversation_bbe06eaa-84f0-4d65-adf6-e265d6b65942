/**
 * Утилиты для плавной прокрутки
 * Поддерживает как современные браузеры, так и legacy fallback
 */

import React from 'react';

// Проверка поддержки smooth scroll
export const supportsSmoothScroll = (): boolean => {
  try {
    return 'scrollBehavior' in document.documentElement.style;
  } catch {
    return false;
  }
};

// Easing функции для анимации прокрутки
export const easingFunctions = {
  linear: (t: number): number => t,
  easeInQuad: (t: number): number => t * t,
  easeOutQuad: (t: number): number => t * (2 - t),
  easeInOutQuad: (t: number): number => (t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t),
  easeInCubic: (t: number): number => t * t * t,
  easeOutCubic: (t: number): number => --t * t * t + 1,
  easeInOutCubic: (t: number): number => (t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1),
  easeInQuart: (t: number): number => t * t * t * t,
  easeOutQuart: (t: number): number => 1 - --t * t * t * t,
  easeInOutQuart: (t: number): number => (t < 0.5 ? 8 * t * t * t * t : 1 - 8 * --t * t * t * t),
};

export interface SmoothScrollOptions {
  duration?: number;
  easing?: keyof typeof easingFunctions;
  offset?: number;
  callback?: () => void;
}

// Плавная прокрутка к элементу
export const scrollToElement = (
  target: string | HTMLElement,
  options: SmoothScrollOptions = {}
): Promise<void> => {
  return new Promise((resolve) => {
    const {
      duration = 800,
      easing = 'easeInOutCubic',
      offset = 0,
      callback,
    } = options;

    let targetElement: HTMLElement | null = null;

    if (typeof target === 'string') {
      // Если передан селектор
      if (target.startsWith('#')) {
        targetElement = document.getElementById(target.slice(1));
      } else {
        targetElement = document.querySelector(target);
      }
    } else {
      targetElement = target;
    }

    if (!targetElement) {
      console.warn(`Element not found: ${target}`);
      resolve();
      return;
    }

    const startPosition = window.pageYOffset;
    const targetPosition = targetElement.offsetTop - offset;
    const distance = targetPosition - startPosition;
    const startTime = performance.now();

    // Если поддерживается нативный smooth scroll и расстояние небольшое
    if (supportsSmoothScroll() && Math.abs(distance) < window.innerHeight * 2) {
      targetElement.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
      });
      
      // Ждем завершения анимации
      setTimeout(() => {
        if (callback) callback();
        resolve();
      }, duration);
      return;
    }

    // Fallback анимация для старых браузеров или больших расстояний
    const easingFunction = easingFunctions[easing];

    const animateScroll = (currentTime: number) => {
      const timeElapsed = currentTime - startTime;
      const progress = Math.min(timeElapsed / duration, 1);
      const ease = easingFunction(progress);
      const currentPosition = startPosition + distance * ease;

      window.scrollTo(0, currentPosition);

      if (progress < 1) {
        requestAnimationFrame(animateScroll);
      } else {
        if (callback) callback();
        resolve();
      }
    };

    requestAnimationFrame(animateScroll);
  });
};

// Прокрутка к верху страницы
export const scrollToTop = (options: SmoothScrollOptions = {}): Promise<void> => {
  return new Promise((resolve) => {
    const { duration = 600, easing = 'easeOutQuart', callback } = options;

    const startPosition = window.pageYOffset;
    const startTime = performance.now();

    if (startPosition === 0) {
      if (callback) callback();
      resolve();
      return;
    }

    // Используем нативный smooth scroll если поддерживается
    if (supportsSmoothScroll()) {
      window.scrollTo({
        top: 0,
        behavior: 'smooth',
      });

      setTimeout(() => {
        if (callback) callback();
        resolve();
      }, duration);
      return;
    }

    // Fallback анимация
    const easingFunction = easingFunctions[easing];

    const animateScroll = (currentTime: number) => {
      const timeElapsed = currentTime - startTime;
      const progress = Math.min(timeElapsed / duration, 1);
      const ease = easingFunction(progress);
      const currentPosition = startPosition * (1 - ease);

      window.scrollTo(0, currentPosition);

      if (progress < 1) {
        requestAnimationFrame(animateScroll);
      } else {
        if (callback) callback();
        resolve();
      }
    };

    requestAnimationFrame(animateScroll);
  });
};

// Прокрутка на определенное количество пикселей
export const scrollBy = (
  pixels: number,
  options: SmoothScrollOptions = {}
): Promise<void> => {
  return new Promise((resolve) => {
    const { duration = 400, easing = 'easeInOutQuad', callback } = options;

    const startPosition = window.pageYOffset;
    const targetPosition = startPosition + pixels;
    const startTime = performance.now();

    // Используем нативный smooth scroll если поддерживается
    if (supportsSmoothScroll()) {
      window.scrollBy({
        top: pixels,
        behavior: 'smooth',
      });

      setTimeout(() => {
        if (callback) callback();
        resolve();
      }, duration);
      return;
    }

    // Fallback анимация
    const easingFunction = easingFunctions[easing];

    const animateScroll = (currentTime: number) => {
      const timeElapsed = currentTime - startTime;
      const progress = Math.min(timeElapsed / duration, 1);
      const ease = easingFunction(progress);
      const currentPosition = startPosition + pixels * ease;

      window.scrollTo(0, currentPosition);

      if (progress < 1) {
        requestAnimationFrame(animateScroll);
      } else {
        if (callback) callback();
        resolve();
      }
    };

    requestAnimationFrame(animateScroll);
  });
};

// Хук для отслеживания позиции скролла
export const useScrollPosition = () => {
  const [scrollPosition, setScrollPosition] = React.useState(0);
  const [isScrolling, setIsScrolling] = React.useState(false);

  React.useEffect(() => {
    let timeoutId: NodeJS.Timeout;

    const handleScroll = () => {
      setScrollPosition(window.pageYOffset);
      setIsScrolling(true);

      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        setIsScrolling(false);
      }, 150);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });

    return () => {
      window.removeEventListener('scroll', handleScroll);
      clearTimeout(timeoutId);
    };
  }, []);

  return { scrollPosition, isScrolling };
};

// Утилита для создания навигационных ссылок с плавной прокруткой
export const createSmoothScrollHandler = (
  target: string,
  options: SmoothScrollOptions = {}
) => {
  return (event?: React.MouseEvent) => {
    if (event) {
      event.preventDefault();
    }
    scrollToElement(target, options);
  };
};

// Проверка видимости элемента в viewport
export const isElementInViewport = (element: HTMLElement): boolean => {
  const rect = element.getBoundingClientRect();
  return (
    rect.top >= 0 &&
    rect.left >= 0 &&
    rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
    rect.right <= (window.innerWidth || document.documentElement.clientWidth)
  );
};

// Получение процента видимости элемента
export const getElementVisibilityPercentage = (element: HTMLElement): number => {
  const rect = element.getBoundingClientRect();
  const windowHeight = window.innerHeight || document.documentElement.clientHeight;
  const windowWidth = window.innerWidth || document.documentElement.clientWidth;

  const visibleHeight = Math.min(rect.bottom, windowHeight) - Math.max(rect.top, 0);
  const visibleWidth = Math.min(rect.right, windowWidth) - Math.max(rect.left, 0);

  if (visibleHeight <= 0 || visibleWidth <= 0) {
    return 0;
  }

  const elementArea = rect.height * rect.width;
  const visibleArea = visibleHeight * visibleWidth;

  return (visibleArea / elementArea) * 100;
};
