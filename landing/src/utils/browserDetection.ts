/**
 * Утилиты для определения возможностей браузера
 * Используется для progressive enhancement и fallback логики
 */

export interface BrowserCapabilities {
  // ES6+ возможности
  modules: boolean;
  es6: boolean;
  es2015: boolean;
  es2017: boolean;
  es2020: boolean;

  // Web APIs
  fetch: boolean;
  promise: boolean;
  asyncAwait: boolean;
  intersectionObserver: boolean;

  // CSS возможности
  cssGrid: boolean;
  cssFlexbox: boolean;
  cssCustomProperties: boolean;
  cssSupports: boolean;

  // Другие возможности
  localStorage: boolean;
  sessionStorage: boolean;
  webWorkers: boolean;
  serviceWorkers: boolean;

  // Информация о браузере
  userAgent: string;
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;

  // Версии браузеров
  chrome: number | null;
  firefox: number | null;
  safari: number | null;
  edge: number | null;
}

/**
 * Определяет возможности текущего браузера
 */
export function detectBrowserCapabilities(): BrowserCapabilities {
  const userAgent = navigator.userAgent;

  // ES6+ возможности
  const modules = 'noModule' in HTMLScriptElement.prototype;

  const es6 = (() => {
    try {
      new Function('(a = 0) => a');
      return true;
    } catch {
      return false;
    }
  })();

  const es2015 = (() => {
    try {
      new Function('class Test {}');
      return true;
    } catch {
      return false;
    }
  })();

  const es2017 = (() => {
    try {
      new Function('async function test() { await 1; }');
      return true;
    } catch {
      return false;
    }
  })();

  const es2020 = (() => {
    try {
      new Function('const x = { a: 1, ...{ b: 2 } }; const y = x?.a ?? 0;');
      return true;
    } catch {
      return false;
    }
  })();

  // Web APIs
  const fetch = 'fetch' in window;
  const promise = 'Promise' in window;
  const asyncAwait = es2017; // async/await появился в ES2017
  const intersectionObserver = 'IntersectionObserver' in window;

  // CSS возможности
  const cssSupports = 'CSS' in window && 'supports' in CSS;
  const cssGrid = cssSupports && CSS.supports('display', 'grid');
  const cssFlexbox = cssSupports && CSS.supports('display', 'flex');
  const cssCustomProperties = cssSupports && CSS.supports('--test', 'red');

  // Storage APIs
  const localStorage = (() => {
    try {
      return 'localStorage' in window && window.localStorage !== null;
    } catch {
      return false;
    }
  })();

  const sessionStorage = (() => {
    try {
      return 'sessionStorage' in window && window.sessionStorage !== null;
    } catch {
      return false;
    }
  })();

  // Workers
  const webWorkers = 'Worker' in window;
  const serviceWorkers = 'serviceWorker' in navigator;

  // Определение типа устройства
  const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);
  const isTablet =
    /iPad|Android(?=.*\bMobile\b)(?=.*\bSafari\b)|Android(?=.*(?:\bTablet\b|\bTab\b))/i.test(
      userAgent
    );
  const isDesktop = !isMobile && !isTablet;

  // Определение версий браузеров
  const chrome = (() => {
    const match = userAgent.match(/Chrome\/(\d+)/);
    return match ? parseInt(match[1], 10) : null;
  })();

  const firefox = (() => {
    const match = userAgent.match(/Firefox\/(\d+)/);
    return match ? parseInt(match[1], 10) : null;
  })();

  const safari = (() => {
    const match = userAgent.match(/Version\/(\d+).*Safari/);
    return match ? parseInt(match[1], 10) : null;
  })();

  const edge = (() => {
    const match = userAgent.match(/Edg\/(\d+)/);
    return match ? parseInt(match[1], 10) : null;
  })();

  return {
    // ES6+ возможности
    modules,
    es6,
    es2015,
    es2017,
    es2020,

    // Web APIs
    fetch,
    promise,
    asyncAwait,
    intersectionObserver,

    // CSS возможности
    cssGrid,
    cssFlexbox,
    cssCustomProperties,
    cssSupports,

    // Storage
    localStorage,
    sessionStorage,

    // Workers
    webWorkers,
    serviceWorkers,

    // Устройство
    userAgent,
    isMobile,
    isTablet,
    isDesktop,

    // Версии браузеров
    chrome,
    firefox,
    safari,
    edge,
  };
}

/**
 * Проверяет поддержку минимальных требований
 */
export function checkMinimumRequirements(capabilities: BrowserCapabilities): boolean {
  // Минимальные требования для UnveilVPN
  return (
    capabilities.es2015 &&
    capabilities.promise &&
    capabilities.cssFlexbox &&
    capabilities.localStorage
  );
}

/**
 * Проверяет поддержку современных возможностей
 */
export function checkModernFeatures(capabilities: BrowserCapabilities): boolean {
  return (
    capabilities.modules &&
    capabilities.es6 &&
    capabilities.fetch &&
    capabilities.cssGrid &&
    capabilities.intersectionObserver
  );
}

/**
 * Возвращает уровень поддержки браузера
 */
export function getBrowserSupportLevel(
  capabilities: BrowserCapabilities
): 'modern' | 'legacy' | 'unsupported' {
  if (!checkMinimumRequirements(capabilities)) {
    return 'unsupported';
  }

  if (checkModernFeatures(capabilities)) {
    return 'modern';
  }

  return 'legacy';
}

/**
 * Логирует информацию о браузере для отладки
 */
export function logBrowserInfo(capabilities: BrowserCapabilities): void {
  const level = getBrowserSupportLevel(capabilities);

  console.group('🌐 Browser Capabilities');
  console.log('Support Level:', level);
  console.log('User Agent:', capabilities.userAgent);
  console.log('Device Type:', {
    mobile: capabilities.isMobile,
    tablet: capabilities.isTablet,
    desktop: capabilities.isDesktop,
  });
  console.log('Browser Versions:', {
    chrome: capabilities.chrome,
    firefox: capabilities.firefox,
    safari: capabilities.safari,
    edge: capabilities.edge,
  });
  console.log('ES Features:', {
    modules: capabilities.modules,
    es6: capabilities.es6,
    es2015: capabilities.es2015,
    es2017: capabilities.es2017,
    es2020: capabilities.es2020,
  });
  console.log('Web APIs:', {
    fetch: capabilities.fetch,
    promise: capabilities.promise,
    intersectionObserver: capabilities.intersectionObserver,
  });
  console.log('CSS Features:', {
    grid: capabilities.cssGrid,
    flexbox: capabilities.cssFlexbox,
    customProperties: capabilities.cssCustomProperties,
  });
  console.groupEnd();
}

// Глобальная переменная для доступа из любого места
declare global {
  interface Window {
    browserCapabilities?: BrowserCapabilities;
  }
}

// Автоматическое определение при загрузке модуля
if (typeof window !== 'undefined') {
  window.browserCapabilities = detectBrowserCapabilities();

  // Логируем только в development режиме
  if (import.meta.env.DEV) {
    logBrowserInfo(window.browserCapabilities);
  }
}
