/**
 * Утилиты для определения возможностей браузера
 * Используется для progressive enhancement и fallback логики
 */

export interface BrowserCapabilities {
  // ES6+ возможности
  modules: boolean;
  es6: boolean;
  es2015: boolean;
  es2017: boolean;
  es2020: boolean;
  arrow: boolean;
  classes: boolean;
  destructuring: boolean;
  templateLiterals: boolean;
  const: boolean;
  let: boolean;
  spread: boolean;
  forOf: boolean;

  // ES2017+ возможности
  asyncAwait: boolean;
  objectSpread: boolean;

  // ES2020+ возможности
  optionalChaining: boolean;
  nullishCoalescing: boolean;

  // Web APIs
  fetch: boolean;
  promise: boolean;
  intersectionObserver: boolean;
  mutationObserver: boolean;
  resizeObserver: boolean;
  requestAnimationFrame: boolean;

  // Storage APIs
  localStorage: boolean;
  sessionStorage: boolean;
  indexedDB: boolean;

  // Worker APIs
  webWorkers: boolean;
  serviceWorkers: boolean;

  // Network APIs
  websockets: boolean;
  eventSource: boolean;

  // CSS возможности
  cssGrid: boolean;
  cssFlexbox: boolean;
  cssCustomProperties: boolean;
  cssSupports: boolean;
  cssTransforms: boolean;
  cssTransitions: boolean;
  cssAnimations: boolean;
  cssMediaQueries: boolean;
  cssCalc: boolean;

  // DOM возможности
  querySelector: boolean;
  addEventListener: boolean;
  classList: boolean;
  dataset: boolean;

  // Мультимедиа
  canvas: boolean;
  svg: boolean;
  video: boolean;
  audio: boolean;

  // Безопасность
  crypto: boolean;

  // Производительность
  performance: boolean;

  // Информация о браузере
  userAgent: string;
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  browserName: string;
  browserVersion: string;
  engineName: string;
  osName: string;

  // Версии браузеров
  chrome: number | null;
  firefox: number | null;
  safari: number | null;
  edge: number | null;

  // Уровень поддержки
  supportLevel: 'modern' | 'legacy' | 'minimal' | 'unsupported';

  // Рекомендации
  recommendations: string[];
}

/**
 * Определяет возможности текущего браузера
 */
export function detectBrowserCapabilities(): BrowserCapabilities {
  const userAgent = navigator.userAgent;

  // Определение браузера и версии
  const browserInfo = detectBrowserInfo(userAgent);

  // Определение устройства
  const deviceInfo = detectDeviceInfo(userAgent);

  // Тестирование ES6+ возможностей
  const es6Support = testES6Support();

  // Тестирование Web APIs
  const apiSupport = testWebAPISupport();

  // Тестирование CSS возможностей
  const cssSupport = testCSSSupport();

  // Тестирование DOM возможностей
  const domSupport = testDOMSupport();

  // Тестирование мультимедиа
  const mediaSupport = testMediaSupport();

  // Определение уровня поддержки
  const supportLevel = determineSupportLevel(browserInfo, es6Support, apiSupport, cssSupport);

  // Генерация рекомендаций
  const recommendations = generateRecommendations(supportLevel, browserInfo, es6Support, apiSupport);

  return {
    // ES6+ возможности
    modules: 'noModule' in HTMLScriptElement.prototype,
    ...es6Support,

    // Web APIs
    ...apiSupport,

    // CSS возможности
    ...cssSupport,

    // DOM возможности
    ...domSupport,

    // Мультимедиа
    ...mediaSupport,

    // Информация о браузере
    userAgent,
    ...deviceInfo,
    ...browserInfo,

    // Уровень поддержки и рекомендации
    supportLevel,
    recommendations,
  };
}

// Функция определения информации о браузере
function detectBrowserInfo(userAgent: string): {
  browserName: string;
  browserVersion: string;
  engineName: string;
  osName: string;
  chrome: number | null;
  firefox: number | null;
  safari: number | null;
  edge: number | null;
} {
  let browserName = 'Unknown';
  let browserVersion = '0';
  let engineName = 'Unknown';
  let osName = 'Unknown';
  let chrome: number | null = null;
  let firefox: number | null = null;
  let safari: number | null = null;
  let edge: number | null = null;

  // Определение браузера
  if (userAgent && userAgent.includes && userAgent.includes('Chrome') && !userAgent.includes('Edg')) {
    browserName = 'Chrome';
    const match = userAgent.match(/Chrome\/(\d+)/);
    if (match) {
      chrome = parseInt(match[1]);
      browserVersion = match[1];
    }
    engineName = 'Blink';
  } else if (userAgent && userAgent.includes && userAgent.includes('Edg')) {
    browserName = 'Edge';
    const match = userAgent.match && userAgent.match(/Edg\/(\d+)/);
    if (match) {
      edge = parseInt(match[1]);
      browserVersion = match[1];
    }
    engineName = 'Blink';
  } else if (userAgent && userAgent.includes && userAgent.includes('Firefox')) {
    browserName = 'Firefox';
    const match = userAgent.match && userAgent.match(/Firefox\/(\d+)/);
    if (match) {
      firefox = parseInt(match[1]);
      browserVersion = match[1];
    }
    engineName = 'Gecko';
  } else if (userAgent && userAgent.includes && userAgent.includes('Safari') && !userAgent.includes('Chrome')) {
    browserName = 'Safari';
    const match = userAgent.match && userAgent.match(/Version\/(\d+)/);
    if (match) {
      safari = parseInt(match[1]);
      browserVersion = match[1];
    }
    engineName = 'WebKit';
  }

  // Определение ОС
  if (userAgent && userAgent.includes && userAgent.includes('Windows')) {
    osName = 'Windows';
  } else if (userAgent && userAgent.includes && userAgent.includes('Mac')) {
    osName = 'macOS';
  } else if (userAgent && userAgent.includes && userAgent.includes('Linux')) {
    osName = 'Linux';
  } else if (userAgent && userAgent.includes && userAgent.includes('Android')) {
    osName = 'Android';
  } else if (userAgent && userAgent.includes && userAgent.includes('iOS')) {
    osName = 'iOS';
  }

  return {
    browserName,
    browserVersion,
    engineName,
    osName,
    chrome,
    firefox,
    safari,
    edge,
  };
}

// Функция определения типа устройства
function detectDeviceInfo(userAgent: string): {
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
} {
  const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);
  const isTablet = /iPad|Android(?=.*\bMobile\b)(?=.*\bTablet\b)|Android(?=.*\bTablet\b)/i.test(userAgent);
  const isDesktop = !isMobile && !isTablet;

  return {
    isMobile: isMobile && !isTablet,
    isTablet,
    isDesktop,
  };
}

// Функция тестирования ES6+ возможностей
function testES6Support(): {
  es6: boolean;
  es2015: boolean;
  es2017: boolean;
  es2020: boolean;
  arrow: boolean;
  classes: boolean;
  destructuring: boolean;
  templateLiterals: boolean;
  const: boolean;
  let: boolean;
  spread: boolean;
  forOf: boolean;
  asyncAwait: boolean;
  objectSpread: boolean;
  optionalChaining: boolean;
  nullishCoalescing: boolean;
} {
  const tests = {
    es6: () => {
      try {
        new Function('(a = 0) => a');
        return true;
      } catch {
        return false;
      }
    },
    es2015: () => {
      try {
        new Function('class Test {}');
        return true;
      } catch {
        return false;
      }
    },
    es2017: () => {
      try {
        new Function('async function test() { await 1; }');
        return true;
      } catch {
        return false;
      }
    },
    es2020: () => {
      try {
        new Function('const x = { a: 1, ...{ b: 2 } }; const y = x?.a ?? 0;');
        return true;
      } catch {
        return false;
      }
    },
    arrow: () => {
      try {
        new Function('() => {}');
        return true;
      } catch {
        return false;
      }
    },
    classes: () => {
      try {
        new Function('class Test {}');
        return true;
      } catch {
        return false;
      }
    },
    destructuring: () => {
      try {
        new Function('const [a, b] = [1, 2]; const {c} = {c: 3};');
        return true;
      } catch {
        return false;
      }
    },
    templateLiterals: () => {
      try {
        new Function('const x = `test ${1}`;');
        return true;
      } catch {
        return false;
      }
    },
    const: () => {
      try {
        new Function('const x = 1;');
        return true;
      } catch {
        return false;
      }
    },
    let: () => {
      try {
        new Function('let x = 1;');
        return true;
      } catch {
        return false;
      }
    },
    spread: () => {
      try {
        new Function('const x = [...[1, 2]];');
        return true;
      } catch {
        return false;
      }
    },
    forOf: () => {
      try {
        new Function('for (const x of [1, 2]) {}');
        return true;
      } catch {
        return false;
      }
    },
    asyncAwait: () => {
      try {
        new Function('async function test() { await 1; }');
        return true;
      } catch {
        return false;
      }
    },
    objectSpread: () => {
      try {
        new Function('const x = { ...{a: 1} };');
        return true;
      } catch {
        return false;
      }
    },
    optionalChaining: () => {
      try {
        new Function('const x = {}; const y = x?.a;');
        return true;
      } catch {
        return false;
      }
    },
    nullishCoalescing: () => {
      try {
        new Function('const x = null ?? 1;');
        return true;
      } catch {
        return false;
      }
    },
  };

  const results: any = {};
  if (Object.entries) {
    for (const [key, test] of Object.entries(tests)) {
      results[key] = test();
    }
  }

  return results;
}

// Функция тестирования Web APIs
function testWebAPISupport(): {
  fetch: boolean;
  promise: boolean;
  intersectionObserver: boolean;
  mutationObserver: boolean;
  resizeObserver: boolean;
  requestAnimationFrame: boolean;
  localStorage: boolean;
  sessionStorage: boolean;
  indexedDB: boolean;
  webWorkers: boolean;
  serviceWorkers: boolean;
  websockets: boolean;
  eventSource: boolean;
  crypto: boolean;
  performance: boolean;
} {
  return {
    fetch: 'fetch' in window,
    promise: 'Promise' in window,
    intersectionObserver: 'IntersectionObserver' in window,
    mutationObserver: 'MutationObserver' in window,
    resizeObserver: 'ResizeObserver' in window,
    requestAnimationFrame: 'requestAnimationFrame' in window,
    localStorage: (() => {
      try {
        return 'localStorage' in window && window.localStorage !== null;
      } catch {
        return false;
      }
    })(),
    sessionStorage: (() => {
      try {
        return 'sessionStorage' in window && window.sessionStorage !== null;
      } catch {
        return false;
      }
    })(),
    indexedDB: 'indexedDB' in window,
    webWorkers: 'Worker' in window,
    serviceWorkers: 'serviceWorker' in navigator,
    websockets: 'WebSocket' in window,
    eventSource: 'EventSource' in window,
    crypto: 'crypto' in window && 'getRandomValues' in window.crypto,
    performance: 'performance' in window,
  };
}

// Функция тестирования CSS возможностей
function testCSSSupport(): {
  cssGrid: boolean;
  cssFlexbox: boolean;
  cssCustomProperties: boolean;
  cssSupports: boolean;
  cssTransforms: boolean;
  cssTransitions: boolean;
  cssAnimations: boolean;
  cssMediaQueries: boolean;
  cssCalc: boolean;
} {
  const testElement = document.createElement('div');

  return {
    cssGrid: 'grid' in testElement.style,
    cssFlexbox: 'flex' in testElement.style || 'webkitFlex' in testElement.style,
    cssCustomProperties: 'CSS' in window && 'supports' in window.CSS && window.CSS.supports('--test', '0'),
    cssSupports: 'CSS' in window && 'supports' in window.CSS,
    cssTransforms: 'transform' in testElement.style || 'webkitTransform' in testElement.style,
    cssTransitions: 'transition' in testElement.style || 'webkitTransition' in testElement.style,
    cssAnimations: 'animation' in testElement.style || 'webkitAnimation' in testElement.style,
    cssMediaQueries: 'matchMedia' in window,
    cssCalc: (() => {
      testElement.style.width = 'calc(1px + 1px)';
      return testElement.style.width === 'calc(1px + 1px)';
    })(),
  };
}

// Функция тестирования DOM возможностей
function testDOMSupport(): {
  querySelector: boolean;
  addEventListener: boolean;
  classList: boolean;
  dataset: boolean;
} {
  const testElement = document.createElement('div');

  return {
    querySelector: 'querySelector' in document,
    addEventListener: 'addEventListener' in window,
    classList: 'classList' in testElement,
    dataset: 'dataset' in testElement,
  };
}

// Функция тестирования медиа возможностей
function testMediaSupport(): {
  canvas: boolean;
  svg: boolean;
  video: boolean;
  audio: boolean;
} {
  return {
    canvas: 'HTMLCanvasElement' in window,
    svg: 'SVGElement' in window,
    video: 'HTMLVideoElement' in window,
    audio: 'HTMLAudioElement' in window,
  };
}

// Функция определения уровня поддержки
function determineSupportLevel(
  browserInfo: any,
  es6Support: any,
  apiSupport: any,
  cssSupport: any
): 'modern' | 'legacy' | 'minimal' | 'unsupported' {
  const { chrome, firefox, safari, edge } = browserInfo;

  // Современные браузеры
  if (
    (chrome && chrome >= 80) ||
    (firefox && firefox >= 75) ||
    (safari && safari >= 13) ||
    (edge && edge >= 80)
  ) {
    return 'modern';
  }

  // Legacy браузеры с базовой поддержкой
  if (
    (chrome && chrome >= 60) ||
    (firefox && firefox >= 55) ||
    (safari && safari >= 12) ||
    (edge && edge >= 79)
  ) {
    if (es6Support.es6 && apiSupport.fetch && cssSupport.cssFlexbox) {
      return 'legacy';
    }
  }

  // Минимальная поддержка
  if (
    (chrome && chrome >= 40) ||
    (firefox && firefox >= 40) ||
    (safari && safari >= 10) ||
    (edge && edge >= 12)
  ) {
    if (apiSupport.promise && cssSupport.cssFlexbox) {
      return 'minimal';
    }
  }

  return 'unsupported';
}

// Функция генерации рекомендаций
function generateRecommendations(
  supportLevel: string,
  _browserInfo: any,
  es6Support: any,
  apiSupport: any
): string[] {
  const recommendations: string[] = [];

  if (supportLevel === 'unsupported') {
    recommendations.push('Обновите браузер до последней версии для лучшей производительности и безопасности');
    recommendations.push('Рекомендуем Chrome 80+, Firefox 75+, Safari 13+ или Edge 80+');
  } else if (supportLevel === 'minimal') {
    recommendations.push('Ваш браузер поддерживается, но некоторые функции могут работать медленнее');
    recommendations.push('Рассмотрите возможность обновления для улучшения производительности');
  } else if (supportLevel === 'legacy') {
    recommendations.push('Ваш браузер поддерживается с базовой функциональностью');
    if (!es6Support.asyncAwait) {
      recommendations.push('Некоторые современные функции могут быть недоступны');
    }
  }

  // Специфичные рекомендации
  if (!apiSupport.fetch) {
    recommendations.push('Fetch API не поддерживается - используется XMLHttpRequest fallback');
  }

  if (!es6Support.es6) {
    recommendations.push('ES6 не поддерживается - используется ES5 fallback');
  }

  if (!apiSupport.localStorage) {
    recommendations.push('LocalStorage недоступен - кэширование отключено');
  }

  return recommendations;
}

/**
 * Проверяет поддержку минимальных требований
 */
export function checkMinimumRequirements(capabilities: BrowserCapabilities): boolean {
  // Минимальные требования для UnveilVPN
  const result = (
    capabilities.es2015 &&
    capabilities.promise &&
    capabilities.cssFlexbox &&
    capabilities.localStorage
  );

  // Логирование для отладки
  console.log('Minimum requirements check:', {
    es2015: capabilities.es2015,
    promise: capabilities.promise,
    cssFlexbox: capabilities.cssFlexbox,
    localStorage: capabilities.localStorage,
    result
  });

  return result;
}

/**
 * Проверяет поддержку современных возможностей
 */
export function checkModernFeatures(capabilities: BrowserCapabilities): boolean {
  return (
    capabilities.modules &&
    capabilities.es6 &&
    capabilities.fetch &&
    capabilities.cssGrid &&
    capabilities.intersectionObserver
  );
}

/**
 * Возвращает уровень поддержки браузера
 */
export function getBrowserSupportLevel(
  capabilities: BrowserCapabilities
): 'modern' | 'legacy' | 'unsupported' {
  if (!checkMinimumRequirements(capabilities)) {
    return 'unsupported';
  }

  if (checkModernFeatures(capabilities)) {
    return 'modern';
  }

  return 'legacy';
}

/**
 * Логирует информацию о браузере для отладки
 */
export function logBrowserInfo(capabilities: BrowserCapabilities): void {
  const level = getBrowserSupportLevel(capabilities);

  console.group('🌐 Browser Capabilities');
  console.log('Support Level:', level);
  console.log('User Agent:', capabilities.userAgent);
  console.log('Device Type:', {
    mobile: capabilities.isMobile,
    tablet: capabilities.isTablet,
    desktop: capabilities.isDesktop,
  });
  console.log('Browser Versions:', {
    chrome: capabilities.chrome,
    firefox: capabilities.firefox,
    safari: capabilities.safari,
    edge: capabilities.edge,
  });
  console.log('ES Features:', {
    modules: capabilities.modules,
    es6: capabilities.es6,
    es2015: capabilities.es2015,
    es2017: capabilities.es2017,
    es2020: capabilities.es2020,
  });
  console.log('Web APIs:', {
    fetch: capabilities.fetch,
    promise: capabilities.promise,
    intersectionObserver: capabilities.intersectionObserver,
  });
  console.log('CSS Features:', {
    grid: capabilities.cssGrid,
    flexbox: capabilities.cssFlexbox,
    customProperties: capabilities.cssCustomProperties,
  });
  console.groupEnd();
}

// Глобальная переменная для доступа из любого места
declare global {
  interface Window {
    browserCapabilities?: BrowserCapabilities;
  }
}

// Автоматическое определение при загрузке модуля
if (typeof window !== 'undefined') {
  const capabilities = detectBrowserCapabilities();
  window.browserCapabilities = capabilities;

  // Логируем только в development режиме
  if (import.meta.env.DEV) {
    logBrowserInfo(capabilities);
  }
}
