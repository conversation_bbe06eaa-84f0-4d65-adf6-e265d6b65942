/**
 * Оптимизированные импорты иконок для tree-shaking
 * Импортируем только используемые иконки для уменьшения размера bundle
 */

import {
  Check,
  Star,
  Zap,
  Menu,
  X,
  AlertTriangle,
  RefreshCw,
  Download,
  Loader2,
  ExternalLink,
  MessageCircle,
  Settings,
  Mail,
  Shield,
  Globe,
  Lock,
  Wifi,
  Smartphone,
  Monitor,
  Tablet,
  Server,
  Eye,
  EyeOff,
  ChevronDown,
  ChevronUp,
  ChevronLeft,
  ChevronRight,
  Plus,
  Minus,
  Search,
  Filter,
  ArrowRight,
  ArrowLeft,
  Play,
  Pause,
  Volume2,
  VolumeX,
  Heart,
  Share,
  Copy,
  Info,
  HelpCircle,
  CheckCircle,
  XCircle,
  AlertCircle,
  Clock,
  Calendar,
  User,
  Users,
  Home,
  FileText,
  Video,
  Music,
  Headphones,
  Camera,
  Mic,
  MicOff,
  Phone,
  PhoneCall,
  PhoneOff,
  Trash2,
  Edit,
  Save,
  Upload,
  RotateCw,
  RotateCcw,
  Maximize,
  Minimize,
  MoreHorizontal,
  MoreVertical,
} from 'lucide-react';

// Типы иконок для TypeScript
export type IconComponent = React.ComponentType<{
  size?: number | string;
  color?: string;
  strokeWidth?: number | string;
  className?: string;
}>;

// Карта иконок для динамического использования
export const iconMap: Record<string, IconComponent> = {
  check: Check,
  star: Star,
  zap: Zap,
  menu: Menu,
  x: X,
  'alert-triangle': AlertTriangle,
  'refresh-cw': RefreshCw,
  download: Download,
  'loader-2': Loader2,
  'external-link': ExternalLink,
  'message-circle': MessageCircle,
  settings: Settings,
  mail: Mail,
  shield: Shield,
  globe: Globe,
  lock: Lock,
  wifi: Wifi,
  smartphone: Smartphone,
  monitor: Monitor,
  tablet: Tablet,
  server: Server,
  eye: Eye,
  'eye-off': EyeOff,
  'chevron-down': ChevronDown,
  'chevron-up': ChevronUp,
  'chevron-left': ChevronLeft,
  'chevron-right': ChevronRight,
  plus: Plus,
  minus: Minus,
  search: Search,
  filter: Filter,
  'arrow-right': ArrowRight,
  'arrow-left': ArrowLeft,
  play: Play,
  pause: Pause,
  'volume-2': Volume2,
  'volume-x': VolumeX,
  heart: Heart,
  share: Share,
  copy: Copy,
  info: Info,
  'help-circle': HelpCircle,
  'check-circle': CheckCircle,
  'x-circle': XCircle,
  'alert-circle': AlertCircle,
  clock: Clock,
  calendar: Calendar,
  user: User,
  users: Users,
  home: Home,
  'file-text': FileText,
  video: Video,
  music: Music,
  headphones: Headphones,
  camera: Camera,
  mic: Mic,
  'mic-off': MicOff,
  phone: Phone,
  'phone-call': PhoneCall,
  'phone-off': PhoneOff,
  'trash-2': Trash2,
  edit: Edit,
  save: Save,
  upload: Upload,
  'rotate-cw': RotateCw,
  'rotate-ccw': RotateCcw,
  maximize: Maximize,
  minimize: Minimize,
  'more-horizontal': MoreHorizontal,
  'more-vertical': MoreVertical,
};

// Утилита для получения иконки по имени
export const getIcon = (name: string): IconComponent | null => {
  return iconMap[name] || null;
};

// Утилита для проверки существования иконки
export const hasIcon = (name: string): boolean => {
  return name in iconMap;
};

// Список всех доступных иконок
export const availableIcons = Object.keys ? Object.keys(iconMap) : [];

// Группы иконок по категориям
export const iconCategories = {
  ui: ['check', 'star', 'zap', 'menu', 'x', 'plus', 'minus', 'search', 'filter'],
  navigation: ['chevron-down', 'chevron-up', 'chevron-left', 'chevron-right', 'arrow-right', 'arrow-left'],
  communication: ['message-circle', 'mail', 'phone', 'phone-call', 'phone-off'],
  media: ['play', 'pause', 'volume-2', 'volume-x', 'image', 'video', 'music', 'headphones', 'camera', 'mic', 'mic-off'],
  system: ['settings', 'download', 'upload', 'save', 'edit', 'trash-2', 'refresh-cw', 'rotate-cw', 'rotate-ccw'],
  status: ['loader-2', 'alert-triangle', 'check-circle', 'x-circle', 'alert-circle', 'info', 'help-circle'],
  security: ['shield', 'lock', 'eye', 'eye-off'],
  devices: ['smartphone', 'monitor', 'tablet', 'server', 'wifi'],
  social: ['heart', 'share', 'copy', 'external-link'],
  time: ['clock', 'calendar'],
  users: ['user', 'users'],
  files: ['file-text', 'home'],
  layout: ['maximize', 'minimize', 'more-horizontal', 'more-vertical'],
  world: ['globe'],
};

export default {
  iconMap,
  getIcon,
  hasIcon,
  availableIcons,
  iconCategories,
};
