/**
 * Оптимизированные импорты иконок для tree-shaking
 * Импортируем только используемые иконки для уменьшения размера bundle
 */

// Основные иконки
export { Check } from 'lucide-react';
export { Star } from 'lucide-react';
export { Zap } from 'lucide-react';
export { Menu } from 'lucide-react';
export { X } from 'lucide-react';
export { AlertTriangle } from 'lucide-react';
export { RefreshCw } from 'lucide-react';
export { Download } from 'lucide-react';
export { Loader2 } from 'lucide-react';
export { ExternalLink } from 'lucide-react';
export { MessageCircle } from 'lucide-react';
export { Settings } from 'lucide-react';
export { Mail } from 'lucide-react';

// Дополнительные иконки
export { Shield } from 'lucide-react';
export { Globe } from 'lucide-react';
export { Lock } from 'lucide-react';
export { Wifi } from 'lucide-react';
export { Smartphone } from 'lucide-react';
export { Monitor } from 'lucide-react';
export { Tablet } from 'lucide-react';
export { Server } from 'lucide-react';
export { Eye } from 'lucide-react';
export { EyeOff } from 'lucide-react';
export { ChevronDown } from 'lucide-react';
export { ChevronUp } from 'lucide-react';
export { ChevronLeft } from 'lucide-react';
export { ChevronRight } from 'lucide-react';
export { Plus } from 'lucide-react';
export { Minus } from 'lucide-react';
export { Search } from 'lucide-react';
export { Filter } from 'lucide-react';
export { ArrowRight } from 'lucide-react';
export { ArrowLeft } from 'lucide-react';
export { Play } from 'lucide-react';
export { Pause } from 'lucide-react';
export { Volume2 } from 'lucide-react';
export { VolumeX } from 'lucide-react';
export { Heart } from 'lucide-react';
export { Share } from 'lucide-react';
export { Copy } from 'lucide-react';
export { Info } from 'lucide-react';
export { HelpCircle } from 'lucide-react';
export { CheckCircle } from 'lucide-react';
export { XCircle } from 'lucide-react';
export { AlertCircle } from 'lucide-react';
export { Clock } from 'lucide-react';
export { Calendar } from 'lucide-react';
export { User } from 'lucide-react';
export { Users } from 'lucide-react';
export { Home } from 'lucide-react';
export { FileText } from 'lucide-react';
export { Image } from 'lucide-react';
export { Video } from 'lucide-react';
export { Music } from 'lucide-react';
export { Headphones } from 'lucide-react';
export { Camera } from 'lucide-react';
export { Mic } from 'lucide-react';
export { MicOff } from 'lucide-react';
export { Phone } from 'lucide-react';
export { PhoneCall } from 'lucide-react';
export { PhoneOff } from 'lucide-react';
export { Trash2 } from 'lucide-react';
export { Edit } from 'lucide-react';
export { Save } from 'lucide-react';
export { Upload } from 'lucide-react';
export { RotateCw } from 'lucide-react';
export { RotateCcw } from 'lucide-react';
export { Maximize } from 'lucide-react';
export { Minimize } from 'lucide-react';
export { MoreHorizontal } from 'lucide-react';
export { MoreVertical } from 'lucide-react';

// Типы иконок для TypeScript
export type IconComponent = React.ComponentType<{
  size?: number | string;
  color?: string;
  strokeWidth?: number | string;
  className?: string;
}>;

// Карта иконок для динамического использования
export const iconMap: Record<string, IconComponent> = {
  check: Check,
  star: Star,
  zap: Zap,
  menu: Menu,
  x: X,
  'alert-triangle': AlertTriangle,
  'refresh-cw': RefreshCw,
  download: Download,
  'loader-2': Loader2,
  'external-link': ExternalLink,
  'message-circle': MessageCircle,
  settings: Settings,
  mail: Mail,
  shield: Shield,
  globe: Globe,
  lock: Lock,
  wifi: Wifi,
  smartphone: Smartphone,
  monitor: Monitor,
  tablet: Tablet,
  server: Server,
  eye: Eye,
  'eye-off': EyeOff,
  'chevron-down': ChevronDown,
  'chevron-up': ChevronUp,
  'chevron-left': ChevronLeft,
  'chevron-right': ChevronRight,
  plus: Plus,
  minus: Minus,
  search: Search,
  filter: Filter,
  'arrow-right': ArrowRight,
  'arrow-left': ArrowLeft,
  play: Play,
  pause: Pause,
  'volume-2': Volume2,
  'volume-x': VolumeX,
  heart: Heart,
  share: Share,
  copy: Copy,
  info: Info,
  'help-circle': HelpCircle,
  'check-circle': CheckCircle,
  'x-circle': XCircle,
  'alert-circle': AlertCircle,
  clock: Clock,
  calendar: Calendar,
  user: User,
  users: Users,
  home: Home,
  'file-text': FileText,
  image: Image,
  video: Video,
  music: Music,
  headphones: Headphones,
  camera: Camera,
  mic: Mic,
  'mic-off': MicOff,
  phone: Phone,
  'phone-call': PhoneCall,
  'phone-off': PhoneOff,
  'trash-2': Trash2,
  edit: Edit,
  save: Save,
  upload: Upload,
  'rotate-cw': RotateCw,
  'rotate-ccw': RotateCcw,
  maximize: Maximize,
  minimize: Minimize,
  'more-horizontal': MoreHorizontal,
  'more-vertical': MoreVertical,
};

// Утилита для получения иконки по имени
export const getIcon = (name: string): IconComponent | null => {
  return iconMap[name] || null;
};

// Утилита для проверки существования иконки
export const hasIcon = (name: string): boolean => {
  return name in iconMap;
};

// Список всех доступных иконок
export const availableIcons = Object.keys(iconMap);

// Группы иконок по категориям
export const iconCategories = {
  ui: ['check', 'star', 'zap', 'menu', 'x', 'plus', 'minus', 'search', 'filter'],
  navigation: ['chevron-down', 'chevron-up', 'chevron-left', 'chevron-right', 'arrow-right', 'arrow-left'],
  communication: ['message-circle', 'mail', 'phone', 'phone-call', 'phone-off'],
  media: ['play', 'pause', 'volume-2', 'volume-x', 'image', 'video', 'music', 'headphones', 'camera', 'mic', 'mic-off'],
  system: ['settings', 'download', 'upload', 'save', 'edit', 'trash-2', 'refresh-cw', 'rotate-cw', 'rotate-ccw'],
  status: ['loader-2', 'alert-triangle', 'check-circle', 'x-circle', 'alert-circle', 'info', 'help-circle'],
  security: ['shield', 'lock', 'eye', 'eye-off'],
  devices: ['smartphone', 'monitor', 'tablet', 'server', 'wifi'],
  social: ['heart', 'share', 'copy', 'external-link'],
  time: ['clock', 'calendar'],
  users: ['user', 'users'],
  files: ['file-text', 'home'],
  layout: ['maximize', 'minimize', 'more-horizontal', 'more-vertical'],
  world: ['globe'],
};

export default {
  iconMap,
  getIcon,
  hasIcon,
  availableIcons,
  iconCategories,
};
