/**
 * Универсальный API клиент с поддержкой старых браузеров
 * Поддерживает как fetch (современные браузеры), так и XMLHttpRequest (legacy)
 */

import { NetworkError, APIError, TimeoutError, withRetry } from '@/utils/errorHandler';
import type { RetryOptions } from '@/utils/errorHandler';

// Типы для API ответов
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface RequestConfig {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
  headers?: Record<string, string>;
  body?: any;
  timeout?: number;
  retries?: number;
  retryDelay?: number;
  retryOptions?: RetryOptions;
}

export interface CacheConfig {
  ttl?: number; // Time to live в миллисекундах
  key?: string; // Кастомный ключ кэша
  enabled?: boolean;
}

// Проверка поддержки fetch
const supportsFetch = (): boolean => {
  return typeof fetch !== 'undefined' && typeof Promise !== 'undefined';
};

// Проверка поддержки localStorage
const supportsLocalStorage = (): boolean => {
  try {
    const test = '__localStorage_test__';
    localStorage.setItem(test, test);
    localStorage.removeItem(test);
    return true;
  } catch {
    return false;
  }
};

// Утилиты для кэширования
class CacheManager {
  private static prefix = 'unveilvpn_api_';

  static set(key: string, data: any, ttl: number): void {
    if (!supportsLocalStorage()) return;

    try {
      const item = {
        data,
        timestamp: Date.now(),
        ttl,
      };
      localStorage.setItem(this.prefix + key, JSON.stringify(item));
    } catch (error) {
      console.warn('Failed to cache data:', error);
    }
  }

  static get(key: string): any | null {
    if (!supportsLocalStorage()) return null;

    try {
      const item = localStorage.getItem(this.prefix + key);
      if (!item) return null;

      const parsed = JSON.parse(item);
      const now = Date.now();

      if (now - parsed.timestamp > parsed.ttl) {
        this.remove(key);
        return null;
      }

      return parsed.data;
    } catch (error) {
      console.warn('Failed to get cached data:', error);
      return null;
    }
  }

  static remove(key: string): void {
    if (!supportsLocalStorage()) return;

    try {
      localStorage.removeItem(this.prefix + key);
    } catch (error) {
      console.warn('Failed to remove cached data:', error);
    }
  }

  static clear(): void {
    if (!supportsLocalStorage()) return;

    try {
      if (Object.keys) {
        const keys = Object.keys(localStorage);
        keys.forEach(key => {
          if (key && key.startsWith && key.startsWith(this.prefix)) {
            localStorage.removeItem(key);
          }
        });
      }
    } catch (error) {
      console.warn('Failed to clear cache:', error);
    }
  }

  // Получение статистики кэша
  static getStats(): { totalItems: number; totalSize: number; oldestItem?: string; newestItem?: string } {
    if (!supportsLocalStorage()) return { totalItems: 0, totalSize: 0 };

    try {
      const keys = Object.keys ? Object.keys(localStorage) : [];
      const cacheKeys = keys.filter(key => key && key.startsWith && key.startsWith(this.prefix));

      let totalSize = 0;
      let oldestTime = Date.now();
      let newestTime = 0;
      let oldestItem = '';
      let newestItem = '';

      cacheKeys.forEach(key => {
        try {
          const item = localStorage.getItem(key);
          if (item) {
            totalSize += item.length;
            const data = JSON.parse(item);
            if (data.timestamp) {
              if (data.timestamp < oldestTime) {
                oldestTime = data.timestamp;
                oldestItem = key.replace(this.prefix, '');
              }
              if (data.timestamp > newestTime) {
                newestTime = data.timestamp;
                newestItem = key.replace(this.prefix, '');
              }
            }
          }
        } catch {
          // Игнорируем поврежденные элементы
        }
      });

      return {
        totalItems: cacheKeys.length,
        totalSize,
        oldestItem: oldestItem || undefined,
        newestItem: newestItem || undefined,
      };
    } catch (error) {
      console.warn('Failed to get cache stats:', error);
      return { totalItems: 0, totalSize: 0 };
    }
  }

  // Очистка устаревших элементов
  static cleanExpired(): number {
    if (!supportsLocalStorage()) return 0;

    let cleaned = 0;
    try {
      const keys = Object.keys ? Object.keys(localStorage) : [];
      const cacheKeys = keys.filter(key => key && key.startsWith && key.startsWith(this.prefix));

      cacheKeys.forEach(key => {
        try {
          const item = localStorage.getItem(key);
          if (item) {
            const data = JSON.parse(item);
            if (data.expiry && Date.now() > data.expiry) {
              localStorage.removeItem(key);
              cleaned++;
            }
          }
        } catch {
          // Удаляем поврежденные элементы
          localStorage.removeItem(key);
          cleaned++;
        }
      });
    } catch (error) {
      console.warn('Failed to clean expired cache:', error);
    }

    return cleaned;
  }

  // Автоматическая очистка при превышении лимита
  static autoCleanup(maxItems = 50, maxSize = 5 * 1024 * 1024): void {
    if (!supportsLocalStorage()) return;

    try {
      const stats = this.getStats();

      // Очищаем устаревшие элементы
      const cleaned = this.cleanExpired();

      // Если все еще превышаем лимиты, удаляем самые старые
      if (stats.totalItems - cleaned > maxItems || stats.totalSize > maxSize) {
        const keys = Object.keys ? Object.keys(localStorage) : [];
        const cacheKeys = keys.filter(key => key && key.startsWith && key.startsWith(this.prefix));

        // Сортируем по времени создания
        const sortedKeys = cacheKeys.sort((a, b) => {
          try {
            const itemA = localStorage.getItem(a);
            const itemB = localStorage.getItem(b);
            if (itemA && itemB) {
              const dataA = JSON.parse(itemA);
              const dataB = JSON.parse(itemB);
              return (dataA.timestamp || 0) - (dataB.timestamp || 0);
            }
          } catch {
            // Если не можем парсить, считаем старым
          }
          return 0;
        });

        // Удаляем самые старые элементы
        const toRemove = Math.max(0, sortedKeys.length - maxItems);
        for (let i = 0; i < toRemove; i++) {
          localStorage.removeItem(sortedKeys[i]);
        }
      }
    } catch (error) {
      console.warn('Failed to auto cleanup cache:', error);
    }
  }
}

// Современная реализация с fetch
class FetchApiClient {
  private baseURL: string;
  private defaultTimeout: number;

  constructor(baseURL: string = '', timeout: number = 10000) {
    this.baseURL = baseURL;
    this.defaultTimeout = timeout;
  }

  async request<T>(
    url: string,
    config: RequestConfig = {},
    cacheConfig: CacheConfig = {}
  ): Promise<ApiResponse<T>> {
    const {
      method = 'GET',
      headers = {},
      body,
      timeout = this.defaultTimeout,
      retries = 3,
      retryDelay = 1000,
    } = config;

    const { ttl = 300000, key, enabled: cacheEnabled = true } = cacheConfig;

    // Проверяем кэш для GET запросов
    if (method === 'GET' && cacheEnabled) {
      const cacheKey = key || `${method}_${url}`;
      const cached = CacheManager.get(cacheKey);
      if (cached) {
        return { success: true, data: cached };
      }
    }

    const fullURL = (url && url.startsWith && url.startsWith('http')) ? url : `${this.baseURL}${url}`;

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    const requestHeaders = {
      'Content-Type': 'application/json',
      ...headers,
    };

    let attempt = 0;
    while (attempt <= retries) {
      try {
        const response = await fetch(fullURL, {
          method,
          headers: requestHeaders,
          body: body ? JSON.stringify(body) : undefined,
          signal: controller.signal,
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
          throw new APIError(`HTTP ${response.status}: ${response.statusText}`, response.status, response.status);
        }

        const data = await response.json();

        // Кэшируем успешные GET запросы
        if (method === 'GET' && cacheEnabled) {
          const cacheKey = key || `${method}_${url}`;
          CacheManager.set(cacheKey, data, ttl);
        }

        return { success: true, data };
      } catch (error) {
        attempt++;

        if (attempt > retries) {
          clearTimeout(timeoutId);

          // Определяем тип ошибки
          let apiError = error;
          if (error instanceof Error) {
            if (error.name === 'AbortError') {
              apiError = new TimeoutError('Request timeout');
            } else if (error.message && error.message.includes && error.message.includes('fetch')) {
              apiError = new NetworkError(error.message);
            }
          }

          return {
            success: false,
            error: apiError instanceof Error ? apiError.message : 'Unknown error',
          };
        }

        // Ждем перед повторной попыткой
        await new Promise(resolve => setTimeout(resolve, retryDelay * attempt));
      }
    }

    return { success: false, error: 'Max retries exceeded' };
  }
}

// Legacy реализация с XMLHttpRequest
class XHRApiClient {
  private baseURL: string;
  private defaultTimeout: number;

  constructor(baseURL: string = '', timeout: number = 10000) {
    this.baseURL = baseURL;
    this.defaultTimeout = timeout;
  }

  async request<T>(
    url: string,
    config: RequestConfig = {},
    cacheConfig: CacheConfig = {}
  ): Promise<ApiResponse<T>> {
    const {
      method = 'GET',
      headers = {},
      body,
      timeout = this.defaultTimeout,
      retries = 3,
      retryDelay = 1000,
    } = config;

    const { ttl = 300000, key, enabled: cacheEnabled = true } = cacheConfig;

    // Проверяем кэш для GET запросов
    if (method === 'GET' && cacheEnabled) {
      const cacheKey = key || `${method}_${url}`;
      const cached = CacheManager.get(cacheKey);
      if (cached) {
        return { success: true, data: cached };
      }
    }

    const fullURL = (url && url.startsWith && url.startsWith('http')) ? url : `${this.baseURL}${url}`;

    let attempt = 0;
    while (attempt <= retries) {
      try {
        const data = await new Promise<T>((resolve, reject) => {
          const xhr = new XMLHttpRequest();
          
          xhr.timeout = timeout;
          xhr.ontimeout = () => reject(new TimeoutError('Request timeout'));
          xhr.onerror = () => reject(new NetworkError('Network error'));
          xhr.onabort = () => reject(new NetworkError('Request aborted'));
          
          xhr.onreadystatechange = () => {
            if (xhr.readyState === 4) {
              if (xhr.status >= 200 && xhr.status < 300) {
                try {
                  const response = JSON.parse(xhr.responseText);
                  resolve(response);
                } catch (error) {
                  reject(new Error('Invalid JSON response'));
                }
              } else {
                reject(new APIError(`HTTP ${xhr.status}: ${xhr.statusText}`, xhr.status, xhr.status));
              }
            }
          };

          xhr.open(method, fullURL, true);

          // Устанавливаем заголовки
          if (Object.entries) {
            Object.entries({
              'Content-Type': 'application/json',
              ...headers,
            }).forEach(([key, value]) => {
              xhr.setRequestHeader(key, value);
            });
          }

          xhr.send(body ? JSON.stringify(body) : null);
        });

        // Кэшируем успешные GET запросы
        if (method === 'GET' && cacheEnabled) {
          const cacheKey = key || `${method}_${url}`;
          CacheManager.set(cacheKey, data, ttl);
        }

        return { success: true, data };
      } catch (error) {
        attempt++;

        if (attempt > retries) {
          // Определяем тип ошибки
          let apiError = error;
          if (error instanceof Error && error.name && error.name.includes && !error.name.includes('Error')) {
            if (error.message && error.message.includes && error.message.includes('timeout')) {
              apiError = new TimeoutError(error.message);
            } else if (error.message && error.message.includes && (error.message.includes('Network') || error.message.includes('network'))) {
              apiError = new NetworkError(error.message);
            }
          }

          return {
            success: false,
            error: apiError instanceof Error ? apiError.message : 'Unknown error',
          };
        }

        // Ждем перед повторной попыткой
        await new Promise(resolve => setTimeout(resolve, retryDelay * attempt));
      }
    }

    return { success: false, error: 'Max retries exceeded' };
  }
}

// Универсальный API клиент
export class ApiClient {
  private client: FetchApiClient | XHRApiClient;

  constructor(baseURL: string = '', timeout: number = 10000) {
    if (supportsFetch()) {
      this.client = new FetchApiClient(baseURL, timeout);
    } else {
      this.client = new XHRApiClient(baseURL, timeout);
    }
  }

  async get<T>(
    url: string,
    config: Omit<RequestConfig, 'method'> = {},
    cacheConfig: CacheConfig = {}
  ): Promise<ApiResponse<T>> {
    // Используем withRetry если указаны retryOptions
    if (config.retryOptions) {
      try {
        const result = await withRetry(
          () => this.client.request<T>(url, { ...config, method: 'GET' }, cacheConfig),
          config.retryOptions
        );
        return result;
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
        };
      }
    }

    return this.client.request<T>(url, { ...config, method: 'GET' }, cacheConfig);
  }

  async post<T>(
    url: string,
    data?: any,
    config: Omit<RequestConfig, 'method' | 'body'> = {}
  ): Promise<ApiResponse<T>> {
    return this.client.request<T>(url, { ...config, method: 'POST', body: data });
  }

  async put<T>(
    url: string,
    data?: any,
    config: Omit<RequestConfig, 'method' | 'body'> = {}
  ): Promise<ApiResponse<T>> {
    return this.client.request<T>(url, { ...config, method: 'PUT', body: data });
  }

  async delete<T>(
    url: string,
    config: Omit<RequestConfig, 'method'> = {}
  ): Promise<ApiResponse<T>> {
    return this.client.request<T>(url, { ...config, method: 'DELETE' });
  }

  // Утилиты для кэша
  clearCache(): void {
    CacheManager.clear();
  }

  removeCacheItem(key: string): void {
    CacheManager.remove(key);
  }
}

// Экспорт единственного экземпляра
export const apiClient = new ApiClient('/api/v1');

export default apiClient;
