import axios, { type AxiosInstance, type AxiosResponse } from 'axios';
import type { ApiResponse, Tariff, TrialInfo, FAQItem } from '../types';

export class ApiService {
  private api: AxiosInstance;

  constructor() {
    this.api = axios.create({
      baseURL: import.meta.env.VITE_API_URL || '/api',
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Интерсептор для обработки ответов
    this.api.interceptors.response.use(
      (response: AxiosResponse) => response,
      (error) => {
        console.error('API Error:', error);
        return Promise.reject(error);
      }
    );
  }

  // Получение тарифов
  async getTariffs(): Promise<Tariff[]> {
    try {
      const response = await this.api.get<Tariff[]>('/v1/public/tariffs');
      console.log('API Service: Tariffs raw response:', response.data);

      // API возвращает массив тарифов напрямую
      if (Array.isArray(response.data)) {
        return response.data;
      }

      // Если вдруг API изменится и будет возвращать обертку
      if (response.data && typeof response.data === 'object' && 'data' in response.data) {
        return (response.data as any).data;
      }

      console.warn('Unexpected tariffs response format:', response.data);
      return [];
    } catch (error) {
      console.error('Ошибка получения тарифов:', error);
      throw error; // Пробрасываем ошибку дальше
    }
  }

  // Получение информации о пробном периоде
  async getTrialInfo(): Promise<TrialInfo> {
    try {
      const response = await this.api.get<ApiResponse<TrialInfo>>('/v1/public/trial/info');
      
      if (response.data.success && response.data.data) {
        return response.data.data;
      }
      
      throw new Error(response.data.error || 'Ошибка получения информации о пробном периоде');
    } catch (error) {
      console.error('Ошибка получения информации о пробном периоде:', error);
      throw error;
    }
  }

  // Получение FAQ
  async getFAQ(): Promise<FAQItem[]> {
    try {
      console.log('API Service: Making request to /v1/public/faq');
      const response = await this.api.get('/v1/public/faq');
      console.log('API Service: Raw response:', response.data);

      // Обрабатываем вложенную структуру ответа: {success: true, data: {success: true, data: [...]}}
      if (response.data?.success && response.data?.data) {
        const outerData = response.data.data;
        console.log('API Service: Outer data:', outerData);

        // Проверяем вложенную структуру
        if (outerData?.success && outerData?.data && Array.isArray(outerData.data)) {
          console.log('API Service: Found nested structure, returning inner data:', outerData.data);
          return outerData.data;
        }

        // Если внешний data уже является массивом
        if (Array.isArray(outerData)) {
          console.log('API Service: Outer data is array, returning:', outerData);
          return outerData;
        }
      }

      // Возвращаем пустой массив вместо ошибки для graceful fallback
      console.warn('FAQ data not found, returning empty array');
      return [];
    } catch (error) {
      console.error('Ошибка получения FAQ:', error);
      // Возвращаем пустой массив вместо выброса ошибки
      return [];
    }
  }

  // Создание платежа
  async createPayment(tariffId: string, paymentMethod: string): Promise<{ paymentUrl: string }> {
    try {
      const response = await this.api.post<ApiResponse<{ paymentUrl: string }>>('/payment/create', {
        tariff_id: tariffId,
        payment_method: paymentMethod
      });
      
      if (response.data.success && response.data.data) {
        return response.data.data;
      }
      
      throw new Error(response.data.error || 'Ошибка создания платежа');
    } catch (error) {
      console.error('Ошибка создания платежа:', error);
      throw error;
    }
  }
}

export default new ApiService();