/**
 * Сервис для работы с тарифами
 */

import { apiClient } from './apiClient';
import { FALLBACK_TARIFFS } from '@/types/api';
import type { TariffsResponse, Currency } from '@/types/api';
import type { ApiResponse } from './apiClient';

export class TariffsService {
  private static instance: TariffsService;
  private cache: TariffsResponse | null = null;
  private loading = false;
  private lastFetch = 0;
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 минут

  static getInstance(): TariffsService {
    if (!TariffsService.instance) {
      TariffsService.instance = new TariffsService();
    }
    return TariffsService.instance;
  }

  /**
   * Получить все тарифы
   */
  async getTariffs(forceRefresh = false): Promise<TariffsResponse> {
    // Проверяем кэш
    if (!forceRefresh && this.cache && Date.now() - this.lastFetch < this.CACHE_TTL) {
      return this.cache;
    }

    // Если уже загружаем, ждем завершения
    if (this.loading) {
      return new Promise((resolve) => {
        const checkLoading = () => {
          if (!this.loading) {
            resolve(this.cache || FALLBACK_TARIFFS);
          } else {
            setTimeout(checkLoading, 100);
          }
        };
        checkLoading();
      });
    }

    this.loading = true;

    try {
      const response: ApiResponse<TariffsResponse> = await apiClient.get(
        '/public/tariffs',
        {
          timeout: 8000,
          retries: 2,
          retryDelay: 1000,
        },
        {
          ttl: this.CACHE_TTL,
          key: 'tariffs',
          enabled: true,
        }
      );

      if (response.success && response.data) {
        // Валидируем данные
        const validatedData = this.validateTariffsData(response.data);
        this.cache = validatedData;
        this.lastFetch = Date.now();
        return validatedData;
      } else {
        console.warn('Failed to load tariffs from API:', response.error);
        return FALLBACK_TARIFFS;
      }
    } catch (error) {
      console.warn('Error loading tariffs:', error);
      return FALLBACK_TARIFFS;
    } finally {
      this.loading = false;
    }
  }

  /**
   * Получить тариф по ID
   */
  async getTariffById(id: string): Promise<TariffsResponse['tariffs'][0] | null> {
    const tariffs = await this.getTariffs();
    return tariffs.tariffs.find(tariff => tariff.id === id) || null;
  }

  /**
   * Получить популярные тарифы
   */
  async getPopularTariffs(): Promise<TariffsResponse['tariffs']> {
    const tariffs = await this.getTariffs();
    return tariffs.tariffs.filter(tariff => tariff.popular);
  }

  /**
   * Получить рекомендуемые тарифы
   */
  async getRecommendedTariffs(): Promise<TariffsResponse['tariffs']> {
    const tariffs = await this.getTariffs();
    return tariffs.tariffs.filter(tariff => tariff.recommended);
  }

  /**
   * Получить доступные валюты
   */
  async getCurrencies(): Promise<Currency[]> {
    const tariffs = await this.getTariffs();
    return tariffs.currencies;
  }

  /**
   * Получить валюту по умолчанию
   */
  async getDefaultCurrency(): Promise<Currency> {
    const tariffs = await this.getTariffs();
    return tariffs.defaultCurrency;
  }

  /**
   * Форматировать цену
   */
  formatPrice(amount: number, currency: Currency): string {
    switch (currency) {
      case 'RUB':
        return `${amount} ₽`;
      case 'USD':
        return `$${amount}`;
      case 'STARS':
        return `${amount} ⭐`;
      default:
        return `${amount}`;
    }
  }

  /**
   * Получить название периода
   */
  getPeriodName(period: 'monthly' | 'quarterly' | 'yearly'): string {
    switch (period) {
      case 'monthly':
        return 'месяц';
      case 'quarterly':
        return '3 месяца';
      case 'yearly':
        return 'год';
      default:
        return period;
    }
  }

  /**
   * Рассчитать скидку
   */
  calculateDiscount(
    monthlyPrice: number,
    periodPrice: number,
    months: number
  ): number {
    const totalMonthlyPrice = monthlyPrice * months;
    const discount = ((totalMonthlyPrice - periodPrice) / totalMonthlyPrice) * 100;
    return Math.round(discount);
  }

  /**
   * Получить лучшее предложение (с максимальной скидкой)
   */
  getBestDeal(tariff: TariffsResponse['tariffs'][0], currency: Currency): {
    period: 'monthly' | 'quarterly' | 'yearly';
    discount: number;
  } {
    const prices = tariff.prices[currency];
    
    const quarterlyDiscount = this.calculateDiscount(prices.monthly, prices.quarterly, 3);
    const yearlyDiscount = this.calculateDiscount(prices.monthly, prices.yearly, 12);

    if (yearlyDiscount > quarterlyDiscount) {
      return { period: 'yearly', discount: yearlyDiscount };
    } else if (quarterlyDiscount > 0) {
      return { period: 'quarterly', discount: quarterlyDiscount };
    } else {
      return { period: 'monthly', discount: 0 };
    }
  }

  /**
   * Валидация данных тарифов
   */
  private validateTariffsData(data: any): TariffsResponse {
    try {
      // Проверяем основную структуру
      if (!data || typeof data !== 'object') {
        throw new Error('Invalid data structure');
      }

      const { tariffs, currencies, defaultCurrency } = data;

      // Проверяем тарифы
      if (!Array.isArray(tariffs) || tariffs.length === 0) {
        throw new Error('Invalid tariffs array');
      }

      // Проверяем валюты
      if (!Array.isArray(currencies) || currencies.length === 0) {
        throw new Error('Invalid currencies array');
      }

      // Проверяем валюту по умолчанию
      if (!defaultCurrency || !currencies || !currencies.includes || !currencies.includes(defaultCurrency)) {
        throw new Error('Invalid default currency');
      }

      // Валидируем каждый тариф
      const validatedTariffs = tariffs.map((tariff: any) => {
        if (!tariff.id || !tariff.name || !tariff.prices) {
          throw new Error(`Invalid tariff structure: ${tariff.id}`);
        }

        // Проверяем цены для всех валют
        currencies.forEach((currency: Currency) => {
          if (!tariff.prices[currency] || 
              typeof tariff.prices[currency].monthly !== 'number' ||
              typeof tariff.prices[currency].quarterly !== 'number' ||
              typeof tariff.prices[currency].yearly !== 'number') {
            throw new Error(`Invalid prices for currency ${currency} in tariff ${tariff.id}`);
          }
        });

        return {
          ...tariff,
          features: Array.isArray(tariff.features) ? tariff.features : [],
          maxDevices: typeof tariff.maxDevices === 'number' ? tariff.maxDevices : 1,
          countries: typeof tariff.countries === 'number' ? tariff.countries : 1,
          trial: Boolean(tariff.trial),
          trialDays: typeof tariff.trialDays === 'number' ? tariff.trialDays : 7,
        };
      });

      return {
        tariffs: validatedTariffs,
        currencies,
        defaultCurrency,
      };
    } catch (error) {
      console.warn('Tariffs data validation failed:', error);
      return FALLBACK_TARIFFS;
    }
  }

  /**
   * Очистить кэш
   */
  clearCache(): void {
    this.cache = null;
    this.lastFetch = 0;
    apiClient.removeCacheItem('tariffs');
  }

  /**
   * Проверить, загружаются ли данные
   */
  isLoading(): boolean {
    return this.loading;
  }
}

// Экспорт единственного экземпляра
export const tariffsService = TariffsService.getInstance();

export default tariffsService;
