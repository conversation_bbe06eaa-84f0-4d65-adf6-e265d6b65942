/**
 * Сервис для работы с FAQ
 */

import { apiClient } from './apiClient';
import { FALLBACK_FAQ } from '@/types/api';
import type { FAQResponse, FAQItem, FAQCategory, FAQSearchParams } from '@/types/api';
import type { ApiResponse } from './apiClient';

export class FAQService {
  private static instance: FAQService;
  private cache: FAQResponse | null = null;
  private loading = false;
  private lastFetch = 0;
  private readonly CACHE_TTL = 10 * 60 * 1000; // 10 минут

  static getInstance(): FAQService {
    if (!FAQService.instance) {
      FAQService.instance = new FAQService();
    }
    return FAQService.instance;
  }

  /**
   * Получить все FAQ
   */
  async getFAQ(params: FAQSearchParams = {}, forceRefresh = false): Promise<FAQResponse> {
    // Проверяем кэш только для запросов без параметров
    const isSimpleRequest = !params.query && !params.category && !params.limit && !params.offset;
    
    if (!forceRefresh && isSimpleRequest && this.cache && Date.now() - this.lastFetch < this.CACHE_TTL) {
      return this.cache;
    }

    // Если уже загружаем, ждем завершения
    if (this.loading) {
      return new Promise((resolve) => {
        const checkLoading = () => {
          if (!this.loading) {
            resolve(this.cache || FALLBACK_FAQ);
          } else {
            setTimeout(checkLoading, 100);
          }
        };
        checkLoading();
      });
    }

    this.loading = true;

    try {
      // Формируем URL с параметрами
      const searchParams = new URLSearchParams();
      if (params.query) searchParams.append('query', params.query);
      if (params.category) searchParams.append('category', params.category);
      if (params.limit) searchParams.append('limit', params.limit.toString());
      if (params.offset) searchParams.append('offset', params.offset.toString());

      const url = `/public/faq${searchParams.toString() ? `?${searchParams.toString()}` : ''}`;

      const response: ApiResponse<FAQResponse> = await apiClient.get(
        url,
        {
          timeout: 8000,
          retries: 2,
          retryDelay: 1000,
        },
        {
          ttl: this.CACHE_TTL,
          key: `faq_${searchParams.toString()}`,
          enabled: isSimpleRequest, // Кэшируем только простые запросы
        }
      );

      if (response.success && response.data) {
        // Валидируем данные
        const validatedData = this.validateFAQData(response.data);
        
        // Кэшируем только простые запросы
        if (isSimpleRequest) {
          this.cache = validatedData;
          this.lastFetch = Date.now();
        }
        
        return validatedData;
      } else {
        console.warn('Failed to load FAQ from API:', response.error);
        return this.filterFallbackData(params);
      }
    } catch (error) {
      console.warn('Error loading FAQ:', error);
      return this.filterFallbackData(params);
    } finally {
      this.loading = false;
    }
  }

  /**
   * Поиск по FAQ
   */
  async searchFAQ(query: string, category?: string): Promise<FAQItem[]> {
    const response = await this.getFAQ({ query, category });
    return response.items;
  }

  /**
   * Получить FAQ по категории
   */
  async getFAQByCategory(category: string): Promise<FAQItem[]> {
    const response = await this.getFAQ({ category });
    return response.items;
  }

  /**
   * Получить популярные FAQ
   */
  async getPopularFAQ(): Promise<FAQItem[]> {
    const response = await this.getFAQ();
    return response.items.filter(item => item.popular).sort((a, b) => a.order - b.order);
  }

  /**
   * Получить категории FAQ
   */
  async getCategories(): Promise<FAQCategory[]> {
    const response = await this.getFAQ();
    return response.categories.sort((a, b) => a.order - b.order);
  }

  /**
   * Получить FAQ по ID
   */
  async getFAQById(id: string): Promise<FAQItem | null> {
    const response = await this.getFAQ();
    return response.items.find(item => item.id === id) || null;
  }

  /**
   * Локальный поиск в fallback данных
   */
  private filterFallbackData(params: FAQSearchParams): FAQResponse {
    let items = [...FALLBACK_FAQ.items];

    // Фильтрация по категории
    if (params.category) {
      items = items.filter(item => item.category === params.category);
    }

    // Поиск по тексту
    if (params.query) {
      const query = params.query.toLowerCase();
      items = items.filter(item => {
        try {
          if (!item) return false;

          const question = String(item.question || '').toLowerCase();
          const answer = String(item.answer || '').toLowerCase();
          const tags = Array.isArray(item.tags) ? item.tags.map(tag => String(tag || '').toLowerCase()) : [];

          const questionMatch = question && question.includes ? question.includes(query) : false;
          const answerMatch = answer && answer.includes ? answer.includes(query) : false;
          const tagsMatch = tags.some ? tags.some(tag => tag && tag.includes ? tag.includes(query) : false) : false;

          return questionMatch || answerMatch || tagsMatch;
        } catch {
          return false;
        }
      });
    }

    // Сортировка
    items.sort((a, b) => {
      // Популярные сначала
      if (a.popular && !b.popular) return -1;
      if (!a.popular && b.popular) return 1;
      // Затем по порядку
      return a.order - b.order;
    });

    // Пагинация
    if (params.offset || params.limit) {
      const offset = params.offset || 0;
      const limit = params.limit || items.length;
      items = items.slice(offset, offset + limit);
    }

    return {
      items,
      categories: FALLBACK_FAQ.categories,
      total: items.length,
    };
  }

  /**
   * Валидация данных FAQ
   */
  private validateFAQData(data: any): FAQResponse {
    try {
      // Проверяем основную структуру
      if (!data || typeof data !== 'object') {
        throw new Error('Invalid data structure');
      }

      const { items, categories, total } = data;

      // Проверяем элементы FAQ
      if (!Array.isArray(items)) {
        throw new Error('Invalid items array');
      }

      // Проверяем категории
      if (!Array.isArray(categories)) {
        throw new Error('Invalid categories array');
      }

      // Валидируем каждый элемент FAQ
      const validatedItems = items.map((item: any) => {
        if (!item.id || !item.question || !item.answer) {
          throw new Error(`Invalid FAQ item structure: ${item.id}`);
        }

        return {
          ...item,
          tags: Array.isArray(item.tags) ? item.tags : [],
          order: typeof item.order === 'number' ? item.order : 999,
          popular: Boolean(item.popular),
        };
      });

      // Валидируем категории
      const validatedCategories = categories.map((category: any) => {
        if (!category.id || !category.name) {
          throw new Error(`Invalid category structure: ${category.id}`);
        }

        return {
          ...category,
          order: typeof category.order === 'number' ? category.order : 999,
        };
      });

      return {
        items: validatedItems,
        categories: validatedCategories,
        total: typeof total === 'number' ? total : validatedItems.length,
      };
    } catch (error) {
      console.warn('FAQ data validation failed:', error);
      return FALLBACK_FAQ;
    }
  }

  /**
   * Очистить кэш
   */
  clearCache(): void {
    this.cache = null;
    this.lastFetch = 0;
    // Очищаем все FAQ кэши
    apiClient.clearCache();
  }

  /**
   * Проверить, загружаются ли данные
   */
  isLoading(): boolean {
    return this.loading;
  }

  /**
   * Получить статистику FAQ
   */
  async getFAQStats(): Promise<{
    totalItems: number;
    totalCategories: number;
    popularItems: number;
  }> {
    const response = await this.getFAQ();
    return {
      totalItems: response.total,
      totalCategories: response.categories.length,
      popularItems: response.items.filter(item => item.popular).length,
    };
  }
}

// Экспорт единственного экземпляра
export const faqService = FAQService.getInstance();

export default faqService;
