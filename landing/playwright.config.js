/**
 * Конфигурация Playwright для тестирования совместимости браузеров
 */

import { defineConfig, devices } from '@playwright/test';

export default defineConfig({
  // Директория с тестами
  testDir: './tests',
  
  // Паттерн для поиска тестов
  testMatch: '**/*.test.js',
  
  // Таймауты
  timeout: 30000,
  expect: {
    timeout: 5000,
  },
  
  // Настройки запуска
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  
  // Репортеры
  reporter: [
    ['html', { outputFolder: 'test-results/html-report' }],
    ['json', { outputFile: 'test-results/results.json' }],
    ['junit', { outputFile: 'test-results/junit.xml' }],
    ['list']
  ],
  
  // Глобальные настройки
  use: {
    // Базовый URL
    baseURL: 'http://localhost:3000',
    
    // Трейсинг при ошибках
    trace: 'on-first-retry',
    
    // Скриншоты при ошибках
    screenshot: 'only-on-failure',
    
    // Видео при ошибках
    video: 'retain-on-failure',
    
    // Настройки браузера
    actionTimeout: 10000,
    navigationTimeout: 30000,
  },

  // Конфигурация проектов для разных браузеров
  projects: [
    // Desktop Chrome
    {
      name: 'chromium',
      use: { 
        ...devices['Desktop Chrome'],
        // Дополнительные флаги для Chrome
        launchOptions: {
          args: [
            '--disable-web-security',
            '--disable-features=TranslateUI',
            '--disable-ipc-flooding-protection',
          ],
        },
      },
    },

    // Desktop Firefox
    {
      name: 'firefox',
      use: { 
        ...devices['Desktop Firefox'],
        // Настройки для Firefox
        launchOptions: {
          firefoxUserPrefs: {
            'dom.webnotifications.enabled': false,
            'dom.push.enabled': false,
          },
        },
      },
    },

    // Desktop Safari
    {
      name: 'webkit',
      use: { 
        ...devices['Desktop Safari'],
      },
    },

    // Mobile Chrome
    {
      name: 'Mobile Chrome',
      use: { 
        ...devices['Pixel 5'],
      },
    },

    // Mobile Safari
    {
      name: 'Mobile Safari',
      use: { 
        ...devices['iPhone 12'],
      },
    },

    // Tablet
    {
      name: 'iPad',
      use: { 
        ...devices['iPad Pro'],
      },
    },

    // Legacy browser simulation
    {
      name: 'Legacy Chrome',
      use: {
        ...devices['Desktop Chrome'],
        // Симуляция старого Chrome
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/60.0.3112.113 Safari/537.36',
        viewport: { width: 1280, height: 720 },
        // Отключаем современные функции
        javaScriptEnabled: true,
        extraHTTPHeaders: {
          'Accept-Language': 'en-US,en;q=0.9',
        },
      },
    },

    // Slow network simulation
    {
      name: 'Slow Network',
      use: {
        ...devices['Desktop Chrome'],
        // Симуляция медленного соединения
        launchOptions: {
          args: ['--force-effective-connection-type=slow-2g'],
        },
      },
    },

    // High DPI display
    {
      name: 'High DPI',
      use: {
        ...devices['Desktop Chrome'],
        viewport: { width: 1920, height: 1080 },
        deviceScaleFactor: 2,
      },
    },
  ],

  // Веб-сервер для тестирования
  webServer: {
    command: 'npm run dev',
    port: 3000,
    reuseExistingServer: !process.env.CI,
    timeout: 120000,
  },

  // Директории для вывода
  outputDir: 'test-results/artifacts',
  
  // Глобальные хуки
  globalSetup: require.resolve('./tests/global-setup.js'),
  globalTeardown: require.resolve('./tests/global-teardown.js'),
});
