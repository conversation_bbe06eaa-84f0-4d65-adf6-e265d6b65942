#!/usr/bin/env node

/**
 * Скрипт для анализа размера bundle
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { execSync } from 'child_process';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const projectRoot = path.resolve(__dirname, '..');
const distDir = path.join(projectRoot, 'dist');

// Цвета для консоли
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function formatBytes(bytes) {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
}

function getFileSize(filePath) {
  try {
    return fs.statSync(filePath).size;
  } catch (error) {
    return 0;
  }
}

function getGzipSize(filePath) {
  try {
    const gzipCommand = process.platform === 'win32' 
      ? `powershell -command "& {Add-Type -AssemblyName System.IO.Compression; [System.IO.Compression.GzipStream]::new([System.IO.File]::OpenRead('${filePath}'), [System.IO.Compression.CompressionMode]::Compress) | Out-Null; (Get-Item '${filePath}').Length}"`
      : `gzip -c "${filePath}" | wc -c`;
    
    const result = execSync(gzipCommand, { encoding: 'utf8' });
    return parseInt(result.trim());
  } catch (error) {
    return 0;
  }
}

function analyzeFiles(directory, pattern) {
  const files = [];
  
  if (!fs.existsSync(directory)) {
    return files;
  }
  
  const items = fs.readdirSync(directory);
  
  for (const item of items) {
    const fullPath = path.join(directory, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isFile() && pattern.test(item)) {
      const size = getFileSize(fullPath);
      const gzipSize = getGzipSize(fullPath);
      
      files.push({
        name: item,
        path: fullPath,
        size,
        gzipSize,
        compression: size > 0 ? ((size - gzipSize) / size * 100).toFixed(1) : 0,
      });
    }
  }
  
  return files.sort((a, b) => b.size - a.size);
}

function printTable(title, files, sizeThresholds = {}) {
  console.log(`\n${colors.bright}${colors.blue}${title}${colors.reset}`);
  console.log('─'.repeat(80));
  
  if (files.length === 0) {
    console.log(`${colors.yellow}Файлы не найдены${colors.reset}`);
    return;
  }
  
  console.log(`${'Файл'.padEnd(40)} ${'Размер'.padEnd(12)} ${'Gzip'.padEnd(12)} ${'Сжатие'.padEnd(8)}`);
  console.log('─'.repeat(80));
  
  let totalSize = 0;
  let totalGzipSize = 0;
  
  for (const file of files) {
    totalSize += file.size;
    totalGzipSize += file.gzipSize;
    
    let sizeColor = colors.green;
    if (sizeThresholds.warning && file.size > sizeThresholds.warning) {
      sizeColor = colors.yellow;
    }
    if (sizeThresholds.error && file.size > sizeThresholds.error) {
      sizeColor = colors.red;
    }
    
    const fileName = file.name.length > 38 ? file.name.substring(0, 35) + '...' : file.name;
    
    console.log(
      `${fileName.padEnd(40)} ` +
      `${sizeColor}${formatBytes(file.size).padEnd(12)}${colors.reset} ` +
      `${formatBytes(file.gzipSize).padEnd(12)} ` +
      `${file.compression}%`.padEnd(8)
    );
  }
  
  console.log('─'.repeat(80));
  console.log(
    `${'ИТОГО'.padEnd(40)} ` +
    `${colors.bright}${formatBytes(totalSize).padEnd(12)}${colors.reset} ` +
    `${colors.bright}${formatBytes(totalGzipSize).padEnd(12)}${colors.reset} ` +
    `${colors.bright}${totalSize > 0 ? ((totalSize - totalGzipSize) / totalSize * 100).toFixed(1) : 0}%${colors.reset}`.padEnd(8)
  );
}

function analyzeBundle() {
  console.log(`${colors.bright}${colors.cyan}📊 Анализ размера bundle${colors.reset}`);
  console.log(`Директория: ${distDir}`);
  
  if (!fs.existsSync(distDir)) {
    console.log(`${colors.red}❌ Директория dist не найдена. Запустите 'npm run build' сначала.${colors.reset}`);
    process.exit(1);
  }
  
  // Анализ JavaScript файлов
  const jsFiles = analyzeFiles(path.join(distDir, 'assets'), /\.js$/);
  printTable('📦 JavaScript файлы', jsFiles, {
    warning: 500 * 1024, // 500KB
    error: 1024 * 1024,  // 1MB
  });
  
  // Анализ CSS файлов
  const cssFiles = analyzeFiles(path.join(distDir, 'assets'), /\.css$/);
  printTable('🎨 CSS файлы', cssFiles, {
    warning: 100 * 1024, // 100KB
    error: 200 * 1024,   // 200KB
  });
  
  // Анализ изображений
  const imageFiles = analyzeFiles(distDir, /\.(png|jpg|jpeg|gif|svg|webp|ico)$/);
  printTable('🖼️  Изображения', imageFiles, {
    warning: 200 * 1024, // 200KB
    error: 500 * 1024,   // 500KB
  });
  
  // Общая статистика
  const allFiles = [...jsFiles, ...cssFiles, ...imageFiles];
  const totalSize = allFiles.reduce((sum, file) => sum + file.size, 0);
  const totalGzipSize = allFiles.reduce((sum, file) => sum + file.gzipSize, 0);
  
  console.log(`\n${colors.bright}${colors.magenta}📈 Общая статистика${colors.reset}`);
  console.log('─'.repeat(50));
  console.log(`Общий размер: ${colors.bright}${formatBytes(totalSize)}${colors.reset}`);
  console.log(`Размер после gzip: ${colors.bright}${formatBytes(totalGzipSize)}${colors.reset}`);
  console.log(`Сжатие: ${colors.bright}${totalSize > 0 ? ((totalSize - totalGzipSize) / totalSize * 100).toFixed(1) : 0}%${colors.reset}`);
  
  // Рекомендации
  console.log(`\n${colors.bright}${colors.yellow}💡 Рекомендации${colors.reset}`);
  console.log('─'.repeat(50));
  
  const largeJsFiles = jsFiles.filter(file => file.size > 500 * 1024);
  if (largeJsFiles.length > 0) {
    console.log(`${colors.yellow}⚠️  Большие JS файлы (>500KB):${colors.reset}`);
    largeJsFiles.forEach(file => {
      console.log(`   - ${file.name}: ${formatBytes(file.size)}`);
    });
    console.log('   Рассмотрите возможность разделения кода или lazy loading');
  }
  
  const largeCssFiles = cssFiles.filter(file => file.size > 100 * 1024);
  if (largeCssFiles.length > 0) {
    console.log(`${colors.yellow}⚠️  Большие CSS файлы (>100KB):${colors.reset}`);
    largeCssFiles.forEach(file => {
      console.log(`   - ${file.name}: ${formatBytes(file.size)}`);
    });
    console.log('   Рассмотрите возможность удаления неиспользуемых стилей');
  }
  
  const largeImages = imageFiles.filter(file => file.size > 200 * 1024);
  if (largeImages.length > 0) {
    console.log(`${colors.yellow}⚠️  Большие изображения (>200KB):${colors.reset}`);
    largeImages.forEach(file => {
      console.log(`   - ${file.name}: ${formatBytes(file.size)}`);
    });
    console.log('   Рассмотрите возможность оптимизации изображений');
  }
  
  if (totalGzipSize < 1024 * 1024) {
    console.log(`${colors.green}✅ Отличный размер bundle! (<1MB после gzip)${colors.reset}`);
  } else if (totalGzipSize < 2 * 1024 * 1024) {
    console.log(`${colors.yellow}⚠️  Приемлемый размер bundle (1-2MB после gzip)${colors.reset}`);
  } else {
    console.log(`${colors.red}❌ Большой размер bundle (>2MB после gzip)${colors.reset}`);
  }
  
  // Ссылка на bundle analyzer
  const statsPath = path.join(distDir, 'stats.html');
  if (fs.existsSync(statsPath)) {
    console.log(`\n${colors.bright}${colors.cyan}📊 Детальный анализ доступен в:${colors.reset}`);
    console.log(`   file://${statsPath}`);
  }
}

// Запуск анализа
analyzeBundle();
