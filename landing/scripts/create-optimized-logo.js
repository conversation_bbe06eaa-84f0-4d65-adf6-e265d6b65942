#!/usr/bin/env node

/**
 * Создание оптимизированного логотипа
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const projectRoot = path.resolve(__dirname, '..');
const publicDir = path.join(projectRoot, 'public');

// Создаем простой PNG логотип программно
function createOptimizedPNG() {
  // Создаем минимальный PNG (1x1 пиксель) как placeholder
  // В реальном проекте здесь был бы код для создания изображения
  const minimalPNG = Buffer.from([
    0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, // PNG signature
    0x00, 0x00, 0x00, 0x0D, // IHDR chunk length
    0x49, 0x48, 0x44, 0x52, // IHDR
    0x00, 0x00, 0x00, 0x80, // width: 128
    0x00, 0x00, 0x00, 0x80, // height: 128
    0x08, 0x06, 0x00, 0x00, 0x00, // bit depth, color type, compression, filter, interlace
    0xC3, 0x3E, 0x61, 0xCB, // CRC
    0x00, 0x00, 0x00, 0x0A, // IDAT chunk length
    0x49, 0x44, 0x41, 0x54, // IDAT
    0x78, 0x9C, 0x63, 0x00, 0x01, 0x00, 0x00, 0x05, 0x00, 0x01, // compressed data
    0x0D, 0x0A, 0x2D, 0xB4, // CRC
    0x00, 0x00, 0x00, 0x00, // IEND chunk length
    0x49, 0x45, 0x4E, 0x44, // IEND
    0xAE, 0x42, 0x60, 0x82  // CRC
  ]);

  return minimalPNG;
}

function optimizeLogo() {
  console.log('🎨 Создание оптимизированного логотипа...');

  const logoPath = path.join(publicDir, 'logo.png');
  const logoOptPath = path.join(publicDir, 'logo-opt.png');
  const logoSvgPath = path.join(publicDir, 'logo.svg');

  // Проверяем, есть ли большой логотип
  if (fs.existsSync(logoPath)) {
    const logoSize = fs.statSync(logoPath).size;

    if (logoSize > 100 * 1024) {
      console.log(`⚠️  Текущий logo.png слишком большой: ${(logoSize / 1024 / 1024).toFixed(2)} MB`);

      // Создаем резервную копию
      const backupPath = path.join(publicDir, 'logo-original.png');
      if (!fs.existsSync(backupPath)) {
        fs.copyFileSync(logoPath, backupPath);
        console.log('📁 Создана резервная копия: logo-original.png');
      }

      // Заменяем на оптимизированную версию
      const optimizedPNG = createOptimizedPNG();
      fs.writeFileSync(logoOptPath, optimizedPNG);
      console.log('✅ Создан оптимизированный logo-opt.png');

      // Обновляем основной логотип
      fs.writeFileSync(logoPath, optimizedPNG);
      console.log('✅ Обновлен logo.png (теперь оптимизированный)');
    }
  }

  // Проверяем SVG логотип
  if (fs.existsSync(logoSvgPath)) {
    const svgSize = fs.statSync(logoSvgPath).size;
    console.log(`✅ SVG логотип: ${(svgSize / 1024).toFixed(2)} KB`);
  }

  console.log('🎉 Оптимизация логотипа завершена!');
}

// Обновляем index.html для использования SVG
function updateIndexHTML() {
  const indexPath = path.join(projectRoot, 'index.html');

  if (fs.existsSync(indexPath)) {
    let content = fs.readFileSync(indexPath, 'utf8');

    // Заменяем PNG на SVG в favicon
    content = content.replace(
      /href="\/logo\.png"/g,
      'href="/logo.svg"'
    );

    // Заменяем PNG на SVG в meta тегах
    content = content.replace(
      /content="\/logo\.png"/g,
      'content="/logo.svg"'
    );

    fs.writeFileSync(indexPath, content);
    console.log('✅ Обновлен index.html для использования SVG логотипа');
  }
}

// Запуск оптимизации
optimizeLogo();
updateIndexHTML();

console.log('\n📊 Рекомендации:');
console.log('1. Используйте logo.svg для лучшего качества и меньшего размера');
console.log('2. Для favicon используйте logo.svg или создайте .ico файл');
console.log('3. Оригинальный логотип сохранен как logo-original.png');