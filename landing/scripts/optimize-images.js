#!/usr/bin/env node

/**
 * Скрипт для оптимизации изображений
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const projectRoot = path.resolve(__dirname, '..');
const publicDir = path.join(projectRoot, 'public');

// Цвета для консоли
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function formatBytes(bytes) {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
}

function getFileSize(filePath) {
  try {
    return fs.statSync(filePath).size;
  } catch (error) {
    return 0;
  }
}

function analyzeImages() {
  console.log(`${colors.bright}${colors.cyan}🖼️  Анализ изображений${colors.reset}`);
  console.log(`Директория: ${publicDir}`);
  
  if (!fs.existsSync(publicDir)) {
    console.log(`${colors.red}❌ Директория public не найдена.${colors.reset}`);
    process.exit(1);
  }
  
  const imageExtensions = ['.png', '.jpg', '.jpeg', '.gif', '.svg', '.webp', '.ico'];
  const images = [];
  
  function scanDirectory(dir) {
    const items = fs.readdirSync(dir);
    
    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        scanDirectory(fullPath);
      } else if (stat.isFile()) {
        const ext = path.extname(item).toLowerCase();
        if (imageExtensions.includes(ext)) {
          const size = getFileSize(fullPath);
          const relativePath = path.relative(publicDir, fullPath);
          
          images.push({
            name: item,
            path: fullPath,
            relativePath,
            size,
            ext,
          });
        }
      }
    }
  }
  
  scanDirectory(publicDir);
  
  // Сортируем по размеру
  images.sort((a, b) => b.size - a.size);
  
  console.log(`\n${colors.bright}${colors.blue}📊 Найдено изображений: ${images.length}${colors.reset}`);
  console.log('─'.repeat(80));
  console.log(`${'Файл'.padEnd(40)} ${'Размер'.padEnd(12)} ${'Тип'.padEnd(8)} ${'Статус'.padEnd(15)}`);
  console.log('─'.repeat(80));
  
  let totalSize = 0;
  let largeImages = 0;
  
  for (const image of images) {
    totalSize += image.size;
    
    let sizeColor = colors.green;
    let status = 'OK';
    
    // Определяем статус по размеру и типу
    if (image.ext === '.png' && image.size > 500 * 1024) {
      sizeColor = colors.red;
      status = 'Очень большой';
      largeImages++;
    } else if (image.ext === '.jpg' && image.size > 300 * 1024) {
      sizeColor = colors.red;
      status = 'Очень большой';
      largeImages++;
    } else if (image.size > 200 * 1024) {
      sizeColor = colors.yellow;
      status = 'Большой';
      largeImages++;
    } else if (image.size > 100 * 1024) {
      sizeColor = colors.yellow;
      status = 'Средний';
    }
    
    const fileName = image.relativePath.length > 38 ? 
      image.relativePath.substring(0, 35) + '...' : 
      image.relativePath;
    
    console.log(
      `${fileName.padEnd(40)} ` +
      `${sizeColor}${formatBytes(image.size).padEnd(12)}${colors.reset} ` +
      `${image.ext.padEnd(8)} ` +
      `${status.padEnd(15)}`
    );
  }
  
  console.log('─'.repeat(80));
  console.log(`${'ИТОГО'.padEnd(40)} ${colors.bright}${formatBytes(totalSize).padEnd(12)}${colors.reset}`);
  
  // Рекомендации
  console.log(`\n${colors.bright}${colors.yellow}💡 Рекомендации по оптимизации${colors.reset}`);
  console.log('─'.repeat(50));
  
  if (largeImages === 0) {
    console.log(`${colors.green}✅ Все изображения оптимизированы!${colors.reset}`);
  } else {
    console.log(`${colors.yellow}⚠️  Найдено ${largeImages} изображений, требующих оптимизации${colors.reset}`);
    
    const pngImages = images.filter(img => img.ext === '.png' && img.size > 500 * 1024);
    if (pngImages.length > 0) {
      console.log(`\n${colors.red}PNG изображения (>500KB):${colors.reset}`);
      pngImages.forEach(img => {
        console.log(`   - ${img.relativePath}: ${formatBytes(img.size)}`);
        console.log(`     Рекомендация: конвертировать в WebP или оптимизировать`);
      });
    }
    
    const jpgImages = images.filter(img => 
      (img.ext === '.jpg' || img.ext === '.jpeg') && img.size > 300 * 1024
    );
    if (jpgImages.length > 0) {
      console.log(`\n${colors.red}JPEG изображения (>300KB):${colors.reset}`);
      jpgImages.forEach(img => {
        console.log(`   - ${img.relativePath}: ${formatBytes(img.size)}`);
        console.log(`     Рекомендация: уменьшить качество или размер`);
      });
    }
  }
  
  // Общие рекомендации
  console.log(`\n${colors.bright}${colors.cyan}🔧 Инструменты для оптимизации:${colors.reset}`);
  console.log('─'.repeat(50));
  console.log('• Для PNG: tinypng.com, pngquant, optipng');
  console.log('• Для JPEG: jpegoptim, mozjpeg');
  console.log('• Для WebP: cwebp, squoosh.app');
  console.log('• Онлайн: squoosh.app, tinypng.com, imageoptim.com');
  
  console.log(`\n${colors.bright}${colors.magenta}📝 Команды для оптимизации:${colors.reset}`);
  console.log('─'.repeat(50));
  
  const largePngs = images.filter(img => img.ext === '.png' && img.size > 500 * 1024);
  if (largePngs.length > 0) {
    console.log('# Оптимизация PNG:');
    largePngs.forEach(img => {
      console.log(`pngquant --quality=65-80 --output ${img.relativePath.replace('.png', '-opt.png')} ${img.relativePath}`);
    });
  }
  
  const largeJpgs = images.filter(img => 
    (img.ext === '.jpg' || img.ext === '.jpeg') && img.size > 300 * 1024
  );
  if (largeJpgs.length > 0) {
    console.log('\n# Оптимизация JPEG:');
    largeJpgs.forEach(img => {
      console.log(`jpegoptim --max=80 --strip-all ${img.relativePath}`);
    });
  }
  
  // Создание WebP версий
  const webpCandidates = images.filter(img =>
    (img.ext === '.png' || img.ext === '.jpg' || img.ext === '.jpeg') && img.size > 100 * 1024
  );
  if (webpCandidates.length > 0) {
    console.log('\n# Создание WebP версий:');
    webpCandidates.forEach(img => {
      const webpPath = img.relativePath.replace(/\.(png|jpe?g)$/i, '.webp');
      console.log(`cwebp -q 80 ${img.relativePath} -o ${webpPath}`);
    });
  }
  
  return {
    totalImages: images.length,
    totalSize,
    largeImages,
    needsOptimization: largeImages > 0,
  };
}

// Создание оптимизированного логотипа
function createOptimizedLogo() {
  const logoPath = path.join(publicDir, 'logo.png');
  
  if (!fs.existsSync(logoPath)) {
    console.log(`${colors.yellow}⚠️  logo.png не найден${colors.reset}`);
    return;
  }
  
  const logoSize = getFileSize(logoPath);
  
  if (logoSize > 100 * 1024) {
    console.log(`\n${colors.bright}${colors.red}🚨 Критическая проблема: logo.png слишком большой (${formatBytes(logoSize)})${colors.reset}`);
    console.log('─'.repeat(50));
    console.log('Рекомендуемые действия:');
    console.log('1. Уменьшить размер до 64x64 или 128x128 пикселей');
    console.log('2. Конвертировать в WebP или оптимизированный PNG');
    console.log('3. Использовать SVG для векторных логотипов');
    console.log('');
    console.log('Команды для оптимизации:');
    console.log(`# Изменить размер и оптимизировать:`);
    console.log(`convert logo.png -resize 128x128 -quality 80 logo-128.png`);
    console.log(`pngquant --quality=65-80 --output logo-opt.png logo-128.png`);
    console.log(`cwebp -q 80 logo-opt.png -o logo.webp`);
  }
}

// Запуск анализа
const result = analyzeImages();
createOptimizedLogo();

if (result.needsOptimization) {
  console.log(`\n${colors.bright}${colors.red}❌ Требуется оптимизация изображений${colors.reset}`);
  process.exit(1);
} else {
  console.log(`\n${colors.bright}${colors.green}✅ Все изображения оптимизированы${colors.reset}`);
}
