/**
 * Автоматизированные тесты совместимости браузеров
 * Проверяет все аспекты лендинга: загрузку, responsive дизайн, API, accessibility
 */

import { test, expect } from '@playwright/test';

// Конфигурация тестовых разрешений
const viewports = [
  { name: 'Mobile Small', width: 320, height: 568 },
  { name: 'Mobile', width: 375, height: 667 },
  { name: 'Tablet Portrait', width: 768, height: 1024 },
  { name: 'Tablet Landscape', width: 1024, height: 768 },
  { name: 'Desktop', width: 1280, height: 720 },
  { name: 'Desktop Large', width: 1920, height: 1080 },
  { name: 'Ultrawide', width: 2560, height: 1440 },
];

// Базовый URL для тестирования
const BASE_URL = 'http://localhost:3000';

test.describe('UnveilVPN Landing - Compatibility Tests', () => {
  
  test.beforeEach(async ({ page }) => {
    // Настройка страницы перед каждым тестом
    await page.goto(BASE_URL);
  });

  test('Основная загрузка страницы', async ({ page }) => {
    // Проверяем, что страница загружается
    await expect(page).toHaveTitle(/UnveilVPN/);
    
    // Проверяем отсутствие ошибок в консоли
    const errors = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(msg.text());
      }
    });
    
    await page.waitForLoadState('networkidle');
    expect(errors).toHaveLength(0);
  });

  test('Browser capabilities detection', async ({ page }) => {
    // Проверяем, что скрипт определения возможностей работает
    const capabilities = await page.evaluate(() => {
      return window.browserSupport;
    });
    
    expect(capabilities).toBeDefined();
    expect(capabilities).toHaveProperty('modules');
    expect(capabilities).toHaveProperty('es6');
    expect(capabilities).toHaveProperty('fetch');
    expect(capabilities).toHaveProperty('promise');
  });

  test('Все ресурсы загружаются успешно', async ({ page }) => {
    const failedRequests = [];
    
    page.on('response', response => {
      if (response.status() >= 400) {
        failedRequests.push({
          url: response.url(),
          status: response.status()
        });
      }
    });
    
    await page.waitForLoadState('networkidle');
    expect(failedRequests).toHaveLength(0);
  });

  // Тестирование responsive дизайна
  viewports.forEach(viewport => {
    test(`Responsive: ${viewport.name} (${viewport.width}x${viewport.height})`, async ({ page }) => {
      await page.setViewportSize({ width: viewport.width, height: viewport.height });
      
      // Ждем загрузки
      await page.waitForLoadState('networkidle');
      
      // Проверяем, что контент отображается корректно
      await expect(page.locator('body')).toBeVisible();
      
      // Делаем скриншот для визуальной проверки
      await page.screenshot({
        path: `tests/screenshots/responsive-${viewport.name.toLowerCase().replace(' ', '-')}.png`,
        fullPage: true
      });
      
      // Проверяем отсутствие горизонтальной прокрутки
      const hasHorizontalScroll = await page.evaluate(() => {
        return document.body.scrollWidth > window.innerWidth;
      });
      expect(hasHorizontalScroll).toBeFalsy();
    });
  });

  test('API интеграция и fallback данные', async ({ page }) => {
    // Проверяем, что fallback данные используются при недоступности API
    await page.waitForLoadState('networkidle');
    
    // Проверяем, что нет запросов к несуществующему API
    const apiRequests = [];
    page.on('request', request => {
      if (request.url().includes('/api/v1/public/')) {
        apiRequests.push(request.url());
      }
    });
    
    await page.waitForTimeout(3000);
    expect(apiRequests).toHaveLength(0);
  });

  test('Клавиатурная навигация (Accessibility)', async ({ page }) => {
    // Проверяем Tab навигацию
    await page.keyboard.press('Tab');
    
    // Проверяем, что фокус виден
    const focusedElement = await page.evaluate(() => {
      return document.activeElement.tagName;
    });
    
    expect(focusedElement).toBeTruthy();
    
    // Проверяем несколько Tab нажатий
    for (let i = 0; i < 5; i++) {
      await page.keyboard.press('Tab');
      await page.waitForTimeout(100);
    }
  });

  test('Семантическая структура HTML', async ({ page }) => {
    // Проверяем наличие основных семантических элементов
    await expect(page.locator('header')).toBeVisible();
    await expect(page.locator('main')).toBeVisible();
    await expect(page.locator('footer')).toBeVisible();
    
    // Проверяем структуру заголовков
    const headings = await page.locator('h1, h2, h3, h4, h5, h6').count();
    expect(headings).toBeGreaterThan(0);
    
    // Проверяем наличие alt текстов для изображений
    const imagesWithoutAlt = await page.locator('img:not([alt])').count();
    expect(imagesWithoutAlt).toBe(0);
  });

  test('Производительность загрузки', async ({ page }) => {
    const startTime = Date.now();
    
    await page.goto(BASE_URL);
    await page.waitForLoadState('networkidle');
    
    const loadTime = Date.now() - startTime;
    
    // Проверяем, что страница загружается быстро (менее 3 секунд)
    expect(loadTime).toBeLessThan(3000);
  });

  test('Размер bundle оптимизирован', async ({ page }) => {
    const responses = [];
    
    page.on('response', response => {
      if (response.url().includes('.js') || response.url().includes('.css')) {
        responses.push({
          url: response.url(),
          size: response.headers()['content-length']
        });
      }
    });
    
    await page.waitForLoadState('networkidle');
    
    // Проверяем, что основные JS файлы не слишком большие
    const jsFiles = responses.filter(r => r.url.includes('.js'));
    jsFiles.forEach(file => {
      if (file.size) {
        const sizeKB = parseInt(file.size) / 1024;
        expect(sizeKB).toBeLessThan(500); // Максимум 500KB на файл
      }
    });
  });

  test('Graceful degradation', async ({ page }) => {
    // Симулируем отключение JavaScript
    await page.addInitScript(() => {
      // Отключаем некоторые современные функции
      delete window.fetch;
      delete window.Promise;
    });
    
    await page.goto(BASE_URL);
    
    // Проверяем, что страница все еще отображается
    await expect(page.locator('body')).toBeVisible();
  });

  test('Безопасность контента', async ({ page }) => {
    // Проверяем отсутствие небезопасного контента
    const unsafeElements = await page.locator('script[src*="http://"]').count();
    expect(unsafeElements).toBe(0);
    
    // Проверяем наличие мета тегов безопасности
    const viewport = await page.locator('meta[name="viewport"]').count();
    expect(viewport).toBeGreaterThan(0);
  });

});

// Дополнительные тесты для специфических браузеров
test.describe('Browser-specific tests', () => {
  
  test('Chrome compatibility', async ({ page, browserName }) => {
    test.skip(browserName !== 'chromium', 'Chrome-specific test');
    
    await page.goto(BASE_URL);
    
    // Проверяем Chrome-специфичные функции
    const chromeFeatures = await page.evaluate(() => {
      return {
        webp: document.createElement('canvas').toDataURL('image/webp').indexOf('data:image/webp') === 0,
        intersectionObserver: 'IntersectionObserver' in window,
        customElements: 'customElements' in window
      };
    });
    
    expect(chromeFeatures.intersectionObserver).toBeTruthy();
  });

  test('Firefox compatibility', async ({ page, browserName }) => {
    test.skip(browserName !== 'firefox', 'Firefox-specific test');
    
    await page.goto(BASE_URL);
    
    // Проверяем Firefox-специфичные функции
    const firefoxFeatures = await page.evaluate(() => {
      return {
        cssGrid: CSS.supports('display', 'grid'),
        flexbox: CSS.supports('display', 'flex')
      };
    });
    
    expect(firefoxFeatures.flexbox).toBeTruthy();
  });

  test('Safari compatibility', async ({ page, browserName }) => {
    test.skip(browserName !== 'webkit', 'Safari-specific test');
    
    await page.goto(BASE_URL);
    
    // Проверяем Safari-специфичные функции
    const safariFeatures = await page.evaluate(() => {
      return {
        webkitAppearance: 'webkitAppearance' in document.body.style,
        touchAction: 'touchAction' in document.body.style
      };
    });
    
    expect(safariFeatures.touchAction).toBeTruthy();
  });

});
