/**
 * Глобальная настройка перед запуском тестов
 */

import fs from 'fs';
import path from 'path';

async function globalSetup() {
  console.log('🚀 Запуск глобальной настройки тестов...');
  
  // Создаем директории для результатов тестов
  const dirs = [
    'test-results',
    'test-results/screenshots',
    'test-results/html-report',
    'test-results/artifacts',
  ];
  
  dirs.forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
      console.log(`📁 Создана директория: ${dir}`);
    }
  });
  
  // Очищаем старые результаты
  const screenshotsDir = 'test-results/screenshots';
  if (fs.existsSync(screenshotsDir)) {
    const files = fs.readdirSync(screenshotsDir);
    files.forEach(file => {
      fs.unlinkSync(path.join(screenshotsDir, file));
    });
    console.log('🧹 Очищены старые скриншоты');
  }
  
  // Проверяем, что dev сервер доступен
  console.log('🔍 Проверка доступности dev сервера...');
  
  try {
    const response = await fetch('http://localhost:3000');
    if (response.ok) {
      console.log('✅ Dev сервер доступен');
    } else {
      console.log('⚠️ Dev сервер отвечает с ошибкой:', response.status);
    }
  } catch (error) {
    console.log('❌ Dev сервер недоступен:', error.message);
    console.log('💡 Убедитесь, что запущен npm run dev');
  }
  
  // Записываем информацию о тестовом окружении
  const envInfo = {
    timestamp: new Date().toISOString(),
    nodeVersion: process.version,
    platform: process.platform,
    arch: process.arch,
    cwd: process.cwd(),
  };
  
  fs.writeFileSync(
    'test-results/environment.json',
    JSON.stringify(envInfo, null, 2)
  );
  
  console.log('✅ Глобальная настройка завершена');
}

export default globalSetup;
