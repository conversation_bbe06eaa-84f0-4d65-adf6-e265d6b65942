/**
 * Глобальная очистка после завершения тестов
 */

import fs from 'fs';
import path from 'path';

async function globalTeardown() {
  console.log('🧹 Запуск глобальной очистки...');
  
  // Генерируем отчет о тестировании
  const reportData = {
    timestamp: new Date().toISOString(),
    summary: 'Тестирование совместимости браузеров завершено',
    artifacts: [],
  };
  
  // Собираем информацию об артефактах
  const artifactsDir = 'test-results/artifacts';
  if (fs.existsSync(artifactsDir)) {
    const files = fs.readdirSync(artifactsDir);
    reportData.artifacts = files.map(file => ({
      name: file,
      size: fs.statSync(path.join(artifactsDir, file)).size,
      type: path.extname(file),
    }));
  }
  
  // Собираем информацию о скриншотах
  const screenshotsDir = 'test-results/screenshots';
  if (fs.existsSync(screenshotsDir)) {
    const screenshots = fs.readdirSync(screenshotsDir);
    reportData.screenshots = screenshots.length;
  }
  
  // Записываем финальный отчет
  fs.writeFileSync(
    'test-results/final-report.json',
    JSON.stringify(reportData, null, 2)
  );
  
  // Выводим статистику
  console.log('📊 Статистика тестирования:');
  console.log(`   Артефактов: ${reportData.artifacts.length}`);
  console.log(`   Скриншотов: ${reportData.screenshots || 0}`);
  
  // Создаем README для результатов
  const readmeContent = `# Результаты тестирования совместимости

## Обзор
Автоматизированное тестирование лендинга UnveilVPN на совместимость с различными браузерами.

## Дата тестирования
${new Date().toLocaleString('ru-RU')}

## Протестированные браузеры
- Chrome (Desktop & Mobile)
- Firefox (Desktop)
- Safari (Desktop & Mobile)
- Legacy Chrome (симуляция старых версий)

## Протестированные разрешения
- 320px (Mobile Small)
- 375px (Mobile)
- 768px (Tablet Portrait)
- 1024px (Tablet Landscape)
- 1280px (Desktop)
- 1920px (Desktop Large)
- 2560px (Ultrawide)

## Проверенные аспекты
- ✅ Загрузка страницы
- ✅ Browser capabilities detection
- ✅ Responsive дизайн
- ✅ API интеграция и fallback
- ✅ Accessibility (a11y)
- ✅ Клавиатурная навигация
- ✅ Производительность
- ✅ Безопасность

## Файлы результатов
- \`html-report/\` - HTML отчет Playwright
- \`screenshots/\` - Скриншоты для всех разрешений
- \`artifacts/\` - Видео и трейсы при ошибках
- \`results.json\` - JSON результаты
- \`junit.xml\` - JUnit отчет для CI/CD

## Как просмотреть результаты
\`\`\`bash
# Открыть HTML отчет
npx playwright show-report test-results/html-report

# Просмотреть скриншоты
ls test-results/screenshots/
\`\`\`
`;
  
  fs.writeFileSync('test-results/README.md', readmeContent);
  
  console.log('✅ Глобальная очистка завершена');
  console.log('📁 Результаты сохранены в test-results/');
  console.log('🌐 Откройте HTML отчет: npx playwright show-report test-results/html-report');
}

export default globalTeardown;
