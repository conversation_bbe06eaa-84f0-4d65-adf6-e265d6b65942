{"name": "unveilvpn-landing", "private": true, "version": "1.0.0", "type": "module", "description": "UnveilVPN Landing Page - React + TypeScript + Tailwind CSS", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "preview": "vite preview", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "type-check": "tsc --noEmit"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@juggle/resize-observer": "^3.4.0", "axios": "^1.10.0", "es6-promise": "^4.2.8", "framer-motion": "^12.19.1", "intersection-observer": "^0.12.2", "lucide-react": "^0.523.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.58.1", "react-router-dom": "^7.6.2", "tslib": "^2.8.1", "whatwg-fetch": "^3.6.20", "zod": "^3.25.67"}, "devDependencies": {"@eslint/js": "^9.29.0", "@tailwindcss/typography": "^0.5.16", "@types/node": "^24.0.4", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@typescript-eslint/eslint-plugin": "^8.35.0", "@typescript-eslint/parser": "^8.35.0", "@vitejs/plugin-legacy": "^7.0.0", "@vitejs/plugin-react": "^4.5.2", "autoprefixer": "^10.4.21", "eslint": "^9.29.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.5.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "postcss": "^8.5.6", "prettier": "^3.6.1", "tailwindcss": "^3.4.17", "terser": "^5.43.1", "typescript": "~5.8.3", "typescript-eslint": "^8.34.1", "vite": "^7.0.0"}}