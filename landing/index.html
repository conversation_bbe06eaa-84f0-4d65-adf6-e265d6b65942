<!doctype html>
<html lang="ru">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <!-- Primary Meta Tags -->
    <title>UnveilVPN - Безопасный VPN сервис | 7 дней бесплатно</title>
    <meta name="title" content="UnveilVPN - Безопасный VPN сервис | 7 дней бесплатно" />
    <meta name="description" content="Надежный VPN сервис с высокой скоростью и защитой данных. Попробуйте 7 дней бесплатно! 50+ стран, военное шифрование, без логов." />
    <meta name="keywords" content="VPN, безопасность, приватность, интернет, шифрование, анонимность, защита данных" />
    <meta name="author" content="UnveilVPN" />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://unveilvpn.com/" />
    <meta property="og:title" content="UnveilVPN - Безопасный VPN сервис | 7 дней бесплатно" />
    <meta property="og:description" content="Надежный VPN сервис с высокой скоростью и защитой данных. Попробуйте 7 дней бесплатно!" />
    <meta property="og:image" content="https://unveilvpn.com/og-image.jpg" />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://unveilvpn.com/" />
    <meta property="twitter:title" content="UnveilVPN - Безопасный VPN сервис | 7 дней бесплатно" />
    <meta property="twitter:description" content="Надежный VPN сервис с высокой скоростью и защитой данных. Попробуйте 7 дней бесплатно!" />
    <meta property="twitter:image" content="https://unveilvpn.com/og-image.jpg" />

    <!-- Additional Meta Tags -->
    <meta name="robots" content="index, follow" />
    <meta name="language" content="Russian" />
    <meta name="theme-color" content="#007acc" />

    <!-- Preconnect to external domains -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />

    <!-- Browser compatibility detection -->
    <script>
      // Определение возможностей браузера для выбора подходящих скриптов
      window.browserSupport = {
        modules: 'noModule' in HTMLScriptElement.prototype,
        es6: (function() {
          try {
            new Function('(a = 0) => a');
            return true;
          } catch (e) {
            return false;
          }
        })(),
        fetch: 'fetch' in window,
        promise: 'Promise' in window,
        flexbox: (function() {
          var d = document.createElement('div');
          return 'flex' in d.style || 'webkitFlex' in d.style;
        })(),
        grid: (function() {
          var d = document.createElement('div');
          return 'grid' in d.style;
        })()
      };

      // Добавляем классы для CSS условий
      var classes = [];
      if (window.browserSupport.modules) classes.push('modern');
      else classes.push('legacy');
      if (window.browserSupport.es6) classes.push('es6');
      if (window.browserSupport.fetch) classes.push('fetch');
      if (window.browserSupport.flexbox) classes.push('flexbox');
      if (window.browserSupport.grid) classes.push('grid');

      document.documentElement.className += ' ' + classes.join(' ');
    </script>

    <!-- Critical CSS для мгновенной загрузки -->
    <style>
      /* Критические стили для первого рендера */
      body {
        margin: 0;
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        background-color: #ffffff;
        color: #1f2937;
        line-height: 1.6;
      }

      #root {
        min-height: 100vh;
      }

      /* Browser-specific styles */
      .legacy .modern-only {
        display: none !important;
      }
      .modern .legacy-only {
        display: none !important;
      }

      /* Fallback styles for legacy browsers */
      .legacy {
        /* Упрощенные стили для старых браузеров */
      }

      /* Progressive enhancement styles */
      .no-flexbox .flex-container {
        /* Fallback для браузеров без flexbox */
        display: table;
        width: 100%;
      }

      .no-flexbox .flex-item {
        display: table-cell;
        vertical-align: top;
      }

      .no-grid .grid-container {
        /* Fallback для браузеров без CSS Grid */
        display: block;
      }

      .no-grid .grid-item {
        display: block;
        width: 100%;
        margin-bottom: 1rem;
      }

      /* Loading состояние */
      .loading-fallback {
        display: flex;
        align-items: center;
        justify-content: center;
        min-height: 100vh;
        flex-direction: column;
        background: linear-gradient(135deg, #007acc 0%, #0ea5e9 50%, #005a9e 100%);
        color: white;
      }

      .loading-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid rgba(255, 255, 255, 0.3);
        border-top: 4px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 20px;
      }

      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }

      .loading-text {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 8px;
      }

      .loading-subtext {
        font-size: 14px;
        opacity: 0.8;
      }

      /* Скрыть fallback когда JS загружен */
      .js-loaded .loading-fallback {
        display: none;
      }
    </style>

    <!-- Browser capability detection -->
    <script>
      // Определение возможностей браузера
      window.browserCapabilities = {
        modules: 'noModule' in HTMLScriptElement.prototype,
        es6: (function() {
          try {
            new Function('(a = 0) => a');
            return true;
          } catch (e) {
            return false;
          }
        })(),
        fetch: 'fetch' in window,
        promise: 'Promise' in window,
        cssGrid: CSS && CSS.supports && CSS.supports('display', 'grid'),
        intersectionObserver: 'IntersectionObserver' in window
      };

      // Добавляем классы для CSS
      document.documentElement.className += window.browserCapabilities.modules ? ' supports-modules' : ' no-modules';
      document.documentElement.className += window.browserCapabilities.es6 ? ' supports-es6' : ' no-es6';

      // Логирование для отладки
      console.log('Browser capabilities:', window.browserCapabilities);
    </script>
  </head>
  <body>
    <div id="root">
      <!-- Fallback контент для случаев когда JS не загружается -->
      <div class="loading-fallback">
        <div class="loading-spinner"></div>
        <div class="loading-text">Загрузка UnveilVPN</div>
        <div class="loading-subtext">Подготавливаем безопасное соединение...</div>
        <noscript>
          <div style="margin-top: 20px; text-align: center; max-width: 400px;">
            <h2>JavaScript отключен</h2>
            <p>Для работы UnveilVPN необходимо включить JavaScript в вашем браузере.</p>
            <p>Пожалуйста, включите JavaScript и обновите страницу.</p>
          </div>
        </noscript>
      </div>
    </div>

    <!-- Polyfill для require() -->
    <script type="module" src="/src/polyfills/require-polyfill.ts"></script>

    <!-- Современная версия для браузеров с поддержкой ES6 модулей -->
    <script type="module" src="/src/main.tsx"></script>

    <!-- Legacy версия для старых браузеров (будет добавлена Vite Legacy Plugin) -->
    <!-- Vite автоматически добавит nomodule скрипты при сборке -->

    <!-- Скрипт для скрытия loading состояния -->
    <script>
      // Помечаем что JS загружен
      document.documentElement.classList.add('js-loaded');

      // Удаляем loading fallback через небольшую задержку
      setTimeout(function() {
        var fallback = document.querySelector('.loading-fallback');
        if (fallback && window.React) {
          fallback.style.display = 'none';
        }
      }, 100);
    </script>
  </body>
</html>
