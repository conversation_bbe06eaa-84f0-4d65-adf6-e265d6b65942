<!doctype html>
<html lang="ru">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <!-- Primary Meta Tags -->
    <title>UnveilVPN - Безопасный VPN сервис | 7 дней бесплатно</title>
    <meta name="title" content="UnveilVPN - Безопасный VPN сервис | 7 дней бесплатно" />
    <meta name="description" content="Надежный VPN сервис с высокой скоростью и защитой данных. Попробуйте 7 дней бесплатно! 50+ стран, военное шифрование, без логов." />
    <meta name="keywords" content="VPN, безопасность, приватность, интернет, шифрование, анонимность, защита данных" />
    <meta name="author" content="UnveilVPN" />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://unveilvpn.com/" />
    <meta property="og:title" content="UnveilVPN - Безопасный VPN сервис | 7 дней бесплатно" />
    <meta property="og:description" content="Надежный VPN сервис с высокой скоростью и защитой данных. Попробуйте 7 дней бесплатно!" />
    <meta property="og:image" content="https://unveilvpn.com/og-image.jpg" />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://unveilvpn.com/" />
    <meta property="twitter:title" content="UnveilVPN - Безопасный VPN сервис | 7 дней бесплатно" />
    <meta property="twitter:description" content="Надежный VPN сервис с высокой скоростью и защитой данных. Попробуйте 7 дней бесплатно!" />
    <meta property="twitter:image" content="https://unveilvpn.com/og-image.jpg" />

    <!-- Additional Meta Tags -->
    <meta name="robots" content="index, follow" />
    <meta name="language" content="Russian" />
    <meta name="theme-color" content="#3B82F6" />

    <!-- Preconnect to external domains -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
