# =============================================================================
# UnveilVPN Shop - Production Secrets
# Сгенерировано: Чт 26 июн 2025 21:07:50 MSK
# ВАЖНО: Сохраните этот файл в безопасном месте и удалите после настройки!
# =============================================================================

# База данных
DB_PASS=Wcxfh9nLpEA4RTzOWflwaGeC

# Redis
REDIS_PASSWORD=LYYyv8w29CQpfq5V8Pl6sCzM

# Безопасность
JWT_SECRET=4U7unlSMxUi94mc8HOfVoOJK5ZLVMMoRpnTQbKCWhrnXxdga
WEBHOOK_SECRET=JqPc7XunmXQVizFYWmYjikSoKcGB98AC
ENCRYPTION_KEY=47ffa2ab0e7c11ded36f720cfbec213c03f47fb061428c8f34ae1e45502c277b
SESSION_SECRET=Ox1T68IIYUmrJZCi4XwosGCI0WA4MvoQ
API_SECRET_KEY=G4BGPjgpr7PjWEzvSyFevgT883R67vkpqyJDVw2OgsGIM49m

# Webhook боты
BOT_WEBHOOK_SECRET=3IAT6Zo8weV1Ay0mIjjvSBd1

# =============================================================================
# ИНСТРУКЦИИ ПО ИСПОЛЬЗОВАНИЮ
# =============================================================================

1. Скопируйте эти значения в соответствующие поля .env файла
2. Получите реальные токены от внешних сервисов:
   - Telegram боты: @BotFather
   - YooKassa: личный кабинет
   - Cryptomus: панель мерчанта
   - Remnawave: API панель
   - Sentry: проект мониторинга

3. Обновите домены на реальные
4. Настройте SMTP для email
5. Запустите проверку: python3 scripts/check_env_vars.py
6. УДАЛИТЕ ЭТОТ ФАЙЛ после настройки!

# =============================================================================
