#!/bin/bash

# =============================================================================
# UnveilVPN Shop - Automated Health Check Script
# =============================================================================

set -e

echo "🔍 UnveilVPN Shop Health Check Starting..."
echo "=============================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    if [ "$2" = "OK" ]; then
        echo -e "${GREEN}✅ $1${NC}"
    elif [ "$2" = "WARNING" ]; then
        echo -e "${YELLOW}⚠️  $1${NC}"
    else
        echo -e "${RED}❌ $1${NC}"
    fi
}

# Function to check service health
check_service() {
    local service=$1
    local container_name=$2
    
    if docker compose ps "$service" | grep -q "healthy\|Up"; then
        print_status "$service service is running" "OK"
        return 0
    else
        print_status "$service service is not running" "ERROR"
        return 1
    fi
}

# Function to fix common issues
fix_issues() {
    echo -e "\n🔧 Attempting to fix detected issues..."
    
    # Check and start API service
    if ! check_service "api" "unveilvpn-api-prod" >/dev/null 2>&1; then
        echo "Starting API service..."
        docker compose up api -d
        sleep 10
    fi
    
    # Check and restart Nginx if needed
    if ! check_service "nginx" "unveilvpn-nginx-prod" >/dev/null 2>&1; then
        echo "Restarting Nginx service..."
        docker compose restart nginx
        sleep 5
    fi
}

# Main health check
main() {
    echo "1. Checking Docker Compose services..."
    echo "--------------------------------------"
    
    # Check core services
    api_ok=true
    nginx_ok=true
    landing_ok=true
    postgres_ok=true
    redis_ok=true
    
    check_service "postgres" "unveilvpn-postgres-prod" || postgres_ok=false
    check_service "redis" "unveilvpn-redis-prod" || redis_ok=false
    check_service "api" "unveilvpn-api-prod" || api_ok=false
    check_service "landing" "unveilvpn-landing-prod" || landing_ok=false
    check_service "nginx" "unveilvpn-nginx-prod" || nginx_ok=false
    
    echo -e "\n2. Testing external connectivity..."
    echo "-----------------------------------"
    
    # Test landing page accessibility
    if curl -s -o /dev/null -w "%{http_code}" http://localhost:80 | grep -q "200"; then
        print_status "Landing page is accessible (HTTP 200)" "OK"
        landing_accessible=true
    else
        print_status "Landing page is not accessible" "ERROR"
        landing_accessible=false
    fi
    
    # Test API health endpoint
    if curl -s -o /dev/null -w "%{http_code}" http://localhost:8000/health 2>/dev/null | grep -q "200"; then
        print_status "API health endpoint is accessible" "OK"
        api_accessible=true
    else
        print_status "API health endpoint is not accessible" "WARNING"
        api_accessible=false
    fi
    
    echo -e "\n3. Resource usage check..."
    echo "--------------------------"
    
    # Check Docker system resources
    docker system df --format "table {{.Type}}\t{{.TotalCount}}\t{{.Size}}\t{{.Reclaimable}}"
    
    echo -e "\n4. Summary..."
    echo "-------------"
    
    issues_found=false
    
    if [ "$postgres_ok" = false ] || [ "$redis_ok" = false ] || [ "$api_ok" = false ] || [ "$nginx_ok" = false ] || [ "$landing_ok" = false ]; then
        issues_found=true
        print_status "Some services are not running properly" "ERROR"
    fi
    
    if [ "$landing_accessible" = false ]; then
        issues_found=true
        print_status "Landing page is not accessible" "ERROR"
    fi
    
    if [ "$issues_found" = true ]; then
        echo -e "\n🔧 Issues detected. Attempting automatic fixes..."
        fix_issues
        
        echo -e "\n🔄 Re-checking after fixes..."
        sleep 5
        
        # Re-test landing page
        if curl -s -o /dev/null -w "%{http_code}" http://localhost:80 | grep -q "200"; then
            print_status "Landing page is now accessible after fixes" "OK"
        else
            print_status "Landing page is still not accessible" "ERROR"
            echo "Manual intervention may be required."
        fi
    else
        print_status "All systems are operational" "OK"
    fi
    
    echo -e "\n=============================================="
    echo "🏁 Health Check Complete"
    echo "=============================================="
}

# Run main function
main "$@"
