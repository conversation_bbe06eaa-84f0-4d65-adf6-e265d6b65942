#!/bin/bash
# =============================================================================
# UnveilVPN Shop - Production Secrets Generator
# Генерирует все необходимые секретные ключи и пароли для production
# =============================================================================

set -euo pipefail

# Цвета для вывода
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Функции для вывода
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Функция генерации случайного пароля
generate_password() {
    local length=${1:-32}
    openssl rand -base64 $length | tr -d "=+/" | cut -c1-$length
}

# Функция генерации hex ключа
generate_hex_key() {
    local length=${1:-32}
    openssl rand -hex $length
}

# Функция генерации UUID
generate_uuid() {
    python3 -c "import uuid; print(str(uuid.uuid4()))"
}

# Проверка зависимостей
check_dependencies() {
    log_info "Проверка зависимостей..."
    
    local deps=("openssl" "python3")
    for dep in "${deps[@]}"; do
        if ! command -v "$dep" &> /dev/null; then
            log_error "Зависимость $dep не найдена. Установите её и повторите попытку."
            exit 1
        fi
    done
    
    log_success "Все зависимости найдены"
}

# Генерация всех секретов
generate_secrets() {
    log_info "Генерация секретных ключей и паролей..."
    
    # База данных
    DB_PASS=$(generate_password 24)
    
    # Redis
    REDIS_PASSWORD=$(generate_password 24)
    
    # Безопасность
    JWT_SECRET=$(generate_password 48)
    WEBHOOK_SECRET=$(generate_password 32)
    ENCRYPTION_KEY=$(generate_hex_key 32)
    SESSION_SECRET=$(generate_password 32)
    API_SECRET_KEY=$(generate_password 48)
    
    # Webhook боты
    BOT_WEBHOOK_SECRET=$(generate_password 24)
    
    # Экспорт переменных для использования в других функциях
    export DB_PASS REDIS_PASSWORD JWT_SECRET WEBHOOK_SECRET ENCRYPTION_KEY
    export SESSION_SECRET API_SECRET_KEY BOT_WEBHOOK_SECRET
    
    log_success "Секретные ключи сгенерированы"
}

# Создание .env файла
create_env_file() {
    local env_file=${1:-.env}
    log_info "Создание файла $env_file..."
    
    cat > "$env_file" << EOF
# =============================================================================
# UnveilVPN Shop - Production Environment Configuration
# Автоматически сгенерировано $(date)
# =============================================================================

# =============================================================================
# ОСНОВНЫЕ НАСТРОЙКИ ОКРУЖЕНИЯ
# =============================================================================
ENVIRONMENT=production
DEBUG=false
LOG_LEVEL=INFO
TIMEZONE=Europe/Moscow

# =============================================================================
# БАЗА ДАННЫХ (PostgreSQL)
# =============================================================================
DB_HOST=postgres
DB_PORT=5432
DB_NAME=unveilvpn_prod
DB_USER=unveilvpn_user
DB_PASS=$DB_PASS
DATABASE_URL=**************************************************/unveilvpn_prod

# Настройки пула соединений
DB_POOL_SIZE=20
DB_MAX_OVERFLOW=30
DB_POOL_TIMEOUT=30
DB_POOL_RECYCLE=3600

# =============================================================================
# REDIS
# =============================================================================
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=$REDIS_PASSWORD
REDIS_DB=0
REDIS_URL=redis://:$REDIS_PASSWORD@redis:6379/0

# Настройки Redis
REDIS_POOL_SIZE=20
REDIS_SOCKET_TIMEOUT=5
REDIS_SOCKET_CONNECT_TIMEOUT=5

# =============================================================================
# REMNAWAVE VPN ПАНЕЛЬ
# =============================================================================
REMNAWAVE_PANEL_URL=https://panel.unveilvpn.com
REMNAWAVE_API_KEY=your_remnawave_api_key_here
REMNAWAVE_SUBSCRIPTION_URL=https://sub.unveilvpn.com
REMNAWAVE_PROTOCOLS=vless vmess trojan shadowsocks

# Настройки Remnawave
REMNAWAVE_TIMEOUT=30
REMNAWAVE_MAX_RETRIES=3
REMNAWAVE_RETRY_DELAY=5

# =============================================================================
# TELEGRAM БОТЫ
# =============================================================================
CLIENT_BOT_TOKEN=your_client_bot_token_here
ADMIN_BOT_TOKEN=your_admin_bot_token_here
SUPPORT_BOT_TOKEN=your_support_bot_token_here

# Настройки ботов
BOT_WEBHOOK_URL=https://unveilvpn.cm/webhook
BOT_WEBHOOK_SECRET=$BOT_WEBHOOK_SECRET
BOT_WEBHOOK_PORT=8443

# Webhook настройки (используется в коде)
WEBHOOK_URL=https://unveilvpn.cm/webhook
WEBHOOK_PORT=8080
WEBHOOK_MODE=true

# =============================================================================
# ПЛАТЕЖНЫЕ СИСТЕМЫ
# =============================================================================

# YooKassa (банковские карты, рубли)
YOOKASSA_SHOPID=your_yookassa_shop_id
YOOKASSA_SECRET_KEY=your_yookassa_secret_key_here

# Cryptomus (криптовалюта, доллары)  
CRYPTOMUS_MERCHANT_ID=your_cryptomus_merchant_id
CRYPTOMUS_API_KEY=your_cryptomus_api_key_here

# Telegram Stars
TELEGRAM_STARS_ENABLED=true

# Устаревшие переменные (для обратной совместимости со старыми webhook'ами)
YOOKASSA_TOKEN=your_yookassa_secret_key_here
CRYPTO_TOKEN=your_cryptomus_api_key_here
MERCHANT_UUID=your_cryptomus_merchant_id

# =============================================================================
# БЕЗОПАСНОСТЬ
# =============================================================================
JWT_SECRET=$JWT_SECRET
WEBHOOK_SECRET=$WEBHOOK_SECRET
ENCRYPTION_KEY=$ENCRYPTION_KEY
SESSION_SECRET=$SESSION_SECRET

# CORS настройки
CORS_ORIGINS=https://unveilvpn.com,https://www.unveilvpn.com,https://api.unveilvpn.com
TRUSTED_HOSTS=unveilvpn.com,www.unveilvpn.com,api.unveilvpn.com

# =============================================================================
# API НАСТРОЙКИ
# =============================================================================
API_HOST=0.0.0.0
API_PORT=8000
API_WORKERS=4
API_TIMEOUT=60
API_MAX_REQUESTS=1000

# Rate limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60

# API дополнительные настройки (используется в коде)
API_DEBUG=false
API_SECRET_KEY=$API_SECRET_KEY
API_ALGORITHM=HS256
API_ACCESS_TOKEN_EXPIRE_MINUTES=30
API_RATE_LIMIT_ENABLED=true
API_RATE_LIMIT_CALLS=100
API_RATE_LIMIT_PERIOD=60
API_LOG_LEVEL=INFO

# =============================================================================
# ДОМЕНЫ И SSL
# =============================================================================
DOMAIN=unveilvpn.com
API_DOMAIN=api.unveilvpn.com
LANDING_DOMAIN=www.unveilvpn.com
SUBSCRIPTION_DOMAIN=sub.unveilvpn.com

# SSL настройки
SSL_CERT_PATH=/etc/ssl/certs/unveilvpn.com.crt
SSL_KEY_PATH=/etc/ssl/private/unveilvpn.com.key
SSL_ENABLED=true

# =============================================================================
# МОНИТОРИНГ И ЛОГИРОВАНИЕ
# =============================================================================
SENTRY_DSN=your_sentry_dsn_here
PROMETHEUS_ENABLED=true
PROMETHEUS_PORT=9090

# Логирование
LOG_FORMAT=json
LOG_FILE=/app/logs/unveilvpn.log
LOG_MAX_SIZE=100MB
LOG_BACKUP_COUNT=10

# =============================================================================
# EMAIL НАСТРОЙКИ
# =============================================================================
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_smtp_password_here
SMTP_TLS=true

# Email адреса
ADMIN_EMAIL=<EMAIL>
SUPPORT_EMAIL=<EMAIL>
NOREPLY_EMAIL=<EMAIL>

# =============================================================================
# БИЗНЕС НАСТРОЙКИ
# =============================================================================
SHOP_NAME=UnveilVPN
SHOP_DESCRIPTION=Надежный VPN сервис для обхода блокировок
SUPPORT_LINK=https://t.me/unveilvpn_support_bot
RULES_LINK=https://unveilvpn.com/rules
ABOUT=Профессиональный VPN сервис с высокой скоростью и надежностью

# Тестовый период
TEST_PERIOD=true
TEST_PERIOD_DURATION_HOURS=168
PERIOD_LIMIT=1

# Уведомления
RENEW_NOTIFICATION_TIME=24

# Реферальная система (используется в коде)
REFERRAL_LEVEL_1_PERCENT=10.0
REFERRAL_LEVEL_2_PERCENT=5.0
REFERRAL_LEVEL_3_PERCENT=2.0
REFERRAL_MIN_PAYOUT=100

# Локализация (используется в коде)
DEFAULT_LOCALE=ru

# =============================================================================
# ДОПОЛНИТЕЛЬНЫЕ НАСТРОЙКИ
# =============================================================================

# Локализация
DEFAULT_LANGUAGE=ru
SUPPORTED_LANGUAGES=ru,en

# =============================================================================
# ПРОВЕРКА КОНФИГУРАЦИИ
# =============================================================================
# Используйте скрипт для проверки: python3 scripts/check_env_vars.py
# Все переменные с "your_*" должны быть заменены на реальные значения

# =============================================================================
# ПРИМЕЧАНИЯ
# =============================================================================
# 1. Замените ВСЕ значения "your_*" на реальные production данные
# 2. Получите токены ботов от @BotFather в Telegram
# 3. Настройте платежные системы (YooKassa, Cryptomus)
# 4. Получите API ключ от Remnawave панели
# 5. Настройте SMTP для отправки email
# 6. Настройте Sentry для мониторинга ошибок
# 7. Протестируйте все интеграции в staging окружении
# 8. Запустите проверку переменных: python3 scripts/check_env_vars.py
EOF

    log_success "Файл $env_file создан"
}

# Создание файла с секретами для администратора
create_secrets_file() {
    local secrets_file="production_secrets.txt"
    log_info "Создание файла с секретами..."
    
    cat > "$secrets_file" << EOF
# =============================================================================
# UnveilVPN Shop - Production Secrets
# Сгенерировано: $(date)
# ВАЖНО: Сохраните этот файл в безопасном месте и удалите после настройки!
# =============================================================================

# База данных
DB_PASS=$DB_PASS

# Redis
REDIS_PASSWORD=$REDIS_PASSWORD

# Безопасность
JWT_SECRET=$JWT_SECRET
WEBHOOK_SECRET=$WEBHOOK_SECRET
ENCRYPTION_KEY=$ENCRYPTION_KEY
SESSION_SECRET=$SESSION_SECRET
API_SECRET_KEY=$API_SECRET_KEY

# Webhook боты
BOT_WEBHOOK_SECRET=$BOT_WEBHOOK_SECRET

# =============================================================================
# ИНСТРУКЦИИ ПО ИСПОЛЬЗОВАНИЮ
# =============================================================================

1. Скопируйте эти значения в соответствующие поля .env файла
2. Получите реальные токены от внешних сервисов:
   - Telegram боты: @BotFather
   - YooKassa: личный кабинет
   - Cryptomus: панель мерчанта
   - Remnawave: API панель
   - Sentry: проект мониторинга

3. Обновите домены на реальные
4. Настройте SMTP для email
5. Запустите проверку: python3 scripts/check_env_vars.py
6. УДАЛИТЕ ЭТОТ ФАЙЛ после настройки!

# =============================================================================
EOF

    chmod 600 "$secrets_file"
    log_success "Файл с секретами создан: $secrets_file"
    log_warning "ВАЖНО: Удалите файл $secrets_file после копирования секретов!"
}

# Основная функция
main() {
    echo "============================================================================="
    echo "🔐 UnveilVPN Shop - Production Secrets Generator"
    echo "============================================================================="
    
    check_dependencies
    generate_secrets
    create_env_file ".env"
    create_secrets_file
    
    echo "============================================================================="
    log_success "Генерация production конфигурации завершена!"
    echo ""
    log_info "Созданные файлы:"
    echo "  - .env (готовый к использованию файл окружения)"
    echo "  - production_secrets.txt (секреты для администратора)"
    echo ""
    log_warning "Следующие шаги:"
    echo "  1. Замените placeholder значения в .env на реальные токены"
    echo "  2. Запустите: python3 scripts/check_env_vars.py"
    echo "  3. Удалите production_secrets.txt после настройки"
    echo "============================================================================="
}

# Запуск скрипта
main "$@"
