# =============================================================================
# UnveilVPN Shop - WSL2 Port Forwarding Setup
# Запускать в PowerShell от имени администратора в Windows
# =============================================================================

# Получаем IP адрес WSL2
$wslIP = (wsl hostname -I).Trim()
Write-Host "WSL2 IP адрес: $wslIP" -ForegroundColor Green

# Порты для проброса
$ports = @(80, 443)

# Удаляем существующие правила (если есть)
Write-Host "Удаление существующих правил..." -ForegroundColor Yellow
foreach ($port in $ports) {
    try {
        netsh interface portproxy delete v4tov4 listenport=$port
        Write-Host "Удалено правило для порта $port" -ForegroundColor Gray
    } catch {
        Write-Host "Правило для порта $port не найдено" -ForegroundColor Gray
    }
}

# Добавляем новые правила проброса портов
Write-Host "Добавление новых правил проброса портов..." -ForegroundColor Yellow
foreach ($port in $ports) {
    try {
        netsh interface portproxy add v4tov4 listenport=$port listenaddress=0.0.0.0 connectport=$port connectaddress=$wslIP
        Write-Host "✅ Порт $port пробрасывается на $wslIP" -ForegroundColor Green
    } catch {
        Write-Host "❌ Ошибка при добавлении правила для порта $port" -ForegroundColor Red
    }
}

# Настройка Windows Firewall
Write-Host "Настройка Windows Firewall..." -ForegroundColor Yellow
foreach ($port in $ports) {
    try {
        # Удаляем существующие правила
        netsh advfirewall firewall delete rule name="WSL2 UnveilVPN Port $port"
        
        # Добавляем новые правила
        netsh advfirewall firewall add rule name="WSL2 UnveilVPN Port $port" dir=in action=allow protocol=TCP localport=$port
        Write-Host "✅ Firewall правило для порта $port добавлено" -ForegroundColor Green
    } catch {
        Write-Host "❌ Ошибка при настройке firewall для порта $port" -ForegroundColor Red
    }
}

# Показываем текущие правила
Write-Host "`nТекущие правила проброса портов:" -ForegroundColor Cyan
netsh interface portproxy show all

Write-Host "`n🎉 Настройка завершена!" -ForegroundColor Green
Write-Host "Теперь сайт доступен по адресам:" -ForegroundColor White
Write-Host "  • http://localhost" -ForegroundColor Yellow
Write-Host "  • https://localhost" -ForegroundColor Yellow
Write-Host "  • http://127.0.0.1" -ForegroundColor Yellow
Write-Host "  • https://127.0.0.1" -ForegroundColor Yellow
Write-Host "  • http://$wslIP" -ForegroundColor Yellow
Write-Host "  • https://$wslIP" -ForegroundColor Yellow

Write-Host "`n📝 Примечание:" -ForegroundColor Cyan
Write-Host "При перезапуске WSL2 IP может измениться." -ForegroundColor Gray
Write-Host "В таком случае запустите этот скрипт повторно." -ForegroundColor Gray
