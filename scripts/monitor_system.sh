#!/bin/bash
# =============================================================================
# UnveilVPN Shop - System Monitoring Script
# Мониторинг работоспособности всех компонентов системы
# =============================================================================

set -euo pipefail

# Цвета для вывода
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Функции для вывода
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_check() {
    echo -e "${PURPLE}[CHECK]${NC} $1"
}

# Конфигурация
COMPOSE_CMD="docker compose"
ALERT_EMAIL="${ALERT_EMAIL:-<EMAIL>}"
LOG_FILE="/var/log/unveilvpn-monitor.log"

# Проверка Docker Compose
check_compose() {
    if docker compose version &> /dev/null; then
        COMPOSE_CMD="docker compose"
    elif docker-compose --version &> /dev/null; then
        COMPOSE_CMD="docker-compose"
    else
        log_error "Docker Compose не найден"
        exit 1
    fi
}

# Проверка контейнеров
check_containers() {
    log_check "Проверка состояния контейнеров..."
    
    local containers=("nginx" "postgres" "redis" "api" "landing" "client-bot" "admin-bot" "support-bot")
    local failed_containers=()
    
    for container in "${containers[@]}"; do
        if $COMPOSE_CMD ps "$container" --format "table {{.State}}" | grep -q "running"; then
            log_success "✓ $container работает"
        else
            log_error "✗ $container не работает"
            failed_containers+=("$container")
        fi
    done
    
    if [[ ${#failed_containers[@]} -gt 0 ]]; then
        log_error "Неработающие контейнеры: ${failed_containers[*]}"
        return 1
    fi
    
    return 0
}

# Проверка health checks
check_health() {
    log_check "Проверка health checks..."
    
    local unhealthy_services=()
    
    # Получаем список сервисов с health checks
    local services=$($COMPOSE_CMD ps --format "table {{.Service}}\t{{.Health}}" | tail -n +2)
    
    while IFS=$'\t' read -r service health; do
        if [[ "$health" == "healthy" ]]; then
            log_success "✓ $service здоров"
        elif [[ "$health" == "unhealthy" ]]; then
            log_error "✗ $service нездоров"
            unhealthy_services+=("$service")
        elif [[ "$health" == "starting" ]]; then
            log_warning "⏳ $service запускается"
        fi
    done <<< "$services"
    
    if [[ ${#unhealthy_services[@]} -gt 0 ]]; then
        log_error "Нездоровые сервисы: ${unhealthy_services[*]}"
        return 1
    fi
    
    return 0
}

# Проверка сетевой доступности
check_network() {
    log_check "Проверка сетевой доступности..."
    
    local endpoints=(
        "http://localhost/health:Nginx"
        "http://localhost:8000/health:API"
        "https://www.unveilvpn.cm:Landing"
        "https://api.unveilvpn.cm/health:API_External"
    )
    
    local failed_endpoints=()
    
    for endpoint in "${endpoints[@]}"; do
        local url="${endpoint%:*}"
        local name="${endpoint#*:}"
        
        if curl -f -s --max-time 10 "$url" > /dev/null; then
            log_success "✓ $name доступен ($url)"
        else
            log_error "✗ $name недоступен ($url)"
            failed_endpoints+=("$name")
        fi
    done
    
    if [[ ${#failed_endpoints[@]} -gt 0 ]]; then
        log_error "Недоступные endpoints: ${failed_endpoints[*]}"
        return 1
    fi
    
    return 0
}

# Проверка базы данных
check_database() {
    log_check "Проверка базы данных..."
    
    if $COMPOSE_CMD exec -T postgres pg_isready -U unveilvpn_user -d unveilvpn_prod > /dev/null; then
        log_success "✓ PostgreSQL доступен"
        
        # Проверка количества подключений
        local connections=$($COMPOSE_CMD exec -T postgres psql -U unveilvpn_user -d unveilvpn_prod -t -c "SELECT count(*) FROM pg_stat_activity;" | tr -d ' ')
        log_info "Активных подключений: $connections"
        
        if [[ $connections -gt 50 ]]; then
            log_warning "Много активных подключений к БД: $connections"
        fi
        
        return 0
    else
        log_error "✗ PostgreSQL недоступен"
        return 1
    fi
}

# Проверка Redis
check_redis() {
    log_check "Проверка Redis..."
    
    if $COMPOSE_CMD exec -T redis redis-cli ping | grep -q "PONG"; then
        log_success "✓ Redis доступен"
        
        # Проверка использования памяти
        local memory_usage=$($COMPOSE_CMD exec -T redis redis-cli info memory | grep "used_memory_human" | cut -d: -f2 | tr -d '\r')
        log_info "Использование памяти Redis: $memory_usage"
        
        return 0
    else
        log_error "✗ Redis недоступен"
        return 1
    fi
}

# Проверка SSL сертификатов
check_ssl() {
    log_check "Проверка SSL сертификатов..."
    
    local cert_file="/etc/ssl/certs/unveilvpn.cm.crt"
    
    if [[ -f "$cert_file" ]]; then
        local expiry_date=$(openssl x509 -in "$cert_file" -noout -enddate | cut -d= -f2)
        local expiry_timestamp=$(date -d "$expiry_date" +%s)
        local current_timestamp=$(date +%s)
        local days_until_expiry=$(( (expiry_timestamp - current_timestamp) / 86400 ))
        
        if [[ $days_until_expiry -gt 30 ]]; then
            log_success "✓ SSL сертификат действителен ($days_until_expiry дней)"
        elif [[ $days_until_expiry -gt 7 ]]; then
            log_warning "⚠ SSL сертификат истекает через $days_until_expiry дней"
        else
            log_error "✗ SSL сертификат истекает через $days_until_expiry дней!"
            return 1
        fi
        
        return 0
    else
        log_error "✗ SSL сертификат не найден"
        return 1
    fi
}

# Проверка использования ресурсов
check_resources() {
    log_check "Проверка использования ресурсов..."
    
    # Проверка использования диска
    local disk_usage=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
    if [[ $disk_usage -gt 90 ]]; then
        log_error "✗ Критическое использование диска: ${disk_usage}%"
        return 1
    elif [[ $disk_usage -gt 80 ]]; then
        log_warning "⚠ Высокое использование диска: ${disk_usage}%"
    else
        log_success "✓ Использование диска: ${disk_usage}%"
    fi
    
    # Проверка использования памяти
    local memory_usage=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
    if [[ $memory_usage -gt 90 ]]; then
        log_error "✗ Критическое использование памяти: ${memory_usage}%"
        return 1
    elif [[ $memory_usage -gt 80 ]]; then
        log_warning "⚠ Высокое использование памяти: ${memory_usage}%"
    else
        log_success "✓ Использование памяти: ${memory_usage}%"
    fi
    
    return 0
}

# Проверка логов на ошибки
check_logs() {
    log_check "Проверка логов на ошибки..."
    
    local services=("nginx" "api" "client-bot" "admin-bot" "support-bot")
    local error_count=0
    
    for service in "${services[@]}"; do
        local errors=$($COMPOSE_CMD logs --since="1h" "$service" 2>/dev/null | grep -i "error\|exception\|failed" | wc -l)
        
        if [[ $errors -gt 10 ]]; then
            log_error "✗ Много ошибок в $service: $errors за последний час"
            ((error_count++))
        elif [[ $errors -gt 0 ]]; then
            log_warning "⚠ Ошибки в $service: $errors за последний час"
        else
            log_success "✓ Нет ошибок в $service"
        fi
    done
    
    if [[ $error_count -gt 0 ]]; then
        return 1
    fi
    
    return 0
}

# Отправка алерта
send_alert() {
    local message="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    # Логирование
    echo "[$timestamp] ALERT: $message" >> "$LOG_FILE"
    
    # Отправка email (если настроен)
    if command -v mail &> /dev/null; then
        echo "$message" | mail -s "UnveilVPN Shop Alert - $timestamp" "$ALERT_EMAIL"
    fi
    
    # Webhook уведомление (если настроен)
    if [[ -n "${WEBHOOK_URL:-}" ]]; then
        curl -X POST "$WEBHOOK_URL" \
            -H "Content-Type: application/json" \
            -d "{\"text\":\"🚨 UnveilVPN Shop Alert\\n$message\\nTime: $timestamp\"}" \
            &> /dev/null || true
    fi
}

# Основная проверка
run_checks() {
    local failed_checks=()
    local check_functions=(
        "check_containers:Контейнеры"
        "check_health:Health Checks"
        "check_network:Сетевая доступность"
        "check_database:База данных"
        "check_redis:Redis"
        "check_ssl:SSL сертификаты"
        "check_resources:Ресурсы системы"
        "check_logs:Логи"
    )
    
    for check in "${check_functions[@]}"; do
        local func="${check%:*}"
        local name="${check#*:}"
        
        if ! $func; then
            failed_checks+=("$name")
        fi
    done
    
    return ${#failed_checks[@]}
}

# Показать статистику
show_stats() {
    echo ""
    echo "📊 Статистика системы:"
    echo "======================"
    
    # Uptime контейнеров
    echo "🐳 Контейнеры:"
    $COMPOSE_CMD ps --format "table {{.Service}}\t{{.Status}}\t{{.Health}}"
    
    echo ""
    echo "💾 Использование ресурсов:"
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}"
    
    echo ""
    echo "💿 Диск:"
    df -h / | awk 'NR==2 {print "Использовано: " $3 " из " $2 " (" $5 ")"}'
    
    echo ""
    echo "🧠 Память:"
    free -h | awk 'NR==2 {print "Использовано: " $3 " из " $2}'
}

# Основная функция
main() {
    local mode="${1:-check}"
    
    case "$mode" in
        "check")
            echo "🔍 UnveilVPN Shop - System Health Check"
            echo "======================================="
            
            check_compose
            
            if run_checks; then
                log_success "🎉 Все проверки пройдены успешно!"
                exit 0
            else
                local failed_count=$?
                log_error "❌ Провалено проверок: $failed_count"
                send_alert "System health check failed: $failed_count checks failed"
                exit 1
            fi
            ;;
        "stats")
            echo "📊 UnveilVPN Shop - System Statistics"
            echo "====================================="
            show_stats
            ;;
        "monitor")
            echo "👁️ UnveilVPN Shop - Continuous Monitoring"
            echo "========================================="
            
            while true; do
                echo "$(date '+%Y-%m-%d %H:%M:%S') - Running health check..."
                
                if ! run_checks > /dev/null 2>&1; then
                    log_error "Health check failed at $(date)"
                    send_alert "Continuous monitoring detected system issues"
                fi
                
                sleep 300  # Проверка каждые 5 минут
            done
            ;;
        *)
            echo "Использование: $0 [check|stats|monitor]"
            echo ""
            echo "Команды:"
            echo "  check    - Однократная проверка системы (по умолчанию)"
            echo "  stats    - Показать статистику системы"
            echo "  monitor  - Непрерывный мониторинг"
            echo ""
            echo "Переменные окружения:"
            echo "  ALERT_EMAIL   - Email для алертов (по умолчанию: <EMAIL>)"
            echo "  WEBHOOK_URL   - URL для webhook уведомлений"
            exit 1
            ;;
    esac
}

# Запуск
main "$@"
