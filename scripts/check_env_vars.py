#!/usr/bin/env python3
"""
UnveilVPN Shop - Environment Variables Checker
Проверка всех необходимых переменных окружения для production развертывания
"""

import os
import re
import sys
from pathlib import Path
from typing import Dict, List, Tuple, Optional

class EnvironmentChecker:
    """Класс для проверки переменных окружения"""
    
    def __init__(self, env_file: str = ".env.prod"):
        self.env_file = env_file
        self.errors: List[str] = []
        self.warnings: List[str] = []
        self.info: List[str] = []
        
        # Критически важные переменные
        self.critical_vars = {
            # База данных
            'DB_HOST', 'DB_PORT', 'DB_NAME', 'DB_USER', 'DB_PASS', 'DATABASE_URL',

            # Redis
            'REDIS_HOST', 'REDIS_PORT', 'REDIS_PASSWORD', 'REDIS_URL',

            # Remnawave
            'REMNAWAVE_PANEL_URL', 'REMNAWAVE_API_KEY', 'REMNAWAVE_SUBSCRIPTION_URL',

            # Telegram боты
            'CLIENT_BOT_TOKEN', 'ADMIN_BOT_TOKEN', 'SUPPORT_BOT_TOKEN',

            # Платежные системы
            'YOOKASSA_SHOPID', 'YOOKASSA_SECRET_KEY',
            'CRYPTOMUS_MERCHANT_ID', 'CRYPTOMUS_API_KEY',

            # Безопасность
            'JWT_SECRET', 'WEBHOOK_SECRET', 'ENCRYPTION_KEY', 'SESSION_SECRET', 'API_SECRET_KEY',

            # Домены
            'DOMAIN', 'API_DOMAIN'
        }

        # Удаленные переменные (больше не используются)
        self.removed_vars = {
            'YOOKASSA_WEBHOOK_SECRET', 'CRYPTOMUS_WEBHOOK_SECRET',
            'CLOUDFLARE_API_TOKEN', 'CLOUDFLARE_ZONE_ID',
            'BACKUP_S3_ACCESS_KEY', 'BACKUP_S3_SECRET_KEY',
            'ANALYTICS_ENABLED', 'CACHE_TTL', 'UPLOAD_MAX_SIZE'
        }
        
        # Переменные, которые не должны содержать placeholder значения
        self.placeholder_patterns = [
            r'your_.*',
            r'test_.*',
            r'example.*',
            r'placeholder.*',
            r'change_me.*',
            r'replace_.*'
        ]

    def load_env_file(self) -> Dict[str, str]:
        """Загрузка переменных из .env файла"""
        env_vars = {}
        
        if not os.path.exists(self.env_file):
            self.errors.append(f"❌ Файл {self.env_file} не найден")
            return env_vars
        
        try:
            with open(self.env_file, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    
                    # Пропускаем комментарии и пустые строки
                    if not line or line.startswith('#'):
                        continue
                    
                    # Парсим переменную
                    if '=' in line:
                        key, value = line.split('=', 1)
                        key = key.strip()
                        value = value.strip()
                        
                        # Убираем кавычки если есть
                        if value.startswith('"') and value.endswith('"'):
                            value = value[1:-1]
                        elif value.startswith("'") and value.endswith("'"):
                            value = value[1:-1]
                        
                        env_vars[key] = value
                    else:
                        self.warnings.append(f"⚠️ Строка {line_num}: неверный формат - {line}")
        
        except Exception as e:
            self.errors.append(f"❌ Ошибка чтения файла {self.env_file}: {e}")
        
        return env_vars

    def check_critical_variables(self, env_vars: Dict[str, str]) -> None:
        """Проверка критически важных переменных"""
        missing_critical = []
        
        for var in self.critical_vars:
            if var not in env_vars:
                missing_critical.append(var)
            elif not env_vars[var].strip():
                missing_critical.append(f"{var} (пустое значение)")
        
        if missing_critical:
            self.errors.append("❌ Отсутствуют критически важные переменные:")
            for var in missing_critical:
                self.errors.append(f"   - {var}")

    def check_placeholder_values(self, env_vars: Dict[str, str]) -> None:
        """Проверка на placeholder значения"""
        placeholder_vars = []
        
        for key, value in env_vars.items():
            if not value.strip():
                continue
            
            for pattern in self.placeholder_patterns:
                if re.match(pattern, value.lower()):
                    placeholder_vars.append(f"{key}={value}")
                    break
        
        if placeholder_vars:
            self.errors.append("❌ Найдены placeholder значения (требуют замены):")
            for var in placeholder_vars:
                self.errors.append(f"   - {var}")

    def check_password_strength(self, env_vars: Dict[str, str]) -> None:
        """Проверка силы паролей и ключей"""
        password_vars = [
            'DB_PASS', 'REDIS_PASSWORD', 'JWT_SECRET', 'WEBHOOK_SECRET',
            'ENCRYPTION_KEY', 'SESSION_SECRET', 'YOOKASSA_SECRET_KEY',
            'CRYPTOMUS_API_KEY'
        ]
        
        weak_passwords = []
        
        for var in password_vars:
            if var in env_vars:
                password = env_vars[var]
                if len(password) < 16:
                    weak_passwords.append(f"{var} (длина: {len(password)})")
                elif password.isalnum() and len(password) < 32:
                    weak_passwords.append(f"{var} (только буквы/цифры)")
        
        if weak_passwords:
            self.warnings.append("⚠️ Слабые пароли/ключи (рекомендуется усилить):")
            for var in weak_passwords:
                self.warnings.append(f"   - {var}")

    def check_url_formats(self, env_vars: Dict[str, str]) -> None:
        """Проверка форматов URL"""
        url_vars = [
            'REMNAWAVE_PANEL_URL', 'REMNAWAVE_SUBSCRIPTION_URL',
            'DATABASE_URL', 'REDIS_URL', 'DOMAIN', 'API_DOMAIN'
        ]
        
        invalid_urls = []
        
        for var in url_vars:
            if var in env_vars:
                url = env_vars[var]
                
                # Проверка базового формата URL
                if var.endswith('_URL') and not (url.startswith('http://') or url.startswith('https://') or url.startswith('postgresql://') or url.startswith('redis://')):
                    invalid_urls.append(f"{var}={url}")
                
                # Проверка доменов
                elif var.endswith('_DOMAIN') and ('your-domain' in url or 'example' in url):
                    invalid_urls.append(f"{var}={url}")
        
        if invalid_urls:
            self.warnings.append("⚠️ Некорректные URL/домены:")
            for var in invalid_urls:
                self.warnings.append(f"   - {var}")

    def check_telegram_tokens(self, env_vars: Dict[str, str]) -> None:
        """Проверка токенов Telegram ботов"""
        bot_tokens = ['CLIENT_BOT_TOKEN', 'ADMIN_BOT_TOKEN', 'SUPPORT_BOT_TOKEN']
        
        invalid_tokens = []
        
        for var in bot_tokens:
            if var in env_vars:
                token = env_vars[var]
                
                # Базовая проверка формата токена (должен содержать : и быть достаточно длинным)
                if ':' not in token or len(token) < 40:
                    invalid_tokens.append(f"{var} (неверный формат)")
                elif 'your_' in token or 'test_' in token:
                    invalid_tokens.append(f"{var} (placeholder значение)")
        
        if invalid_tokens:
            self.errors.append("❌ Некорректные токены Telegram ботов:")
            for var in invalid_tokens:
                self.errors.append(f"   - {var}")

    def check_database_config(self, env_vars: Dict[str, str]) -> None:
        """Проверка конфигурации базы данных"""
        db_issues = []
        
        # Проверка DATABASE_URL
        if 'DATABASE_URL' in env_vars:
            db_url = env_vars['DATABASE_URL']
            if not db_url.startswith('postgresql://'):
                db_issues.append("DATABASE_URL должен начинаться с postgresql://")
            
            # Проверка соответствия отдельных переменных и DATABASE_URL
            if all(var in env_vars for var in ['DB_HOST', 'DB_USER', 'DB_PASS', 'DB_NAME']):
                expected_url = f"postgresql://{env_vars['DB_USER']}:{env_vars['DB_PASS']}@{env_vars['DB_HOST']}:{env_vars.get('DB_PORT', '5432')}/{env_vars['DB_NAME']}"
                if db_url != expected_url:
                    db_issues.append("DATABASE_URL не соответствует отдельным DB_* переменным")
        
        # Проверка пула соединений
        pool_size = env_vars.get('DB_POOL_SIZE', '20')
        try:
            if int(pool_size) > 50:
                self.warnings.append("⚠️ DB_POOL_SIZE больше 50 может быть избыточным")
        except ValueError:
            db_issues.append("DB_POOL_SIZE должен быть числом")
        
        if db_issues:
            self.warnings.append("⚠️ Проблемы с конфигурацией базы данных:")
            for issue in db_issues:
                self.warnings.append(f"   - {issue}")

    def check_security_settings(self, env_vars: Dict[str, str]) -> None:
        """Проверка настроек безопасности"""
        security_issues = []
        
        # Проверка production настроек
        if env_vars.get('ENVIRONMENT') != 'production':
            security_issues.append("ENVIRONMENT должен быть 'production'")
        
        if env_vars.get('DEBUG', '').lower() != 'false':
            security_issues.append("DEBUG должен быть 'false' в production")
        
        # Проверка SSL настроек
        if env_vars.get('SSL_ENABLED', '').lower() != 'true':
            security_issues.append("SSL_ENABLED должен быть 'true' в production")
        
        # Проверка CORS
        cors_origins = env_vars.get('CORS_ORIGINS', '')
        if '*' in cors_origins:
            security_issues.append("CORS_ORIGINS не должен содержать '*' в production")
        
        if security_issues:
            self.warnings.append("⚠️ Проблемы с настройками безопасности:")
            for issue in security_issues:
                self.warnings.append(f"   - {issue}")

    def check_removed_variables(self, env_vars: Dict[str, str]) -> None:
        """Проверка на устаревшие переменные"""
        found_removed = []

        for var in self.removed_vars:
            if var in env_vars:
                found_removed.append(var)

        if found_removed:
            self.warnings.append("⚠️ Найдены устаревшие переменные (можно удалить):")
            for var in found_removed:
                self.warnings.append(f"   - {var}")

    def generate_recommendations(self, env_vars: Dict[str, str]) -> None:
        """Генерация рекомендаций"""
        recommendations = []

        # Рекомендации по мониторингу
        if env_vars.get('SENTRY_DSN', '').startswith('your_'):
            recommendations.append("Настройте Sentry для мониторинга ошибок")

        if env_vars.get('PROMETHEUS_ENABLED', '').lower() != 'true':
            recommendations.append("Включите Prometheus для мониторинга метрик")

        # Рекомендации по email
        if env_vars.get('SMTP_HOST', '').startswith('smtp.your-'):
            recommendations.append("Настройте SMTP для отправки email уведомлений")

        if recommendations:
            self.info.append("💡 Рекомендации для улучшения:")
            for rec in recommendations:
                self.info.append(f"   - {rec}")

    def run_checks(self) -> bool:
        """Запуск всех проверок"""
        print(f"🔍 Проверка переменных окружения из файла: {self.env_file}")
        print("=" * 60)
        
        # Загрузка переменных
        env_vars = self.load_env_file()
        
        if not env_vars:
            return False
        
        print(f"📋 Загружено переменных: {len(env_vars)}")
        print()
        
        # Выполнение проверок
        self.check_critical_variables(env_vars)
        self.check_placeholder_values(env_vars)
        self.check_password_strength(env_vars)
        self.check_url_formats(env_vars)
        self.check_telegram_tokens(env_vars)
        self.check_database_config(env_vars)
        self.check_security_settings(env_vars)
        self.check_removed_variables(env_vars)
        self.generate_recommendations(env_vars)
        
        # Вывод результатов
        self.print_results()
        
        return len(self.errors) == 0

    def print_results(self) -> None:
        """Вывод результатов проверки"""
        # Ошибки
        if self.errors:
            print("🚨 КРИТИЧЕСКИЕ ОШИБКИ:")
            for error in self.errors:
                print(error)
            print()
        
        # Предупреждения
        if self.warnings:
            print("⚠️ ПРЕДУПРЕЖДЕНИЯ:")
            for warning in self.warnings:
                print(warning)
            print()
        
        # Информация и рекомендации
        if self.info:
            for info in self.info:
                print(info)
            print()
        
        # Итоговый статус
        if not self.errors and not self.warnings:
            print("✅ ВСЕ ПРОВЕРКИ ПРОЙДЕНЫ УСПЕШНО!")
            print("🚀 Конфигурация готова к production развертыванию")
        elif not self.errors:
            print("✅ КРИТИЧЕСКИХ ОШИБОК НЕ НАЙДЕНО")
            print("⚠️ Есть предупреждения, рекомендуется их исправить")
        else:
            print("❌ НАЙДЕНЫ КРИТИЧЕСКИЕ ОШИБКИ")
            print("🛑 Развертывание в production НЕ РЕКОМЕНДУЕТСЯ")
        
        print()
        print("📊 СТАТИСТИКА:")
        print(f"   Ошибки: {len(self.errors)}")
        print(f"   Предупреждения: {len(self.warnings)}")
        print(f"   Рекомендации: {len([i for i in self.info if i.startswith('💡')])}")


def main():
    """Основная функция"""
    # Определение файла для проверки
    env_file = sys.argv[1] if len(sys.argv) > 1 else ".env.prod"
    
    # Создание и запуск проверки
    checker = EnvironmentChecker(env_file)
    success = checker.run_checks()
    
    # Возврат кода выхода
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
