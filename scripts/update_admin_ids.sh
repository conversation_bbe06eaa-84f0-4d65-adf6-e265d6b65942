#!/bin/bash

# =============================================================================
# UnveilVPN Shop - Update Admin IDs Script
# Скрипт для обновления ID администраторов в .env файле
# =============================================================================

set -e

# Цвета для вывода
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Функция для вывода сообщений
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Проверка существования .env файла
if [ ! -f ".env" ]; then
    log_error ".env файл не найден!"
    exit 1
fi

echo "==============================================================================="
echo "                    UnveilVPN Shop - Обновление Admin IDs"
echo "==============================================================================="
echo

log_info "Для получения вашего Telegram ID:"
log_info "1. Напишите боту @userinfobot в Telegram"
log_info "2. Или используйте бота @getmyid_bot"
log_info "3. Скопируйте ваш User ID (только цифры)"
echo

# Запрос Super Admin ID
echo -n "Введите ваш Telegram User ID (Super Admin): "
read SUPER_ADMIN_ID

# Валидация Super Admin ID
if ! [[ "$SUPER_ADMIN_ID" =~ ^[0-9]+$ ]]; then
    log_error "Неверный формат ID! Используйте только цифры."
    exit 1
fi

# Запрос дополнительных Admin IDs
echo -n "Введите дополнительные Admin IDs через запятую (или Enter для пропуска): "
read ADDITIONAL_ADMINS

# Формирование списка всех админов
if [ -n "$ADDITIONAL_ADMINS" ]; then
    ADMIN_USER_IDS="$SUPER_ADMIN_ID,$ADDITIONAL_ADMINS"
else
    ADMIN_USER_IDS="$SUPER_ADMIN_ID"
fi

log_info "Super Admin ID: $SUPER_ADMIN_ID"
log_info "Все Admin IDs: $ADMIN_USER_IDS"
echo

# Подтверждение
echo -n "Продолжить обновление? (y/N): "
read CONFIRM

if [[ ! "$CONFIRM" =~ ^[Yy]$ ]]; then
    log_warning "Операция отменена."
    exit 0
fi

# Создание резервной копии
cp .env .env.backup.$(date +%Y%m%d_%H%M%S)
log_success "Создана резервная копия .env файла"

# Обновление .env файла
sed -i "s/^ADMIN_USER_IDS=.*/ADMIN_USER_IDS=$ADMIN_USER_IDS/" .env
sed -i "s/^SUPER_ADMIN_ID=.*/SUPER_ADMIN_ID=$SUPER_ADMIN_ID/" .env

log_success "Admin IDs обновлены в .env файле"

# Проверка обновления
echo
log_info "Проверка обновленных значений:"
grep "ADMIN_USER_IDS=" .env
grep "SUPER_ADMIN_ID=" .env

echo
log_success "✅ Admin IDs успешно обновлены!"
log_warning "⚠️  Перезапустите ботов для применения изменений:"
echo "   docker compose restart client-bot admin-bot support-bot"
