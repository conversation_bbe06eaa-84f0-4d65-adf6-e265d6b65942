# =============================================================================
# UnveilVPN Shop - WSL2 Port Forwarding Cleanup
# Запускать в PowerShell от имени администратора в Windows
# =============================================================================

Write-Host "Очистка правил проброса портов WSL2..." -ForegroundColor Yellow

# Порты для очистки
$ports = @(80, 443)

# Удаляем правила проброса портов
foreach ($port in $ports) {
    try {
        netsh interface portproxy delete v4tov4 listenport=$port
        Write-Host "✅ Удалено правило проброса для порта $port" -ForegroundColor Green
    } catch {
        Write-Host "⚠️ Правило для порта $port не найдено" -ForegroundColor Gray
    }
}

# Удаляем правила firewall
foreach ($port in $ports) {
    try {
        netsh advfirewall firewall delete rule name="WSL2 UnveilVPN Port $port"
        Write-Host "✅ Удалено firewall правило для порта $port" -ForegroundColor Green
    } catch {
        Write-Host "⚠️ Firewall правило для порта $port не найдено" -ForegroundColor Gray
    }
}

Write-Host "`n🧹 Очистка завершена!" -ForegroundColor Green
