#!/bin/bash
# =============================================================================
# UnveilVPN Shop - SSL Certificate Setup with acme.sh
# Автоматическая настройка SSL сертификатов через Cloudflare DNS
# =============================================================================

set -euo pipefail

# Цвета для вывода
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Функции для вывода
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Конфигурация
DOMAIN="unveilvpn.com"
EMAIL="<EMAIL>"
ACME_HOME="/opt/acme.sh"
CERT_DIR="/etc/ssl/certs"
KEY_DIR="/etc/ssl/private"

# Проверка переменных окружения
check_environment() {
    log_info "Проверка переменных окружения..."
    
    if [[ -z "${CF_Token:-}" ]]; then
        log_error "Переменная CF_Token не установлена"
        log_info "Получите Global API Key в Cloudflare Dashboard:"
        log_info "1. Войдите в https://dash.cloudflare.com/profile/api-tokens"
        log_info "2. Скопируйте Global API Key"
        log_info "3. Экспортируйте: export CF_Token=your_global_api_key"
        exit 1
    fi
    
    if [[ -z "${CF_Account_ID:-}" ]]; then
        log_error "Переменная CF_Account_ID не установлена"
        log_info "Получите Account ID в Cloudflare Dashboard:"
        log_info "1. Войдите в https://dash.cloudflare.com"
        log_info "2. Выберите домен $DOMAIN"
        log_info "3. Скопируйте Account ID из правой панели"
        log_info "4. Экспортируйте: export CF_Account_ID=your_account_id"
        exit 1
    fi
    
    if [[ -z "${CF_Zone_ID:-}" ]]; then
        log_error "Переменная CF_Zone_ID не установлена"
        log_info "Получите Zone ID в Cloudflare Dashboard:"
        log_info "1. Войдите в https://dash.cloudflare.com"
        log_info "2. Выберите домен $DOMAIN"
        log_info "3. Скопируйте Zone ID из правой панели"
        log_info "4. Экспортируйте: export CF_Zone_ID=your_zone_id"
        exit 1
    fi
    
    log_success "Переменные окружения настроены"
}

# Установка acme.sh
install_acme() {
    log_info "Установка acme.sh..."
    
    if [[ -d "$ACME_HOME" ]]; then
        log_info "acme.sh уже установлен, обновляем..."
        "$ACME_HOME/acme.sh" --upgrade
    else
        curl https://get.acme.sh | sh -s email="$EMAIL"
        
        # Создание символической ссылки
        if [[ ! -d "$ACME_HOME" ]]; then
            ln -sf "$HOME/.acme.sh" "$ACME_HOME"
        fi
    fi
    
    log_success "acme.sh установлен"
}

# Создание директорий для сертификатов
create_directories() {
    log_info "Создание директорий для сертификатов..."
    
    sudo mkdir -p "$CERT_DIR" "$KEY_DIR"
    sudo chmod 755 "$CERT_DIR"
    sudo chmod 700 "$KEY_DIR"
    
    log_success "Директории созданы"
}

# Получение SSL сертификата
obtain_certificate() {
    log_info "Получение SSL сертификата для $DOMAIN..."
    
    # Экспорт переменных для acme.sh
    export CF_Token
    export CF_Account_ID
    export CF_Zone_ID
    
    # Получение сертификата с SAN для всех поддоменов
    "$ACME_HOME/acme.sh" --issue \
        --dns dns_cf \
        -d "$DOMAIN" \
        -d "www.$DOMAIN" \
        -d "api.$DOMAIN" \
        -d "sub.$DOMAIN" \
        --keylength ec-256 \
        --force
    
    if [[ $? -eq 0 ]]; then
        log_success "Сертификат получен успешно"
    else
        log_error "Ошибка получения сертификата"
        exit 1
    fi
}

# Установка сертификата
install_certificate() {
    log_info "Установка сертификата..."
    
    # Установка сертификата в системные директории
    sudo "$ACME_HOME/acme.sh" --install-cert \
        -d "$DOMAIN" \
        --cert-file "$CERT_DIR/$DOMAIN.crt" \
        --key-file "$KEY_DIR/$DOMAIN.key" \
        --fullchain-file "$CERT_DIR/$DOMAIN.crt" \
        --reloadcmd "docker compose restart nginx"
    
    # Установка правильных прав доступа
    sudo chmod 644 "$CERT_DIR/$DOMAIN.crt"
    sudo chmod 600 "$KEY_DIR/$DOMAIN.key"
    
    log_success "Сертификат установлен"
}

# Настройка автоматического обновления
setup_auto_renewal() {
    log_info "Настройка автоматического обновления..."
    
    # Создание cron задачи для обновления сертификатов
    (crontab -l 2>/dev/null; echo "0 2 * * * $ACME_HOME/acme.sh --cron --home $ACME_HOME > /dev/null") | crontab -
    
    log_success "Автоматическое обновление настроено"
}

# Проверка сертификата
verify_certificate() {
    log_info "Проверка сертификата..."
    
    if [[ -f "$CERT_DIR/$DOMAIN.crt" && -f "$KEY_DIR/$DOMAIN.key" ]]; then
        # Проверка срока действия
        expiry_date=$(openssl x509 -in "$CERT_DIR/$DOMAIN.crt" -noout -enddate | cut -d= -f2)
        log_success "Сертификат действителен до: $expiry_date"
        
        # Проверка доменов в сертификате
        domains=$(openssl x509 -in "$CERT_DIR/$DOMAIN.crt" -noout -text | grep -A1 "Subject Alternative Name" | tail -1)
        log_info "Домены в сертификате: $domains"
        
        return 0
    else
        log_error "Файлы сертификата не найдены"
        return 1
    fi
}

# Создание тестового сертификата (для разработки)
create_test_certificate() {
    log_warning "Создание самоподписанного сертификата для тестирования..."
    
    sudo openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
        -keyout "$KEY_DIR/$DOMAIN.key" \
        -out "$CERT_DIR/$DOMAIN.crt" \
        -subj "/C=US/ST=State/L=City/O=UnveilVPN/CN=$DOMAIN" \
        -addext "subjectAltName=DNS:$DOMAIN,DNS:www.$DOMAIN,DNS:api.$DOMAIN,DNS:sub.$DOMAIN"
    
    sudo chmod 644 "$CERT_DIR/$DOMAIN.crt"
    sudo chmod 600 "$KEY_DIR/$DOMAIN.key"
    
    log_warning "Тестовый сертификат создан (НЕ для production!)"
}

# Основная функция
main() {
    echo "============================================================================="
    echo "🔒 UnveilVPN Shop - SSL Certificate Setup"
    echo "============================================================================="
    
    # Проверка аргументов
    if [[ "${1:-}" == "--test" ]]; then
        log_info "Режим тестирования: создание самоподписанного сертификата"
        create_directories
        create_test_certificate
        verify_certificate
        log_success "Тестовый SSL сертификат готов"
        return 0
    fi
    
    # Production режим
    check_environment
    create_directories
    install_acme
    obtain_certificate
    install_certificate
    setup_auto_renewal
    verify_certificate
    
    echo "============================================================================="
    log_success "SSL сертификат настроен успешно!"
    echo ""
    log_info "Файлы сертификата:"
    echo "  - Сертификат: $CERT_DIR/$DOMAIN.crt"
    echo "  - Приватный ключ: $KEY_DIR/$DOMAIN.key"
    echo ""
    log_info "Автоматическое обновление настроено через cron"
    log_info "Перезапустите Nginx: docker compose restart nginx"
    echo "============================================================================="
}

# Показать справку
show_help() {
    echo "Использование: $0 [--test]"
    echo ""
    echo "Опции:"
    echo "  --test    Создать самоподписанный сертификат для тестирования"
    echo ""
    echo "Переменные окружения для production:"
    echo "  CF_Token      - Cloudflare Global API Key"
    echo "  CF_Account_ID - Cloudflare Account ID"
    echo "  CF_Zone_ID    - Cloudflare Zone ID"
    echo ""
    echo "Пример:"
    echo "  export CF_Token=your_global_api_key"
    echo "  export CF_Account_ID=your_account_id"
    echo "  export CF_Zone_ID=your_zone_id"
    echo "  $0"
}

# Обработка аргументов
case "${1:-}" in
    --help|-h)
        show_help
        exit 0
        ;;
    *)
        main "$@"
        ;;
esac
