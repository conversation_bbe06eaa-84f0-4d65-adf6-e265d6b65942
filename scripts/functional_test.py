#!/usr/bin/env python3
"""
UnveilVPN Shop - Functional Testing Script
Комплексное функциональное тестирование всех компонентов системы
"""

import json
import time
import subprocess
import urllib.request
import urllib.error
from pathlib import Path

def test_api_health():
    """Тестирование health check API"""
    print("🔍 Тестирование API health check...")

    try:
        with urllib.request.urlopen('http://localhost:8000/health', timeout=10) as response:
            if response.status == 200:
                data = json.loads(response.read().decode())
                print(f"  ✅ API health check: {data.get('status', 'unknown')}")
                return True
            else:
                print(f"  ❌ API health check failed: {response.status}")
                return False
    except urllib.error.HTTPError as e:
        print(f"  ❌ API health check HTTP error: {e.code}")
        return False
    except Exception as e:
        print(f"  ❌ API health check error: {e}")
        return False

async def test_api_endpoints():
    """Тестирование основных API endpoints"""
    print("\n🔌 Тестирование API endpoints...")
    
    endpoints = [
        ('/api/tariffs', 'GET', 'Получение тарифов'),
        ('/api/config', 'GET', 'Конфигурация API'),
        ('/api/stats', 'GET', 'Статистика системы')
    ]
    
    results = []
    
    try:
        async with aiohttp.ClientSession() as session:
            for endpoint, method, description in endpoints:
                try:
                    async with session.request(method, f'http://localhost:8000{endpoint}') as response:
                        if response.status in [200, 404]:  # 404 может быть нормальным для некоторых endpoints
                            print(f"  ✅ {description}: {response.status}")
                            results.append(True)
                        else:
                            print(f"  ❌ {description}: {response.status}")
                            results.append(False)
                except Exception as e:
                    print(f"  ❌ {description}: {e}")
                    results.append(False)
    except Exception as e:
        print(f"  ❌ API endpoints test error: {e}")
        return False
    
    return all(results)

async def test_database_connection():
    """Тестирование подключения к базе данных"""
    print("\n🗄️ Тестирование подключения к базе данных...")
    
    try:
        # Тестируем через API endpoint
        async with aiohttp.ClientSession() as session:
            async with session.get('http://localhost:8000/api/health/db') as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"  ✅ База данных: {data.get('status', 'connected')}")
                    return True
                else:
                    print(f"  ❌ База данных недоступна: {response.status}")
                    return False
    except Exception as e:
        print(f"  ❌ Ошибка подключения к БД: {e}")
        return False

async def test_redis_connection():
    """Тестирование подключения к Redis"""
    print("\n🔴 Тестирование подключения к Redis...")
    
    try:
        # Тестируем через API endpoint
        async with aiohttp.ClientSession() as session:
            async with session.get('http://localhost:8000/api/health/redis') as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"  ✅ Redis: {data.get('status', 'connected')}")
                    return True
                else:
                    print(f"  ❌ Redis недоступен: {response.status}")
                    return False
    except Exception as e:
        print(f"  ❌ Ошибка подключения к Redis: {e}")
        return False

async def test_remnawave_integration():
    """Тестирование интеграции с Remnawave"""
    print("\n🔌 Тестирование интеграции с Remnawave...")
    
    try:
        # Тестируем через API endpoint
        async with aiohttp.ClientSession() as session:
            async with session.get('http://localhost:8000/api/health/remnawave') as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"  ✅ Remnawave: {data.get('status', 'connected')}")
                    return True
                elif response.status == 404:
                    print("  ⚠️ Remnawave health endpoint не найден (нормально для тестового окружения)")
                    return True
                else:
                    print(f"  ❌ Remnawave недоступен: {response.status}")
                    return False
    except Exception as e:
        print(f"  ❌ Ошибка подключения к Remnawave: {e}")
        return False

def test_file_structure():
    """Тестирование структуры файлов"""
    print("\n📁 Тестирование структуры файлов...")
    
    project_root = Path(__file__).parent.parent
    
    critical_files = [
        'bot/utils/remnawave_api.py',
        'bot/services/vpn_panel_service.py',
        'bot/services/subscription_service.py',
        'bot/client_bot/keyboards/about.py',
        'bot/db/models.py',
        'bot/common/config.py'
    ]
    
    missing_files = []
    
    for file_path in critical_files:
        full_path = project_root / file_path
        if full_path.exists():
            print(f"  ✅ {file_path}")
        else:
            print(f"  ❌ {file_path} - отсутствует")
            missing_files.append(file_path)
    
    return len(missing_files) == 0

def test_docker_containers():
    """Тестирование Docker контейнеров"""
    print("\n🐳 Тестирование Docker контейнеров...")
    
    import subprocess
    
    try:
        # Получаем статус контейнеров
        result = subprocess.run(
            ['docker', 'compose', 'ps', '--format', 'json'],
            cwd=Path(__file__).parent.parent,
            capture_output=True,
            text=True
        )
        
        if result.returncode != 0:
            print(f"  ❌ Ошибка получения статуса контейнеров: {result.stderr}")
            return False
        
        containers = []
        for line in result.stdout.strip().split('\n'):
            if line.strip():
                try:
                    containers.append(json.loads(line))
                except json.JSONDecodeError:
                    continue
        
        required_services = ['api', 'postgres', 'redis']
        running_services = []
        
        for container in containers:
            service = container.get('Service', '')
            state = container.get('State', '')
            
            if service in required_services:
                if 'running' in state.lower() or 'up' in state.lower():
                    print(f"  ✅ {service}: {state}")
                    running_services.append(service)
                else:
                    print(f"  ❌ {service}: {state}")
        
        # Проверяем клиентский бот отдельно (может перезапускаться)
        client_bot_running = False
        for container in containers:
            if container.get('Service') == 'client-bot':
                state = container.get('State', '')
                if 'running' in state.lower() or 'up' in state.lower():
                    print(f"  ✅ client-bot: {state}")
                    client_bot_running = True
                else:
                    print(f"  ⚠️ client-bot: {state} (может перезапускаться)")
        
        return len(running_services) >= len(required_services)
        
    except Exception as e:
        print(f"  ❌ Ошибка тестирования контейнеров: {e}")
        return False

async def test_webhook_endpoints():
    """Тестирование webhook endpoints"""
    print("\n🔗 Тестирование webhook endpoints...")
    
    webhook_endpoints = [
        '/webhook/yookassa',
        '/webhook/cryptomus', 
        '/webhook/telegram'
    ]
    
    results = []
    
    try:
        async with aiohttp.ClientSession() as session:
            for endpoint in webhook_endpoints:
                try:
                    # Тестируем POST запрос (webhook должен отвечать)
                    async with session.post(
                        f'http://localhost:8000{endpoint}',
                        json={'test': 'data'}
                    ) as response:
                        # Webhook может вернуть 400, 422 или 200 - это нормально
                        if response.status in [200, 400, 422, 405]:
                            print(f"  ✅ {endpoint}: {response.status}")
                            results.append(True)
                        else:
                            print(f"  ❌ {endpoint}: {response.status}")
                            results.append(False)
                except Exception as e:
                    print(f"  ❌ {endpoint}: {e}")
                    results.append(False)
    except Exception as e:
        print(f"  ❌ Webhook endpoints test error: {e}")
        return False
    
    return len([r for r in results if r]) >= len(results) // 2  # Половина должна работать

async def test_landing_page():
    """Тестирование лендинговой страницы"""
    print("\n🌐 Тестирование лендинговой страницы...")
    
    try:
        async with aiohttp.ClientSession() as session:
            # Проверяем доступность лендинга через API
            async with session.get('http://localhost:8000/') as response:
                if response.status == 200:
                    print("  ✅ Лендинговая страница доступна")
                    return True
                elif response.status == 404:
                    print("  ⚠️ Лендинговая страница не настроена (нормально)")
                    return True
                else:
                    print(f"  ❌ Лендинговая страница: {response.status}")
                    return False
    except Exception as e:
        print(f"  ❌ Ошибка тестирования лендинга: {e}")
        return False

async def main():
    """Основная функция функционального тестирования"""
    print("🚀 Функциональное тестирование UnveilVPN Shop")
    print("=" * 60)
    
    tests = [
        ("Структура файлов", test_file_structure),
        ("Docker контейнеры", test_docker_containers),
        ("API health check", test_api_health),
        ("API endpoints", test_api_endpoints),
        ("База данных", test_database_connection),
        ("Redis", test_redis_connection),
        ("Remnawave интеграция", test_remnawave_integration),
        ("Webhook endpoints", test_webhook_endpoints),
        ("Лендинговая страница", test_landing_page)
    ]
    
    passed_tests = 0
    failed_tests = []
    
    for test_name, test_func in tests:
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            
            if result:
                passed_tests += 1
            else:
                failed_tests.append(test_name)
        except Exception as e:
            print(f"  ❌ Критическая ошибка в тесте '{test_name}': {e}")
            failed_tests.append(test_name)
    
    # Итоговый отчет
    print("\n" + "=" * 60)
    print("📊 ИТОГОВЫЙ ОТЧЕТ ФУНКЦИОНАЛЬНОГО ТЕСТИРОВАНИЯ")
    print("=" * 60)
    
    total_tests = len(tests)
    success_rate = (passed_tests / total_tests) * 100
    
    print(f"✅ Пройдено тестов: {passed_tests}/{total_tests} ({success_rate:.1f}%)")
    
    if failed_tests:
        print(f"❌ Неудачные тесты: {', '.join(failed_tests)}")
    
    if success_rate >= 90:
        print("🎉 ОТЛИЧНАЯ ФУНКЦИОНАЛЬНОСТЬ!")
        return 0
    elif success_rate >= 75:
        print("✅ Хорошая функциональность")
        return 0
    elif success_rate >= 50:
        print("⚠️ Удовлетворительная функциональность")
        return 1
    else:
        print("❌ Критические проблемы с функциональностью")
        return 2

if __name__ == "__main__":
    exit(asyncio.run(main()))
