#!/usr/bin/env python3
"""
UnveilVPN Shop - Security Audit
Аудит безопасности интеграции с Remnawave API
"""

import re
import os
from pathlib import Path

def check_hardcoded_secrets():
    """Проверка на захардкоженные секреты"""
    print("🔐 Проверка на захардкоженные секреты...")
    
    project_root = Path(__file__).parent.parent
    
    # Паттерны для поиска секретов
    secret_patterns = [
        (r'password\s*=\s*["\'][^"\']+["\']', 'Пароль'),
        (r'api_key\s*=\s*["\'][^"\']+["\']', 'API ключ'),
        (r'secret\s*=\s*["\'][^"\']+["\']', 'Секрет'),
        (r'token\s*=\s*["\'][^"\']+["\']', 'Токен'),
        (r'["\'][a-zA-Z0-9]{32,}["\']', 'Возможный ключ'),
        (r'mysql://[^"\']+', 'MySQL строка подключения'),
        (r'postgresql://[^"\']+', 'PostgreSQL строка подключения'),
        (r'redis://[^"\']+', 'Redis строка подключения')
    ]
    
    # Исключаемые файлы
    exclude_files = [
        '.env.example',
        '.env.prod.example',
        'security_audit.py',
        'test_',
        '__pycache__'
    ]
    
    found_secrets = []
    
    # Проверяем Python файлы
    for py_file in project_root.rglob('*.py'):
        if any(exclude in str(py_file) for exclude in exclude_files):
            continue
            
        try:
            content = py_file.read_text(encoding='utf-8')
            
            for pattern, secret_type in secret_patterns:
                matches = re.finditer(pattern, content, re.IGNORECASE)
                for match in matches:
                    # Исключаем очевидно безопасные значения
                    matched_text = match.group()
                    if any(safe in matched_text.lower() for safe in [
                        'example', 'test', 'demo', 'placeholder', 'your_', 'xxx',
                        '{password}', '{host}', '{port}', '{db_', 'f"', "f'"
                    ]):
                        continue
                    
                    found_secrets.append({
                        'file': py_file.relative_to(project_root),
                        'type': secret_type,
                        'line': content[:match.start()].count('\n') + 1,
                        'text': matched_text[:50] + '...' if len(matched_text) > 50 else matched_text
                    })
                    
        except Exception as e:
            print(f"  ⚠️ Не удалось прочитать файл {py_file}: {e}")
    
    if found_secrets:
        print(f"  ❌ Найдено потенциальных секретов: {len(found_secrets)}")
        for secret in found_secrets[:5]:  # Показываем первые 5
            print(f"    {secret['file']}:{secret['line']} - {secret['type']}: {secret['text']}")
        if len(found_secrets) > 5:
            print(f"    ... и еще {len(found_secrets) - 5}")
    else:
        print("  ✅ Захардкоженные секреты не найдены")
    
    return found_secrets

def check_environment_variables():
    """Проверка переменных окружения"""
    print("\n🌍 Проверка переменных окружения...")
    
    project_root = Path(__file__).parent.parent
    
    # Проверяем .env.example
    env_example = project_root / '.env.example'
    
    if not env_example.exists():
        print("  ❌ .env.example не найден")
        return False
    
    content = env_example.read_text()
    
    # Критически важные переменные для безопасности
    security_vars = [
        'REMNAWAVE_API_KEY',
        'DB_PASS',  # DATABASE_PASSWORD в проекте называется DB_PASS
        'REDIS_PASSWORD',
        'WEBHOOK_SECRET',
        'YOOKASSA_SECRET_KEY',
        'CRYPTOMUS_API_KEY'
    ]
    
    security_issues = []
    
    for var in security_vars:
        if var in content:
            # Проверяем, что переменная не имеет реального значения
            pattern = rf'{var}\s*=\s*(.+)'
            match = re.search(pattern, content)
            if match:
                value = match.group(1).strip()
                if value and not any(placeholder in value.lower() for placeholder in [
                    'your_', 'example', 'change_me', 'replace_', 'xxx', 'test'
                ]):
                    security_issues.append(f"{var} содержит реальное значение")
                else:
                    print(f"  ✅ {var} - placeholder")
            else:
                print(f"  ⚠️ {var} - не найден")
        else:
            security_issues.append(f"{var} отсутствует")
    
    if security_issues:
        print(f"  ❌ Проблемы безопасности:")
        for issue in security_issues:
            print(f"    - {issue}")
        return False
    else:
        print("  ✅ Переменные окружения настроены безопасно")
        return True

def check_api_security():
    """Проверка безопасности API"""
    print("\n🔒 Проверка безопасности API...")
    
    project_root = Path(__file__).parent.parent
    
    # Проверяем файлы API
    api_files = [
        'bot/utils/remnawave_api.py',
        'bot/api/config.py',
        'bot/app/routes.py'
    ]
    
    security_features = {
        'SSL/TLS проверка': False,
        'Валидация входных данных': False,
        'Обработка ошибок': False,
        'Таймауты запросов': False,
        'Retry логика': False,
        'Логирование безопасности': False
    }
    
    for file_path in api_files:
        full_path = project_root / file_path
        if full_path.exists():
            try:
                content = full_path.read_text(encoding='utf-8')
                
                # Проверяем наличие функций безопасности
                if 'ssl' in content.lower() or 'tls' in content.lower():
                    security_features['SSL/TLS проверка'] = True
                
                if 'validate' in content.lower() or 'pydantic' in content.lower():
                    security_features['Валидация входных данных'] = True
                
                if 'except' in content and 'error' in content.lower():
                    security_features['Обработка ошибок'] = True
                
                if 'timeout' in content.lower():
                    security_features['Таймауты запросов'] = True
                
                if 'retry' in content.lower() or 'attempt' in content.lower():
                    security_features['Retry логика'] = True
                
                if 'logging' in content and ('security' in content.lower() or 'audit' in content.lower()):
                    security_features['Логирование безопасности'] = True
                    
            except Exception as e:
                print(f"  ⚠️ Не удалось прочитать файл {file_path}: {e}")
    
    # Отчет по функциям безопасности
    for feature, implemented in security_features.items():
        status = "✅" if implemented else "❌"
        print(f"  {status} {feature}")
    
    implemented_count = sum(security_features.values())
    total_count = len(security_features)
    security_score = (implemented_count / total_count) * 100
    
    print(f"\n  📊 Оценка безопасности API: {security_score:.1f}% ({implemented_count}/{total_count})")
    
    return security_score

def check_input_validation():
    """Проверка валидации входных данных"""
    print("\n✅ Проверка валидации входных данных...")
    
    project_root = Path(__file__).parent.parent
    
    # Файлы для проверки
    validation_files = [
        'bot/utils/remnawave_api.py',
        'bot/services/vpn_panel_service.py',
        'bot/handlers/messages.py'
    ]
    
    validation_patterns = [
        (r'if\s+not\s+\w+:', 'Проверка на пустые значения'),
        (r'isinstance\s*\(', 'Проверка типов'),
        (r'len\s*\(\s*\w+\s*\)\s*[<>]=?', 'Проверка длины'),
        (r'validate\w*\s*\(', 'Функции валидации'),
        (r'raise\s+ValueError', 'Исключения валидации'),
        (r'@validator', 'Pydantic валидаторы')
    ]
    
    validation_found = {}
    
    for file_path in validation_files:
        full_path = project_root / file_path
        if full_path.exists():
            try:
                content = full_path.read_text(encoding='utf-8')
                file_validations = []
                
                for pattern, description in validation_patterns:
                    matches = len(re.findall(pattern, content, re.IGNORECASE))
                    if matches > 0:
                        file_validations.append(f"{description}: {matches}")
                
                validation_found[file_path] = file_validations
                
                if file_validations:
                    print(f"  ✅ {file_path}:")
                    for validation in file_validations:
                        print(f"    - {validation}")
                else:
                    print(f"  ⚠️ {file_path}: валидация не найдена")
                    
            except Exception as e:
                print(f"  ❌ Ошибка чтения {file_path}: {e}")
    
    return validation_found

def check_logging_security():
    """Проверка безопасности логирования"""
    print("\n📝 Проверка безопасности логирования...")
    
    project_root = Path(__file__).parent.parent
    
    # Поиск потенциально небезопасного логирования
    unsafe_logging_patterns = [
        (r'log\w*\([^)]*password[^)]*\)', 'Логирование паролей'),
        (r'log\w*\([^)]*api_key[^)]*\)', 'Логирование API ключей'),
        (r'log\w*\([^)]*secret[^)]*\)', 'Логирование секретов'),
        (r'log\w*\([^)]*token[^)]*\)', 'Логирование токенов'),
        (r'print\([^)]*password[^)]*\)', 'Print паролей'),
        (r'print\([^)]*api_key[^)]*\)', 'Print API ключей')
    ]
    
    unsafe_logging = []
    
    for py_file in project_root.rglob('*.py'):
        if 'test_' in str(py_file) or '__pycache__' in str(py_file):
            continue
            
        try:
            content = py_file.read_text(encoding='utf-8')
            
            for pattern, issue_type in unsafe_logging_patterns:
                matches = re.finditer(pattern, content, re.IGNORECASE)
                for match in matches:
                    line_num = content[:match.start()].count('\n') + 1
                    unsafe_logging.append({
                        'file': py_file.relative_to(project_root),
                        'line': line_num,
                        'type': issue_type,
                        'text': match.group()[:100]
                    })
                    
        except Exception as e:
            print(f"  ⚠️ Не удалось прочитать файл {py_file}: {e}")
    
    if unsafe_logging:
        print(f"  ❌ Найдено небезопасное логирование: {len(unsafe_logging)}")
        for log_issue in unsafe_logging[:3]:  # Показываем первые 3
            print(f"    {log_issue['file']}:{log_issue['line']} - {log_issue['type']}")
    else:
        print("  ✅ Небезопасное логирование не найдено")
    
    return unsafe_logging

def check_dependency_security():
    """Проверка безопасности зависимостей"""
    print("\n📦 Проверка безопасности зависимостей...")
    
    project_root = Path(__file__).parent.parent
    
    # Проверяем requirements.txt
    req_files = ['requirements.txt', 'requirements-prod.txt']
    
    security_recommendations = []
    
    for req_file in req_files:
        req_path = project_root / req_file
        if req_path.exists():
            try:
                content = req_path.read_text()
                
                # Проверяем на закрепленные версии
                lines = content.strip().split('\n')
                unpinned_deps = []
                
                for line in lines:
                    line = line.strip()
                    if line and not line.startswith('#'):
                        if '==' not in line and '>=' not in line:
                            unpinned_deps.append(line)
                
                if unpinned_deps:
                    print(f"  ⚠️ {req_file}: незакрепленные версии:")
                    for dep in unpinned_deps[:3]:
                        print(f"    - {dep}")
                    security_recommendations.append(f"Закрепить версии в {req_file}")
                else:
                    print(f"  ✅ {req_file}: все версии закреплены")
                    
            except Exception as e:
                print(f"  ❌ Ошибка чтения {req_file}: {e}")
    
    # Общие рекомендации по безопасности
    general_recommendations = [
        "Регулярно обновляйте зависимости",
        "Используйте инструменты проверки уязвимостей (safety, bandit)",
        "Настройте автоматические обновления безопасности",
        "Проводите аудит зависимостей перед релизом"
    ]
    
    print(f"\n  📋 Рекомендации по безопасности:")
    for rec in general_recommendations:
        print(f"    • {rec}")
    
    return security_recommendations

def main():
    """Основная функция аудита безопасности"""
    print("🔒 Аудит безопасности Remnawave интеграции")
    print("=" * 60)
    
    security_issues = []
    
    # Проверка захардкоженных секретов
    hardcoded_secrets = check_hardcoded_secrets()
    if hardcoded_secrets:
        security_issues.extend(hardcoded_secrets)
    
    # Проверка переменных окружения
    env_secure = check_environment_variables()
    if not env_secure:
        security_issues.append("Проблемы с переменными окружения")
    
    # Проверка безопасности API
    api_security_score = check_api_security()
    if api_security_score < 80:
        security_issues.append(f"Низкая оценка безопасности API: {api_security_score:.1f}%")
    
    # Проверка валидации входных данных
    validation_found = check_input_validation()
    
    # Проверка безопасности логирования
    unsafe_logging = check_logging_security()
    if unsafe_logging:
        security_issues.extend([f"Небезопасное логирование: {log['type']}" for log in unsafe_logging])
    
    # Проверка зависимостей
    dep_recommendations = check_dependency_security()
    
    # Итоговый отчет
    print("\n" + "=" * 60)
    print("📊 ИТОГОВЫЙ ОТЧЕТ БЕЗОПАСНОСТИ")
    print("=" * 60)
    
    if not security_issues:
        print("🎉 ОТЛИЧНЫЙ УРОВЕНЬ БЕЗОПАСНОСТИ!")
        print("✅ Критических проблем безопасности не найдено")
        return 0
    else:
        print(f"⚠️ Найдено проблем безопасности: {len(security_issues)}")
        print("\n❌ ПРОБЛЕМЫ:")
        for i, issue in enumerate(security_issues[:10], 1):
            if isinstance(issue, dict):
                print(f"  {i}. {issue['type']} в {issue['file']}")
            else:
                print(f"  {i}. {issue}")
        
        if len(security_issues) > 10:
            print(f"  ... и еще {len(security_issues) - 10}")
        
        print("\n🔧 РЕКОМЕНДАЦИИ:")
        print("  • Удалите все захардкоженные секреты")
        print("  • Используйте переменные окружения для всех секретов")
        print("  • Реализуйте валидацию всех входных данных")
        print("  • Настройте безопасное логирование")
        print("  • Регулярно обновляйте зависимости")
        
        return 1

if __name__ == "__main__":
    exit(main())
