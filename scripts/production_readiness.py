#!/usr/bin/env python3
"""
UnveilVPN Shop - Production Readiness Check
Финальная проверка готовности к продакшену после миграции на Remnawave
"""

import os
import sys
from pathlib import Path

def check_environment_variables():
    """Проверка переменных окружения для продакшена"""
    print("🌍 Проверка переменных окружения...")
    
    required_vars = {
        'REMNAWAVE_PANEL_URL': 'URL панели Remnawave',
        'REMNAWAVE_API_KEY': 'API ключ Remnawave',
        'DATABASE_URL': 'Строка подключения к PostgreSQL',
        'REDIS_URL': 'Строка подключения к Redis',
        'TELEGRAM_BOT_TOKEN': 'Токен клиентского бота',
        'ADMIN_BOT_TOKEN': 'Токен админ бота',
        'SUPPORT_BOT_TOKEN': 'Токен бота поддержки'
    }
    
    missing_vars = []
    configured_vars = []
    
    for var, description in required_vars.items():
        value = os.getenv(var)
        if not value:
            missing_vars.append(f"{var} - {description}")
        elif value in ['your_', 'example', 'change_me', 'replace_']:
            missing_vars.append(f"{var} - содержит placeholder")
        else:
            configured_vars.append(var)
            print(f"  ✅ {var}")
    
    if missing_vars:
        print(f"  ❌ Не настроены переменные:")
        for var in missing_vars:
            print(f"    - {var}")
        return False
    
    print(f"  ✅ Все переменные настроены ({len(configured_vars)}/{len(required_vars)})")
    return True

def check_file_structure():
    """Проверка структуры файлов"""
    print("\n📁 Проверка структуры файлов...")
    
    project_root = Path(__file__).parent.parent
    
    critical_files = [
        'bot/utils/remnawave_api.py',
        'bot/services/vpn_panel_service.py',
        'bot/services/subscription_service.py',
        'bot/services/payment_vpn_integration.py',
        'docker-compose.yml',
        'docker-compose.prod.yml',
        'requirements.txt'
    ]
    
    missing_files = []
    
    for file_path in critical_files:
        if not (project_root / file_path).exists():
            missing_files.append(file_path)
        else:
            print(f"  ✅ {file_path}")
    
    if missing_files:
        print(f"  ❌ Отсутствуют критические файлы:")
        for file_path in missing_files:
            print(f"    - {file_path}")
        return False
    
    return True

def check_docker_configuration():
    """Проверка Docker конфигурации"""
    print("\n🐳 Проверка Docker конфигурации...")
    
    project_root = Path(__file__).parent.parent
    
    # Проверяем docker-compose.prod.yml
    prod_compose = project_root / 'docker-compose.prod.yml'
    if not prod_compose.exists():
        print("  ❌ docker-compose.prod.yml не найден")
        return False
    
    content = prod_compose.read_text()
    
    # Проверяем наличие продакшен настроек
    prod_checks = [
        ('restart: unless-stopped', 'Политика перезапуска'),
        ('REMNAWAVE_PANEL_URL', 'Remnawave URL'),
        ('REMNAWAVE_API_KEY', 'Remnawave API ключ'),
        ('healthcheck:', 'Health checks')
    ]
    
    missing_configs = []
    
    for check, description in prod_checks:
        if check in content:
            print(f"  ✅ {description}")
        else:
            missing_configs.append(description)
    
    if missing_configs:
        print(f"  ⚠️ Отсутствуют настройки:")
        for config in missing_configs:
            print(f"    - {config}")
    
    return len(missing_configs) == 0

def check_database_migration():
    """Проверка готовности базы данных"""
    print("\n🗄️ Проверка готовности базы данных...")
    
    project_root = Path(__file__).parent.parent
    
    # Проверяем модели
    models_file = project_root / 'bot/db/models.py'
    if not models_file.exists():
        print("  ❌ Файл моделей не найден")
        return False
    
    content = models_file.read_text()
    
    # Проверяем наличие Remnawave полей
    remnawave_fields = [
        'remnawave_user_id',
        'remnawave_subscription_id'
    ]
    
    missing_fields = []
    
    for field in remnawave_fields:
        if field in content:
            print(f"  ✅ Поле {field} присутствует")
        else:
            missing_fields.append(field)
    
    if missing_fields:
        print(f"  ❌ Отсутствуют поля:")
        for field in missing_fields:
            print(f"    - {field}")
        return False
    
    return True

def check_api_endpoints():
    """Проверка API endpoints"""
    print("\n🔌 Проверка API endpoints...")
    
    project_root = Path(__file__).parent.parent
    
    # Проверяем routes.py
    routes_file = project_root / 'bot/app/routes.py'
    if not routes_file.exists():
        print("  ❌ Файл routes не найден")
        return False
    
    content = routes_file.read_text()
    
    # Проверяем наличие основных endpoints
    endpoints = [
        ('/webhook', 'Webhook endpoint'),
        ('/health', 'Health check'),
        ('/api/', 'API endpoints')
    ]
    
    missing_endpoints = []
    
    for endpoint, description in endpoints:
        if endpoint in content:
            print(f"  ✅ {description}")
        else:
            missing_endpoints.append(description)
    
    if missing_endpoints:
        print(f"  ⚠️ Возможно отсутствуют endpoints:")
        for endpoint in missing_endpoints:
            print(f"    - {endpoint}")
    
    return len(missing_endpoints) == 0

def check_security_configuration():
    """Проверка настроек безопасности"""
    print("\n🔒 Проверка настроек безопасности...")
    
    project_root = Path(__file__).parent.parent
    
    security_checks = []
    
    # Проверяем .env.example
    env_example = project_root / '.env.example'
    if env_example.exists():
        content = env_example.read_text()
        
        # Проверяем, что секреты являются placeholders
        security_vars = ['API_KEY', 'SECRET', 'PASSWORD', 'TOKEN']
        
        for var in security_vars:
            if var in content:
                # Ищем строки с этими переменными
                lines = [line for line in content.split('\n') if var in line and '=' in line]
                for line in lines:
                    value = line.split('=', 1)[1].strip()
                    if value and not any(placeholder in value.lower() for placeholder in [
                        'your_', 'example', 'change_me', 'replace_', 'xxx'
                    ]):
                        security_checks.append(f"Возможно реальное значение в {line}")
    
    if security_checks:
        print(f"  ⚠️ Проблемы безопасности:")
        for check in security_checks:
            print(f"    - {check}")
    else:
        print("  ✅ Настройки безопасности корректны")
    
    return len(security_checks) == 0

def check_monitoring_readiness():
    """Проверка готовности мониторинга"""
    print("\n📊 Проверка готовности мониторинга...")
    
    project_root = Path(__file__).parent.parent
    
    # Проверяем наличие логирования
    logging_files = [
        'bot/common/config.py',
        'bot/utils/remnawave_api.py'
    ]
    
    logging_configured = False
    
    for file_path in logging_files:
        full_path = project_root / file_path
        if full_path.exists():
            content = full_path.read_text()
            if 'logging' in content.lower():
                logging_configured = True
                break
    
    if logging_configured:
        print("  ✅ Логирование настроено")
    else:
        print("  ⚠️ Логирование может быть не настроено")
    
    # Проверяем health checks
    health_check_files = [
        'bot/app/routes.py',
        'docker-compose.prod.yml'
    ]
    
    health_checks = False
    
    for file_path in health_check_files:
        full_path = project_root / file_path
        if full_path.exists():
            content = full_path.read_text()
            if 'health' in content.lower():
                health_checks = True
                break
    
    if health_checks:
        print("  ✅ Health checks настроены")
    else:
        print("  ⚠️ Health checks могут отсутствовать")
    
    return logging_configured and health_checks

def generate_deployment_checklist():
    """Генерация чеклиста для развертывания"""
    print("\n📋 Чеклист для развертывания:")
    
    checklist = [
        "[ ] Настроить переменные окружения в продакшене",
        "[ ] Создать базу данных PostgreSQL",
        "[ ] Настроить Redis",
        "[ ] Настроить Remnawave панель",
        "[ ] Получить API ключ Remnawave",
        "[ ] Настроить домены и SSL сертификаты",
        "[ ] Запустить миграции базы данных",
        "[ ] Провести smoke тесты",
        "[ ] Настроить мониторинг и алерты",
        "[ ] Настроить бэкапы базы данных",
        "[ ] Проверить все webhook endpoints",
        "[ ] Протестировать платежные системы",
        "[ ] Проверить работу всех ботов",
        "[ ] Настроить логирование и ротацию логов"
    ]
    
    for item in checklist:
        print(f"  {item}")

def main():
    """Основная функция проверки готовности"""
    print("🚀 Проверка готовности к продакшену - Remnawave Migration")
    print("=" * 70)
    
    checks = [
        ("Переменные окружения", check_environment_variables),
        ("Структура файлов", check_file_structure),
        ("Docker конфигурация", check_docker_configuration),
        ("База данных", check_database_migration),
        ("API endpoints", check_api_endpoints),
        ("Безопасность", check_security_configuration),
        ("Мониторинг", check_monitoring_readiness)
    ]
    
    passed_checks = 0
    total_checks = len(checks)
    
    for check_name, check_func in checks:
        try:
            if check_func():
                passed_checks += 1
        except Exception as e:
            print(f"  ❌ Ошибка в проверке '{check_name}': {e}")
    
    # Генерируем чеклист
    generate_deployment_checklist()
    
    # Итоговый отчет
    print("\n" + "=" * 70)
    print("📊 ИТОГОВЫЙ ОТЧЕТ ГОТОВНОСТИ")
    print("=" * 70)
    
    readiness_score = (passed_checks / total_checks) * 100
    
    print(f"📈 Готовность к продакшену: {readiness_score:.1f}% ({passed_checks}/{total_checks})")
    
    if readiness_score >= 90:
        print("🎉 СИСТЕМА ГОТОВА К ПРОДАКШЕНУ!")
        print("✅ Можно приступать к развертыванию")
        return 0
    elif readiness_score >= 75:
        print("⚠️ Система почти готова, но есть проблемы для исправления")
        return 1
    else:
        print("❌ Система НЕ ГОТОВА к продакшену")
        print("🔧 Необходимо исправить критические проблемы")
        return 2

if __name__ == "__main__":
    sys.exit(main())
