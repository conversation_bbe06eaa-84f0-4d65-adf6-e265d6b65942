#!/bin/bash
# =============================================================================
# UnveilVPN Shop - Production Deployment Script
# Полное автоматизированное развертывание production системы
# =============================================================================

set -euo pipefail

# Цвета для вывода
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Функции для вывода
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${PURPLE}[STEP]${NC} $1"
}

# Конфигурация
PROJECT_NAME="unveilvpn-shop"
COMPOSE_FILE="docker-compose.yml"
ENV_FILE=".env"
BACKUP_DIR="./backups/$(date +%Y%m%d_%H%M%S)"

# Проверка зависимостей
check_dependencies() {
    log_step "Проверка зависимостей..."
    
    local deps=("docker" "curl" "openssl")
    for dep in "${deps[@]}"; do
        if ! command -v "$dep" &> /dev/null; then
            log_error "Зависимость $dep не найдена. Установите её и повторите попытку."
            exit 1
        fi
    done
    
    # Проверка версии Docker Compose
    if docker compose version &> /dev/null; then
        COMPOSE_CMD="docker compose"
    elif docker-compose --version &> /dev/null; then
        COMPOSE_CMD="docker-compose"
    else
        log_error "Docker Compose не найден"
        exit 1
    fi
    
    log_success "Все зависимости найдены"
}

# Проверка конфигурации
check_configuration() {
    log_step "Проверка конфигурации..."
    
    # Проверка .env файла
    if [[ ! -f "$ENV_FILE" ]]; then
        log_error "Файл $ENV_FILE не найден"
        log_info "Запустите: ./scripts/generate_production_secrets.sh"
        exit 1
    fi
    
    # Проверка docker-compose.yml
    if [[ ! -f "$COMPOSE_FILE" ]]; then
        log_error "Файл $COMPOSE_FILE не найден"
        exit 1
    fi
    
    # Валидация Docker Compose конфигурации
    if ! $COMPOSE_CMD config --quiet; then
        log_error "Ошибка в конфигурации Docker Compose"
        exit 1
    fi
    
    # Проверка переменных окружения
    log_info "Проверка переменных окружения..."
    if python3 scripts/check_env_vars.py "$ENV_FILE" --quiet; then
        log_success "Конфигурация валидна"
    else
        log_warning "Найдены проблемы в конфигурации, но продолжаем..."
    fi
}

# Создание резервной копии
create_backup() {
    log_step "Создание резервной копии..."
    
    mkdir -p "$BACKUP_DIR"
    
    # Бэкап конфигурации
    cp "$ENV_FILE" "$BACKUP_DIR/"
    cp "$COMPOSE_FILE" "$BACKUP_DIR/"
    
    # Бэкап базы данных (если контейнер запущен)
    if docker ps --format "table {{.Names}}" | grep -q "unveilvpn-postgres-prod"; then
        log_info "Создание бэкапа базы данных..."
        docker exec unveilvpn-postgres-prod pg_dump -U unveilvpn_user unveilvpn_prod > "$BACKUP_DIR/database_backup.sql" || true
        log_success "Бэкап базы данных создан"
    else
        log_info "База данных не запущена, пропускаем бэкап"
    fi
    
    log_success "Резервная копия создана: $BACKUP_DIR"
}

# Остановка старых контейнеров
stop_old_containers() {
    log_step "Остановка старых контейнеров..."
    
    if $COMPOSE_CMD ps --services --filter "status=running" | grep -q .; then
        log_info "Остановка запущенных сервисов..."
        $COMPOSE_CMD down --timeout 30
        log_success "Контейнеры остановлены"
    else
        log_info "Запущенные контейнеры не найдены"
    fi
}

# Сборка образов
build_images() {
    log_step "Сборка Docker образов..."
    
    log_info "Сборка образов (это может занять несколько минут)..."
    $COMPOSE_CMD build --no-cache --parallel
    
    log_success "Образы собраны"
}

# Настройка SSL сертификатов
setup_ssl() {
    log_step "Настройка SSL сертификатов..."
    
    if [[ "${SKIP_SSL:-false}" == "true" ]]; then
        log_warning "Пропуск настройки SSL (SKIP_SSL=true)"
        ./scripts/setup_ssl.sh --test
    else
        log_info "Настройка production SSL сертификатов..."
        if [[ -n "${CF_Token:-}" && -n "${CF_Account_ID:-}" && -n "${CF_Zone_ID:-}" ]]; then
            ./scripts/setup_ssl.sh
        else
            log_warning "Переменные Cloudflare не настроены, создание тестового сертификата..."
            ./scripts/setup_ssl.sh --test
        fi
    fi
    
    log_success "SSL сертификаты настроены"
}

# Запуск сервисов
start_services() {
    log_step "Запуск сервисов..."
    
    # Запуск базовых сервисов (база данных, Redis)
    log_info "Запуск базовых сервисов..."
    $COMPOSE_CMD up -d postgres redis
    
    # Ожидание готовности базовых сервисов
    log_info "Ожидание готовности базовых сервисов..."
    wait_for_service "postgres" "PostgreSQL"
    wait_for_service "redis" "Redis"
    
    # Запуск приложений
    log_info "Запуск приложений..."
    $COMPOSE_CMD up -d api landing
    
    # Ожидание готовности приложений
    wait_for_service "api" "FastAPI"
    wait_for_service "landing" "Landing"
    
    # Запуск Nginx
    log_info "Запуск Nginx..."
    $COMPOSE_CMD up -d nginx
    wait_for_service "nginx" "Nginx"
    
    # Запуск ботов
    log_info "Запуск Telegram ботов..."
    $COMPOSE_CMD up -d client-bot admin-bot support-bot
    
    log_success "Все сервисы запущены"
}

# Ожидание готовности сервиса
wait_for_service() {
    local service_name="$1"
    local display_name="$2"
    local max_attempts=30
    local attempt=1
    
    log_info "Ожидание готовности $display_name..."
    
    while [[ $attempt -le $max_attempts ]]; do
        if $COMPOSE_CMD ps "$service_name" --format "table {{.Health}}" | grep -q "healthy"; then
            log_success "$display_name готов"
            return 0
        fi
        
        if [[ $attempt -eq $max_attempts ]]; then
            log_error "$display_name не готов после $max_attempts попыток"
            return 1
        fi
        
        echo -n "."
        sleep 2
        ((attempt++))
    done
}

# Проверка работоспособности
health_check() {
    log_step "Проверка работоспособности системы..."
    
    local checks_passed=0
    local total_checks=5
    
    # Проверка Nginx
    if curl -f -s http://localhost/health > /dev/null; then
        log_success "✓ Nginx отвечает"
        ((checks_passed++))
    else
        log_error "✗ Nginx не отвечает"
    fi
    
    # Проверка API
    if curl -f -s http://localhost:8000/health > /dev/null 2>&1 || curl -f -s https://api.unveilvpn.com/health > /dev/null 2>&1; then
        log_success "✓ API отвечает"
        ((checks_passed++))
    else
        log_error "✗ API не отвечает"
    fi
    
    # Проверка базы данных
    if $COMPOSE_CMD exec -T postgres pg_isready -U unveilvpn_user > /dev/null; then
        log_success "✓ PostgreSQL работает"
        ((checks_passed++))
    else
        log_error "✗ PostgreSQL не работает"
    fi
    
    # Проверка Redis
    if $COMPOSE_CMD exec -T redis redis-cli ping | grep -q "PONG"; then
        log_success "✓ Redis работает"
        ((checks_passed++))
    else
        log_error "✗ Redis не работает"
    fi
    
    # Проверка ботов
    local bot_count=$($COMPOSE_CMD ps --filter "name=bot" --format "table {{.Names}}" | wc -l)
    if [[ $bot_count -ge 3 ]]; then
        log_success "✓ Telegram боты запущены ($bot_count)"
        ((checks_passed++))
    else
        log_error "✗ Не все Telegram боты запущены ($bot_count/3)"
    fi
    
    # Итоговая оценка
    log_info "Проверок пройдено: $checks_passed/$total_checks"
    
    if [[ $checks_passed -eq $total_checks ]]; then
        log_success "Все проверки пройдены успешно!"
        return 0
    else
        log_warning "Некоторые проверки не пройдены"
        return 1
    fi
}

# Показать статус системы
show_status() {
    log_step "Статус системы:"
    
    echo ""
    echo "🐳 Docker контейнеры:"
    $COMPOSE_CMD ps
    
    echo ""
    echo "🌐 Доступные URL:"
    echo "  - Основной сайт: https://www.unveilvpn.com"
    echo "  - API: https://api.unveilvpn.com"
    echo "  - Подписки: https://sub.unveilvpn.com"
    echo "  - Health check: http://localhost/health"
    
    echo ""
    echo "📊 Использование ресурсов:"
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}"
}

# Очистка при ошибке
cleanup_on_error() {
    log_error "Произошла ошибка во время развертывания"
    log_info "Остановка сервисов..."
    $COMPOSE_CMD down --timeout 30 || true
    exit 1
}

# Основная функция
main() {
    echo "============================================================================="
    echo "🚀 UnveilVPN Shop - Production Deployment"
    echo "============================================================================="
    
    # Установка обработчика ошибок
    trap cleanup_on_error ERR
    
    check_dependencies
    check_configuration
    
    # Создание бэкапа только если есть запущенные контейнеры
    if $COMPOSE_CMD ps --services --filter "status=running" | grep -q .; then
        create_backup
    fi
    
    stop_old_containers
    build_images
    setup_ssl
    start_services
    
    # Небольшая пауза перед проверкой
    log_info "Ожидание стабилизации системы..."
    sleep 10
    
    if health_check; then
        show_status
        
        echo "============================================================================="
        log_success "🎉 Production развертывание завершено успешно!"
        echo ""
        log_info "Следующие шаги:"
        echo "  1. Проверьте работу сайта: https://www.unveilvpn.com"
        echo "  2. Протестируйте API: https://api.unveilvpn.com/docs"
        echo "  3. Настройте мониторинг и алерты"
        echo "  4. Проведите нагрузочное тестирование"
        echo "============================================================================="
    else
        log_warning "Развертывание завершено с предупреждениями"
        log_info "Проверьте логи: $COMPOSE_CMD logs"
    fi
}

# Показать справку
show_help() {
    echo "Использование: $0 [опции]"
    echo ""
    echo "Переменные окружения:"
    echo "  SKIP_SSL=true     - Пропустить настройку SSL (использовать тестовый)"
    echo "  CF_Token          - Cloudflare Global API Key"
    echo "  CF_Account_ID     - Cloudflare Account ID"
    echo "  CF_Zone_ID        - Cloudflare Zone ID"
    echo ""
    echo "Примеры:"
    echo "  $0                          # Полное развертывание"
    echo "  SKIP_SSL=true $0            # Развертывание с тестовым SSL"
    echo ""
    echo "Требования:"
    echo "  - Docker и Docker Compose"
    echo "  - Файл .env с настройками"
    echo "  - Доступ к интернету"
}

# Обработка аргументов
case "${1:-}" in
    --help|-h)
        show_help
        exit 0
        ;;
    *)
        main "$@"
        ;;
esac
