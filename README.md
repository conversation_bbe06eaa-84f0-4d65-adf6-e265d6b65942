# 🚀 UnveilVPN Shop

Современная система продажи VPN подписок с тремя специализированными Telegram ботами, React лендингом, реферальной системой и системой поддержки. Интегрирована с **Remnawave VPN панелью**.

## 🏗️ Архитектура

### Компоненты системы
- **3 специализированных бота**: клиентский, админ, поддержка
- **PostgreSQL 16**: основная база данных с UUID и JSONB
- **Redis**: кэширование и сессии
- **React лендинг**: современная веб-страница
- **FastAPI**: REST API сервер
- **Remnawave VPN панель**: управление VPN пользователями и конфигурациями
- **Многоуровневая реферальная система**: 10%, 5%, 2%
- **Гибкая система промокодов**: процентные, фиксированные, дни
- **Тикетная система поддержки**: с категориями и приоритетами

### Платежные системы
- **YooKassa**: банковские карты, СБП
- **Cryptomus**: криптовалюты
- **Telegram Stars**: встроенные платежи

## 🚀 Быстрый старт

### Предварительные требования
- Docker и Docker Compose
- Git

### 1. Клонирование репозитория
```bash
git clone https://github.com/your-username/unveilvpn-shop.git
cd unveilvpn-shop
```

### 2. Настройка переменных окружения
```bash
cp .env.prod .env
# Отредактируйте .env файл с вашими настройками
python3 scripts/check_env_vars.py  # Проверка конфигурации
```

Отредактируйте `.env` файл, указав:
- Токены ботов от @BotFather
- Пароли для PostgreSQL и Redis
- Настройки платежных систем
- Параметры Remnawave VPN панели (URL и API ключ)

### 3. Запуск в режиме разработки
```bash
# Запуск только клиентского бота с базой данных
docker compose up postgres redis client-bot

# Запуск с pgAdmin для управления БД
docker compose up postgres redis pgadmin client-bot
```

### 4. Запуск всех компонентов
```bash
# Запуск всех ботов
docker compose --profile admin --profile support up

# Или запуск в фоне
docker compose --profile admin --profile support up -d
```

### 5. Запуск в продакшене
```bash
docker compose -f docker-compose.prod.yml up -d
```

## 🔧 Конфигурация

### PostgreSQL
- **Порт**: 5432 (по умолчанию)
- **База данных**: unveilvpn
- **Пользователь**: unveilvpn_user
- **Расширения**: uuid-ossp, pg_stat_statements, pg_trgm

### Redis
- **Порт**: 6379 (по умолчанию)
- **Режим**: persistent с AOF
- **Политика памяти**: allkeys-lru (в продакшене)

### pgAdmin
- **URL**: http://localhost:5050
- **Email**: <EMAIL> (по умолчанию)
- **Пароль**: настраивается в .env

## 📊 Мониторинг

### Логи
```bash
# Просмотр логов всех сервисов
docker compose logs -f

# Логи конкретного сервиса
docker compose logs -f client-bot
docker compose logs -f postgres
```

### Здоровье сервисов
```bash
# Проверка статуса
docker compose ps

# Проверка здоровья PostgreSQL
docker compose exec postgres pg_isready -U unveilvpn_user -d unveilvpn
```

## 🗄️ База данных

### Подключение к PostgreSQL
```bash
# Через Docker
docker compose exec postgres psql -U unveilvpn_user -d unveilvpn

# Локально (если установлен psql)
psql -h localhost -p 5432 -U unveilvpn_user -d unveilvpn
```

### Миграции
```bash
# Применение миграций
docker compose exec client-bot alembic upgrade head

# Создание новой миграции
docker compose exec client-bot alembic revision --autogenerate -m "Description"
```

### Резервное копирование
```bash
# Создание бэкапа
docker compose exec postgres pg_dump -U unveilvpn_user unveilvpn > backup.sql

# Восстановление
docker compose exec -T postgres psql -U unveilvpn_user unveilvpn < backup.sql
```

## 🔐 Безопасность

### Обязательные изменения для продакшена
1. Смените все пароли в `.env`
2. Настройте SSL сертификаты
3. Ограничьте доступ к pgAdmin
4. Настройте firewall
5. Включите мониторинг

### Рекомендации
- Используйте сильные пароли (минимум 16 символов)
- Регулярно обновляйте зависимости
- Настройте автоматические бэкапы
- Мониторьте логи на подозрительную активность

## 🛠️ Разработка

### Структура проекта
```
unveilvpn-shop/
├── bot/                    # Backend приложение
│   ├── client_bot/         # Клиентский бот
│   ├── admin_bot/          # Админ бот
│   ├── support_bot/        # Бот поддержки
│   ├── db/                 # Модели базы данных
│   ├── api/                # FastAPI сервер
│   └── common/             # Общие компоненты
├── landing/                # React лендинг
├── docker/                 # Docker файлы
├── postgresql/             # PostgreSQL конфигурация
└── tests/                  # Тесты
```

### Тестирование
```bash
# Запуск тестов
docker compose exec client-bot pytest

# Тесты с покрытием
docker compose exec client-bot pytest --cov=bot
```

## 📈 Масштабирование

### Горизонтальное масштабирование
```bash
# Запуск нескольких экземпляров клиентского бота
docker compose up --scale client-bot=3
```

### Мониторинг производительности
- Используйте pg_stat_statements для анализа запросов
- Мониторьте использование Redis
- Отслеживайте метрики ботов

## 🤝 Поддержка

### Документация
- [Техническая документация](docs/technical.md)
- [API документация](docs/api.md)
- [Руководство администратора](docs/admin.md)

### Сообщество
- [GitHub Issues](https://github.com/your-username/unveilvpn-shop/issues)
- [Telegram чат](https://t.me/unveilvpn_dev)

## 📄 Лицензия

MIT License - см. [LICENSE](LICENSE) файл для деталей.
