# =============================================================================
# UnveilVPN Shop - Production Environment Configuration
# Автоматически сгенерировано Чт 26 июн 2025 21:07:50 MSK
# =============================================================================

# =============================================================================
# ОСНОВНЫЕ НАСТРОЙКИ ОКРУЖЕНИЯ
# =============================================================================
ENVIRONMENT=production
DEBUG=false
LOG_LEVEL=INFO
TIMEZONE=Europe/Moscow

# =============================================================================
# БАЗА ДАННЫХ (PostgreSQL)
# =============================================================================
DB_HOST=postgres
DB_PORT=5432
DB_NAME=unveilvpn_prod
DB_USER=unveilvpn_user
DB_PASS=Wcxfh9nLpEA4RTzOWflwaGeC
DATABASE_URL=******************************************************************/unveilvpn_prod

# Настройки пула соединений
DB_POOL_SIZE=20
DB_MAX_OVERFLOW=30
DB_POOL_TIMEOUT=30
DB_POOL_RECYCLE=3600

# =============================================================================
# REDIS
# =============================================================================
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=LYYyv8w29CQpfq5V8Pl6sCzM
REDIS_DB=0
REDIS_URL=redis://:LYYyv8w29CQpfq5V8Pl6sCzM@redis:6379/0

# Настройки Redis
REDIS_POOL_SIZE=20
REDIS_SOCKET_TIMEOUT=5
REDIS_SOCKET_CONNECT_TIMEOUT=5

# =============================================================================
# REMNAWAVE VPN ПАНЕЛЬ
# =============================================================================
REMNAWAVE_PANEL_URL=https://panel.unveilvpn.com
REMNAWAVE_API_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1dWlkIjoiOTI5YTdmYTUtNDI5ZC00NzcwLTkyYzItMGQ1OWU2MThlNmM2IiwidXNlcm5hbWUiOm51bGwsInJvbGUiOiJBUEkiLCJpYXQiOjE3NTA5NTM2ODIsImV4cCI6MTAzOTA4NjcyODJ9.5v70qAHLCOejY-zu3u-kJlIxIZaL30pdqkH-fVXjz8k
REMNAWAVE_SUBSCRIPTION_URL=https://sub.unveilvpn.com
REMNAWAVE_PROTOCOLS=vless

# Настройки Remnawave
REMNAWAVE_TIMEOUT=30
REMNAWAVE_MAX_RETRIES=3
REMNAWAVE_RETRY_DELAY=5

# =============================================================================
# TELEGRAM БОТЫ
# =============================================================================
CLIENT_BOT_TOKEN=7831260337:AAFymdSqgHuOOSCjYBFexBXWeLUJXS5Ie20
ADMIN_BOT_TOKEN=7754328231:AAH-9SP-aEyrtG57PuH7A_MFiUfoT5aKtBM
SUPPORT_BOT_TOKEN=7971569021:AAHo_Mbe4d3FLkWQbqmeS5rRmaTxQ6utxGg

# Администраторы ботов (Telegram User ID)
ADMIN_USER_IDS=5777893929
SUPPORT_USER_IDS=5777893929
SUPER_ADMIN_ID=5777893929

# Настройки ботов
BOT_WEBHOOK_URL=https://unveilvpn.com/webhook
BOT_WEBHOOK_SECRET=3IAT6Zo8weV1Ay0mIjjvSBd1
BOT_WEBHOOK_PORT=8443

# Настройки доступа
ALLOWED_USERS=
BANNED_USERS=

# Webhook настройки (используется в коде)
WEBHOOK_URL=https://unveilvpn.com/webhook
WEBHOOK_PORT=8080
WEBHOOK_MODE=false

# Настройки безопасности ботов
BOT_RATE_LIMIT=30
BOT_RATE_LIMIT_WINDOW=60
BOT_MAX_MESSAGE_LENGTH=4096

# Настройки уведомлений
NOTIFICATION_CHANNEL_ID=-1001234567890
ERROR_NOTIFICATION_ENABLED=true
ADMIN_NOTIFICATION_ENABLED=true

# Настройки логирования ботов
BOT_LOG_LEVEL=INFO
BOT_LOG_TO_FILE=true
BOT_LOG_TO_CHANNEL=false

# =============================================================================
# ПЛАТЕЖНЫЕ СИСТЕМЫ
# =============================================================================

# YooKassa (банковские карты, рубли)
YOOKASSA_SHOPID=1101373
YOOKASSA_SECRET_KEY=test_NlO9Ua0Q2I_a5wfAO-clRVdJYZUe14SA7i5kT7Rw7pk

# Cryptomus (криптовалюта, доллары)  
CRYPTOMUS_MERCHANT_ID=your_cryptomus_merchant_id
CRYPTOMUS_API_KEY=your_cryptomus_api_key_here

# Telegram Stars
TELEGRAM_STARS_ENABLED=true

# =============================================================================
# БЕЗОПАСНОСТЬ
# =============================================================================
JWT_SECRET=4U7unlSMxUi94mc8HOfVoOJK5ZLVMMoRpnTQbKCWhrnXxdga
WEBHOOK_SECRET=JqPc7XunmXQVizFYWmYjikSoKcGB98AC
ENCRYPTION_KEY=47ffa2ab0e7c11ded36f720cfbec213c03f47fb061428c8f34ae1e45502c277b
SESSION_SECRET=Ox1T68IIYUmrJZCi4XwosGCI0WA4MvoQ

# CORS настройки
CORS_ORIGINS=https://unveilvpn.com,https://www.unveilvpn.com,https://api.unveilvpn.com
TRUSTED_HOSTS=unveilvpn.com,www.unveilvpn.com,api.unveilvpn.com

# =============================================================================
# API НАСТРОЙКИ
# =============================================================================
API_HOST=0.0.0.0
API_PORT=8000
API_WORKERS=4
API_TIMEOUT=60
API_MAX_REQUESTS=1000

# Rate limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60

# API дополнительные настройки (используется в коде)
API_DEBUG=false
API_SECRET_KEY=G4BGPjgpr7PjWEzvSyFevgT883R67vkpqyJDVw2OgsGIM49m
API_ALGORITHM=HS256
API_ACCESS_TOKEN_EXPIRE_MINUTES=30
API_RATE_LIMIT_ENABLED=true
API_RATE_LIMIT_CALLS=100
API_RATE_LIMIT_PERIOD=60
API_LOG_LEVEL=INFO

# =============================================================================
# ДОМЕНЫ И SSL
# =============================================================================
DOMAIN=unveilvpn.com
API_DOMAIN=api.unveilvpn.com
LANDING_DOMAIN=www.unveilvpn.com
SUBSCRIPTION_DOMAIN=sub.unveilvpn.com

# SSL настройки
SSL_CERT_PATH=/etc/ssl/certs/unveilvpn.com.crt
SSL_KEY_PATH=/etc/ssl/private/unveilvpn.com.key
SSL_ENABLED=true

# =============================================================================
# МОНИТОРИНГ И ЛОГИРОВАНИЕ
# =============================================================================
SENTRY_DSN=your_sentry_dsn_here
PROMETHEUS_ENABLED=true
PROMETHEUS_PORT=9090

# Логирование
LOG_FORMAT=json
LOG_FILE=/app/logs/unveilvpn.log
LOG_MAX_SIZE=100MB
LOG_BACKUP_COUNT=10

# =============================================================================
# EMAIL НАСТРОЙКИ
# =============================================================================
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_smtp_password_here
SMTP_TLS=true

# Email адреса
ADMIN_EMAIL=<EMAIL>
SUPPORT_EMAIL=<EMAIL>
NOREPLY_EMAIL=<EMAIL>

# =============================================================================
# БИЗНЕС НАСТРОЙКИ
# =============================================================================
SHOP_NAME=UnveilVPN
SHOP_DESCRIPTION=Надежный VPN сервис для обхода блокировок
SUPPORT_LINK=https://t.me/unveilvpn_support_bot
RULES_LINK=https://unveilvpn.com/rules
ABOUT=Профессиональный VPN сервис с высокой скоростью и надежностью

# Тестовый период
TEST_PERIOD=true
TEST_PERIOD_ENABLED=true
TEST_PERIOD_DURATION_HOURS=168
PERIOD_LIMIT=1

# Уведомления
RENEW_NOTIFICATION_TIME=24

# Реферальная система (используется в коде)
REFERRAL_LEVEL_1_PERCENT=10.0
REFERRAL_LEVEL_2_PERCENT=5.0
REFERRAL_LEVEL_3_PERCENT=2.0
REFERRAL_MIN_PAYOUT=100

# Локализация (используется в коде)
DEFAULT_LOCALE=ru

# =============================================================================
# ДОПОЛНИТЕЛЬНЫЕ НАСТРОЙКИ
# =============================================================================

# Локализация
DEFAULT_LANGUAGE=ru
SUPPORTED_LANGUAGES=ru,en

# =============================================================================
# ПРОВЕРКА КОНФИГУРАЦИИ
# =============================================================================
# Используйте скрипт для проверки: python3 scripts/check_env_vars.py
# Все переменные с "your_*" должны быть заменены на реальные значения

# =============================================================================
# ПРИМЕЧАНИЯ
# =============================================================================
# 1. Замените ВСЕ значения "your_*" на реальные production данные
# 2. Получите токены ботов от @BotFather в Telegram
# 3. Настройте платежные системы (YooKassa, Cryptomus)
# 4. Получите API ключ от Remnawave панели
# 5. Настройте SMTP для отправки email
# 6. Настройте Sentry для мониторинга ошибок
# 7. Протестируйте все интеграции в staging окружении
# 8. Запустите проверку переменных: python3 scripts/check_env_vars.py

# =============================================================================
# ДОПОЛНИТЕЛЬНЫЕ ПЕРЕМЕННЫЕ ДЛЯ СОВМЕСТИМОСТИ
# =============================================================================

# Устаревшие переменные (для обратной совместимости со старыми webhook'ами)
YOOKASSA_TOKEN=your_yookassa_secret_key_here
CRYPTO_TOKEN=your_cryptomus_api_key_here
MERCHANT_UUID=your_cryptomus_merchant_id

# Дополнительные переменные для Docker Compose
PROTOCOLS=vless