<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Test - UnveilVPN</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #1a1a1a;
            color: #ffffff;
        }
        .section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #333;
            border-radius: 5px;
            background: #2a2a2a;
        }
        .success { color: #4CAF50; }
        .error { color: #f44336; }
        button {
            background: #2196F3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #1976D2;
        }
    </style>
</head>
<body>
    <h1>🧪 Simple Test Page</h1>
    <p>Тестирование базового JavaScript без модулей</p>

    <div class="section">
        <h2>📊 Базовая информация</h2>
        <div id="basic-info"></div>
    </div>

    <div class="section">
        <h2>⚙️ JavaScript тесты</h2>
        <button onclick="runTests()">Запустить тесты</button>
        <div id="test-results"></div>
    </div>

    <div class="section">
        <h2>🌐 Сетевые запросы</h2>
        <button onclick="testAPI()">Тестировать API</button>
        <div id="api-results"></div>
    </div>

    <div class="section">
        <h2>🎨 DOM манипуляции</h2>
        <button onclick="testDOM()">Тестировать DOM</button>
        <div id="dom-results"></div>
    </div>

    <script>
        // Базовая информация
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Simple test page loaded');
            
            const info = {
                userAgent: navigator.userAgent,
                platform: navigator.platform,
                language: navigator.language,
                screenWidth: screen.width,
                screenHeight: screen.height,
                windowWidth: window.innerWidth,
                windowHeight: window.innerHeight,
                documentReadyState: document.readyState,
                locationHref: location.href,
                performanceNow: performance.now()
            };

            const container = document.getElementById('basic-info');
            container.innerHTML = `
                <div class="success">
                    <strong>User-Agent:</strong> ${info.userAgent}<br>
                    <strong>Platform:</strong> ${info.platform}<br>
                    <strong>Language:</strong> ${info.language}<br>
                    <strong>Screen:</strong> ${info.screenWidth}x${info.screenHeight}<br>
                    <strong>Window:</strong> ${info.windowWidth}x${info.windowHeight}<br>
                    <strong>Document Ready State:</strong> ${info.documentReadyState}<br>
                    <strong>Location:</strong> ${info.locationHref}<br>
                    <strong>Performance Now:</strong> ${info.performanceNow.toFixed(2)}ms
                </div>
            `;
        });

        function runTests() {
            console.log('🔄 Running JavaScript tests');
            
            const tests = [
                {
                    name: 'Basic Variables',
                    test: function() { var x = 1; return x === 1; }
                },
                {
                    name: 'Functions',
                    test: function() { function test() { return true; } return test(); }
                },
                {
                    name: 'Arrays',
                    test: function() { var arr = [1, 2, 3]; return arr.length === 3; }
                },
                {
                    name: 'Objects',
                    test: function() { var obj = {a: 1}; return obj.a === 1; }
                },
                {
                    name: 'JSON',
                    test: function() { 
                        var obj = {test: true}; 
                        var str = JSON.stringify(obj);
                        var parsed = JSON.parse(str);
                        return parsed.test === true;
                    }
                },
                {
                    name: 'setTimeout',
                    test: function() { return typeof setTimeout === 'function'; }
                },
                {
                    name: 'console',
                    test: function() { return typeof console.log === 'function'; }
                },
                {
                    name: 'document',
                    test: function() { return typeof document.getElementById === 'function'; }
                }
            ];

            const results = tests.map(function(test) {
                try {
                    var passed = test.test();
                    return { name: test.name, passed: passed, error: null };
                } catch (error) {
                    return { name: test.name, passed: false, error: error.message };
                }
            });

            const container = document.getElementById('test-results');
            container.innerHTML = results.map(function(result) {
                var statusClass = result.passed ? 'success' : 'error';
                return '<div class="' + statusClass + '">' +
                    (result.passed ? '✅' : '❌') + ' <strong>' + result.name + '</strong>' +
                    (result.error ? '<br><small>Error: ' + result.error + '</small>' : '') +
                    '</div>';
            }).join('');
        }

        function testAPI() {
            console.log('🌐 Testing API requests');
            
            const container = document.getElementById('api-results');
            container.innerHTML = '<div>🔄 Тестирование API...</div>';

            // Тест с XMLHttpRequest (старый способ)
            var xhr = new XMLHttpRequest();
            xhr.open('GET', '/api/v1/public/tariffs', true);
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    if (xhr.status === 200) {
                        try {
                            var data = JSON.parse(xhr.responseText);
                            container.innerHTML = '<div class="success">✅ API работает: получено ' + 
                                (data.data ? data.data.length : 'неизвестно') + ' тарифов</div>';
                        } catch (e) {
                            container.innerHTML = '<div class="error">❌ Ошибка парсинга JSON: ' + e.message + '</div>';
                        }
                    } else {
                        container.innerHTML = '<div class="error">❌ HTTP ошибка: ' + xhr.status + '</div>';
                    }
                }
            };
            xhr.onerror = function() {
                container.innerHTML = '<div class="error">❌ Сетевая ошибка</div>';
            };
            xhr.send();
        }

        function testDOM() {
            console.log('🎨 Testing DOM manipulation');
            
            const container = document.getElementById('dom-results');
            
            try {
                // Создание элементов
                var div = document.createElement('div');
                div.innerHTML = 'Тестовый элемент';
                div.style.color = '#4CAF50';
                div.style.padding = '10px';
                div.style.border = '1px solid #4CAF50';
                div.style.borderRadius = '3px';
                div.style.marginTop = '10px';
                
                // Добавление в DOM
                container.innerHTML = '<div class="success">✅ DOM манипуляции работают</div>';
                container.appendChild(div);
                
                // Тест событий
                var button = document.createElement('button');
                button.innerHTML = 'Кликни меня';
                button.onclick = function() {
                    button.innerHTML = 'Клик работает! ✅';
                    button.style.background = '#4CAF50';
                };
                container.appendChild(button);
                
            } catch (error) {
                container.innerHTML = '<div class="error">❌ Ошибка DOM: ' + error.message + '</div>';
            }
        }
    </script>
</body>
</html>
