# 🔌 Конфигурация Remnawave для UnveilVPN Shop

## 📋 Обязательные переменные окружения

### Основные настройки Remnawave

```bash
# URL панели Remnawave (обязательно HTTPS в production)
REMNAWAVE_PANEL_URL=https://your-remnawave-panel.com

# API ключ для доступа к Remnawave API
REMNAWAVE_API_KEY=your_remnawave_api_key_here

# URL для подписок (используется клиентами для подключения)
REMNAWAVE_SUBSCRIPTION_URL=https://sub.your-domain.com

# Поддерживаемые протоколы (разделенные пробелами)
REMNAWAVE_PROTOCOLS=vless vmess trojan shadowsocks
```

### Дополнительные настройки

```bash
# Таймаут для API запросов (секунды)
REMNAWAVE_TIMEOUT=30

# Максимальное количество повторных попыток
REMNAWAVE_MAX_RETRIES=3

# Задержка между повторными попытками (секунды)
REMNAWAVE_RETRY_DELAY=5

# Включить SSL верификацию (рекомендуется true для production)
REMNAWAVE_SSL_VERIFY=true

# Пользовательский User-Agent для запросов
REMNAWAVE_USER_AGENT=UnveilVPN-Shop/2.0

# Максимальный размер пула соединений
REMNAWAVE_POOL_SIZE=10
```

## 🔧 Настройка Remnawave панели

### 1. Получение API ключа

1. Войдите в админ панель Remnawave
2. Перейдите в раздел "API Keys" или "Настройки API"
3. Создайте новый API ключ с правами:
   - Управление пользователями
   - Создание подписок
   - Генерация конфигураций
   - Просмотр статистики

### 2. Настройка доменов

```bash
# Основной домен панели
REMNAWAVE_PANEL_URL=https://panel.your-domain.com

# Домен для подписок (должен быть доступен клиентам)
REMNAWAVE_SUBSCRIPTION_URL=https://sub.your-domain.com
```

### 3. Конфигурация протоколов

Поддерживаемые протоколы в Remnawave:

```bash
# Все доступные протоколы
REMNAWAVE_PROTOCOLS=vless vmess trojan shadowsocks

# Только VLESS и VMess (рекомендуется)
REMNAWAVE_PROTOCOLS=vless vmess

# Только современные протоколы
REMNAWAVE_PROTOCOLS=vless trojan
```

## 🔒 Безопасность

### SSL/TLS настройки

```bash
# Обязательно включить SSL верификацию в production
REMNAWAVE_SSL_VERIFY=true

# Путь к кастомному CA сертификату (если нужен)
REMNAWAVE_CA_CERT_PATH=/etc/ssl/certs/remnawave-ca.crt

# Отключить проверку hostname (НЕ рекомендуется для production)
REMNAWAVE_SSL_CHECK_HOSTNAME=true
```

### Аутентификация

```bash
# API ключ должен быть длинным и случайным
REMNAWAVE_API_KEY=rmw_1234567890abcdef1234567890abcdef12345678

# Дополнительные заголовки аутентификации (если требуются)
REMNAWAVE_AUTH_HEADER=X-API-Key
REMNAWAVE_AUTH_PREFIX=Bearer
```

## 📊 Мониторинг и логирование

### Настройки логирования

```bash
# Уровень логирования для Remnawave интеграции
REMNAWAVE_LOG_LEVEL=INFO

# Логировать все API запросы (полезно для отладки)
REMNAWAVE_LOG_REQUESTS=false

# Логировать ответы API (осторожно с чувствительными данными)
REMNAWAVE_LOG_RESPONSES=false

# Маскировать чувствительные данные в логах
REMNAWAVE_MASK_SENSITIVE=true
```

### Health checks

```bash
# Интервал проверки доступности Remnawave (секунды)
REMNAWAVE_HEALTH_CHECK_INTERVAL=60

# Таймаут для health check (секунды)
REMNAWAVE_HEALTH_CHECK_TIMEOUT=10

# URL для health check (обычно /health или /api/health)
REMNAWAVE_HEALTH_CHECK_PATH=/api/health
```

## 🚀 Производительность

### Оптимизация соединений

```bash
# Размер пула HTTP соединений
REMNAWAVE_POOL_SIZE=10

# Максимальное время жизни соединения (секунды)
REMNAWAVE_POOL_TTL=300

# Максимальное количество соединений на хост
REMNAWAVE_POOL_LIMIT_PER_HOST=5

# Включить keep-alive соединения
REMNAWAVE_KEEP_ALIVE=true
```

### Кэширование

```bash
# Кэшировать токены аутентификации (секунды)
REMNAWAVE_TOKEN_CACHE_TTL=3600

# Кэшировать информацию о серверах (секунды)
REMNAWAVE_SERVERS_CACHE_TTL=1800

# Кэшировать пользовательские данные (секунды)
REMNAWAVE_USER_CACHE_TTL=300
```

## 🔄 Интеграция с UnveilVPN Shop

### Маппинг пользователей

```bash
# Префикс для пользователей UnveilVPN в Remnawave
REMNAWAVE_USER_PREFIX=uvpn_

# Формат имени пользователя: {prefix}{user_id}
# Пример: uvpn_123456

# Группа по умолчанию для новых пользователей
REMNAWAVE_DEFAULT_GROUP=unveilvpn_users

# Лимиты по умолчанию
REMNAWAVE_DEFAULT_TRAFFIC_LIMIT=100GB
REMNAWAVE_DEFAULT_DEVICE_LIMIT=3
```

### Подписки и тарифы

```bash
# Автоматически создавать подписки при создании пользователя
REMNAWAVE_AUTO_CREATE_SUBSCRIPTION=true

# Формат названия подписки
REMNAWAVE_SUBSCRIPTION_NAME_FORMAT=UnveilVPN_{tariff_name}_{user_id}

# Включить статистику использования
REMNAWAVE_ENABLE_USAGE_STATS=true

# Интервал обновления статистики (секунды)
REMNAWAVE_STATS_UPDATE_INTERVAL=300
```

## 🧪 Тестирование

### Настройки для тестового окружения

```bash
# Использовать тестовую панель Remnawave
REMNAWAVE_PANEL_URL=https://test-panel.your-domain.com

# Тестовый API ключ
REMNAWAVE_API_KEY=test_api_key_for_development

# Отключить SSL верификацию для локального тестирования
REMNAWAVE_SSL_VERIFY=false

# Включить подробное логирование
REMNAWAVE_LOG_LEVEL=DEBUG
REMNAWAVE_LOG_REQUESTS=true
REMNAWAVE_LOG_RESPONSES=true
```

## 📝 Примеры конфигурации

### Production конфигурация

```bash
# Production Remnawave Configuration
REMNAWAVE_PANEL_URL=https://panel.unveilvpn.com
REMNAWAVE_API_KEY=rmw_prod_1234567890abcdef1234567890abcdef
REMNAWAVE_SUBSCRIPTION_URL=https://sub.unveilvpn.com
REMNAWAVE_PROTOCOLS=vless vmess trojan
REMNAWAVE_TIMEOUT=30
REMNAWAVE_MAX_RETRIES=3
REMNAWAVE_SSL_VERIFY=true
REMNAWAVE_LOG_LEVEL=INFO
REMNAWAVE_POOL_SIZE=20
```

### Development конфигурация

```bash
# Development Remnawave Configuration
REMNAWAVE_PANEL_URL=https://dev-panel.unveilvpn.com
REMNAWAVE_API_KEY=rmw_dev_test_key_12345
REMNAWAVE_SUBSCRIPTION_URL=https://dev-sub.unveilvpn.com
REMNAWAVE_PROTOCOLS=vless vmess
REMNAWAVE_TIMEOUT=60
REMNAWAVE_MAX_RETRIES=1
REMNAWAVE_SSL_VERIFY=false
REMNAWAVE_LOG_LEVEL=DEBUG
REMNAWAVE_LOG_REQUESTS=true
```

## ⚠️ Важные замечания

1. **Безопасность API ключа**: Никогда не коммитьте реальные API ключи в репозиторий
2. **SSL в production**: Всегда используйте HTTPS и SSL верификацию в production
3. **Мониторинг**: Настройте мониторинг доступности Remnawave панели
4. **Резервное копирование**: Регулярно создавайте резервные копии конфигурации Remnawave
5. **Обновления**: Следите за обновлениями Remnawave API и обновляйте интеграцию
6. **Тестирование**: Всегда тестируйте изменения в staging окружении перед production

## 🔗 Полезные ссылки

- [Документация Remnawave API](https://docs.remnawave.com)
- [Примеры интеграции](https://github.com/remnawave/examples)
- [Troubleshooting Guide](https://docs.remnawave.com/troubleshooting)
