# 🗄️ Конфигурация базы данных PostgreSQL для UnveilVPN Shop

## 📋 Основные переменные окружения

### Подключение к базе данных

```bash
# Хост базы данных
DB_HOST=your_postgres_host.com

# Порт PostgreSQL (стандартный 5432)
DB_PORT=5432

# Название базы данных
DB_NAME=unveilvpn_prod

# Пользователь базы данных
DB_USER=unveilvpn_user

# Пароль пользователя (используйте сильный пароль)
DB_PASS=your_production_database_password_here

# Полная строка подключения
DATABASE_URL=**********************************************************************************************/unveilvpn_prod
```

### Настройки пула соединений

```bash
# Размер пула соединений (рекомендуется 10-20 для production)
DB_POOL_SIZE=20

# Максимальное количество дополнительных соединений
DB_MAX_OVERFLOW=30

# Таймаут получения соединения из пула (секунды)
DB_POOL_TIMEOUT=30

# Время жизни соединения в пуле (секунды)
DB_POOL_RECYCLE=3600

# Проверка соединений перед использованием
DB_POOL_PRE_PING=true
```

## 🔧 Настройка PostgreSQL сервера

### Рекомендуемые настройки postgresql.conf

```ini
# Память
shared_buffers = 256MB
effective_cache_size = 1GB
work_mem = 4MB
maintenance_work_mem = 64MB

# Соединения
max_connections = 100
superuser_reserved_connections = 3

# Логирование
log_destination = 'stderr'
logging_collector = on
log_directory = 'log'
log_filename = 'postgresql-%Y-%m-%d_%H%M%S.log'
log_min_messages = warning
log_min_error_statement = error
log_min_duration_statement = 1000

# Производительность
checkpoint_completion_target = 0.9
wal_buffers = 16MB
default_statistics_target = 100

# Автовакуум
autovacuum = on
autovacuum_max_workers = 3
autovacuum_naptime = 1min
```

### Настройки pg_hba.conf

```ini
# TYPE  DATABASE        USER            ADDRESS                 METHOD

# Local connections
local   all             postgres                                peer
local   all             all                                     md5

# IPv4 local connections
host    all             all             127.0.0.1/32            md5

# Production connections (замените на ваши IP)
host    unveilvpn_prod  unveilvpn_user  10.0.0.0/8             md5
host    unveilvpn_prod  unveilvpn_user  **********/12          md5
host    unveilvpn_prod  unveilvpn_user  ***********/16         md5

# SSL connections (рекомендуется для production)
hostssl unveilvpn_prod  unveilvpn_user  0.0.0.0/0              md5
```

## 🔒 Безопасность базы данных

### SSL настройки

```bash
# Включить SSL соединения
DB_SSL_MODE=require

# Путь к SSL сертификату клиента (если требуется)
DB_SSL_CERT=/path/to/client-cert.pem

# Путь к SSL ключу клиента (если требуется)
DB_SSL_KEY=/path/to/client-key.pem

# Путь к корневому CA сертификату
DB_SSL_ROOT_CERT=/path/to/ca-cert.pem
```

### Создание пользователя и базы данных

```sql
-- Создание пользователя
CREATE USER unveilvpn_user WITH PASSWORD 'your_production_database_password_here';

-- Создание базы данных
CREATE DATABASE unveilvpn_prod OWNER unveilvpn_user;

-- Предоставление прав
GRANT ALL PRIVILEGES ON DATABASE unveilvpn_prod TO unveilvpn_user;

-- Подключение к базе данных
\c unveilvpn_prod

-- Предоставление прав на схему
GRANT ALL ON SCHEMA public TO unveilvpn_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO unveilvpn_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO unveilvpn_user;

-- Права по умолчанию для новых объектов
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO unveilvpn_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO unveilvpn_user;
```

## 📊 Мониторинг и производительность

### Переменные для мониторинга

```bash
# Включить сбор статистики
DB_ENABLE_STATS=true

# Интервал обновления статистики (секунды)
DB_STATS_INTERVAL=60

# Логировать медленные запросы (миллисекунды)
DB_LOG_SLOW_QUERIES=1000

# Максимальное время выполнения запроса (секунды)
DB_QUERY_TIMEOUT=30
```

### Полезные запросы для мониторинга

```sql
-- Активные соединения
SELECT count(*) FROM pg_stat_activity WHERE state = 'active';

-- Размер базы данных
SELECT pg_size_pretty(pg_database_size('unveilvpn_prod'));

-- Топ медленных запросов
SELECT query, mean_time, calls 
FROM pg_stat_statements 
ORDER BY mean_time DESC 
LIMIT 10;

-- Статистика по таблицам
SELECT schemaname, tablename, n_tup_ins, n_tup_upd, n_tup_del 
FROM pg_stat_user_tables 
ORDER BY n_tup_ins + n_tup_upd + n_tup_del DESC;
```

## 🔄 Резервное копирование

### Настройки бэкапа

```bash
# Включить автоматические бэкапы
BACKUP_ENABLED=true

# Расписание бэкапов (cron формат)
BACKUP_SCHEDULE=0 2 * * *

# Количество дней хранения бэкапов
BACKUP_RETENTION_DAYS=30

# Путь для локальных бэкапов
BACKUP_LOCAL_PATH=/var/backups/unveilvpn

# S3 настройки для удаленного хранения
BACKUP_S3_BUCKET=unveilvpn-backups
BACKUP_S3_ACCESS_KEY=your_s3_access_key
BACKUP_S3_SECRET_KEY=your_s3_secret_key
BACKUP_S3_REGION=us-east-1
```

### Скрипт бэкапа

```bash
#!/bin/bash
# backup_database.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="unveilvpn_backup_${DATE}.sql"

# Создание бэкапа
pg_dump -h $DB_HOST -U $DB_USER -d $DB_NAME > $BACKUP_FILE

# Сжатие
gzip $BACKUP_FILE

# Загрузка в S3 (если настроено)
if [ "$BACKUP_S3_BUCKET" != "" ]; then
    aws s3 cp ${BACKUP_FILE}.gz s3://$BACKUP_S3_BUCKET/
fi

# Удаление старых локальных бэкапов
find /var/backups/unveilvpn -name "*.sql.gz" -mtime +$BACKUP_RETENTION_DAYS -delete
```

## 🚀 Оптимизация производительности

### Индексы для основных таблиц

```sql
-- Индексы для таблицы пользователей
CREATE INDEX idx_vpn_users_telegram_id ON vpn_users(telegram_id);
CREATE INDEX idx_vpn_users_remnawave_id ON vpn_users(remnawave_user_id);
CREATE INDEX idx_vpn_users_created_at ON vpn_users(created_at);

-- Индексы для подписок
CREATE INDEX idx_subscriptions_user_id ON subscriptions(user_id);
CREATE INDEX idx_subscriptions_status ON subscriptions(status);
CREATE INDEX idx_subscriptions_expires_at ON subscriptions(expires_at);

-- Индексы для платежей
CREATE INDEX idx_payments_user_id ON payments(user_id);
CREATE INDEX idx_payments_status ON payments(status);
CREATE INDEX idx_payments_created_at ON payments(created_at);

-- Индексы для тикетов поддержки
CREATE INDEX idx_support_tickets_user_id ON support_tickets(user_id);
CREATE INDEX idx_support_tickets_status ON support_tickets(status);
CREATE INDEX idx_support_tickets_created_at ON support_tickets(created_at);
```

### Настройки автовакуума

```sql
-- Настройка автовакуума для активных таблиц
ALTER TABLE vpn_users SET (
    autovacuum_vacuum_scale_factor = 0.1,
    autovacuum_analyze_scale_factor = 0.05
);

ALTER TABLE payments SET (
    autovacuum_vacuum_scale_factor = 0.1,
    autovacuum_analyze_scale_factor = 0.05
);

ALTER TABLE subscriptions SET (
    autovacuum_vacuum_scale_factor = 0.1,
    autovacuum_analyze_scale_factor = 0.05
);
```

## 🔧 Миграции и обновления

### Переменные для миграций

```bash
# Автоматически запускать миграции при старте
DB_AUTO_MIGRATE=true

# Создавать резервную копию перед миграциями
DB_BACKUP_BEFORE_MIGRATE=true

# Проверять целостность данных после миграций
DB_VERIFY_AFTER_MIGRATE=true
```

### Пример миграции

```sql
-- migration_001_add_remnawave_fields.sql
BEGIN;

-- Добавление полей для Remnawave
ALTER TABLE vpn_users 
ADD COLUMN remnawave_user_id VARCHAR(255),
ADD COLUMN remnawave_subscription_id VARCHAR(255);

-- Создание индексов
CREATE INDEX idx_vpn_users_remnawave_user_id ON vpn_users(remnawave_user_id);
CREATE INDEX idx_vpn_users_remnawave_subscription_id ON vpn_users(remnawave_subscription_id);

-- Обновление версии схемы
INSERT INTO schema_migrations (version, applied_at) 
VALUES ('001_add_remnawave_fields', NOW());

COMMIT;
```

## 🧪 Тестирование

### Настройки для тестовой базы данных

```bash
# Тестовая база данных
TEST_DB_HOST=localhost
TEST_DB_PORT=5432
TEST_DB_NAME=unveilvpn_test
TEST_DB_USER=unveilvpn_test_user
TEST_DB_PASS=test_password_123
TEST_DATABASE_URL=postgresql://unveilvpn_test_user:test_password_123@localhost:5432/unveilvpn_test

# Настройки для тестов
TEST_DB_POOL_SIZE=5
TEST_DB_TIMEOUT=10
```

## ⚠️ Важные замечания

1. **Пароли**: Используйте сильные пароли (минимум 16 символов, включая цифры и спецсимволы)
2. **SSL**: Всегда используйте SSL соединения в production
3. **Мониторинг**: Настройте мониторинг производительности и доступности БД
4. **Бэкапы**: Регулярно тестируйте восстановление из резервных копий
5. **Обновления**: Планируйте обновления PostgreSQL и тестируйте их в staging
6. **Безопасность**: Ограничьте сетевой доступ к базе данных
7. **Логирование**: Настройте ротацию логов для предотвращения переполнения диска

## 📈 Рекомендации по масштабированию

### Для небольшой нагрузки (до 1000 пользователей)
- 2 CPU, 4GB RAM
- DB_POOL_SIZE=10
- shared_buffers=128MB

### Для средней нагрузки (до 10000 пользователей)
- 4 CPU, 8GB RAM
- DB_POOL_SIZE=20
- shared_buffers=256MB

### Для высокой нагрузки (более 10000 пользователей)
- 8+ CPU, 16GB+ RAM
- DB_POOL_SIZE=30+
- shared_buffers=512MB+
- Рассмотрите read replicas
