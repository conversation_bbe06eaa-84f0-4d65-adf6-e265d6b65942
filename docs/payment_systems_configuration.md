# 💳 Конфигурация платежных систем для UnveilVPN Shop

## 📋 Обзор платежных систем

UnveilVPN Shop поддерживает три основные платежные системы:

1. **YooKassa** - банковские карты, рубли
2. **Cryptomus** - криптовалюта, доллары  
3. **Telegram Stars** - внутренняя валюта Telegram

## 🏦 YooKassa (банковские карты, рубли)

### Основные переменные

```bash
# ID магазина в YooKassa
YOOKASSA_SHOPID=your_yookassa_shop_id

# Секретный ключ магазина
YOOKASSA_SECRET_KEY=your_yookassa_secret_key_here

# Секрет для webhook'ов (для проверки подлинности)
YOOKASSA_WEBHOOK_SECRET=your_yookassa_webhook_secret

# URL для уведомлений (webhook)
YOOKASSA_WEBHOOK_URL=https://your-domain.com/api/v1/webhooks/yookassa

# Валюта (RUB для рублей)
YOOKASSA_CURRENCY=RUB

# Включить тестовый режим (false для production)
YOOKASSA_TEST_MODE=false
```

### Дополнительные настройки

```bash
# Таймаут для API запросов (секунды)
YOOKASSA_TIMEOUT=30

# Максимальное количество повторных попыток
YOOKASSA_MAX_RETRIES=3

# Время жизни платежа (минуты)
YOOKASSA_PAYMENT_TTL=15

# Автоматическое подтверждение платежей
YOOKASSA_AUTO_CAPTURE=true

# Описание платежа по умолчанию
YOOKASSA_PAYMENT_DESCRIPTION=Оплата подписки UnveilVPN

# Включить сохранение платежных методов
YOOKASSA_SAVE_PAYMENT_METHOD=false
```

### Поддерживаемые методы оплаты

```bash
# Банковские карты
YOOKASSA_ENABLE_BANK_CARD=true

# Яндекс.Деньги
YOOKASSA_ENABLE_YANDEX_MONEY=true

# QIWI кошелек
YOOKASSA_ENABLE_QIWI=true

# Webmoney
YOOKASSA_ENABLE_WEBMONEY=true

# Альфа-Клик
YOOKASSA_ENABLE_ALFABANK=true

# Сбербанк Онлайн
YOOKASSA_ENABLE_SBERBANK=true

# Тинькофф
YOOKASSA_ENABLE_TINKOFF=true
```

### Настройка в личном кабинете YooKassa

1. Зарегистрируйтесь на [yookassa.ru](https://yookassa.ru)
2. Создайте магазин
3. Получите Shop ID и Secret Key
4. Настройте webhook URL: `https://your-domain.com/api/v1/webhooks/yookassa`
5. Включите необходимые методы оплаты
6. Настройте уведомления для статусов: `succeeded`, `canceled`

## 🪙 Cryptomus (криптовалюта, доллары)

### Основные переменные

```bash
# ID мерчанта в Cryptomus
CRYPTOMUS_MERCHANT_ID=your_cryptomus_merchant_id

# API ключ для доступа к Cryptomus API
CRYPTOMUS_API_KEY=your_cryptomus_api_key_here

# Секрет для webhook'ов
CRYPTOMUS_WEBHOOK_SECRET=your_cryptomus_webhook_secret

# URL для уведомлений
CRYPTOMUS_WEBHOOK_URL=https://your-domain.com/api/v1/webhooks/cryptomus

# Валюта (USD для долларов)
CRYPTOMUS_CURRENCY=USD

# Включить тестовый режим
CRYPTOMUS_TEST_MODE=false
```

### Дополнительные настройки

```bash
# Таймаут для API запросов
CRYPTOMUS_TIMEOUT=60

# Максимальное количество повторных попыток
CRYPTOMUS_MAX_RETRIES=3

# Время жизни платежа (минуты)
CRYPTOMUS_PAYMENT_TTL=30

# Минимальная сумма платежа (USD)
CRYPTOMUS_MIN_AMOUNT=1.00

# Максимальная сумма платежа (USD)
CRYPTOMUS_MAX_AMOUNT=10000.00

# Комиссия сети (включить в сумму платежа)
CRYPTOMUS_INCLUDE_NETWORK_FEE=true
```

### Поддерживаемые криптовалюты

```bash
# Bitcoin
CRYPTOMUS_ENABLE_BTC=true

# Ethereum
CRYPTOMUS_ENABLE_ETH=true

# USDT (Tether)
CRYPTOMUS_ENABLE_USDT=true

# USDC
CRYPTOMUS_ENABLE_USDC=true

# Litecoin
CRYPTOMUS_ENABLE_LTC=true

# Bitcoin Cash
CRYPTOMUS_ENABLE_BCH=true

# Dogecoin
CRYPTOMUS_ENABLE_DOGE=true

# Tron
CRYPTOMUS_ENABLE_TRX=true
```

### Настройка в Cryptomus

1. Зарегистрируйтесь на [cryptomus.com](https://cryptomus.com)
2. Создайте мерчант аккаунт
3. Получите Merchant ID и API Key
4. Настройте webhook URL: `https://your-domain.com/api/v1/webhooks/cryptomus`
5. Выберите поддерживаемые криптовалюты
6. Настройте уведомления для статусов платежей

## ⭐ Telegram Stars

### Основные переменные

```bash
# Включить оплату через Telegram Stars
TELEGRAM_STARS_ENABLED=true

# Токен бота для работы с платежами
TELEGRAM_STARS_BOT_TOKEN=your_telegram_bot_token

# Провайдер платежей (обычно пустая строка для Stars)
TELEGRAM_STARS_PROVIDER_TOKEN=

# Валюта (XTR для Telegram Stars)
TELEGRAM_STARS_CURRENCY=XTR

# Максимальная сумма в Stars за один платеж
TELEGRAM_STARS_MAX_AMOUNT=2500
```

### Дополнительные настройки

```bash
# Описание товара для Stars
TELEGRAM_STARS_PRODUCT_DESCRIPTION=Подписка UnveilVPN

# Фото товара (URL)
TELEGRAM_STARS_PRODUCT_PHOTO_URL=https://your-domain.com/images/product.jpg

# Включить отправку чека
TELEGRAM_STARS_SEND_RECEIPT=true

# Включить гибкую цену
TELEGRAM_STARS_FLEXIBLE_PRICE=false
```

### Курсы обмена Stars

```bash
# Курс Stars к рублю (1 Star = X рублей)
TELEGRAM_STARS_TO_RUB_RATE=2.00

# Курс Stars к доллару (1 Star = X долларов)
TELEGRAM_STARS_TO_USD_RATE=0.02

# Автоматически обновлять курсы
TELEGRAM_STARS_AUTO_UPDATE_RATES=true

# Интервал обновления курсов (часы)
TELEGRAM_STARS_RATE_UPDATE_INTERVAL=24
```

## 🔄 Синхронизация цен

### Настройки синхронизации

```bash
# Базовая валюта для расчетов
BASE_CURRENCY=RUB

# Автоматическая синхронизация цен
AUTO_SYNC_PRICES=true

# Интервал синхронизации (часы)
PRICE_SYNC_INTERVAL=6

# Источник курсов валют
EXCHANGE_RATE_API=https://api.exchangerate-api.com/v4/latest/RUB

# API ключ для курсов валют (если требуется)
EXCHANGE_RATE_API_KEY=your_exchange_rate_api_key
```

### Маржа и комиссии

```bash
# Маржа для криптовалютных платежей (%)
CRYPTO_MARGIN_PERCENT=5.0

# Маржа для Telegram Stars (%)
STARS_MARGIN_PERCENT=3.0

# Комиссия YooKassa (%)
YOOKASSA_FEE_PERCENT=2.9

# Комиссия Cryptomus (%)
CRYPTOMUS_FEE_PERCENT=1.5

# Включить комиссии в цену
INCLUDE_FEES_IN_PRICE=true
```

## 🔒 Безопасность платежей

### Общие настройки безопасности

```bash
# Секретный ключ для подписи платежей
PAYMENT_SIGNATURE_SECRET=your_payment_signature_secret_32_chars

# Время жизни платежной ссылки (минуты)
PAYMENT_LINK_TTL=30

# Максимальное количество попыток оплаты
MAX_PAYMENT_ATTEMPTS=3

# Блокировка пользователя при превышении попыток (минуты)
PAYMENT_BLOCK_DURATION=60
```

### Проверка платежей

```bash
# Включить дополнительную проверку платежей
ENABLE_PAYMENT_VERIFICATION=true

# Проверять IP адрес webhook'ов
VERIFY_WEBHOOK_IP=true

# Разрешенные IP адреса для webhook'ов YooKassa
YOOKASSA_ALLOWED_IPS=***********/27,***********/27,***********/25

# Разрешенные IP адреса для webhook'ов Cryptomus
CRYPTOMUS_ALLOWED_IPS=your_cryptomus_webhook_ips

# Логировать все платежные операции
LOG_ALL_PAYMENTS=true
```

## 📊 Мониторинг и аналитика

### Настройки мониторинга

```bash
# Включить мониторинг платежей
ENABLE_PAYMENT_MONITORING=true

# Интервал проверки зависших платежей (минуты)
PAYMENT_CHECK_INTERVAL=5

# Время ожидания подтверждения платежа (минуты)
PAYMENT_CONFIRMATION_TIMEOUT=30

# Уведомления о проблемах с платежами
PAYMENT_ALERTS_ENABLED=true

# Email для уведомлений о платежах
PAYMENT_ALERTS_EMAIL=<EMAIL>
```

### Метрики платежей

```bash
# Включить сбор метрик
PAYMENT_METRICS_ENABLED=true

# Интервал агрегации метрик (минуты)
PAYMENT_METRICS_INTERVAL=15

# Хранить детальную статистику (дни)
PAYMENT_STATS_RETENTION_DAYS=90

# Экспорт метрик в Prometheus
PAYMENT_PROMETHEUS_ENABLED=true
```

## 🧪 Тестирование платежей

### Тестовые настройки

```bash
# Включить тестовый режим для всех платежных систем
PAYMENT_TEST_MODE=true

# Тестовые данные YooKassa
YOOKASSA_TEST_SHOPID=test_shop_id
YOOKASSA_TEST_SECRET_KEY=test_secret_key

# Тестовые данные Cryptomus
CRYPTOMUS_TEST_MERCHANT_ID=test_merchant_id
CRYPTOMUS_TEST_API_KEY=test_api_key

# Автоматически подтверждать тестовые платежи
AUTO_CONFIRM_TEST_PAYMENTS=true

# Задержка для тестовых платежей (секунды)
TEST_PAYMENT_DELAY=5
```

## ⚠️ Важные замечания

1. **Безопасность**: Никогда не коммитьте реальные API ключи в репозиторий
2. **Webhook'и**: Обязательно проверяйте подлинность webhook уведомлений
3. **Мониторинг**: Настройте алерты для неуспешных платежей
4. **Логирование**: Логируйте все платежные операции для аудита
5. **Тестирование**: Всегда тестируйте платежи в sandbox режиме
6. **Резервирование**: Настройте резервные платежные методы
7. **Соответствие**: Убедитесь в соответствии требованиям ЦБ РФ и международным стандартам

## 📈 Рекомендации по оптимизации

1. **Конверсия**: Предлагайте несколько методов оплаты
2. **UX**: Минимизируйте количество шагов для оплаты
3. **Локализация**: Адаптируйте интерфейс под регион пользователя
4. **Мобильность**: Оптимизируйте для мобильных устройств
5. **Скорость**: Минимизируйте время обработки платежей
6. **Прозрачность**: Четко показывайте все комиссии и сборы
