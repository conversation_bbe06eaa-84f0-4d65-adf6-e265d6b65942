# 🤖 Конфигурация Telegram ботов для UnveilVPN Shop

## 📋 Обзор архитектуры ботов

UnveilVPN Shop использует архитектуру из 3 отдельных Telegram ботов:

1. **Клиентский бот** - основной интерфейс для пользователей
2. **Админ бот** - управление системой и пользователями
3. **Бот поддержки** - обработка тикетов и помощь пользователям

## 🎯 Клиентский бот (основной)

### Основные переменные

```bash
# Токен клиентского бота от @BotFather
CLIENT_BOT_TOKEN=your_client_bot_token_here

# Имя пользователя бота (без @)
CLIENT_BOT_USERNAME=unveilvpn_bot

# Webhook URL для клиентского бота
CLIENT_BOT_WEBHOOK_URL=https://your-domain.com/webhook/client

# Секрет для webhook (для безопасности)
CLIENT_BOT_WEBHOOK_SECRET=your_client_webhook_secret_here
```

### Функциональные настройки

```bash
# Включить реферальную систему
CLIENT_BOT_REFERRAL_ENABLED=true

# Бонус за реферала (в рублях)
CLIENT_BOT_REFERRAL_BONUS=100

# Максимальное количество рефералов для бонуса
CLIENT_BOT_MAX_REFERRALS=50

# Включить тестовый период
CLIENT_BOT_TEST_PERIOD_ENABLED=true

# Длительность тестового периода (часы)
CLIENT_BOT_TEST_PERIOD_HOURS=168

# Лимит тестовых периодов на пользователя
CLIENT_BOT_TEST_PERIOD_LIMIT=1
```

### Настройки интерфейса

```bash
# Язык по умолчанию
CLIENT_BOT_DEFAULT_LANGUAGE=ru

# Поддерживаемые языки
CLIENT_BOT_SUPPORTED_LANGUAGES=ru,en

# Включить inline клавиатуры
CLIENT_BOT_INLINE_KEYBOARDS=true

# Максимальная длина сообщения
CLIENT_BOT_MAX_MESSAGE_LENGTH=4096

# Таймаут для ответа пользователю (секунды)
CLIENT_BOT_RESPONSE_TIMEOUT=30
```

## 👨‍💼 Админ бот

### Основные переменные

```bash
# Токен админ бота от @BotFather
ADMIN_BOT_TOKEN=your_admin_bot_token_here

# Имя пользователя админ бота
ADMIN_BOT_USERNAME=unveilvpn_admin_bot

# Webhook URL для админ бота
ADMIN_BOT_WEBHOOK_URL=https://your-domain.com/webhook/admin

# Секрет для webhook
ADMIN_BOT_WEBHOOK_SECRET=your_admin_webhook_secret_here
```

### Права доступа

```bash
# Список Telegram ID администраторов (через запятую)
ADMIN_BOT_ADMIN_IDS=123456789,987654321

# Список Telegram ID супер-администраторов
ADMIN_BOT_SUPER_ADMIN_IDS=123456789

# Включить проверку прав доступа
ADMIN_BOT_ACCESS_CONTROL=true

# Логировать все действия администраторов
ADMIN_BOT_LOG_ACTIONS=true
```

### Функциональные возможности

```bash
# Включить управление пользователями
ADMIN_BOT_USER_MANAGEMENT=true

# Включить управление тарифами
ADMIN_BOT_TARIFF_MANAGEMENT=true

# Включить просмотр статистики
ADMIN_BOT_STATISTICS=true

# Включить управление промокодами
ADMIN_BOT_PROMOCODE_MANAGEMENT=true

# Включить массовые операции
ADMIN_BOT_BULK_OPERATIONS=true

# Максимальное количество пользователей для массовых операций
ADMIN_BOT_BULK_LIMIT=1000
```

## 🎧 Бот поддержки

### Основные переменные

```bash
# Токен бота поддержки от @BotFather
SUPPORT_BOT_TOKEN=your_support_bot_token_here

# Имя пользователя бота поддержки
SUPPORT_BOT_USERNAME=unveilvpn_support_bot

# Webhook URL для бота поддержки
SUPPORT_BOT_WEBHOOK_URL=https://your-domain.com/webhook/support

# Секрет для webhook
SUPPORT_BOT_WEBHOOK_SECRET=your_support_webhook_secret_here
```

### Настройки поддержки

```bash
# Список Telegram ID сотрудников поддержки
SUPPORT_BOT_AGENT_IDS=111111111,222222222,333333333

# Автоматическое распределение тикетов
SUPPORT_BOT_AUTO_ASSIGN=true

# Максимальное количество активных тикетов на агента
SUPPORT_BOT_MAX_TICKETS_PER_AGENT=10

# Время ожидания ответа от агента (минуты)
SUPPORT_BOT_AGENT_RESPONSE_TIMEOUT=30

# Автоматическое закрытие неактивных тикетов (часы)
SUPPORT_BOT_AUTO_CLOSE_TIMEOUT=24
```

### Уведомления

```bash
# Включить уведомления о новых тикетах
SUPPORT_BOT_NEW_TICKET_NOTIFICATIONS=true

# Включить уведомления о просроченных тикетах
SUPPORT_BOT_OVERDUE_NOTIFICATIONS=true

# Интервал проверки просроченных тикетов (минуты)
SUPPORT_BOT_OVERDUE_CHECK_INTERVAL=15

# Канал для уведомлений поддержки
SUPPORT_BOT_NOTIFICATION_CHANNEL=-1001234567890
```

## 🔧 Общие настройки для всех ботов

### Webhook конфигурация

```bash
# Основной домен для webhook'ов
BOT_WEBHOOK_DOMAIN=your-domain.com

# Порт для webhook сервера
BOT_WEBHOOK_PORT=8443

# Использовать SSL для webhook'ов
BOT_WEBHOOK_SSL=true

# Путь к SSL сертификату
BOT_WEBHOOK_SSL_CERT=/etc/ssl/certs/webhook.crt

# Путь к SSL ключу
BOT_WEBHOOK_SSL_KEY=/etc/ssl/private/webhook.key
```

### Настройки производительности

```bash
# Максимальное количество одновременных соединений
BOT_MAX_CONNECTIONS=100

# Таймаут для HTTP запросов к Telegram API (секунды)
BOT_REQUEST_TIMEOUT=30

# Максимальное количество повторных попыток
BOT_MAX_RETRIES=3

# Задержка между повторными попытками (секунды)
BOT_RETRY_DELAY=1

# Включить пул соединений
BOT_CONNECTION_POOL=true

# Размер пула соединений
BOT_POOL_SIZE=10
```

### Логирование и мониторинг

```bash
# Уровень логирования для ботов
BOT_LOG_LEVEL=INFO

# Логировать все входящие сообщения
BOT_LOG_MESSAGES=true

# Логировать все исходящие сообщения
BOT_LOG_RESPONSES=false

# Маскировать чувствительные данные в логах
BOT_MASK_SENSITIVE_DATA=true

# Включить метрики для ботов
BOT_METRICS_ENABLED=true

# Интервал сбора метрик (секунды)
BOT_METRICS_INTERVAL=60
```

## 🔒 Безопасность ботов

### Общие настройки безопасности

```bash
# Секретный ключ для подписи данных
BOT_SECRET_KEY=your_bot_secret_key_32_characters_long

# Включить проверку подписи webhook'ов
BOT_VERIFY_WEBHOOK_SIGNATURE=true

# Максимальное количество сообщений от пользователя в минуту
BOT_RATE_LIMIT_MESSAGES=20

# Время блокировки при превышении лимита (минуты)
BOT_RATE_LIMIT_BLOCK_TIME=5

# Список заблокированных пользователей
BOT_BLOCKED_USERS=

# Включить антиспам фильтры
BOT_ANTISPAM_ENABLED=true
```

### Фильтрация контента

```bash
# Максимальная длина сообщения от пользователя
BOT_MAX_USER_MESSAGE_LENGTH=1000

# Запрещенные слова (через запятую)
BOT_BANNED_WORDS=спам,реклама,мошенничество

# Автоматически удалять сообщения с запрещенными словами
BOT_AUTO_DELETE_BANNED=true

# Предупреждать пользователей о нарушениях
BOT_WARN_USERS=true

# Количество предупреждений до блокировки
BOT_MAX_WARNINGS=3
```

## 📱 Настройка команд и меню

### Команды клиентского бота

```bash
# Основные команды
CLIENT_BOT_COMMANDS=start,help,profile,tariffs,payment,support,referral

# Скрытые команды (не показываются в меню)
CLIENT_BOT_HIDDEN_COMMANDS=debug,test,admin

# Включить команду /cancel для отмены операций
CLIENT_BOT_CANCEL_COMMAND=true

# Включить команду /back для возврата назад
CLIENT_BOT_BACK_COMMAND=true
```

### Команды админ бота

```bash
# Команды администратора
ADMIN_BOT_COMMANDS=start,users,stats,tariffs,promocodes,broadcast,logs

# Команды супер-администратора
ADMIN_BOT_SUPER_COMMANDS=config,backup,restart,maintenance

# Включить команды для управления ботами
ADMIN_BOT_BOT_MANAGEMENT=true
```

### Команды бота поддержки

```bash
# Команды поддержки
SUPPORT_BOT_COMMANDS=start,tickets,assign,close,stats,help

# Команды для агентов
SUPPORT_BOT_AGENT_COMMANDS=take,resolve,escalate,note

# Включить команды для управления очередью
SUPPORT_BOT_QUEUE_MANAGEMENT=true
```

## 🌐 Интернационализация

### Настройки языков

```bash
# Языки по умолчанию для каждого бота
CLIENT_BOT_DEFAULT_LANG=ru
ADMIN_BOT_DEFAULT_LANG=ru
SUPPORT_BOT_DEFAULT_LANG=ru

# Автоматическое определение языка по локали Telegram
BOT_AUTO_DETECT_LANGUAGE=true

# Путь к файлам переводов
BOT_TRANSLATIONS_PATH=/app/locales

# Поддерживаемые языки
BOT_SUPPORTED_LOCALES=ru,en,es,de,fr

# Fallback язык при отсутствии перевода
BOT_FALLBACK_LANGUAGE=en
```

## 🧪 Тестирование ботов

### Настройки для тестирования

```bash
# Включить тестовый режим
BOT_TEST_MODE=false

# Тестовые токены ботов
CLIENT_BOT_TEST_TOKEN=your_test_client_bot_token
ADMIN_BOT_TEST_TOKEN=your_test_admin_bot_token
SUPPORT_BOT_TEST_TOKEN=your_test_support_bot_token

# Тестовые пользователи (Telegram ID)
BOT_TEST_USERS=123456789,987654321

# Автоматические ответы в тестовом режиме
BOT_AUTO_RESPONSES=true

# Задержка для автоматических ответов (секунды)
BOT_AUTO_RESPONSE_DELAY=2
```

## 📊 Мониторинг и аналитика

### Метрики ботов

```bash
# Включить сбор метрик
BOT_ANALYTICS_ENABLED=true

# Отслеживать активность пользователей
BOT_TRACK_USER_ACTIVITY=true

# Отслеживать использование команд
BOT_TRACK_COMMAND_USAGE=true

# Отслеживать время ответа
BOT_TRACK_RESPONSE_TIME=true

# Экспорт метрик в Prometheus
BOT_PROMETHEUS_METRICS=true

# Интеграция с Google Analytics
BOT_GOOGLE_ANALYTICS_ID=UA-XXXXXXXXX-X
```

## ⚠️ Важные замечания

1. **Токены**: Никогда не коммитьте реальные токены ботов в репозиторий
2. **Webhook'и**: Используйте HTTPS для всех webhook URL
3. **Безопасность**: Проверяйте подлинность всех входящих webhook запросов
4. **Лимиты**: Соблюдайте лимиты Telegram API (30 сообщений в секунду)
5. **Мониторинг**: Настройте мониторинг доступности и производительности ботов
6. **Резервирование**: Подготовьте резервные токены на случай блокировки
7. **Соответствие**: Убедитесь в соответствии правилам Telegram

## 🚀 Рекомендации по оптимизации

1. **Производительность**: Используйте webhook'и вместо polling
2. **UX**: Минимизируйте количество шагов для выполнения действий
3. **Персонализация**: Адаптируйте интерфейс под предпочтения пользователя
4. **Аналитика**: Отслеживайте метрики использования для улучшения UX
5. **A/B тестирование**: Тестируйте различные варианты интерфейса
6. **Кэширование**: Кэшируйте часто используемые данные
