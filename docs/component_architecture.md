# Архитектура компонентной системы UnveilVPN Landing

## 🏗️ Структура компонентов

### 1. Базовые компоненты (Base Components)
```
src/components/base/
├── Button/
│   ├── Button.tsx          # Основной компонент кнопки
│   ├── Button.types.ts     # TypeScript типы
│   └── Button.stories.tsx  # Storybook истории
├── Card/
│   ├── Card.tsx
│   ├── Card.types.ts
│   └── Card.stories.tsx
├── Input/
├── Badge/
├── Modal/
└── Spinner/
```

### 2. Композитные компоненты (Composite Components)
```
src/components/composite/
├── Header/
│   ├── Header.tsx
│   ├── Navigation.tsx
│   ├── MobileMenu.tsx
│   └── Logo.tsx
├── Hero/
│   ├── Hero.tsx
│   ├── HeroContent.tsx
│   └── HeroBackground.tsx
├── Features/
│   ├── Features.tsx
│   ├── FeatureCard.tsx
│   └── FeatureIcon.tsx
├── Pricing/
│   ├── Pricing.tsx
│   ├── PricingCard.tsx
│   └── PricingToggle.tsx
├── FAQ/
│   ├── FAQ.tsx
│   ├── FAQItem.tsx
│   └── FAQSearch.tsx
└── Footer/
    ├── Footer.tsx
    ├── FooterLinks.tsx
    └── FooterSocial.tsx
```

### 3. Утилитарные компоненты (Utility Components)
```
src/components/utils/
├── AnimatedSection/       # Анимации при скролле
├── LazyImage/            # Ленивая загрузка изображений
├── ErrorBoundary/        # Обработка ошибок
├── LoadingState/         # Состояния загрузки
└── SEOHead/             # SEO метаданные
```

## 🎨 Дизайн-система

### Цветовая палитра
```typescript
// Основные цвета
primary: {
  50: '#e6f3ff',   // Фоны
  500: '#007acc',  // Основной (из логотипа)
  700: '#005a9e',  // Заголовки
}

// Состояния
success: '#22c55e'   // Успех
warning: '#f59e0b'   // Предупреждение
accent: '#ef4444'    // Ошибки
```

### Типографика
```typescript
// Заголовки
h1: 'text-4xl md:text-6xl font-bold text-neutral-900'
h2: 'text-3xl md:text-5xl font-bold text-neutral-800'
h3: 'text-2xl md:text-3xl font-semibold text-neutral-700'

// Текст
body: 'text-base text-neutral-600'
small: 'text-sm text-neutral-500'
```

### Компонентные стили
```typescript
// Кнопки
primary: 'bg-gradient-button hover:bg-gradient-button-hover text-white shadow-button'
secondary: 'bg-white border-2 border-primary-500 text-primary-500 hover:bg-primary-50'
ghost: 'text-primary-500 hover:bg-primary-50'

// Карточки
default: 'bg-white rounded-xl shadow-card hover:shadow-card-hover'
featured: 'bg-gradient-card border border-primary-200'
```

## 🔄 Система анимаций

### Появление элементов
```typescript
// Fade анимации
fadeIn: 'animate-fade-in'
fadeInUp: 'animate-fade-in-up'

// Slide анимации  
slideUp: 'animate-slide-up'
slideLeft: 'animate-slide-left'
slideRight: 'animate-slide-right'
```

### Интерактивные эффекты
```typescript
// Hover эффекты
cardHover: 'hover:scale-105 transition-transform duration-300'
buttonHover: 'hover:shadow-button-hover transition-all duration-200'

// Focus состояния
focusRing: 'focus:ring-2 focus:ring-primary-500 focus:ring-offset-2'
```

## 📱 Responsive дизайн

### Breakpoints
```typescript
sm: '640px'   // Мобильные (портрет)
md: '768px'   // Планшеты
lg: '1024px'  // Десктопы
xl: '1280px'  // Большие экраны
2xl: '1536px' // Очень большие экраны
```

### Адаптивные классы
```typescript
// Мобильные первые
'text-sm md:text-base lg:text-lg'
'p-4 md:p-6 lg:p-8'
'grid-cols-1 md:grid-cols-2 lg:grid-cols-3'
```

## 🧩 Переиспользуемые паттерны

### Section Container
```typescript
const SectionContainer = ({ children, className = '' }) => (
  <section className={`py-16 md:py-24 ${className}`}>
    <div className="container mx-auto px-4 md:px-6 lg:px-8">
      {children}
    </div>
  </section>
)
```

### Animated Wrapper
```typescript
const AnimatedWrapper = ({ children, animation = 'fadeInUp' }) => (
  <div className={`animate-${animation}`}>
    {children}
  </div>
)
```

### Feature Icon
```typescript
const FeatureIcon = ({ icon, color = 'primary' }) => (
  <div className={`
    w-12 h-12 rounded-lg bg-${color}-100 
    flex items-center justify-center
    text-${color}-600 text-xl
  `}>
    {icon}
  </div>
)
```

## 🎯 Принципы разработки

### 1. Композиция над наследованием
- Маленькие, переиспользуемые компоненты
- Композиция через props и children
- Избегание глубокой вложенности

### 2. Accessibility First
- Семантические HTML элементы
- ARIA атрибуты
- Клавиатурная навигация
- Контрастность цветов

### 3. Performance
- Ленивая загрузка компонентов
- Мемоизация тяжелых вычислений
- Оптимизация изображений
- Code splitting

### 4. Типизация
- Строгие TypeScript типы
- Props интерфейсы
- Enum для состояний
- Generic компоненты

## 📋 Чек-лист компонента

- [ ] TypeScript типы определены
- [ ] Props документированы
- [ ] Responsive дизайн реализован
- [ ] Анимации добавлены
- [ ] Accessibility проверено
- [ ] Тесты написаны
- [ ] Storybook история создана
- [ ] Performance оптимизировано
