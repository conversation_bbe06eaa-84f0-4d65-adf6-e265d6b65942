# 🔧 Руководство по устранению неполадок Docker для UnveilVPN Shop

## 🚨 Частые проблемы и решения

### 1. **Проблема: "Постоянные перезапуски" контейнера лендинга**

#### Симптомы:
- Лендинг недоступен через браузер
- В логах видны сообщения о запуске docker-entrypoint.sh
- Nginx reverse proxy показывает ошибки

#### Диагностика:
```bash
# Проверить статус всех контейнеров
docker compose ps

# Проверить логи nginx reverse proxy
docker compose logs nginx --tail=20

# Проверить логи лендинга
docker compose logs landing --tail=20

# Проверить health checks
docker compose ps | grep healthy
```

#### Решение:
```bash
# 1. Убедиться, что API сервис запущен
docker compose up api -d

# 2. Перезапустить nginx reverse proxy
docker compose restart nginx

# 3. Проверить доступность
curl -I http://localhost:80
```

### 2. **Проблема: API сервис не запускается**

#### Диагностика:
```bash
# Проверить логи API
docker compose logs api --tail=50

# Проверить переменные окружения
docker compose config | grep -A 20 "api:"
```

#### Решение:
```bash
# Проверить .env файл
python3 scripts/check_env_vars.py

# Пересобрать и запустить API
docker compose build api
docker compose up api -d
```

### 3. **Проблема: Nginx не может найти upstream сервисы**

#### Симптомы:
- Ошибка: "host not found in upstream"
- Nginx не запускается

#### Решение:
1. Убедиться, что все зависимые сервисы запущены
2. Проверить сетевые настройки Docker
3. Использовать улучшенную конфигурацию с health checks

## 🛠️ Команды для диагностики

### Общие команды:
```bash
# Статус всех сервисов
docker compose ps

# Логи всех сервисов
docker compose logs --tail=20

# Перезапуск всех сервисов
docker compose restart

# Полная пересборка
docker compose down && docker compose build && docker compose up -d
```

### Проверка сети:
```bash
# Проверить Docker сети
docker network ls

# Проверить подключения контейнеров
docker network inspect unveilvpn-shop_unveilvpn-network
```

### Проверка ресурсов:
```bash
# Использование ресурсов
docker stats --no-stream

# Проверка дискового пространства
docker system df
```

## 🔄 Автоматизированная диагностика

### Скрипт быстрой диагностики:
```bash
#!/bin/bash
echo "=== UnveilVPN Shop Health Check ==="

echo "1. Checking container status..."
docker compose ps

echo -e "\n2. Checking API service..."
if docker compose ps api | grep -q "healthy"; then
    echo "✅ API service is healthy"
else
    echo "❌ API service is not healthy"
    echo "Starting API service..."
    docker compose up api -d
fi

echo -e "\n3. Checking Nginx..."
if docker compose ps nginx | grep -q "healthy"; then
    echo "✅ Nginx is healthy"
else
    echo "❌ Nginx is not healthy"
    echo "Restarting Nginx..."
    docker compose restart nginx
fi

echo -e "\n4. Testing landing page..."
if curl -s -o /dev/null -w "%{http_code}" http://localhost:80 | grep -q "200"; then
    echo "✅ Landing page is accessible"
else
    echo "❌ Landing page is not accessible"
fi

echo -e "\n=== Health Check Complete ==="
```

## 📋 Контрольный список для устранения неполадок

- [ ] Все контейнеры запущены (`docker compose ps`)
- [ ] API сервис здоров (`docker compose ps api`)
- [ ] Nginx reverse proxy здоров (`docker compose ps nginx`)
- [ ] Лендинг доступен (`curl -I http://localhost:80`)
- [ ] Логи не содержат критических ошибок
- [ ] Переменные окружения настроены правильно
- [ ] Достаточно ресурсов (память, CPU, диск)

## 🚀 Профилактические меры

1. **Регулярный мониторинг:**
   - Настроить автоматические health checks
   - Использовать мониторинг ресурсов
   - Настроить алерты при падении сервисов

2. **Улучшенная конфигурация:**
   - Использовать `depends_on` с `condition: service_healthy`
   - Настроить правильные `max_fails` и `fail_timeout` в nginx
   - Добавить graceful shutdown для всех сервисов

3. **Автоматизация:**
   - Создать скрипты автоматической диагностики
   - Настроить автоматический перезапуск при проблемах
   - Использовать Docker Compose profiles для разных окружений
