# 🗄️ UnveilVPN Shop - Схема базы данных

## Обзор

UnveilVPN Shop использует PostgreSQL 16 с современными возможностями:
- **UUID** как первичные ключи для лучшей масштабируемости
- **JSONB** для гибкого хранения метаданных
- **Индексы GIN** для быстрого поиска по JSONB
- **ENUM типы** для строгой типизации статусов
- **Временные зоны** для корректной работы с датами

## Основные таблицы

### 1. vpnusers - Пользователи VPN

Центральная таблица пользователей с расширенными возможностями.

```sql
CREATE TABLE vpnusers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    telegram_id BIGINT UNIQUE NOT NULL,
    username VARCHAR(255),
    first_name VARCHAR(255),
    last_name VARCHAR(255),
    language_code VARCHAR(10) DEFAULT 'ru',
    
    -- VPN данные
    vpn_id VARCHAR(64),
    is_test_used BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    is_banned BOOLEAN DEFAULT FALSE,
    ban_reason TEXT,
    
    -- Реферальная система
    referral_code VARCHAR(20) UNIQUE,
    referred_by_id UUID REFERENCES vpnusers(id),
    referral_earnings NUMERIC(10,2) DEFAULT 0,
    
    -- Метаданные (JSONB)
    user_metadata JSONB NOT NULL DEFAULT '{}',
    
    -- Временные метки
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

**Ключевые особенности:**
- `user_metadata` содержит гибкие данные: предпочтения, статистику, настройки
- Поддержка самореферентных связей для реферальной системы
- Автоматическое обновление `updated_at` через триггер

### 2. tariffs - Тарифы

Динамическая система тарифов с поддержкой множественных валют.

```sql
CREATE TABLE tariffs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    duration_days INTEGER NOT NULL,
    
    -- Цены в разных валютах (JSONB)
    prices JSONB NOT NULL DEFAULT '{"rub": 0, "usd": 0, "stars": 0}',
    
    -- Характеристики тарифа (JSONB)
    features JSONB NOT NULL DEFAULT '{}',
    
    -- Настройки
    is_active BOOLEAN DEFAULT TRUE,
    is_popular BOOLEAN DEFAULT FALSE,
    is_test_available BOOLEAN DEFAULT TRUE,
    sort_order INTEGER DEFAULT 0,
    
    -- Скидки
    discount_percent INTEGER DEFAULT 0,
    is_promo BOOLEAN DEFAULT FALSE,
    promo_text VARCHAR(500),
    
    version INTEGER DEFAULT 1,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

**Структура JSONB полей:**

```json
// prices
{
  "rub": 500,     // Цена в рублях (YooKassa)
  "usd": 5.99,    // Цена в долларах (Cryptomus)
  "stars": 100    // Цена в Telegram Stars
}

// features
{
  "traffic_limit": null,        // GB, null = безлимит
  "speed_limit": null,          // Mbps, null = безлимит
  "devices_count": 3,           // Количество устройств
  "protocols": ["vless", "ss"], // Поддерживаемые протоколы
  "locations": ["ru", "us"],    // Доступные локации
  "support_level": "premium"    // Уровень поддержки
}
```

### 3. referrals - Реферальная система

Многоуровневая реферальная система (до 3 уровней).

```sql
CREATE TABLE referrals (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    referrer_id UUID NOT NULL REFERENCES vpnusers(id),
    referred_id UUID NOT NULL REFERENCES vpnusers(id),
    
    level INTEGER NOT NULL CHECK (level >= 1 AND level <= 3),
    
    -- Финансовые данные
    bonus_earned NUMERIC(10,2) DEFAULT 0,
    bonus_paid NUMERIC(10,2) DEFAULT 0,
    bonus_pending NUMERIC(10,2) DEFAULT 0,
    
    -- Статистика
    total_purchases INTEGER DEFAULT 0,
    total_amount NUMERIC(10,2) DEFAULT 0,
    
    is_active BOOLEAN DEFAULT TRUE,
    is_verified BOOLEAN DEFAULT FALSE,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_earning_at TIMESTAMP WITH TIME ZONE
);
```

**Логика начисления бонусов:**
- Уровень 1: 10% от покупки реферала
- Уровень 2: 5% от покупки реферала 2-го уровня  
- Уровень 3: 2% от покупки реферала 3-го уровня

### 4. promocodes - Промокоды

Гибкая система промокодов с различными типами скидок.

```sql
CREATE TABLE promocodes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    code VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(255),
    description TEXT,
    
    discount_type discount_type NOT NULL, -- 'percent', 'fixed', 'days'
    discount_value NUMERIC(10,2) NOT NULL,
    
    -- Ограничения
    usage_limit INTEGER,
    usage_limit_per_user INTEGER DEFAULT 1,
    usage_count INTEGER DEFAULT 0,
    
    -- Время действия
    starts_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE,
    
    -- Применимость
    applicable_tariffs JSONB, -- Список ID тарифов
    min_purchase_amount NUMERIC(10,2),
    
    is_active BOOLEAN DEFAULT TRUE,
    is_public BOOLEAN DEFAULT FALSE,
    
    created_by_id UUID REFERENCES vpnusers(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 5. support_tickets - Система поддержки

Тикетная система с JSONB сообщениями.

```sql
CREATE TABLE support_tickets (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    ticket_number VARCHAR(20) UNIQUE NOT NULL,
    user_id UUID NOT NULL REFERENCES vpnusers(id),
    
    subject VARCHAR(500) NOT NULL,
    category VARCHAR(100) NOT NULL,
    priority ticket_priority DEFAULT 'medium', -- 'low', 'medium', 'high', 'urgent'
    status ticket_status DEFAULT 'open',       -- 'open', 'in_progress', 'waiting_user', 'closed'
    
    -- Сообщения в JSONB
    messages JSONB NOT NULL DEFAULT '[]',
    
    -- Метаданные
    metadata JSONB NOT NULL DEFAULT '{}',
    
    assigned_to_id UUID REFERENCES vpnusers(id),
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    closed_at TIMESTAMP WITH TIME ZONE
);
```

**Структура сообщений:**
```json
[
  {
    "id": "uuid",
    "content": "Текст сообщения",
    "author_id": "uuid",
    "is_internal": false,
    "created_at": "2024-01-01T12:00:00Z",
    "attachments": []
  }
]
```

### 6. payments - Платежи

Единая таблица для всех платежных систем.

```sql
CREATE TABLE payments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES vpnusers(id),
    tariff_id UUID NOT NULL REFERENCES tariffs(id),
    
    payment_method payment_method NOT NULL, -- 'yookassa', 'cryptomus', 'telegram_stars'
    status payment_status DEFAULT 'pending', -- 'pending', 'completed', 'failed', 'refunded'
    
    -- Суммы
    original_amount NUMERIC(10,2) NOT NULL,
    discount_amount NUMERIC(10,2) DEFAULT 0,
    final_amount NUMERIC(10,2) NOT NULL,
    currency VARCHAR(10) NOT NULL,
    
    -- Внешние ID
    external_payment_id VARCHAR(255),
    order_id VARCHAR(255),
    
    -- Метаданные платежа (JSONB)
    payment_metadata JSONB NOT NULL DEFAULT '{}',
    
    promocode_id UUID REFERENCES promocodes(id),
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    paid_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE
);
```

## Индексы и производительность

### Основные индексы

```sql
-- VPNUsers
CREATE INDEX idx_vpnusers_telegram_id ON vpnusers(telegram_id);
CREATE INDEX idx_vpnusers_referral_code ON vpnusers(referral_code);
CREATE INDEX idx_vpnusers_metadata_gin ON vpnusers USING GIN(user_metadata);

-- Tariffs  
CREATE INDEX idx_tariffs_active ON tariffs(is_active);
CREATE INDEX idx_tariffs_prices_gin ON tariffs USING GIN(prices);
CREATE INDEX idx_tariffs_features_gin ON tariffs USING GIN(features);

-- Payments
CREATE INDEX idx_payments_user ON payments(user_id);
CREATE INDEX idx_payments_status ON payments(status);
CREATE INDEX idx_payments_method ON payments(payment_method);
CREATE INDEX idx_payments_created_at ON payments(created_at);

-- Support Tickets
CREATE INDEX idx_support_tickets_user ON support_tickets(user_id);
CREATE INDEX idx_support_tickets_status ON support_tickets(status);
CREATE INDEX idx_support_tickets_messages_gin ON support_tickets USING GIN(messages);
```

### Составные индексы

```sql
-- Для аналитики платежей
CREATE INDEX idx_payments_status_method ON payments(status, payment_method);
CREATE INDEX idx_payments_user_created ON payments(user_id, created_at);

-- Для реферальной системы
CREATE INDEX idx_referrals_referrer_active ON referrals(referrer_id) WHERE is_active = true;
CREATE INDEX idx_referrals_level_active ON referrals(level, is_active);
```

## Связи между таблицами

### Диаграмма связей

```
vpnusers (1) ←→ (N) referrals
    ↓ (1)
    ↓
    ↓ (N) payments ←→ (1) tariffs
    ↓ (N)                ↑ (1)
    ↓                    ↓
support_tickets     promocodes
    ↓ (N)               ↓ (N)
    ↓                   ↓
promocode_usage ←→ payments
```

### Каскадные операции

- **VPNUsers → Payments**: `CASCADE DELETE` - при удалении пользователя удаляются его платежи
- **VPNUsers → SupportTickets**: `CASCADE DELETE` - при удалении пользователя удаляются его тикеты  
- **Tariffs → Payments**: `RESTRICT` - нельзя удалить тариф с существующими платежами
- **Promocodes → Payments**: `SET NULL` - при удалении промокода ссылка обнуляется

## Триггеры и функции

### Автоматическое обновление updated_at

```sql
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Применение ко всем таблицам
CREATE TRIGGER update_vpnusers_updated_at BEFORE UPDATE ON vpnusers
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

### Генерация реферального кода

```sql
CREATE OR REPLACE FUNCTION generate_referral_code()
RETURNS TEXT AS $$
DECLARE
    chars TEXT := 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    result TEXT := '';
    i INTEGER;
BEGIN
    FOR i IN 1..8 LOOP
        result := result || substr(chars, floor(random() * length(chars) + 1)::integer, 1);
    END LOOP;
    RETURN result;
END;
$$ LANGUAGE plpgsql;
```

## Миграции

Все изменения схемы управляются через Alembic:

```bash
# Создание новой миграции
alembic revision --autogenerate -m "Описание изменений"

# Применение миграций
alembic upgrade head

# Откат миграции
alembic downgrade -1
```

## Резервное копирование

### Ежедневный бэкап

```bash
#!/bin/bash
pg_dump -h localhost -U unveilvpn_user unveilvpn | gzip > backup_$(date +%Y%m%d).sql.gz
```

### Восстановление

```bash
gunzip -c backup_20240101.sql.gz | psql -h localhost -U unveilvpn_user unveilvpn
```
