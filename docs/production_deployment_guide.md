# 🚀 Руководство по развертыванию UnveilVPN Shop в Production

## 📋 Предварительные требования

### Системные требования

**Минимальные требования:**
- CPU: 2 ядра
- RAM: 4 GB
- Диск: 50 GB SSD
- ОС: Ubuntu 20.04+ / Debian 11+ / CentOS 8+

**Рекомендуемые требования:**
- CPU: 4 ядра
- RAM: 8 GB
- Диск: 100 GB SSD
- ОС: Ubuntu 22.04 LTS

### Необходимое ПО

```bash
# Docker и Docker Compose
sudo apt update
sudo apt install docker.io docker-compose-plugin
sudo systemctl enable docker
sudo usermod -aG docker $USER

# Дополнительные утилиты
sudo apt install curl wget git nginx certbot python3-certbot-nginx
```

## 🔧 Пошаговое развертывание

### Шаг 1: Подготовка сервера

```bash
# Клонирование репозитория
git clone https://github.com/your-org/unveilvpn-shop.git
cd unveilvpn-shop

# Создание директорий
sudo mkdir -p /var/log/unveilvpn
sudo mkdir -p /var/backups/unveilvpn
sudo mkdir -p /etc/ssl/unveilvpn

# Настройка прав
sudo chown -R $USER:$USER /var/log/unveilvpn
sudo chown -R $USER:$USER /var/backups/unveilvpn
```

### Шаг 2: Настройка SSL сертификатов

```bash
# Получение SSL сертификата через Let's Encrypt
sudo certbot certonly --nginx -d your-domain.com -d api.your-domain.com -d sub.your-domain.com

# Копирование сертификатов
sudo cp /etc/letsencrypt/live/your-domain.com/fullchain.pem /etc/ssl/unveilvpn/
sudo cp /etc/letsencrypt/live/your-domain.com/privkey.pem /etc/ssl/unveilvpn/

# Настройка автообновления
sudo crontab -e
# Добавить: 0 12 * * * /usr/bin/certbot renew --quiet
```

### Шаг 3: Настройка переменных окружения

```bash
# Копирование шаблона
cp .env.prod .env

# Редактирование переменных
nano .env
```

**Обязательные изменения в .env:**

```bash
# Домены (замените на ваши)
DOMAIN=your-domain.com
API_DOMAIN=api.your-domain.com
LANDING_DOMAIN=www.your-domain.com
SUBSCRIPTION_DOMAIN=sub.your-domain.com

# База данных (используйте сильные пароли)
DB_HOST=postgres
DB_PASS=your_strong_database_password_here
DATABASE_URL=****************************************************************************/unveilvpn_prod

# Redis
REDIS_PASSWORD=your_strong_redis_password_here
REDIS_URL=redis://:your_strong_redis_password_here@redis:6379/0

# Remnawave VPN панель
REMNAWAVE_PANEL_URL=https://your-remnawave-panel.com
REMNAWAVE_API_KEY=your_real_remnawave_api_key
REMNAWAVE_SUBSCRIPTION_URL=https://sub.your-domain.com

# Telegram боты (получите от @BotFather)
CLIENT_BOT_TOKEN=1234567890:ABCdefGHIjklMNOpqrsTUVwxyz
ADMIN_BOT_TOKEN=1234567890:ABCdefGHIjklMNOpqrsTUVwxyz
SUPPORT_BOT_TOKEN=1234567890:ABCdefGHIjklMNOpqrsTUVwxyz

# Платежные системы
YOOKASSA_SHOPID=your_real_shop_id
YOOKASSA_SECRET_KEY=your_real_yookassa_secret_key
CRYPTOMUS_MERCHANT_ID=your_real_merchant_id
CRYPTOMUS_API_KEY=your_real_cryptomus_api_key

# Безопасность (генерируйте случайные ключи)
JWT_SECRET=$(openssl rand -base64 32)
WEBHOOK_SECRET=$(openssl rand -base64 32)
ENCRYPTION_KEY=$(openssl rand -base64 32)
SESSION_SECRET=$(openssl rand -base64 32)
```

### Шаг 4: Проверка конфигурации

```bash
# Запуск проверки переменных окружения
python3 scripts/check_env_vars.py

# Должен вывести: "✅ ВСЕ ПРОВЕРКИ ПРОЙДЕНЫ УСПЕШНО!"
```

### Шаг 5: Настройка внешних сервисов

#### PostgreSQL (если используете внешний)

```sql
-- Подключение к PostgreSQL
psql -h your_postgres_host -U postgres

-- Создание пользователя и базы данных
CREATE USER unveilvpn_user WITH PASSWORD 'your_strong_database_password_here';
CREATE DATABASE unveilvpn_prod OWNER unveilvpn_user;
GRANT ALL PRIVILEGES ON DATABASE unveilvpn_prod TO unveilvpn_user;
```

#### Redis (если используете внешний)

```bash
# Настройка Redis с паролем
redis-cli
CONFIG SET requirepass your_strong_redis_password_here
CONFIG REWRITE
```

#### Nginx (обратный прокси)

```nginx
# /etc/nginx/sites-available/unveilvpn
server {
    listen 80;
    server_name your-domain.com www.your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com www.your-domain.com;

    ssl_certificate /etc/ssl/unveilvpn/fullchain.pem;
    ssl_certificate_key /etc/ssl/unveilvpn/privkey.pem;

    # Landing page
    location / {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}

server {
    listen 443 ssl http2;
    server_name api.your-domain.com;

    ssl_certificate /etc/ssl/unveilvpn/fullchain.pem;
    ssl_certificate_key /etc/ssl/unveilvpn/privkey.pem;

    # API
    location / {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}

server {
    listen 443 ssl http2;
    server_name sub.your-domain.com;

    ssl_certificate /etc/ssl/unveilvpn/fullchain.pem;
    ssl_certificate_key /etc/ssl/unveilvpn/privkey.pem;

    # Subscription proxy to Remnawave
    location / {
        proxy_pass $REMNAWAVE_SUBSCRIPTION_URL;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

```bash
# Активация конфигурации
sudo ln -s /etc/nginx/sites-available/unveilvpn /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### Шаг 6: Запуск системы

```bash
# Сборка и запуск всех сервисов
docker compose -f docker-compose.prod.yml build
docker compose -f docker-compose.prod.yml up -d

# Проверка статуса
docker compose -f docker-compose.prod.yml ps

# Просмотр логов
docker compose -f docker-compose.prod.yml logs -f
```

### Шаг 7: Инициализация базы данных

```bash
# Запуск миграций
docker compose -f docker-compose.prod.yml exec api python -m alembic upgrade head

# Создание начальных данных
docker compose -f docker-compose.prod.yml exec api python scripts/init_data.py
```

### Шаг 8: Проверка работоспособности

```bash
# Проверка API
curl -s https://api.your-domain.com/health/ | jq

# Проверка лендинга
curl -s https://your-domain.com | head -20

# Проверка подписок
curl -s https://sub.your-domain.com

# Интеграционное тестирование
python3 scripts/integration_test.py
```

## 🔒 Настройка безопасности

### Firewall

```bash
# UFW настройка
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw enable
```

### Мониторинг

```bash
# Установка мониторинга
docker compose -f docker-compose.monitoring.yml up -d

# Настройка алертов
# Grafana: http://your-domain.com:3001
# Prometheus: http://your-domain.com:9090
```

### Резервное копирование

```bash
# Настройка автоматических бэкапов
crontab -e

# Добавить:
0 2 * * * /home/<USER>/unveilvpn-shop/scripts/backup.sh
0 6 * * 0 /home/<USER>/unveilvpn-shop/scripts/cleanup_old_backups.sh
```

## 📊 Мониторинг и обслуживание

### Ежедневные проверки

```bash
# Статус сервисов
docker compose -f docker-compose.prod.yml ps

# Использование ресурсов
docker stats

# Логи ошибок
docker compose -f docker-compose.prod.yml logs --tail=100 | grep ERROR

# Размер базы данных
docker compose -f docker-compose.prod.yml exec postgres psql -U unveilvpn_user -d unveilvpn_prod -c "SELECT pg_size_pretty(pg_database_size('unveilvpn_prod'));"
```

### Еженедельные задачи

```bash
# Обновление системы
sudo apt update && sudo apt upgrade -y

# Очистка Docker
docker system prune -f

# Проверка SSL сертификатов
sudo certbot certificates

# Анализ логов
sudo logrotate -f /etc/logrotate.conf
```

### Ежемесячные задачи

```bash
# Обновление зависимостей
git pull origin main
docker compose -f docker-compose.prod.yml build --no-cache
docker compose -f docker-compose.prod.yml up -d

# Проверка безопасности
docker scout cves
```

## 🚨 Устранение неполадок

### Частые проблемы

**1. API не отвечает**
```bash
# Проверка логов
docker compose logs api

# Перезапуск API
docker compose restart api
```

**2. База данных недоступна**
```bash
# Проверка подключения
docker compose exec postgres pg_isready

# Проверка логов PostgreSQL
docker compose logs postgres
```

**3. Боты не работают**
```bash
# Проверка токенов
python3 scripts/check_env_vars.py

# Проверка webhook'ов
curl -X POST https://api.telegram.org/bot<TOKEN>/getWebhookInfo
```

### Контакты поддержки

- **Техническая поддержка**: <EMAIL>
- **Экстренные вопросы**: +7 (XXX) XXX-XX-XX
- **Документация**: https://docs.your-domain.com

---

## ✅ Чек-лист развертывания

- [ ] Сервер подготовлен и настроен
- [ ] SSL сертификаты получены и настроены
- [ ] Все переменные окружения настроены
- [ ] Проверка конфигурации пройдена успешно
- [ ] Внешние сервисы настроены (PostgreSQL, Redis, Remnawave)
- [ ] Nginx настроен как обратный прокси
- [ ] Система запущена и работает
- [ ] База данных инициализирована
- [ ] Интеграционные тесты пройдены
- [ ] Безопасность настроена (firewall, мониторинг)
- [ ] Резервное копирование настроено
- [ ] Мониторинг и алерты настроены
- [ ] Документация обновлена

**🎉 Поздравляем! UnveilVPN Shop успешно развернут в production!**
