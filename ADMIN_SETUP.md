# UnveilVPN Shop - Настройка администраторов

## Получение Telegram User ID

Для работы с ботами необходимо указать ваш Telegram User ID в качестве администратора.

### Способы получения User ID:

1. **Через @userinfobot**:
   - Напишите боту [@userinfobot](https://t.me/userinfobot) в Telegram
   - Отправьте любое сообщение
   - Бот вернет ваш User ID

2. **Через @getmyid_bot**:
   - Напишите боту [@getmyid_bot](https://t.me/getmyid_bot)
   - Отправьте команду `/start`
   - Получите ваш User ID

3. **Через веб-версию Telegram**:
   - Откройте [web.telegram.org](https://web.telegram.org)
   - В URL вашего профиля будет ваш ID

## Настройка Admin IDs

### Автоматическая настройка (рекомендуется):

```bash
./scripts/update_admin_ids.sh
```

### Ручная настройка:

Отредактируйте файл `.env`:

```env
# Администраторы ботов (Telegram User ID)
ADMIN_USER_IDS=123456789,987654321
SUPER_ADMIN_ID=123456789
```

Где:
- `SUPER_ADMIN_ID` - основной администратор (ваш ID)
- `ADMIN_USER_IDS` - список всех администраторов через запятую

## Применение изменений

После обновления Admin IDs перезапустите ботов:

```bash
docker compose restart client-bot admin-bot support-bot
```

## Проверка настройки

1. **Проверьте переменные в .env**:
   ```bash
   grep "ADMIN_USER_IDS\|SUPER_ADMIN_ID" .env
   ```

2. **Проверьте логи ботов**:
   ```bash
   docker compose logs client-bot --tail 10
   docker compose logs admin-bot --tail 10
   docker compose logs support-bot --tail 10
   ```

3. **Протестируйте доступ**:
   - Напишите любому из ботов
   - Проверьте, что команды администратора работают

## Роли администраторов

### Super Admin (SUPER_ADMIN_ID):
- Полный доступ ко всем функциям
- Управление другими администраторами
- Доступ к критическим настройкам
- Просмотр финансовой отчетности

### Admin (ADMIN_USER_IDS):
- Управление пользователями
- Просмотр статистики
- Техническая поддержка
- Управление подписками

## Безопасность

⚠️ **Важные рекомендации**:

1. **Не делитесь Admin ID** с посторонними
2. **Регулярно проверяйте** список администраторов
3. **Удаляйте неактивных** администраторов
4. **Используйте разные ID** для разных ролей

## Устранение неполадок

### Проблема: "Доступ запрещен"
- Проверьте правильность вашего User ID
- Убедитесь, что ID добавлен в ADMIN_USER_IDS
- Перезапустите ботов после изменений

### Проблема: "Бот не отвечает"
- Проверьте логи ботов
- Убедитесь, что токены ботов корректны
- Проверьте подключение к базе данных

### Проблема: "Команды не работают"
- Убедитесь, что вы Super Admin
- Проверьте синтаксис команд
- Посмотрите документацию по командам

## Команды администратора

После настройки будут доступны команды:

- `/admin` - панель администратора
- `/stats` - статистика системы
- `/users` - управление пользователями
- `/payments` - управление платежами
- `/settings` - настройки системы

## Резервное копирование

Скрипт автоматически создает резервные копии .env файла:
```
.env.backup.YYYYMMDD_HHMMSS
```

Для восстановления:
```bash
cp .env.backup.YYYYMMDD_HHMMSS .env
docker compose restart client-bot admin-bot support-bot
```
