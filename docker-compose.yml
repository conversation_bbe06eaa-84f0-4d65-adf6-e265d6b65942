# =============================================================================
# UnveilVPN Shop - Production Docker Compose Configuration
# =============================================================================

networks:
  unveilvpn-network:
    driver: bridge

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  nginx_logs:
    driver: local

services:
  # Nginx Reverse Proxy with SSL
  nginx:
    build:
      context: ./nginx
      dockerfile: Dockerfile
    container_name: unveilvpn-nginx-prod
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl/certs:/etc/ssl/certs:ro
      - ./ssl/private:/etc/ssl/private:ro
      - ./ssl/certbot:/var/www/certbot:ro
      - nginx_logs:/var/log/nginx
    networks:
      - unveilvpn-network
    depends_on:
      api:
        condition: service_healthy
      landing:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 128M
          cpus: '0.1'

  # PostgreSQL 16 Database (Production)
  postgres:
    image: postgres:16-alpine
    container_name: unveilvpn-postgres-prod
    restart: unless-stopped
    environment:
      POSTGRES_DB: ${DB_NAME}
      POSTGRES_USER: ${DB_USER}
      POSTGRES_PASSWORD: ${DB_PASS}
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./postgresql/init:/docker-entrypoint-initdb.d:ro
    networks:
      - unveilvpn-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_USER} -d ${DB_NAME}"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'

  # Redis for caching and sessions (Production)
  redis:
    image: redis:7-alpine
    container_name: unveilvpn-redis-prod
    restart: unless-stopped
    command: >
      redis-server
      --appendonly yes
      --requirepass ${REDIS_PASSWORD}
      --maxmemory 512mb
      --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    networks:
      - unveilvpn-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'

  # FastAPI Application (Production)
  api:
    build:
      context: .
      dockerfile: docker/Dockerfile.api
    container_name: unveilvpn-api-prod
    restart: unless-stopped
    environment:
      ENVIRONMENT: "production"
      DEBUG: "false"
      LOG_LEVEL: "INFO"

      # Database
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: ${DB_NAME}
      DB_USER: ${DB_USER}
      DB_PASS: ${DB_PASS}
      DATABASE_URL: ${DATABASE_URL}

      # Redis
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: ${REDIS_PASSWORD}
      REDIS_URL: ${REDIS_URL}

      # API Settings
      API_HOST: "0.0.0.0"
      API_PORT: 8000
      API_SECRET_KEY: ${API_SECRET_KEY}
      API_ALGORITHM: ${API_ALGORITHM}
      API_ACCESS_TOKEN_EXPIRE_MINUTES: ${API_ACCESS_TOKEN_EXPIRE_MINUTES}

      # Security
      JWT_SECRET: ${JWT_SECRET}
      ENCRYPTION_KEY: ${ENCRYPTION_KEY}
      WEBHOOK_SECRET: ${WEBHOOK_SECRET}

      # Payment Systems
      YOOKASSA_SHOPID: ${YOOKASSA_SHOPID}
      YOOKASSA_SECRET_KEY: ${YOOKASSA_SECRET_KEY}
      CRYPTOMUS_MERCHANT_ID: ${CRYPTOMUS_MERCHANT_ID}
      CRYPTOMUS_API_KEY: ${CRYPTOMUS_API_KEY}

      # VPN Panel (Remnawave)
      REMNAWAVE_PANEL_URL: ${REMNAWAVE_PANEL_URL}
      REMNAWAVE_API_KEY: ${REMNAWAVE_API_KEY}
      REMNAWAVE_SUBSCRIPTION_URL: ${REMNAWAVE_SUBSCRIPTION_URL}
      TRUSTED_HOSTS: "localhost,127.0.0.1,api"
    volumes:
      - "./logs:/app/logs"
    networks:
      - unveilvpn-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 256M
          cpus: '0.2'

  # Landing Page (Production)
  landing:
    build:
      context: ./landing
      dockerfile: Dockerfile
    container_name: unveilvpn-landing-prod
    restart: unless-stopped
    command: ["nginx", "-g", "daemon off;"]
    networks:
      - unveilvpn-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.5'
        reservations:
          memory: 64M
          cpus: '0.1'

  # Client Bot (Production)
  client-bot:
    build: 
      context: .
      dockerfile: docker/Dockerfile.client
    container_name: unveilvpn-client-bot-prod
    restart: unless-stopped
    stop_signal: SIGINT
    environment:
      CLIENT_BOT_TOKEN: ${CLIENT_BOT_TOKEN}
      BOT_TYPE: "client"
      ENVIRONMENT: "production"
      DEBUG: "false"
      LOG_LEVEL: "DEBUG"
      
      # Database
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: ${DB_NAME}
      DB_USER: ${DB_USER}
      DB_PASS: ${DB_PASS}
      
      # Redis
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: ${REDIS_PASSWORD}

      # Администраторы
      ADMIN_USER_IDS: ${ADMIN_USER_IDS}
      SUPER_ADMIN_ID: ${SUPER_ADMIN_ID}

      # Webhook
      WEBHOOK_URL: ${WEBHOOK_URL}
      WEBHOOK_SECRET: ${BOT_WEBHOOK_SECRET}
      
      # Shop Configuration
      SHOP_NAME: ${SHOP_NAME}
      PROTOCOLS: ${PROTOCOLS}
      TEST_PERIOD: ${TEST_PERIOD}
      PERIOD_LIMIT: ${PERIOD_LIMIT}
      
      # Payment Systems
      YOOKASSA_TOKEN: ${YOOKASSA_TOKEN}
      YOOKASSA_SHOPID: ${YOOKASSA_SHOPID}
      CRYPTO_TOKEN: ${CRYPTO_TOKEN}
      MERCHANT_UUID: ${MERCHANT_UUID}
      
      # VPN Panel (Remnawave)
      REMNAWAVE_PANEL_URL: ${REMNAWAVE_PANEL_URL}
      REMNAWAVE_API_KEY: ${REMNAWAVE_API_KEY}
      REMNAWAVE_SUBSCRIPTION_URL: ${REMNAWAVE_SUBSCRIPTION_URL}
      TRUSTED_HOSTS: "localhost,127.0.0.1,api"
      REMNAWAVE_PROTOCOLS: ${REMNAWAVE_PROTOCOLS}
      TEST_PERIOD_ENABLED: ${TEST_PERIOD_ENABLED}
      TEST_PERIOD_DURATION_HOURS: ${TEST_PERIOD_DURATION_HOURS}


      
      # Security
      JWT_SECRET: ${JWT_SECRET}
      ENCRYPTION_KEY: ${ENCRYPTION_KEY}
    volumes:
      - "./locales:/app/locales:ro"
      - "./logs:/app/logs"
    networks:
      - unveilvpn-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3

  # Admin Bot (Production)
  admin-bot:
    build:
      context: .
      dockerfile: docker/Dockerfile.admin
    container_name: unveilvpn-admin-bot-prod
    restart: unless-stopped
    stop_signal: SIGINT
    environment:
      ADMIN_BOT_TOKEN: ${ADMIN_BOT_TOKEN}
      BOT_TYPE: "admin"
      ENVIRONMENT: "production"
      DEBUG: "false"
      LOG_LEVEL: "DEBUG"
      
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: ${DB_NAME}
      DB_USER: ${DB_USER}
      DB_PASS: ${DB_PASS}
      
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: ${REDIS_PASSWORD}

      # Администраторы
      ADMIN_USER_IDS: ${ADMIN_USER_IDS}
      SUPER_ADMIN_ID: ${SUPER_ADMIN_ID}

      # Webhook
      WEBHOOK_URL: ${WEBHOOK_URL}
      WEBHOOK_SECRET: ${BOT_WEBHOOK_SECRET}

      JWT_SECRET: ${JWT_SECRET}
      ENCRYPTION_KEY: ${ENCRYPTION_KEY}

      # VPN Panel (Remnawave)
      REMNAWAVE_PANEL_URL: ${REMNAWAVE_PANEL_URL}
      REMNAWAVE_API_KEY: ${REMNAWAVE_API_KEY}
      REMNAWAVE_SUBSCRIPTION_URL: ${REMNAWAVE_SUBSCRIPTION_URL}
      TRUSTED_HOSTS: "localhost,127.0.0.1,api"
    volumes:
      - "./locales:/app/locales:ro"
      - "./logs:/app/logs"
    networks:
      - unveilvpn-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    deploy:
      resources:
        limits:
          memory: 8G
          cpus: '0.25'

  # Support Bot (Production)
  support-bot:
    build: 
      context: .
      dockerfile: docker/Dockerfile.support
    container_name: unveilvpn-support-bot-prod
    restart: unless-stopped
    stop_signal: SIGINT
    environment:
      SUPPORT_BOT_TOKEN: ${SUPPORT_BOT_TOKEN}
      BOT_TYPE: "support"
      ENVIRONMENT: "production"
      DEBUG: "false"
      LOG_LEVEL: "WARNING"
      
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: ${DB_NAME}
      DB_USER: ${DB_USER}
      DB_PASS: ${DB_PASS}
      
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: ${REDIS_PASSWORD}

      # Администраторы
      ADMIN_USER_IDS: ${ADMIN_USER_IDS}
      SUPPORT_USER_IDS: ${SUPPORT_USER_IDS}
      SUPER_ADMIN_ID: ${SUPER_ADMIN_ID}

      # Webhook
      WEBHOOK_URL: ${WEBHOOK_URL}
      WEBHOOK_SECRET: ${BOT_WEBHOOK_SECRET}

      JWT_SECRET: ${JWT_SECRET}
      ENCRYPTION_KEY: ${ENCRYPTION_KEY}
    volumes:
      - "./locales:/app/locales:ro"
      - "./logs:/app/logs"
    networks:
      - unveilvpn-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.25'
